apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${CRYSTAL_FE_APP_NAME}
  namespace: ${CRYSTAL_FE_K0S_NAMESPACE}
  labels:
    app: ${CRYSTAL_FE_APP_NAME}
    version: ${CRYSTAL_FE_IMAGE_TAG}
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: ${CRYSTAL_FE_APP_NAME}
  template:
    metadata:
      labels:
        app: ${CRYSTAL_FE_APP_NAME}
        version: ${CRYSTAL_FE_IMAGE_TAG}
    spec:
      imagePullSecrets:
        - name: crystal-harbor-registry
      containers:
        - name: ${CRYSTAL_FE_APP_NAME}
          image: ${CRYSTAL_FE_FULL_IMAGE_NAME}
          imagePullPolicy: Always
          ports:
            - containerPort: 80
              name: http
          resources:
            requests:
              memory: "2048Mi"
              cpu: "1000m"
            limits:
              memory: "2048Mi"
              cpu: "1000m"
          # volumeMounts:
          # - name: nginx-config
          #   mountPath: /etc/nginx/conf.d/default.conf
          #   subPath: default.conf
      # volumes:
      #   - name: nginx-config
      #     configMap:
      #       name: crystal-fe-nginx-config