# Alternative deployment with init container
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${CRYSTAL_FE_APP_NAME}
  namespace: ${CRYSTAL_FE_K0S_NAMESPACE}
  labels:
    app: ${CRYSTAL_FE_APP_NAME}
    version: ${CRYSTAL_FE_IMAGE_TAG}
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: ${CRYSTAL_FE_APP_NAME}
  template:
    metadata:
      labels:
        app: ${CRYSTAL_FE_APP_NAME}
        version: ${CRYSTAL_FE_IMAGE_TAG}
    spec:
      imagePullSecrets:
        - name: crystal-harbor-registry
      initContainers:
        - name: setup-env
          image: busybox:1.35
          command: ['sh', '-c']
          args:
            - |
              cp /tmp/env/.env /shared/.env
              chmod 644 /shared/.env
          volumeMounts:
            - name: env-secret
              mountPath: /tmp/env
            - name: shared-data
              mountPath: /shared
      containers:
        - name: ${CRYSTAL_FE_APP_NAME}
          image: ${CRYSTAL_FE_FULL_IMAGE_NAME}
          imagePullPolicy: Always
          ports:
            - containerPort: 80
              name: http
          resources:
            requests:
              memory: "2048Mi"
              cpu: "1000m"
            limits:
              memory: "2048Mi"
              cpu: "1000m"
          volumeMounts:
            - name: shared-data
              mountPath: /usr/share/nginx/html/.env
              subPath: .env
      volumes:
        - name: env-secret
          secret:
            secretName: crystal-fe-envfile
            items:
            - key: .env
              path: .env
        - name: shared-data
          emptyDir: {}
