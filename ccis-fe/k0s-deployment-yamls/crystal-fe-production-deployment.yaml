apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${CRYSTAL_FE_APP_NAME_PRODUCTION}
  namespace: ${CRYSTAL_FE_K0S_NAMESPACE_PRODUCTION}
  labels:
    app: ${CRYSTAL_FE_APP_NAME_PRODUCTION}
    version: ${CRYSTAL_FE_IMAGE_TAG_PRODUCTION}
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: ${CRYSTAL_FE_APP_NAME_PRODUCTION}
  template:
    metadata:
      labels:
        app: ${CRYSTAL_FE_APP_NAME_PRODUCTION}
        version: ${CRYSTAL_FE_IMAGE_TAG_PRODUCTION}
    spec:
      imagePullSecrets:
        - name: crystal-harbor-registry
      containers:
        - name: ${CRYSTAL_FE_APP_NAME_PRODUCTION}
          image: ${CRYSTAL_FE_FULL_IMAGE_NAME_PRODUCTION}
          imagePullPolicy: Always
          ports:
            - containerPort: 80
              name: http
          resources:
            requests:
              memory: "2048Mi"
              cpu: "1000m"
            limits:
              memory: "2048Mi"
              cpu: "1000m"
          # volumeMounts:
          # - name: nginx-config
          #   mountPath: /etc/nginx/conf.d/default.conf
          #   subPath: default.conf
      # volumes:
      #   - name: nginx-config
      #     configMap:
      #       name: crystal-fe-nginx-config