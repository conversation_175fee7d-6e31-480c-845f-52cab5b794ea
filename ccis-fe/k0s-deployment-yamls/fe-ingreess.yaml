# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: crystal-fe-ingress
  namespace: crystal-fe
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-production
    nginx.ingress.kubernetes.io/ssl-redirect: "true"  # can set "false" temporarily during first issue if needed
spec:
  tls:
    - hosts:
        - crystal.climbs.coop
      secretName: web-tls            # cert-manager will create/renew this Secret in crystal-fe
  rules:
    - host: crystal.climbs.coop
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service: { name: crystal-fe-service, port: { number: 80 } }
