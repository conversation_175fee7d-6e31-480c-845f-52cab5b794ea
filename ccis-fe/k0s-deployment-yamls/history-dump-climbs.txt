 1956  which helm
 1957  k config current-context
 1958  export KUBECONFIG=~/.kube/climbs-staging-config 
 1959  k config current-context
 1960  k get ns -A
 1961  helm repo update
 1962  helm upgrade --install metallb -n metallb-system metallb/metallb --version 0.14.3
 1963  helm history metallb -n metallb-system
 1964  kubectl get all -n metallb-system
 1965  k get statefulset -A
 1966  k get deployment -A
 1967  k get replicaset -A
 1968  kubectl get events
 1969  helm uninstall metallb -n metallb-system
 1970  helm history metallb -n metallb-system
 1971  helm upgrade --install metallb -n metallb-system metallb/metallb --version 0.14.3
 1972  k get IPAddressPool -n metallb-system
 1973  kubectl get L2Advertisement -n metallb-system
 1974  kubectl config current-context
 1975  kubectl create secret docker-registry crystal-harbor-registry --docker-server="https://registry.climbs.coop" --docker-username="robot$crystal-be+crystal-be-pull-k8s" --docker-password="e9Uv1NS2vJljimdn3Dbb3U5qJKcBZnoT" -n crystal-be 
 1976  k config get secrets -n crystal-be
 1977  k get secrets -n crystal-be
 1978  k get secret crystal-harbor-registry -o yaml -n crystal-be
 1979  ls -l
 1980  cd ..
 1981  k config current-context
 1982  cd ~/Projects/Learning/
 1983  ls -l
 1984  cd kubernetes-integration-templates
 1985  ls -l
 1986  cd crystal-be-test/
 1987  ls -l
 1988  cat env 
 1989  ls -l
 1990  rm -f crystal-be-k8senv.yaml 
 1991  ls -l
 1992  kubectl create secret generic crystal-be-envs --from-env-file=env -n crystal-be
 1993  cat env
 1994  grep BROADCAST env
 1995  vi env 
 1996  grep BROADCAST env
 1997  kubectl create secret generic crystal-be-envs --from-env-file=env -n crystal-be
 1998  k get secret crystal-be-envs -o yaml -n crystal-be
 1999  clear
 2000  ls -l
 2001  vi deployment-envs.txt 
 2002  source deployment-envs.txt 
 2003  env | 
 2004  env
 2005  cat deployment-envs.txt 
 2006  envsubst < kubectl apply -f crystal-be-test-deployment.yaml 
 2007  envsubst < crystal-be-test-deployment.yaml | kubectl apply -f 
 2008  envsubst < crystal-be-test-deployment.yaml | kubectl apply -f - 
 2009  env
 2010  docker login --help
 2011  docker login registry.climbs.coop -u robot$crystal-be+crystal-be-pull-k8s 
 2012  docker login https://registry.climbs.coop -u robot$crystal-be+crystal-be-pull-k8s 
 2013  docker login registry.climbs.coop -u admin_ecr 
 2014  docker login registry.climbs.coop -u robot\$crystal-be+crystal-be-pull-k8s
 2015  docker login registry.climbs.coop -u "robot$crystal-be+crystal-be-pull-k8s"
 2016  docker login registry.climbs.coop -u robot\$crystal-be+crystal-be-pull-k8s
 2017  docker pull registry.climbs.coop/crystal-be/crystal-be:0.0.43-beta
 2018  k get secrets -n crystal-be
 2019  k get secret crystal-harbor-registry -o yaml -n crystal-be > crystal-harbor-registry.yaml
 2020  vi crystal-harbor-registry.yaml 
 2021  k delete secret crystal-harbor-registry -n crystal-be
 2022  kubectl create secret docker-registry crystal-harbor-registry --docker-server="https://registry.climbs.coop" --docker-username="robot\$crystal-be+crystal-be-pull-k8s" --docker-password="e9Uv1NS2vJljimdn3Dbb3U5qJKcBZnoT" -n crystal-be 
 2023  cat crystal-be-test-deployment.yaml 
 2024  envsubst < crystal-be-test-deployment.yaml | kubectl apply -f -
 2025  kubectl logs pod/crystal-be-5d4464fd54-7hz49 -n crystal-be
 2026  vi crystal-be-test-deployment.yaml 
 2027  cat deployment-envs.txt 
 2028  env | grep FULL_IMAGE_NAME
 2029  envsubst < crystal-be-test-deployment.yaml | kubectl apply -f -
 2030  kubectl logs deployment/crystal-be
 2031  kubectl logs deployment/crystal-be -n crystal-be
 2032  ls -l
 2033  cd ..
 2034  ls -l
 2035  cd ..
 2036  ls -l
 2037  cd demo/
 2038  ls -l
 2039  cd kubernetes
 2040  ls -l
 2041  cat nginx-deployment-ni-jade.yaml 
 2042  cp nginx-deployment-ni-jade.yaml nginx-test-deploy.yaml
 2043  vi nginx-test-deploy.yaml 
 2044  kubectl create ns nginx-test
 2045  cat nginx-test-deploy.yaml 
 2046  k apply -f nginx-test-deploy.yaml -n nginx-test
 2047  vi apache-test-deploy.yaml
 2048  k delete ns nginx-test
 2049  k create ns apache-test
 2050  vi apache-test-deploy.yaml 
 2051  k apply -f apache-test-deploy.yaml -n apache-test
 2052  cd ..
 2053  ls -l
 2054  cd kubernetes-integration-templates
 2055  ls -l
 2056  cd crystal-be-test/
 2057  ls -l
 2058  vi deployment-envs.txt 
 2059  cat deployment-envs.txt 
 2060  source deployment-envs.txt 
 2061  env | grep IMAGE
 2062  envsubst < crystal-be-test-deployment.yaml | kubectl apply -f -
 2063  k logs pod/crystal-be-6775b9467f-nscbl -n crystal-be
 2064  cat crystal-be-test-deployment.yaml 
 2065  history 
 2066  history | grep -A 1956 -w 2065 > history-dump-climbs.txt
 2067  cat history-dump-climbs.txt 
 2068  history | sed -n '1956,$p' > history-dump-climbs.txt
 2069  cat history-dump-climbs.txt 
 2070  history | sed -n '1956,$p'
 2071  history | sed -n '1956'
 2072  history | sed -n '1956, $p'
 2073  history | grep -A 1956 -w 2065
 2074  history | grep -A 100 -w 1956 
 2075  history | grep -A 1000 -w 1956 
 2076  history | grep -A 1000 -w 1956 > history-dump-climbs.txt
