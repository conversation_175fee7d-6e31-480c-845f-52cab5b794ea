// Environment configuration utility
class EnvConfig {
  private static instance: EnvConfig;
  private config: Record<string, string> = {};
  private loaded = false;

  private constructor() {}

  static getInstance(): EnvConfig {
    if (!EnvConfig.instance) {
      EnvConfig.instance = new EnvConfig();
    }
    return EnvConfig.instance;
  }

  async loadConfig(): Promise<void> {
    if (this.loaded) return;

    try {
      // Try to fetch .env file from the server
      const response = await fetch('/.env');
      if (response.ok) {
        const envText = await response.text();
        this.parseEnvText(envText);
      } else {
        console.warn('Could not load .env file, using build-time environment variables');
        this.loadBuildTimeEnv();
      }
    } catch (error) {
      console.warn('Error loading .env file:', error);
      this.loadBuildTimeEnv();
    }

    this.loaded = true;
  }

  private parseEnvText(envText: string): void {
    const lines = envText.split('\n');
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          const value = valueParts.join('=').replace(/^["']|["']$/g, '');
          this.config[key.trim()] = value;
        }
      }
    }
  }

  private loadBuildTimeEnv(): void {
    // Fallback to build-time environment variables
    Object.keys(import.meta.env).forEach(key => {
      if (key.startsWith('VITE_')) {
        this.config[key] = import.meta.env[key];
      }
    });
  }

  get(key: string, defaultValue: string = ''): string {
    return this.config[key] || import.meta.env[key] || defaultValue;
  }

  getAll(): Record<string, string> {
    return { ...this.config };
  }
}

export const envConfig = EnvConfig.getInstance();
