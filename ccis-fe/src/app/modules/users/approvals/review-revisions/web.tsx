import Typography from "@components/common/Typography";
import { IGuideline, IGuidelineContent, IGuidelineContentTable } from "@interface/guidelines.interface";
import dayjs from "dayjs";
import { FC, Fragment, useState } from "react";
import { FaChevronLeft } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { TRevisionsWithProduct } from "@state/types/products";
import { useRef } from "react";
import { capitalizeFirstLetterOnly, getTextStatusColor } from "@helpers/text";
import { FaRegFilePdf, FaUsers } from "react-icons/fa6";
import { AttachmentTags } from "@enums/attachment-tags";
import Signatories2 from "./Tabs/Signatories2";
import Tabs from "@components/common/Tabs";
import Button from "@components/common/Button";
import { FiDownload } from "react-icons/fi";
import ApprovalHistory from "@modules/admin/products/components/Common/ApprovalHistory";

type TProps = {
  product?: TRevisionsWithProduct;
  loading?: boolean;
  isSignable?: boolean;
  handleModal?: () => void;
  handlePDFReport?: () => void;
  fileBaseUrl?: string;
  toggleRemarksModal?: () => void;
};

const ReviewRevisionFromEmailWeb: FC<TProps> = ({ loading = false, product, isSignable, fileBaseUrl, handleModal, handlePDFReport, toggleRemarksModal }) => {
  const navigate = useNavigate();
  const tableOfContentsRef = useRef<(HTMLLIElement | HTMLDivElement)[]>([]);
  const [currentIndex, setCurrentIndex] = useState<number>(0);

  const standard =
    product?.commission?.commissionDetails?.filter((row) => {
      return row.commissionAgeType?.name?.toLocaleLowerCase() !== "standard";
    }) ?? [];

  const scrollToSection = (index: number) => {
    tableOfContentsRef?.current[index].scrollIntoView({ behavior: "smooth" });
    setCurrentIndex(index);
  };

  const generateListItem = (label: string, index: number) => {
    return (
      <li
        key={`toc-${index}`}
        className={`
          mt-2 list-none font-poppins-semibold hover:bg-zinc-100 p-4 rounded-md cursor-pointer
          ${currentIndex === index ? "text-primary bg-sky-50" : "text-zinc-500"}
        `}
        onClick={() => scrollToSection(index)}
      >
        <Typography size="sm">{label}</Typography>
      </li>
    );
  };

  const generateTableOfContents = (productGuidelines: IGuideline[]) => {
    const toc = productGuidelines?.map((value, index) => {
      return generateListItem(value.label, index);
    });

    const lastIndex = productGuidelines.length;

    toc[lastIndex] = generateListItem("Commission Structure", lastIndex);

    return toc;
  };

  return (
    <Fragment>
      <div className="flex flex-1 flex-col mx-5 overflow-x-hidden">
        {!loading && (
          <div className="flex flex-1 flex-col mx-4 overscroll-y-none">
            <div className="flex -ml-4">
              <button
                className="btn btn-sm btn-ghost"
                onClick={() => {
                  navigate(-1);
                }}
              >
                <FaChevronLeft />
                <Typography>Back</Typography>
              </button>
            </div>
            <div className="divider"></div>
            <div className="flex flex-1 flex-col pl-2">
              <div className="flex flex-1 flex-row">
                <div className="flex flex-1 flex-row">
                  <div className="min-w-40">
                    <Typography size="md" className="!text-slate-600 mr-3">
                      PRODUCT NAME
                    </Typography>
                  </div>
                  <div>
                    <Typography size="md" className="font-poppins-semibold">
                      {product?.product?.name}
                    </Typography>
                  </div>
                </div>
                <div className="flex flex-1 flex-row pl-10">
                  <div className="min-w-40">
                    <Typography size="md" className="!text-slate-600 mr-3">
                      REVISION No.
                    </Typography>
                  </div>
                  <div>
                    <Typography size="md" className="font-poppins-semibold">
                      {product?.revisionNumber?.toUpperCase()}
                    </Typography>
                  </div>
                </div>
              </div>
              <div className="flex flex-1 flex-row mt-4">
                <div className="flex flex-1 flex-row">
                  <div className="min-w-40">
                    <Typography size="md" className="!text-slate-600 mr-3">
                      CREATED BY
                    </Typography>
                  </div>
                  <div>
                    <Typography size="md" className="font-poppins-semibold">{`${product?.createdBy?.firstname} ${product?.createdBy?.lastname}`}</Typography>
                  </div>
                </div>
                <div className="flex flex-1 flex-row pl-10">
                  <div className="min-w-40">
                    <Typography size="md" className="!text-slate-600 mr-3">
                      CREATION DATE
                    </Typography>
                  </div>
                  <div>
                    <Typography size="md" className="font-poppins-semibold">
                      {dayjs(product?.createdAt).format("D MMMM YYYY")}
                    </Typography>
                  </div>
                </div>
              </div>
              <div className="flex flex-1 flex-row mt-4">
                <div className="flex flex-1 flex-row">
                  <div className="min-w-40">
                    <Typography className="!text-slate-600">APPROVAL STATUS</Typography>
                  </div>
                  <div className="flex flex-1 flex-row">
                    <Typography className={`${getTextStatusColor(product?.approvalStatus ?? "")} flex items-center justify-center font-poppins-semibold`}>
                      {product?.approvalStatus?.toUpperCase()}
                    </Typography>
                    {isSignable && (
                      <button className="btn btn-xs w-36 ml-2 btn-primary" onClick={handleModal}>
                        Change Status
                      </button>
                    )}
                  </div>
                </div>
                <div className="flex flex-1 flex-row pl-10">
                  <div className="min-w-40">
                    <Typography size="md" className="!text-slate-600">
                      DATE APPROVED
                    </Typography>
                  </div>
                  <div>
                    <Typography size="md" className="font-poppins-semibold">
                      {product?.approvedAt ? dayjs(product?.approvedAt).format("D MMMM YYYY") : ""}
                    </Typography>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex flex-1 justify-end my-2 ">
              <div className="flex flex-1 flex-row justify-end">
                <Button
                  type="button"
                  variant="primary"
                  outline
                  onClick={() => handlePDFReport && handlePDFReport()}
                  classNames="flex flex-row items-center border-0 bg-none text-primary text-sm bg-zinc-50 hover:bg-primary hover:text-white mr-2"
                >
                  <FiDownload className="mr-2" />
                  Export
                </Button>
              </div>
            </div>
            <div className="flex flex-row flex-1 p-2 mt-5 gap-4">
              <div className="flex flex-1 flex-col p-4 ">
                <Typography className="text-center text-lg font-semibold">Table of Contents</Typography>
                <ul className="mt-5">{generateTableOfContents(product?.productGuidelines ?? [])}</ul>
              </div>
              <div className="flex flex-[2.8] flex-col p-4 px-8">
                <div className="flex flex-1 flex-col">
                  <Typography className="!text-black !text-lg text-center font-poppins-semibold">{product?.product?.name}</Typography>
                  <div className="flex flex-1 flex-col mt-10">
                    <Typography className="!text-black font-semibold text-md">Product Description</Typography>
                    <div
                      className="  w-full"
                      dangerouslySetInnerHTML={{
                        __html: product?.product?.description || "",
                      }}
                    ></div>
                  </div>
                </div>
                {product?.productGuidelines && (
                  <div className="flex flex-1 flex-col mt-10">
                    {product?.productGuidelines?.map((value, gIndex) => {
                      return (
                        <div
                          ref={(e) => {
                            if (e) {
                              tableOfContentsRef.current[gIndex] = e;
                            }
                          }}
                          key={`guideline-${gIndex}`}
                          className="flex flex-1 flex-col mb-10"
                        >
                          <Typography className="text-md mb-2 font-semibold">{value.label}</Typography>
                          {value.productGuideline.map((pgValue, pgIndex) => {
                            let listValue;
                            let tableValue;
                            if (pgValue.type === "list") {
                              listValue = pgValue.value as IGuidelineContent[];
                            }

                            if (pgValue.type === "table") {
                              tableValue = pgValue.value as IGuidelineContentTable;
                            }

                            return (
                              <div key={`pg-${pgIndex}`}>
                                {pgValue.type === "textfield" && (
                                  <Fragment>
                                    <Typography className="ml-4 mt-4 text-justify">{pgValue.value as string}</Typography>
                                  </Fragment>
                                )}
                                {pgValue.type === "list" && (
                                  <Fragment>
                                    <Typography className="ml-4 mt-4 text-justify">{pgValue.label}</Typography>
                                    <ul className="list-disc ml-12 ">
                                      {listValue &&
                                        listValue.map((listValue, listIndex) => {
                                          return (
                                            <li key={`listItem-${listIndex}`} className="mt-4">
                                              <Typography className="text-justify !text-black">{listValue.value as string}</Typography>
                                            </li>
                                          );
                                        })}
                                    </ul>
                                  </Fragment>
                                )}
                                {pgValue.type === "texteditor" && (
                                  <Fragment>
                                    <div
                                      className="ml-10 mt-4 text-black text-justify"
                                      dangerouslySetInnerHTML={{
                                        __html: (pgValue as any).value ?? "",
                                      }}
                                    ></div>
                                  </Fragment>
                                )}
                                {pgValue.type === "table" && (
                                  <Fragment>
                                    <div className="flex flex-1 mt-4 mx-6 overflow-x-scroll">
                                      <table className="table border-[1px]">
                                        <thead className="table-header-group">
                                          <tr>
                                            {tableValue?.columns?.map((cValue, cIndex) => {
                                              return (
                                                <td key={`col-${cIndex}`} className="table-cell border-[1px]">
                                                  <Typography className="font-semibold text-sm">{cValue.value as string}</Typography>
                                                </td>
                                              );
                                            })}
                                          </tr>
                                        </thead>
                                        <tbody>
                                          {tableValue?.rows?.map((rValue, rIndex) => {
                                            return (
                                              <tr key={`row-${rIndex}`}>
                                                {rValue.map((cell, cellIndex) => {
                                                  return (
                                                    <td className="border-[1px] text-xs" key={`cell-${cellIndex}`}>
                                                      <Typography className="text-xs">{cell.value as string}</Typography>
                                                    </td>
                                                  );
                                                })}
                                              </tr>
                                            );
                                          })}
                                        </tbody>
                                      </table>
                                    </div>
                                  </Fragment>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      );
                    })}
                  </div>
                )}
                {product?.commission && (
                  <div
                    ref={(e) => {
                      if (e) {
                        const lastIndex = product?.productGuidelines?.length ?? 0;
                        tableOfContentsRef.current[lastIndex] = e;
                      }
                    }}
                    className="flex flex-1 flex-col"
                  >
                    <Typography size="md">Commission Structure</Typography>
                    <Fragment>
                      <Typography size="sm" className="ml-4 mt-4">
                        {parseFloat(product?.commission?.maximumDisposableRate ?? "").toFixed(2)}% Maximum Disposable Commission - Standard Rate
                      </Typography>
                      <div className="flex-flex-1 mt-6 mx-6 overflow-x-scroll">
                        <table className="table overflow-scroll">
                          <thead>
                            <tr>
                              <td className="table-cell border-[1px] text-center text-md text-black">Type</td>
                              <td className="table-cell border-[1px] text-center text-md text-black">Age Type</td>
                              {standard.length > 0 && (
                                <Fragment>
                                  <td className="table-cell border-[1px] text-center text-md text-black">Age From</td>
                                  <td className="table-cell border-[1px] text-center text-md text-black">Age To</td>
                                </Fragment>
                              )}
                              <td className="table-cell border-[1px] text-center text-md text-black">Rate</td>
                            </tr>
                          </thead>
                          {product?.commission.commissionDetails && (
                            <tbody>
                              {product?.commission.commissionDetails?.map((rowValue, rowIndex) => {
                                return (
                                  <tr key={`commissionDetailsRow-${rowIndex}`}>
                                    <td className="table-cell border-[1px] text-sm text-black">{capitalizeFirstLetterOnly(rowValue?.commissionType?.commissionName ?? "")}</td>
                                    <td className="table-cell border-[1px] text-sm text-black">{capitalizeFirstLetterOnly(rowValue?.commissionAgeType?.name ?? "")}</td>
                                    {standard.length > 0 && (
                                      <Fragment>
                                        <td className="table-cell border-[1px] text-center text-sm text-black">{rowValue.ageFrom}</td>
                                        <td className="table-cell border-[1px] text-center  text-sm text-black">{rowValue.ageTo}</td>
                                      </Fragment>
                                    )}
                                    <td className="table-cell border-[1px] text-center  text-sm text-black">{rowValue.rate ? parseFloat(rowValue.rate.toString()).toFixed(0) : ""}%</td>
                                  </tr>
                                );
                              })}
                            </tbody>
                          )}
                        </table>
                      </div>
                    </Fragment>
                  </div>
                )}
              </div>
              <div className="flex flex-[1.2] align-center flex-col p-4 bg-sky-50">
                <Typography className="!text-black text-lg text-left font-semibold">Other Documents to Review</Typography>
                <div className="flex flex-col mb-8 overflow-x-hidden overflow-y-auto">
                  {(product?.attachments?.length ?? 0) === 0 && (
                    <div className="flex justify-center">
                      <Typography className="text-zinc-400">No attachments</Typography>
                    </div>
                  )}
                  {product?.attachments?.map((attachment, index) => {
                    if (attachment.tag === AttachmentTags.SIGNATORY_SIGNED) return null;
                    return (
                      <div key={`attachment-${index}`} className="flex flex-row justify-start mt-5">
                        <a className="flex gap-2" href={`${fileBaseUrl}/${attachment.filepath}`} target="_blank" rel="noreferrer">
                          <FaRegFilePdf size={20} color="red" />
                          <Typography size="sm">{attachment.label}</Typography>
                        </a>
                      </div>
                    );
                  })}
                </div>
                <Typography className="!text-black text-lg text-left font-semibold">Signed Documents</Typography>
                <div className="flex flex-col mb-10 overflow-x-hidden overflow-y-auto">
                  {(product?.attachments?.length ?? 0) === 0 && (
                    <div className="flex justify-center">
                      <Typography className="text-zinc-400">No Attachments</Typography>
                    </div>
                  )}
                  {product?.attachments?.map((attachment, index) => {
                    if (attachment.tag !== AttachmentTags.SIGNATORY_SIGNED) return null;
                    return (
                      <div key={`attachment-${index}`} className="flex flex-row justify-start mt-5">
                        <a className="flex gap-2" href={`${fileBaseUrl}/${attachment.filepath}`} target="_blank" rel="noreferrer">
                          <FaRegFilePdf size={20} color="red" />
                          <Typography size="sm">{attachment.label}</Typography>
                        </a>
                      </div>
                    );
                  })}
                </div>
                <div className="flex flex-row justify-center items-center">
                  <FaUsers size={20} className="mr-2" />
                  <Typography size="lg">SIGNEES</Typography>
                </div>
                <div className="flex flex-1 flex-col pr-2">
                  <Tabs
                    className="mt-4"
                    contentClass="p-6 border-0"
                    headerClass="text-xs rounded-t-lg h-10"
                    activeTabClassName="bg-primary !text-white text-xs"
                    headers={["Ongoing Approvals", "Approval History"]}
                    contents={[<Signatories2 signatories={product?.signatories} toggleRemarksModal={toggleRemarksModal} />, <ApprovalHistory revisionId={product?.id ?? ""} />]}
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Fragment>
  );
};

export default ReviewRevisionFromEmailWeb;
