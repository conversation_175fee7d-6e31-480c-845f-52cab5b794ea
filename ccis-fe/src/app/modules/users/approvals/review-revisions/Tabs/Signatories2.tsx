import { ISignatories } from "@interface/products.interface";
import dayjs from "dayjs";
import { FC } from "react";
import { LiaComments } from "react-icons/lia";
import Tooltip from "@components/common/Tooltip";
import { useProductActions } from "@state/reducer/products";
import { CiCircleCheck } from "react-icons/ci";
import { CiCircleAlert } from "react-icons/ci";
import { RevisionStatus } from "@enums/revision-status";
import { getTextStatusColor, formatStringAtoZ0to9 } from "@helpers/text";
import { Statuses } from "@constants/global-constant-value";

type TProps = {
  signatories?: ISignatories[];
  toggleRemarksModal?: () => void;
  selectedSignatory?: (data: any) => void;
};

const Signatories2: FC<TProps> = ({ signatories, toggleRemarksModal, selectedSignatory }) => {
  const { setSignatory } = useProductActions();
  const handleModal = (value: ISignatories) => {
    setSignatory(value);
    toggleRemarksModal && toggleRemarksModal();
    selectedSignatory && selectedSignatory(value);
  };
  return (
    <ul className="timeline timeline-compact timeline-vertical !items-start w-full px-1 sm:px-2">
      {signatories?.map((value, index) => {
        const isApproved = value?.approvalStatus === RevisionStatus.approved || value?.status === RevisionStatus.approved;
        const approvalStatus =
          formatStringAtoZ0to9(value?.approvalStatus || "") === formatStringAtoZ0to9(RevisionStatus.approved) ||
          formatStringAtoZ0to9(value?.status || "") === formatStringAtoZ0to9(RevisionStatus.approved)
            ? RevisionStatus.approved
            : formatStringAtoZ0to9(value?.approvalStatus || "") === formatStringAtoZ0to9(RevisionStatus.rejected) ||
                formatStringAtoZ0to9(value?.status || "") === formatStringAtoZ0to9(RevisionStatus.rejected)
              ? RevisionStatus.rejected
              : RevisionStatus.pending;
        const signedTimestamp = value.datetimeSigned ? dayjs(value.datetimeSigned).format("MM/DD/YY h:m A") : undefined;
        return (
          <li key={`timeline-item-${index}`} className="w-full m-0 -mt-1 sm:-mt-2">
            <hr className="mt-0" />
            <div
              className={`timeline-horizontal ml-1 -mt-4 rounded-full ${
                approvalStatus === Statuses.APPROVED ? "bg-success text-white" : approvalStatus === Statuses.REJECTED ? "text-white bg-red-500" : "text-white bg-amber-300"
              }`}
            >
              {isApproved && <CiCircleCheck size={20} />}
              {!isApproved && <CiCircleAlert size={20} />}
            </div>
            <div className="timeline-end timeline-box w-full ml-1 sm:ml-4 mt-2 sm:mt-3 min-h-[5rem] p-2 sm:p-4">
              <div className="flex items-center gap-2 sm:gap-3 mb-2">
                <div className="size-8 sm:size-10 flex justify-center text-primary flex-shrink-0">
                  {value.user?.profilePicturePath ? (
                    <img src={`${import.meta.env.VITE_AWS_S3_ENDPOINT}/${value.user.profilePicturePath}`} alt="User Profile" className="rounded-full w-8 h-8 sm:w-10 sm:h-10 object-cover" />
                  ) : (
                    <div className="text-xs sm:text-sm text-center text-black bg-slate-400 h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full">
                      {value.user?.firstname[0]}
                      {value.user?.lastname[0]}
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-xs sm:text-sm truncate">{value?.user?.firstname + " " + value?.user?.middlename + " " + value?.user?.lastname}</div>
                  <div className="text-xs text-zinc-400 truncate">{value?.user?.position?.positionName}</div>
                </div>
              </div>
              {/* <div className="flex flex-wrap items-center gap-2 text-[0.6rem] sm:text-xs"> */}
              <div className="flex flex-row flex-wrap  justify-center  items-center gap-1 text-[0.6rem] sm:text-xs border-t border-zinc-200 pt-2 overflow-auto">
                <span className={`${getTextStatusColor((value?.approvalStatus?.toUpperCase() ?? "") || (value?.status?.toUpperCase() ?? ""), false)}   flex-shrink-0`}>
                  {value?.approvalStatus || value?.status}
                </span>
                {signedTimestamp && <span className="text-zinc-400 flex-shrink-0 text-[0.55rem] sm:text-[0.6rem]">{signedTimestamp}</span>}
                <div className="ml-auto flex-shrink-0">
                  <Tooltip text="View Remarks" className="cursor-pointer" position="top">
                    <LiaComments className="mr-1 text-primary" size={20} onClick={() => handleModal(value)} />
                  </Tooltip>
                </div>
              </div>
              {/* <div className="flex flex-row flex-wrap justify-center items-center gap-1 text-[0.6rem] sm:text-xs border-t border-zinc-200 pt-2 overflow-auto">
                <span className={`${getTextStatusColor((value?.approvalStatus?.toUpperCase() ?? "") || (value?.status?.toUpperCase() ?? ""))} flex-shrink-0 text-[0.55rem] sm:text-xs`}>
                  {value?.approvalStatus || value?.status}
                </span>
                {signedTimestamp && <span className="text-zinc-400 flex-shrink-0 text-[0.55rem] sm:text-xs">{signedTimestamp}</span>}
                <div className="ml-auto flex-shrink-0">
                  <Tooltip text="View Remarks" className="cursor-pointer" position="top">
                    <LiaComments className="mr-1 text-primary" size={20} onClick={() => handleModal(value)} />
                  </Tooltip>
                </div>
              </div> */}
            </div>
            <hr />
          </li>
        );
      })}
    </ul>
  );
};

export default Signatories2;
