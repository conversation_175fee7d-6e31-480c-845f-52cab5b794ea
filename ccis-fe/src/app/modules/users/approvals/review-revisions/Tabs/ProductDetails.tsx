import Typography from "@components/common/Typography";
import { IGuidelineContent, IGuidelineContentTable } from "@interface/guidelines.interface";
import { TProductRevisionsWithProduct } from "@state/types/users-product-approval";
import { FC, Fragment } from "react";

type TProps = {
  product?: TProductRevisionsWithProduct;
};

const ProductDetails: FC<TProps> = ({ product }) => {
  const standard =
    product?.commission?.commissionDetails?.filter((row) => {
      return row.commissionAgeType?.name?.toLocaleLowerCase() !== "standard";
    }) ?? [];

  return (
    <Fragment>
      <div className="flex flex-1 flex-col mt-2">
        <Typography className="!text-black mt-4">Product Description</Typography>
        <Typography className="!text-black text-justify mt-4">{product?.product?.description}</Typography>
      </div>
      {product?.productGuidelines && (
        <div className="flex flex-1 flex-col mt-10">
          {product?.productGuidelines?.map((value, gIndex) => {
            return (
              <div key={`guideline-${gIndex}`} className="flex flex-1 flex-col mb-10">
                <Typography className="text-[18px] mb-4">{value.label}</Typography>
                {value.productGuideline.map((pgValue, pgIndex) => {
                  let listValue;
                  let tableValue;
                  if (pgValue.type === "list") {
                    listValue = pgValue.value as IGuidelineContent[];
                  }

                  if (pgValue.type === "table") {
                    tableValue = pgValue.value as IGuidelineContentTable;
                  }

                  return (
                    <div key={`pg-${pgIndex}`}>
                      {pgValue.type === "textfield" && (
                        <Fragment>
                          <Typography className="ml-4 mt-4 text-justify">{pgValue.value as string}</Typography>
                        </Fragment>
                      )}
                      {pgValue.type === "list" && (
                        <Fragment>
                          <Typography className="ml-4 mt-4 text-justify">{pgValue.label}</Typography>
                          <ul className="list-disc ml-12">
                            {listValue &&
                              listValue.map((listValue, listIndex) => {
                                return (
                                  <li key={`listItem-${listIndex}`} className="mt-4">
                                    <Typography className="text-justify">{listValue.value as string}</Typography>
                                  </li>
                                );
                              })}
                          </ul>
                        </Fragment>
                      )}
                      {pgValue.type === "texteditor" && (
                        <Fragment>
                          <div
                            className="ml-10 mt-10 text-primary"
                            dangerouslySetInnerHTML={{
                              __html: (pgValue as any).value || "",
                            }}
                          ></div>
                        </Fragment>
                      )}
                      {pgValue.type === "table" && (
                        <Fragment>
                          <div className="flex flex-1 mt-10 mx-6 overflow-x-scroll">
                            <table className="table border-[1px]">
                              <thead className="table-header-group">
                                <tr>
                                  {tableValue?.columns?.map((cValue, cIndex) => {
                                    return (
                                      <td key={`col-${cIndex}`} className="table-cell border-[1px]">
                                        <Typography className="font-semibold text-xs sm:text-sm">{cValue.value as string}</Typography>
                                      </td>
                                    );
                                  })}
                                </tr>
                              </thead>
                              <tbody>
                                {tableValue?.rows?.map((rValue, rIndex) => {
                                  return (
                                    <tr key={`row-${rIndex}`}>
                                      {rValue.map((cell, cellIndex) => {
                                        return (
                                          <td className="border-[1px] text-xs" key={`cell-${cellIndex}`}>
                                            <Typography className="text-xs sm:text-sm">{cell.value as string}</Typography>
                                          </td>
                                        );
                                      })}
                                    </tr>
                                  );
                                })}
                              </tbody>
                            </table>
                          </div>
                        </Fragment>
                      )}
                    </div>
                  );
                })}
              </div>
            );
          })}
        </div>
      )}
      {product?.commission && (
        <div className="flex flex-1 flex-col">
          <Typography size="md">Commission Structure</Typography>
          <Fragment>
            <Typography size="sm" className="ml-4 mt-4">
              {parseFloat(product?.commission?.maximumDisposableRate ?? "").toFixed(2)}% Maximum Disposable Commission - Standard Rate
            </Typography>
            <div className="flex-flex-1 mt-6 mx-6 overflow-x-scroll">
              <table className="table overflow-scroll">
                <thead>
                  <tr>
                    <td className="table-cell border-[1px] text-center text-xs">Type</td>
                    <td className="table-cell border-[1px] text-center text-xs">Age Type</td>
                    {standard.length > 0 && (
                      <Fragment>
                        <td className="table-cell border-[1px] text-center text-xs">Age From</td>
                        <td className="table-cell border-[1px] text-center text-xs">Age To</td>
                      </Fragment>
                    )}
                    <td className="table-cell border-[1px] text-center text-xs">Rate</td>
                  </tr>
                </thead>
                {product?.commission?.commissionDetails && (
                  <tbody>
                    {product?.commission?.commissionDetails?.map((rowValue, rowIndex) => {
                      return (
                        <tr key={`commissionDetailsRow-${rowIndex}`}>
                          <td className="table-cell border-[1px] text-xs">{rowValue?.commissionType?.commissionName}</td>
                          <td className="table-cell border-[1px] text-xs">{rowValue?.commissionAgeType?.name}</td>
                          {standard.length > 0 && (
                            <Fragment>
                              <td className="table-cell border-[1px] text-center text-xs">{rowValue.ageFrom}</td>
                              <td className="table-cell border-[1px] text-center text-xs">{rowValue.ageTo}</td>
                            </Fragment>
                          )}
                          <td className="table-cell border-[1px] text-center text-xs">{rowValue.rate ? parseFloat(rowValue.rate.toString()).toFixed(0) : ""}%</td>
                        </tr>
                      );
                    })}
                  </tbody>
                )}
              </table>
            </div>
          </Fragment>
        </div>
      )}
    </Fragment>
  );
};

export default ProductDetails;
