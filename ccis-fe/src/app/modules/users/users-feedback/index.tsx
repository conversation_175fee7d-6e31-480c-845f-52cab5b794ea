import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, ChevronLeft, ChevronRight, Clock, MessageCircle, Settings, Shield, Sparkles, Star, ThumbsUp } from "lucide-react";
import httpClient from "@clients/httpClient";
import { IC<PERSON><PERSON>, IFeedbackResponse, IQuestion, ISelectedChoice, ITransactionType } from "@interface/users-feedback.interface";
import { toast } from "react-toastify";

const SurveySystem = () => {
  const [questions, setQuestions] = useState<IQuestion[]>([]);
  const [transactionTypes, setTransactionTypes] = useState<ITransactionType[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [responses, setResponses] = useState<{ [questionId: number]: ISelectedChoice }>({});
  const [selectedTransactionType, setSelectedTransactionType] = useState<number | null>(null);
  const [userName, setUserName] = useState<string>("");
  const [userRemark, setUserRemark] = useState<string>("");
  const [showFinalForm, setShowFinalForm] = useState<boolean>(false);
  const [finalFormStep, setFinalFormStep] = useState<number>(1);
  const [surveySubmitted, setSurveySubmitted] = useState<boolean>(false);
  const [isTransitioning, setIsTransitioning] = useState<boolean>(false);
  const [showSplash, setShowSplash] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async (): Promise<void> => {
    setLoading(true);
    try {
      const questionsResponse = await httpClient.get("/feedback/question/?relations=choices|createdBy");
      const questionsData = questionsResponse.data;

      const transactionResponse = await httpClient.get("/feedback/transaction-type");
      const transactionData = transactionResponse.data;

      if (questionsData && Array.isArray(questionsData)) {
        setQuestions(questionsData.filter((q: IQuestion) => q.status === 1));
      }

      if (transactionData && Array.isArray(transactionData)) {
        setTransactionTypes(transactionData);
      }
    } catch (error: any) {
      toast.error("Error loading data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleResponse = (questionId: number, choice: IChoice) => {
    setResponses((prev) => ({
      ...prev,
      [questionId]: {
        questionId,
        choiceId: choice.id,
        emoji: choice.name,
        label: choice.name,
      },
    }));
  };

  const nextQuestion = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      if (currentQuestion < questions.length - 1) {
        setCurrentQuestion((prev) => prev + 1);
      } else {
        setShowFinalForm(true);
      }
      setIsTransitioning(false);
    }, 300);
  };

  const prevQuestion = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      if (showFinalForm) {
        if (finalFormStep === 2) {
          setFinalFormStep(1);
        } else {
          setShowFinalForm(false);
          setFinalFormStep(1);
        }
      } else if (currentQuestion > 0) {
        setCurrentQuestion((prev) => prev - 1);
      }
      setIsTransitioning(false);
    }, 300);
  };

  const submitSurvey = async () => {
    if (isSubmitting) return;
    try {
      const feedbackResponse: IFeedbackResponse[] = Object.values(responses).map((response) => ({
        questionId: response.questionId,
        choiceId: response.choiceId,
      }));

      const payload = {
        userTransactionTypeId: selectedTransactionType,
        name: userName,
        remark: userRemark,
        feedbackResponse,
      };
      setIsSubmitting(true);

      const response = await httpClient.post("/feedback/user/save-feedback", payload);

      if (response.status === 200) {
        // Reset form
        setCurrentQuestion(0);
        setResponses({});
        setSelectedTransactionType(null);
        setUserName("");
        setUserRemark("");
        setShowFinalForm(false);
        setFinalFormStep(1); // Add this line
      }
      setIsSubmitting(false);
      setSurveySubmitted(true);
    } catch (error: any) {
      setIsSubmitting(false);
      toast.error("Error submitting survey:", error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading survey...</p>
        </div>
      </div>
    );
  }

  if (questions.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">No active survey questions available.</p>
        </div>
      </div>
    );
  }

  if (showSplash) {
    return (
      <div className="flex items-center bg-gradient-to-br from-primary/5 via-primary2/10 to-primary/5 p-4 relative">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-72 h-72 bg-primary/10 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
          <div className="absolute top-40 right-10 w-72 h-72 bg-primary2/20 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
          <div className="absolute bottom-20 left-1/2 w-72 h-72 bg-primary3/10 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
        </div>

        {/* Main Content */}
        <div className="relative w-full max-w-2xl">
          <div className="bg-white rounded-2xl shadow-2xl border border-gray-200 overflow-hidden">
            {/* Header Section */}
            <div className="bg-gradient-primary p-8 sm:p-12 text-white text-center relative overflow-hidden">
              <div className="absolute inset-0 bg-black opacity-5"></div>
              <div className="relative z-10">
                {/* Icon */}
                <div className="inline-flex items-center justify-center w-20 h-20 bg-white/20 backdrop-blur-sm rounded-2xl mb-4 animate-bounce-slow shadow-lg">
                  <Sparkles className="w-10 h-10 text-primary2" />
                </div>

                <h1 className="text-3xl sm:text-4xl font-bold mb-3 animate-fade-in">We Value Your Feedback!</h1>
                <p className="text-lg sm:text-xl text-white/95 animate-fade-in-delay">Help us serve you better</p>
              </div>

              {/* Decorative accent line */}
              <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-primary2 to-transparent"></div>
            </div>

            {/* Content Section */}
            <div className="p-8 sm:p-12">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-semibold text-secondary mb-3">Your Opinion Matters</h2>
                <p className="text-gray leading-relaxed">Take a few moments to share your experience with us. Your feedback helps us improve our services and better meet your needs.</p>
              </div>

              {/* Features Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
                <div className="text-center p-5 rounded-xl bg-primary/5 border border-primary/20 hover:border-primary hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 group">
                  <div className="inline-flex items-center justify-center w-14 h-14 bg-gradient-primary rounded-full mb-3 group-hover:scale-110 transition-transform shadow-md">
                    <Clock className="w-7 h-7 text-white" />
                  </div>
                  <h3 className="font-semibold text-secondary mb-1">Quick & Easy</h3>
                  <p className="text-sm text-gray">Takes only 2-3 minutes</p>
                </div>

                <div className="text-center p-5 rounded-xl bg-primary2/10 border border-primary2/30 hover:border-primary2 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 group">
                  <div className="inline-flex items-center justify-center w-14 h-14 bg-primary2 rounded-full mb-3 group-hover:scale-110 transition-transform shadow-md">
                    <MessageCircle className="w-7 h-7 text-primary" />
                  </div>
                  <h3 className="font-semibold text-secondary mb-1">Your Voice</h3>
                  <p className="text-sm text-gray">Every response counts</p>
                </div>

                <div className="text-center p-5 rounded-xl bg-primary/5 border border-primary/20 hover:border-primary hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 group">
                  <div className="inline-flex items-center justify-center w-14 h-14 bg-gradient-primary rounded-full mb-3 group-hover:scale-110 transition-transform shadow-md">
                    <ThumbsUp className="w-7 h-7 text-white" />
                  </div>
                  <h3 className="font-semibold text-secondary mb-1">Make Impact</h3>
                  <p className="text-sm text-gray">Shape our services</p>
                </div>
              </div>

              {/* Trust Badge */}
              <div className="bg-gradient-to-r from-primary/5 to-primary2/10 border border-primary/20 rounded-xl p-4 mb-8">
                <div className="flex items-center justify-center gap-3 text-primary">
                  <Shield className="w-5 h-5" />
                  <p className="text-sm font-medium">Your responses are confidential and anonymous</p>
                </div>
              </div>

              {/* Call to Action */}
              <div className="text-center">
                <button
                  onClick={() => setShowSplash(false)}
                  className="group relative inline-flex items-center justify-center px-8 py-4 bg-gradient-primary text-white rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 overflow-hidden"
                >
                  <span className="relative z-10 flex items-center">
                    Start Survey
                    <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                  </span>
                  <div className="absolute inset-0 bg-gradient-primary-hover opacity-0 group-hover:opacity-100 transition-opacity"></div>
                </button>

                <div className="flex items-center justify-center gap-2 mt-6">
                  <Star className="w-4 h-4 text-primary2 fill-current" />
                  <p className="text-sm text-custom-gray">Join thousands who've shared their feedback</p>
                  <Star className="w-4 h-4 text-primary2 fill-current" />
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="bg-primary/5 px-8 py-4 text-center border-t border-primary/10">
              <p className="text-sm text-primary font-medium">
                Thank you for helping us improve!
                <span className="inline-block ml-1 text-primary2">★</span>
              </p>
            </div>
          </div>
        </div>

        <style>{`
        @keyframes blob {
          0%, 100% { transform: translate(0, 0) scale(1); }
          33% { transform: translate(30px, -50px) scale(1.1); }
          66% { transform: translate(-20px, 20px) scale(0.9); }
        }
        
        .animate-blob {
          animation: blob 7s infinite;
        }
        
        .animation-delay-2000 {
          animation-delay: 2s;
        }
        
        .animation-delay-4000 {
          animation-delay: 4s;
        }

        @keyframes bounce-slow {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-10px); }
        }

        .animate-bounce-slow {
          animation: bounce-slow 3s ease-in-out infinite;
        }

        @keyframes fade-in {
          from { opacity: 0; transform: translateY(20px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .animate-fade-in {
          animation: fade-in 0.6s ease-out;
        }

        .animate-fade-in-delay {
          animation: fade-in 0.6s ease-out 0.2s both;
        }
      `}</style>
      </div>
    );
  }

  // Thank You Screen
  if (surveySubmitted) {
    return (
      <div className="fixed inset-0 bg-gradient-to-br from-blue-50 to-green-50 flex flex-col items-center justify-center p-4 sm:p-6 overflow-auto">
        <div className="w-full max-w-2xl">
          <div className="bg-white rounded-xl shadow-2xl border border-gray-100 p-8 sm:p-12 text-center">
            <div className="mb-6">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-4">
                <svg className="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h2 className="text-3xl font-bold text-gray-800 mb-3">Thank You!</h2>
              <p className="text-lg text-gray-600 mb-2">Your feedback has been successfully submitted.</p>
              <p className="text-gray-500">We appreciate you taking the time to help us improve our services.</p>
            </div>

            <div className="bg-blue-50 rounded-lg p-6 mb-6">
              <div className="flex items-center justify-center gap-2 text-blue-700 mb-2">
                <Star className="w-5 h-5 fill-current" />
                <span className="font-semibold">Survey Completed</span>
              </div>
              <p className="text-sm text-blue-600">
                You answered {questions.length} question{questions.length !== 1 ? "s" : ""}
              </p>
            </div>

            <button
              onClick={() => {
                // Reset everything
                setCurrentQuestion(0);
                setResponses({});
                setSelectedTransactionType(null);
                setUserName("");
                setUserRemark("");
                setShowFinalForm(false);
                setFinalFormStep(1);
                setSurveySubmitted(false);
              }}
              className="px-8 py-3 bg-gradient-to-r from-primary to-primary-dark text-white rounded-lg font-medium hover:from-primary-dark hover:to-primary shadow-md hover:shadow-lg transition-all"
            >
              Take Another Survey
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Final Form (Transaction Type, Name, Remark)
  // Final Form (Transaction Type, Name, Remark)
  if (showFinalForm) {
    return (
      <div className={`w-full max-w-2xl ${isTransitioning ? "opacity-0 scale-95" : "animate-slide-in-up"} transition-all duration-300`}>
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6 sm:p-8">
          <div className="text-center mb-6">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-primary/10 rounded-full mb-3">
              <Star className="w-6 h-6 text-primary" />
            </div>
            <h2 className="text-2xl font-semibold text-secondary mb-2">{finalFormStep === 1 ? "Select Transaction Type" : "Almost Done!"}</h2>
            <p className="text-gray-600">{finalFormStep === 1 ? "Please select your transaction type" : "Please provide some additional information"}</p>
          </div>

          {finalFormStep === 1 ? (
            // Step 1: Transaction Type Selection
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Transaction Type <span className="text-red-500">*</span>
                </label>
                <div className="space-y-2">
                  {transactionTypes.map((type) => (
                    <label
                      key={type.id}
                      className={`flex items-center p-4 border rounded-lg cursor-pointer transition-all ${
                        selectedTransactionType === type.id ? "border-primary bg-primary/5 ring-1 ring-primary/20" : "border-gray-200 hover:border-primary/50"
                      }`}
                    >
                      <input
                        type="radio"
                        name="transactionType"
                        value={type.id}
                        checked={selectedTransactionType === type.id}
                        onChange={(e) => setSelectedTransactionType(Number(e.target.value))}
                        className="w-4 h-4 text-primary focus:ring-primary"
                      />
                      <span className="ml-3 font-medium text-gray-800">{type.name}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Navigation */}
              <div className="flex justify-between items-center mt-8 gap-3">
                <button onClick={prevQuestion} className="flex items-center justify-center px-4 py-2 rounded-lg font-medium text-primary hover:bg-primary/5 border border-primary/20 transition-all">
                  <ChevronLeft className="w-4 h-4 mr-1" />
                  Back
                </button>

                <button
                  onClick={() => setFinalFormStep(2)}
                  disabled={!selectedTransactionType}
                  className={`flex items-center justify-center px-6 py-2 rounded-lg font-medium transition-all ${
                    !selectedTransactionType
                      ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                      : "bg-gradient-to-r from-primary to-primary-dark text-white hover:from-primary-dark hover:to-primary shadow-md hover:shadow-lg"
                  }`}
                >
                  Next
                  <ChevronRight className="w-4 h-4 ml-1" />
                </button>
              </div>
            </div>
          ) : (
            // Step 2: Name and Additional Comments
            <div className={`space-y-6 ${isTransitioning ? "opacity-0 scale-95" : "animate-slide-in-up"} transition-all duration-300`}>
              {/* Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Your Name <span className="text-gray-500">(Optional)</span>
                </label>
                <input
                  type="text"
                  value={userName}
                  onChange={(e) => setUserName(e.target.value)}
                  placeholder="Enter your name..."
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                />
              </div>

              {/* Remark (Optional) */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Comments <span className="text-gray-500">(Optional)</span>
                </label>
                <textarea
                  value={userRemark}
                  onChange={(e) => setUserRemark(e.target.value)}
                  placeholder="Any additional feedback..."
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                />
              </div>

              {/* Navigation */}
              <div className="flex justify-between items-center mt-8 gap-3">
                <button onClick={prevQuestion} className="flex items-center justify-center px-4 py-2 rounded-lg font-medium text-primary hover:bg-primary/5 border border-primary/20 transition-all">
                  <ChevronLeft className="w-4 h-4 mr-1" />
                  Back
                </button>

                <button
                  onClick={submitSurvey}
                  className={`flex items-center justify-center px-6 py-2 rounded-lg font-medium transition-all bg-gradient-to-r from-primary to-primary-dark text-white hover:from-primary-dark hover:to-primary shadow-md hover:shadow-lg ${isSubmitting ? "opacity-50 cursor-not-allowed" : ""}`}
                  disabled={isSubmitting}
                >
                  <Star className="w-4 h-4 mr-1" />
                  Submit Survey
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Question Interface
  const question = questions[currentQuestion];
  const progress = ((currentQuestion + 1) / questions.length) * 100;
  const isLastQuestion = currentQuestion === questions.length - 1;
  const hasResponse = responses[question.id];

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-primary/5 via-primary2/10 to-primary/5 flex flex-col items-center p-4 sm:p-6 py-8 overflow-auto">
      <div className={`w-full max-w-4xl mx-auto px-4 sm:px-6 ${isTransitioning ? "opacity-0 scale-95" : "animate-slide-in-up"} transition-all duration-300`}>
        {/* Header */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
          <div className="px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
              <div className="flex-1">
                <h1 className="text-xl sm:text-2xl font-semibold text-gray-800 mb-1">Customer Satisfaction Survey</h1>
                <p className="text-gray-600 text-sm">Help us improve our services</p>
              </div>
              <button className="p-2 text-gray-500 hover:text-primary transition-colors self-end sm:self-auto">
                <Settings className="w-5 h-5" />
              </button>
            </div>

            {/* Progress Bar */}
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span className="font-medium">
                  Question {currentQuestion + 1} of {questions.length}
                </span>
                <span className="font-medium">{Math.round(progress)}% Complete</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-gradient-primary h-2 rounded-full transition-all duration-500 ease-out" style={{ width: `${progress}%` }}></div>
              </div>
            </div>
          </div>
        </div>

        {/* Question Card */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="p-4 sm:p-6 lg:p-8">
            <div className="text-center mb-6 sm:mb-8">
              <div className="inline-flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full mb-3">
                <span className="text-lg font-semibold text-blue-600">{currentQuestion + 1}</span>
              </div>
              <h2 className="text-lg sm:text-xl lg:text-2xl font-semibold text-gray-800 leading-tight max-w-3xl mx-auto">{question.question}</h2>
            </div>

            {/* Response Options */}
            <div className="max-w-4xl mx-auto mb-6 sm:mb-8">
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-2 sm:gap-3 lg:gap-4">
                {question.choices.map((choice) => (
                  <button
                    key={choice.id}
                    onClick={() => handleResponse(question.id, choice)}
                    className={`group relative p-3 sm:p-4 lg:p-6 rounded-lg sm:rounded-xl border transition-all duration-300 transform-gpu hover:scale-105 active:scale-95 ${
                      responses[question.id]?.choiceId === choice.id ? "border-blue-600 bg-blue-50 shadow-md ring-1 ring-blue-200" : "border-gray-200 hover:border-blue-400 hover:shadow-sm bg-white"
                    }`}
                  >
                    <div className="text-2xl sm:text-3xl lg:text-4xl mb-1 sm:mb-2">{choice.name}</div>
                    {responses[question.id]?.choiceId === choice.id && (
                      <div className="absolute -top-1 -right-1 w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Selected Response Display */}
            <div className="text-center mb-6 sm:mb-8 min-h-[3rem]">
              {hasResponse && (
                <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 rounded-full border border-blue-200">
                  <div className="text-xs sm:text-sm font-medium text-blue-600">You selected: {responses[question.id]?.label}</div>
                </div>
              )}
            </div>

            {/* Navigation */}
            <div className="flex flex-col sm:flex-row justify-between items-center gap-3">
              <button
                onClick={prevQuestion}
                disabled={currentQuestion === 0}
                className={`w-full sm:w-auto flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                  currentQuestion === 0 ? "text-gray-400 cursor-not-allowed bg-gray-50" : "text-blue-600 hover:bg-blue-50 border border-blue-200"
                }`}
              >
                <ChevronLeft className="w-4 h-4 mr-1" />
                Previous
              </button>

              <div className="sm:block">
                <div className="text-center text-gray-500">
                  <div className="text-xs">
                    {currentQuestion + 1} of {questions.length} questions
                  </div>
                </div>
              </div>

              <button
                onClick={nextQuestion}
                disabled={!hasResponse}
                className={`w-full sm:w-auto flex items-center justify-center px-6 py-2 rounded-lg font-medium transition-all duration-200 ${
                  !hasResponse ? "bg-gray-300 text-gray-500 cursor-not-allowed" : "bg-primary hover:bg-primary-dark text-white hover:from-blue-700 hover:to-blue-800 shadow-md hover:shadow-lg"
                }`}
              >
                {isLastQuestion ? "Continue" : "Next Question"}
                <ChevronRight className="w-4 h-4 ml-1" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SurveySystem;
