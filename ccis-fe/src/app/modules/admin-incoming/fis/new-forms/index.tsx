import { ReactNode } from "react";
import ForReceivingTab from "./tabs/ForReceivingAdminIncomingTab";
import IncomingReturnTab from "./tabs/IncomingReturnTab";
import InventoryTab from "./tabs/InventoryTab";
import Tabs from "@components/common/Tabs";

const fisIndex: React.FC = () => {
  const headers: string[] = ["For Receiving", "Return Pads", "Inventory"];
  const contents: ReactNode[] = [<ForReceivingTab />, <IncomingReturnTab />, <InventoryTab />];

  return (
    <div>
      <div className=" my-4 py-4 border-b border-zinc-300 w-full text-zinc-500">
        Dashboard /{" "}
        <span className="text-primary font-poppins-semibold ">New Forms</span> /{" "}
        <span className="text-primary font-poppins-semibold ">Incoming</span>
      </div>
      <Tabs
        headers={headers}
        contents={contents}
        size="md"
        headerClass="w-52"
        fullWidthHeader={false}
      />
    </div>
  );
};
export default fisIndex;
