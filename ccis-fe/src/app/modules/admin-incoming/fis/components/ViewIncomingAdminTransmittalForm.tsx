import Button from "@components/common/Button";
import Typography from "@components/common/Typography";
import TextField from "@components/form/TextField";
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { IFormTransmittal, IPadAssignments } from "@interface/form-inventory.interface";
import Loader from "@components/Loader";
import { useFormik } from "formik";
import { confirmSaveOrEdit } from "@helpers/prompt";
import { toast } from "react-toastify";
import { AreaCode, FormStatus, RoleType } from "@enums/form-status";
import { usePositionsManagementActions } from "@state/reducer/utilities-positions";
import { useUserManagementActions } from "@state/reducer/users-management";
import { navigateBack } from "@helpers/navigatorHelper";
import { findItem } from "@helpers/array";
// import { releasedVia } from "@constants/global-constant-value";
import { useReleasedMethodActions } from "@state/reducer/form-inventory-utilities-released-methods";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { TFormTransmittalOutgoingPayload } from "@state/types/form-inventory-transmittal";
// import { formatStringAtoZ0to9 } from "@helpers/text";
import { ReturnWithoutReleasedViaValidationSchema } from "@services/form-inventory-transmittal/form-inventory-transmittal.schema";
import { useLocationActions } from "@state/reducer/form-inventory-utilities-location";
import { useFetchWithParams } from "@hooks/useFetchWithParams";
import { useDebouncedSearch } from "@hooks/useDebouncedSearch";
import Table from "@components/common/Table";
import { getColumns } from "./completed-column";
import { IActions, IDefaultParams } from "@interface/common.interface";
import { FaEye } from "react-icons/fa";
import ViewPadDetailsModal from "@components/template/Modals/view-pad-details-modal";
import { Combobox } from "@components/common-v2/Combobox";
import { useAreaAdminActions } from "@state/reducer/form-inventory-utilities-area-admins";

const ViewReturnTransmittalForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  // State
  const [data, setData] = useState<IFormTransmittal | null>(null);
  const [viewPadAssignmentId, setViewPadAssignmentId] = useState<number | undefined>();
  const [isViewOpen, setIsViewOpen] = useState<boolean>(false);

  // Redux selectors
  const returnedTransmittalData = useSelector((state: RootState) => state.formInventoryTransmittal.getReturnedTransmittalTrail);
  const returnLoading = useSelector((state: RootState) => state.formInventoryTransmittal.getReturnedTransmittalTrail.loading);
  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const users = useSelector((state: RootState) => state.usersManagement.users);
  const areaAdmins = useSelector((state: RootState) => state.formInventoryUtilitiesAreaAdmins.areaAdmins);
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const profile = useSelector((state: RootState) => state.profile.profile);
  // const postTrailSuccess = useSelector((state: RootState) => state.formInventoryTransmittal.postFormTransmittalTrail?.success);
  const putTrailSuccess = useSelector((state: RootState) => state.formInventoryTransmittal.putFormTransmittalTrail?.success);

  // Redux actions
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getLocations } = useLocationActions();
  const { getAreas } = useAreaActions();
  const { getAreaAdmins } = useAreaAdminActions();
  const { getUsers } = useUserManagementActions();
  const { getPosition } = usePositionsManagementActions();
  const { getReleasedMethods } = useReleasedMethodActions();
  const { getReturnedTransmittalTrail, postFormTransmittalTrail, putFormTransmittalTrail, clearSelectedTransmittalForm } = useTransmittalFormActions();

  // Formik
  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      id: 0,
      status: FormStatus.RETURNED,
      releasedTo: 0,
      releasedMethodId: undefined,
      deliveredBy: "",
      trackingNo: "",
    } as TFormTransmittalOutgoingPayload,
    validationSchema: ReturnWithoutReleasedViaValidationSchema,
    onSubmit: async (values) => {
      if (!values.releasedTo || values.releasedTo === 0) {
        toast.error("Please select who to release to");
        return;
      }

      const isConfirmed = await confirmSaveOrEdit("Are you sure you want to return this transmittal?");

      if (!isConfirmed) return;

      try {
        if (!id) {
          toast.error("Missing form transmittal ID.");
          return;
        }
        await postFormTransmittalTrail(values);
        if (data?.latestFormTransmittalTrail?.id) {
          const payload = {
            status: FormStatus.RETURNED,
          };
          await putFormTransmittalTrail({
            id: data.latestFormTransmittalTrail?.id,
            status: payload.status,
          });
        }
      } catch (error) {
        toast.error("Failed to release transmittal. Please try again.");
      }
    },
  });

  // Handle put operation success
  useEffect(() => {
    if (putTrailSuccess) {
      toast.success("Returned form successfully");
      clearSelectedTransmittalForm();
      navigateBack();
    }
  }, [putTrailSuccess]);

  useEffect(() => {
    if (data?.id) {
      formik.setFieldValue("id", data.id);
    }
  }, [data?.id]);

  // Debounced search
  const { value: searchUser, handleChange: handleSearchUser } = useDebouncedSearch();

  // Fetch data
  const fetchForm = async () => {
    try {
      if (id) {
        await getReturnedTransmittalTrail({ id: Number(id) } as IDefaultParams);
      }
    } catch (error) {
      toast.error("Failed to load proposal data. Please try again later.");
    }
  };

  // Add useEffect to handle the Redux state update
  useEffect(() => {
    if (returnedTransmittalData.data && !returnedTransmittalData.loading) {
      setData(returnedTransmittalData.data);
    }
  }, [returnedTransmittalData.data, returnedTransmittalData.loading]);

  // // Effects
  // useEffect(() => {
  //   if (postTrailSuccess) {
  //     navigate(ROUTES.INCOMINGADMIN.incomingAdminNewForms.key);
  //     clearSelectedTransmittalForm();
  //   }
  // }, [postTrailSuccess]);

  useEffect(() => {
    getUsers({ filter: "" });
    fetchForm();
  }, [id]);

  useEffect(() => {
    getReleasedMethods({ filter: "" });
    getDivisions({ filter: "" });
    getFormTypes({ filter: "" });
    getAreas({ filter: "" });
    getLocations({ params: { type: AreaCode.LUZON, filter: "" } });
    getPosition({ params: { filter: "" } });
    getAreaAdmins({ filter: "" });
  }, []);

  useEffect(() => {
    if (!data?.areaId) return;

    const matchedAdmin = areaAdmins.find((admin) => admin.userAreaId === data.releasedAreaId);
    if (matchedAdmin && data.releasedToId !== matchedAdmin.userId) {
      setData((prevData) => ({
        ...prevData,
        releasedToId: matchedAdmin.userId,
      }));
    }
  }, [data?.releasedAreaId, areaAdmins]);

  // Fixed handleUserSelect function
  // const handleUserSelect = (eventOrValue: any) => {
  //   let userId: number;
  //   let selectedUser: any = null;

  //   // Handle CustomTextField event object structure
  //   if (eventOrValue && typeof eventOrValue === "object" && eventOrValue.target) {
  //     selectedUser = eventOrValue.target.item;
  //     const targetValue = eventOrValue.target.value;

  //     if (selectedUser && selectedUser.id) {
  //       userId = selectedUser.id;
  //     } else if (targetValue) {
  //       userId = typeof targetValue === "string" ? parseInt(targetValue, 10) : targetValue;
  //     } else {
  //       console.error("No valid user data found in event object");
  //       toast.error("Please select a valid user");
  //       return;
  //     }
  //   } else if (typeof eventOrValue === "object" && eventOrValue !== null && "id" in eventOrValue) {
  //     // Direct user object
  //     selectedUser = eventOrValue;
  //     userId = eventOrValue.id;
  //   } else {
  //     // Direct ID value
  //     userId = typeof eventOrValue === "string" ? parseInt(eventOrValue, 10) : eventOrValue;
  //   }

  //   // Check if it's a valid number
  //   if (isNaN(userId) || userId === 0) {
  //     toast.error("Please select a valid user");
  //     return;
  //   }
  //   formik.setFieldValue("releasedTo", userId);
  // };

  const getActionEvents = (row: IPadAssignments): IActions<any>[] => [
    {
      name: "View",
      event: () => {
        setViewPadAssignmentId(row.id);
        handleToggleViewModal();
      },
      icon: FaEye,
      color: "primary",
    },
  ];

  const handleToggleViewModal = () => {
    setIsViewOpen((prev) => !prev);
  };

  const columns = getColumns({
    getActionEvents,
    transmittalNumber: data?.transmittalNumber,
    divisionName: String(findItem(divisions, "id", Number(data?.returnedPads?.[0]?.form?.divisionId), "divisionName") || "N/A"),
  });

  useFetchWithParams(getUsers, { nameFilter: searchUser, role: RoleType.INCLUDES_CASHIER }, [searchUser], false);

  if (returnLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader />
      </div>
    );
  }

  const userFullName = profile ? `${profile.firstname || "N/A"} ${profile.middlename || ""} ${profile.lastname || ""}`.trim() : "N/A";
  const currentDate = new Date().toLocaleDateString("en-US", { year: "numeric", month: "long", day: "numeric" });
  const createdByFullName = data?.createdBy ? `${data.createdBy.firstname || "N/A"} ${data.createdBy.middlename || ""} ${data.createdBy.lastname || ""}`.trim() : "N/A";
  const areaName = String(findItem(area, "id", Number(data?.returnedPads?.[0]?.form?.areaId), "areaName") || "N/A");
  const releasedToName = areaAdmins.find((admin) => admin.userId === data?.releasedToId)?.adminName || "N/A";
  const returnedAreaName = String(findItem(area, "id", Number(data?.releasedAreaId), "areaName") || "N/A");

  // Get the selected user's name for display
  // const selectedUser = users.find((user) => user.id === formik.values.releasedTo);
  // const selectedUserName = selectedUser ? `${selectedUser.firstname} ${selectedUser.lastname}` : "";

  return (
    <div>
      <div>
        <Button classNames="btn bg-slate-600 btn-sm" onClick={navigateBack}>
          Back
        </Button>
        <div className="mx-6">
          <Typography className="mt-6 text-primary uppercase font-poppins-semibold-">Transmittal Details</Typography>
          <div className="mt-8">
            <form onSubmit={formik.handleSubmit}>
              {/* Assignee Details */}
              <div className="flex w-full flex-col">
                <div className="divider divider-start uppercase">Assignee Details</div>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-3 gap-4">
                  <div className="p-2 flex-1">
                    <p className="text-sm">Returned by</p>
                    <TextField disabled className="w-60" size="sm" value={userFullName} />
                  </div>
                  <div className="p-2 flex-1">
                    <p className="text-sm">Released To *</p>
                    {/* <CustomTextField
                      suggestionOptions={users}
                      getOptionLabel={(u) => `${u.firstname} ${u.lastname}`}
                      getOptionValue={(u) => u.id} // Return the actual ID, not string
                      name="releasedTo"
                      value={selectedUserName} // Use the actual user name for display
                      onChange={(value) => {
                        handleUserSelect(value);
                      }}
                      onInputChange={handleSearchUser}
                      placeholder="Search User"
                      variant="primary"
                      size="sm"
                    /> */}
                    <Combobox
                      suggestionOptions={users}
                      optionLabel={(u) => `${u.firstname} ${u.lastname}`}
                      optionValue={(u) => u.id}
                      placeholder="Select User"
                      onInputChange={handleSearchUser}
                      setData={(u) => {
                        formik.setFieldValue("releasedTo", u.id);
                      }}
                      onClear={() => {
                        formik.setFieldValue("releasedTo", 0);
                      }}
                    />
                    {formik.touched.releasedTo && formik.errors.releasedTo && <p className="text-red-500 text-xs mt-1">{formik.errors.releasedTo}</p>}
                  </div>
                  <div className="p-2 flex-1">
                    <p className="text-sm">Date Released</p>
                    <TextField className="w-48" value={currentDate} size="sm" disabled />
                  </div>
                </div>
              </div>

              {/* Return Details */}
              <div className="flex w-full flex-col">
                <div className="divider divider-start uppercase">RETURN DETAILS</div>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-3 gap-4">
                  <div className="p-2 flex-1">
                    <p className="text-sm">Returned Via</p>
                    <TextField disabled className="w-60" size="sm" value={userFullName} />
                  </div>
                  <div className="p-2 flex-1">
                    <p className="text-sm">Courier Service Name</p>
                    <TextField disabled className="w-60" size="sm" value={userFullName} />
                  </div>
                  <div className="p-2 flex-1">
                    <p className="text-sm">Tracking No.</p>
                    <TextField className="w-48" value={currentDate} size="sm" disabled />
                  </div>
                  <div className="p-2 flex-1">
                    <p className="text-sm">Return Transmittal No.</p>
                    <TextField className="w-48" value={data?.transmittalNumber} size="sm" disabled />
                  </div>
                  <div className="p-2 flex-1">
                    <p className="text-sm">Area Returned</p>
                    <TextField className="w-48" value={returnedAreaName} size="sm" disabled />
                  </div>
                </div>
              </div>

              {/* Series Overview */}
              <div className="flex w-full flex-col">
                <div className="divider divider-start uppercase">Series Overview</div>
              </div>
              <div className="p-6">
                <div className="flex flex-wrap gap-4">
                  <div className="p-2 flex-1">
                    <p className="text-sm">Returned by</p>
                    <TextField disabled className="w-60" size="sm" value={createdByFullName} />
                  </div>
                  <div className="p-2 flex-1">
                    <p className="text-sm">Area</p>
                    <TextField className="w-48" disabled size="sm" value={areaName} />
                  </div>
                  <div className="p-2 flex-1">
                    <p className="text-sm">Returned To</p>
                    <TextField className="w-48" disabled size="sm" value={releasedToName} />
                  </div>
                </div>
              </div>

              {/* Pads Table */}
              <div className="border rounded-md border-slate-300">
                <div className="mb-4">
                  <Table
                    className="!min-h-[100%] h-[300px] border-[1px] border-zinc-300"
                    columns={columns}
                    data={data?.returnedPads || []}
                    searchable={false}
                    multiSelect={false}
                    selectable={false}
                    paginationTotalRows={data?.returnedPads?.length || 0}
                    paginationServer={true}
                  />
                </div>
              </div>

              <ViewPadDetailsModal
                isViewOpen={isViewOpen}
                isReturned={true}
                handleToggleViewModal={() => setIsViewOpen(false)}
                returnedPads={data?.returnedPads?.find((pad) => pad.id === viewPadAssignmentId)}
                formData={data}
              />

              <div className="flex justify-center mt-6">
                <Button classNames="btn bg-slate-400 btn-sm mr-2 w-48" onClick={navigateBack}>
                  Cancel
                </Button>
                <Button classNames="btn btn-sm w-48 bg-primary" type="submit" disabled={!formik.values.releasedTo || formik.values.releasedTo === 0 || !formik.values.releasedMethodId}>
                  Return
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewReturnTransmittalForm;
