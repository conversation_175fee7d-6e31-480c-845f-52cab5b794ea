import React, { ChangeEvent, useEffect, useState } from "react";
import { useDebouncedCallback } from "use-debounce";
import Filter from "@components/common/Filter";
import Select from "@components/form/Select";
import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import Table from "@components/common/Table";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import ActionDropdown from "@components/common/ActionDropdown";
import Typography from "@components/common/Typography";
import { getTextStatusColor } from "@helpers/text";
import { IActions, IDefaultParams } from "@interface/common.interface";
import { TableColumn } from "react-data-table-component";
import { GoVersions } from "react-icons/go";
import { useNavigate } from "react-router-dom";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { getDivisions } from "@state/reducer/form-inventory-utilities-divisions";
import { getFormTypes } from "@state/reducer/form-inventory-utilities-form-types";
import { getAreas } from "@state/reducer/utilities-areas";
import { ROUTES } from "@constants/routes";

import { IFormTransmittal } from "@interface/form-inventory.interface";
import SeriesTransmittalTable from "../Forms/CreateTransmittalForm";
import { FormStatus } from "@enums/form-status";
import { Column } from "@enums/table-columns";
import { findItem } from "@helpers/array";
import FilterPanel from "@components/template/FilterPanel";

const TransmittalTab: React.FC = () => {
  const [searchText] = useState<string | undefined>("");
  const [isFormOpen, setIsFormOpen] = useState<boolean>(false);
  const { getCurrentUserFormTransmittalTrail } = useTransmittalFormActions();
  const [resetCounter, setResetCounter] = useState(0);
  const [divisionFilter] = useState<number | undefined>();
  const userId = useSelector((state: RootState) => state?.auth?.user.data?.id);
  const divisionOptions = [
    { value: 0, text: "Select Division" },
    ...useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions).map((value) => {
      return { value: value.id, text: value.divisionName };
    }),
  ];
  const typeOptions = [
    { value: 0, text: "Select Type" },
    ...useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes).map((value) => {
      return { value: value.id, text: value.formTypeName };
    }),
  ];
  const loading = useSelector((state: RootState) => state.formInventoryTransmittal.getCurrentUserFormTransmittalTrail.loading);
  const { data: forms } = useSelector((state: RootState) => state.formInventoryTransmittal.getCurrentUserFormTransmittalTrail);
  const filterDivision = "";
  const filterFormType = "";
  const filterArea = "";
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const formTypes = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);

  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const navigate = useNavigate();

  const [filters, setFilters] = useState<IDefaultParams>({
    filter: "",
    divisionFilter: undefined,
    type: undefined,
    dateFrom: "",
    dateTo: "",
    page: 1,
    pageSize: 10,
  });

  const updateFilter = (key: keyof IDefaultParams, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value, page: 1 }));
  };

  const handleSearch = useDebouncedCallback((event: ChangeEvent<HTMLInputElement>) => {
    updateFilter("filter", event.target.value);
  }, 500);

  const handleDivisionChange = (event: ChangeEvent<HTMLSelectElement>) => {
    const value = parseInt(event.target.value);
    updateFilter("divisionFilter", value === 0 ? undefined : value);
  };

  const handleTypeChange = (event: ChangeEvent<HTMLSelectElement>) => {
    const value = parseInt(event.target.value);
    updateFilter("type", value === 0 ? undefined : value);
  };

  const handleDateFromChange = (event: ChangeEvent<HTMLInputElement>) => {
    updateFilter("dateFrom", event.target.value);
  };

  const handleDateToChange = (event: ChangeEvent<HTMLInputElement>) => {
    updateFilter("dateTo", event.target.value);
  };

  const handleRowsChange = (newPerPage: number, page: number) => {
    updateFilter("pageSize", newPerPage);
    updateFilter("page", page);
  };

  const handlePaginate = (page: number) => {
    updateFilter("page", page);
  };

  const handleClearAll = () => {
    setFilters({
      filter: "",
      divisionFilter: undefined,
      type: undefined,
      dateFrom: "",
      dateTo: "",
      page: 1,
      pageSize: 10,
    });
    setResetCounter((prev) => prev + 1);
  };

  const handleToggleFormModal = () => {
    setIsFormOpen((prev) => !prev);
  };

  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const getActionEvents = (Transmittal: any): IActions<any>[] => {
    const actions: IActions<any>[] = [
      {
        name: "View",
        event: () => {
          navigate(ROUTES.AREAADMIN.viewReleasedForm.parse(Transmittal.id));
        },
        icon: GoVersions,
        color: "primary",
      },
    ];
    return actions;
  };

  // Get data with RELEASED status and released by head cashier
  const columns: TableColumn<IFormTransmittal>[] = [
    {
      name: Column.NO,
      cell: (row) => row.id,
      width: "80px",
      ...commonSetting,
    },
    {
      name: Column.TRANSMITTAL_NO,
      cell: (row) => row.transmittalNumber,
      ...commonSetting,
    },
    {
      name: Column.ATP,
      cell: (row) => row.padAssignments?.[0]?.form?.atpNumber || "N/A",
      ...commonSetting,
    },
    {
      name: Column.DIVISION,
      cell: (row) => {
        const divisionsList = Array.from(
          new Set(
            row.padAssignments
              ?.map((assignment: { form?: { divisionId?: number } }) => {
                const division = divisions.find((division) => division.id === assignment.form?.divisionId);
                return division ? division.divisionName : null;
              })
              .filter(Boolean) // Remove null values
          )
        );
        return divisionsList.length > 0 ? divisionsList.join(", ") : "N/A";
      },
      ...commonSetting,
    },
    {
      name: Column.TYPE,
      cell: (row) => {
        const uniqueFormTypes = Array.from(
          new Set(
            row.padAssignments
              ?.map((assignment: { form?: { formTypeId?: number } }) => {
                const formType = formTypes.find((type) => type.id === assignment.form?.formTypeId);
                return formType ? formType.formTypeCode : null;
              })
              .filter(Boolean) // Remove null values
          )
        );
        return uniqueFormTypes.length > 0 ? uniqueFormTypes.join(", ") : "N/A";
      },
      ...commonSetting,
    },
    {
      name: Column.AREA,
      cell: (row) => <div>{String(findItem(area, "id", Number(row?.releasedAreaId), "areaName") || "N/A")}</div>,
      ...commonSetting,
    },
    {
      name: Column.NO_OF_PADS,
      cell: (row) => row.padAssignments?.length || "N/A",
      width: "120px",
      ...commonSetting,
    },
    {
      name: Column.STATUS,
      cell: (row) => {
        const trail = row?.formTransmittalTrails?.find((item: any) => item.createdBy?.id === userId);
        const status = trail ? trail.status : "NO_STATUS";

        // Determine the display status
        let displayStatus;
        if (status === FormStatus.RECEIVED) {
          displayStatus = FormStatus.RECEIVED;
        } else {
          displayStatus = FormStatus.NOT_YET_RECEIVED;
        }

        // Format for display
        const formattedStatus = displayStatus
          .replace(/_/g, " ")
          .toLowerCase()
          .replace(/\b\w/g, (char) => char.toUpperCase());

        return <Typography className={getTextStatusColor(displayStatus)}>{formattedStatus}</Typography>;
      },
    },
    {
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />,
    },
  ];
  const sortedTransmittal = forms?.data?.slice().sort((a: IFormTransmittal, b: IFormTransmittal) => {
    return Number(b.id) - Number(a.id); // Explicitly convert to number
  });

  const fetchForms = () => {
    const payload = {
      page: filters.page,
      pageSize: filters.pageSize,
      filter: filters.filter,
      dateFrom: filters.dateFrom,
      dateTo: filters.dateTo,
      divisionFilter: filters.divisionFilter,
      type: filters.type,
      id: userId,
      statusFilter: FormStatus.APPROVED,
    } as IDefaultParams;
    getCurrentUserFormTransmittalTrail(payload);
  };

  useEffect(() => {
    fetchForms();
  }, [filters]);

  useEffect(() => {
    getDivisions({ filter: filterDivision });
    getFormTypes({ filter: filterFormType });
    getAreas({ filter: filterArea });
  }, []);

  return (
    <div className="p-4">
      <div className="text-xl font-semibold uppercase mt-4 tracking-wider text-[#042781]">Transmittal Lists</div>
      <div className="mt-8">
        <div className="flex flex-row justify-between">
          <FilterPanel
            filters={filters}
            resetCounter={resetCounter}
            onSearch={handleSearch}
            onDateFromChange={handleDateFromChange}
            onDateToChange={handleDateToChange}
            onDivisionChange={handleDivisionChange}
            onTypeChange={handleTypeChange}
            onClearAll={handleClearAll}
            divisionOptions={divisionOptions}
            typeOptions={typeOptions}
          />

          <div className="flex justify-end gap-2">
            <Button classNames="text-primary text-xs btn-primary btn btn-sm" onClick={handleToggleFormModal}>
              + Add New
            </Button>
          </div>
        </div>
        <Table
          className="!min-h-[500px] mt-4 border-[1px] border-zinc-300"
          columns={columns}
          paginationServer={true}
          paginationTotalRows={forms?.meta?.total}
          data={sortedTransmittal}
          onChangeRowsPerPage={handleRowsChange}
          onPaginate={handlePaginate}
          loading={loading}
          searchable={false}
          multiSelect={false}
        />
        <Modal
          title={"Add New Transmittal"}
          modalContainerClassName="max-w-3xl mx-auto"
          titleClass="text-primary text-lg uppercase"
          isOpen={isFormOpen}
          onClose={handleToggleFormModal}
          className="w-full"
        >
          <div className="flex justify-start">
            <Filter search={searchText} onChange={handleSearch}>
              <div className="flex justify-end">
                <button className="text-primary text-xs btn-sm" type="button" onClick={handleClearAll}>
                  Clear All
                </button>
              </div>
              <div>
                <div className="text-xs">Division</div>
                <Select key={`division-${resetCounter}`} size="sm" options={divisionOptions} value={divisionFilter} onChange={handleDivisionChange} />
              </div>
            </Filter>
          </div>

          <SeriesTransmittalTable searchText={searchText} divisionFilter={divisionFilter} handleToggleFormModal={handleToggleFormModal} />
        </Modal>
      </div>
    </div>
  );
};

export default TransmittalTab;
