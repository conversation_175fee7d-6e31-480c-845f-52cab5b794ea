import React, { ReactNode } from "react";


import Tabs from "@components/common/Tabs";

import TransmittalTab from "./components/Tabs/TransmittalTab";
import InventoryTab from "./components/Tabs/InventoryTab";
import ForReceivingTab from "./components/Tabs/ForReceivingTab";

const AreaAdminNewForms: React.FC = () => {
    const headers: string[] = ["For Receiving", "Transmittal", "Inventory"];
    const contents: ReactNode[] = [<ForReceivingTab />, <TransmittalTab/>, <InventoryTab />];
  
  return (
    <div>
      <div className=" my-4 py-4 border-b border-zinc-300 w-full text-zinc-500">
        Dashboard /{" "}
        <span className="text-primary font-poppins-semibold ">New Forms</span> 
      </div>
      <Tabs
        headers={headers}
        contents={contents}
        size="md"
        headerClass="w-52"
        fullWidthHeader={false}
      />
    </div>
  );
};

export default AreaAdminNewForms;
