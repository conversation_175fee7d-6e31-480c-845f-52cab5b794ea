import React, { useEffect, useState } from "react";
import Typography from "@components/common/Typography";
import { IActions } from "@interface/common.interface";
import { GoVersions } from "react-icons/go";
import { useNavigate, useParams } from "react-router-dom";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { IPadSeriesDetails } from "@interface/form-inventory.interface";
import Button from "@components/common/Button";
import { PadStatus } from "@enums/form-status";
import { ROUTES } from "@constants/routes";
import { useDebouncedSearch } from "@hooks/useDebouncedSearch";
import TableFilter from "./table/filter/TableFilter";
import { createDateChangeHandler, createStringSelectChangeHandler } from "@helpers/handlers";
import Table from "@components/common/Table";
import { getColumns } from "./table/column/pr-pad-details-column";
import { getD<PERSON>, has<PERSON><PERSON> } from "@helpers/storage";
import { TPadData } from "./table/column/pr-issuance-column";
import { enumToArray } from "@helpers/format-status";
import { useSelectOptions } from "@hooks/useSelectOptions";
import { useFetchWithParams } from "@hooks/useFetchWithParams";

const PRTable: React.FC = () => {
  const navigate = useNavigate();

  // Get ID in URL
  const { id } = useParams();

  // States
  const [resetCounter, setResetCounter] = useState(0);
  const [status, setStatus] = useState<string>("");
  const [dateFrom, setDateFrom] = useState<string>("");
  const [dateTo, setDateTo] = useState<string>("");
  // const [page, setPage] = useState<number>(1);
  // const [pageSize, setPageSize] = useState<number>(10);

  // Global States
  const { userPadAssignmentById } = useSelector((state: RootState) => state.formInventoryTransmittal);

  // actions
  const { getUserPadAssignmentById } = useTransmittalFormActions();

  // Custom Hook
  const { value: searchText, handleChange: handleSearch, setValue: setSearchText } = useDebouncedSearch();

  // convert enums to array
  const padStatusArray = enumToArray(PadStatus);
  const padStatusOptions = useSelectOptions({
    data: padStatusArray,
    valueKey: "id",
    textKey: "label",
    firstOptionText: "Select Status",
  });

  // Select handlers
  const handleStatusChange = createStringSelectChangeHandler(setStatus);

  // Date handlers
  const handleDateFromChange = createDateChangeHandler(setDateFrom);
  const handleDateToChange = createDateChangeHandler(setDateTo);

  // getUserPadAssignmentById({ id: parseInt(id) });
  useFetchWithParams(
    getUserPadAssignmentById,
    {
      id,
    },
    []
  );

  const handleClearAll = () => {
    setSearchText("");
    setDateFrom("");
    setDateTo("");
    setStatus("");
    setResetCounter((prev) => prev + 1);
  };

  const filteredAndSortedPadDetails = (() => {
    if (!userPadAssignmentById?.data?.padSeriesDetails) return [];

    const padDetails = [...userPadAssignmentById.data.padSeriesDetails];

    const defaultSortOrder = [PadStatus.UNUSED, PadStatus.USED, PadStatus.CANCELLED];

    const statusFilter: string = defaultSortOrder.includes(status as PadStatus) ? status : "";

    let filtered: IPadSeriesDetails[] = [];

    if (statusFilter === PadStatus.UNUSED) {
      // Show only UNUSED
      filtered = padDetails.filter((pad) => pad.status === PadStatus.UNUSED);
    } else if (statusFilter === PadStatus.USED) {
      // Show only USED
      filtered = padDetails.filter((pad) => pad.status === PadStatus.USED);
    } else if (statusFilter === PadStatus.CANCELLED) {
      // Show only CANCELLED
      filtered = padDetails.filter((pad) => pad.status === PadStatus.CANCELLED);
    } else {
      // No filter — default behavior
      const unused = padDetails.filter((pad) => pad.status === PadStatus.UNUSED);
      const usedAndCancelled = padDetails.filter((pad) => pad.status === PadStatus.USED || pad.status === PadStatus.CANCELLED).sort((a, b) => a.seriesNo - b.seriesNo); // sort by ID ascending (change to b.id - a.id for descending)

      filtered = [...unused, ...usedAndCancelled];
    }

    // Apply search text filter if present
    if (searchText) {
      filtered = filtered.filter((pad) => pad.cooperative?.coopName?.toLowerCase().includes(searchText));
    }

    return filtered;
  })();

  const getActionEvents = (row: IPadSeriesDetails): IActions<any>[] => {
    const actions: IActions<any>[] = [];

    if (row.status === PadStatus.UNUSED) {
      actions.push({
        name: "Issue",
        event: () => {
          navigate(ROUTES.CAC.issuePRForm.parse(row.id.toString()));
        },
        icon: GoVersions,
        color: "primary",
      });
    }

    if (row.status === PadStatus.USED || row.status === PadStatus.CANCELLED) {
      actions.push({
        name: "View",
        event: () => {
          navigate(ROUTES.CAC.viewPR.parse(row.id.toString()));
        },
        icon: GoVersions,
        color: "primary",
      });
    }

    return actions;
  };

  const columns = getColumns({ getActionEvents, usablePad: filteredAndSortedPadDetails.length !== 0 ? filteredAndSortedPadDetails[0].id : 0 });

  useEffect(() => {
    if (id) {
      // Check if a current pad is saved in local storage.
      if (hasKey("currentPad")) {
        const currentPad: TPadData | null = getData<TPadData>("currentPad");
        // Check whether the current usable pad matches the ID in the link.
        if (currentPad !== null && currentPad.padId !== parseInt(id)) {
          navigate(ROUTES.CAC.cacNewForm.key);
        }
      } else {
        navigate(ROUTES.CAC.cacNewForm.key);
      }
    }
  }, []);

  return (
    <div>
      <div className="flex justify-start">
        <Button variant="secondary" onClick={() => navigate(ROUTES.CAC.cacNewForm.key)}>
          Back
        </Button>
      </div>
      <Typography className="text-primary text-xl font-semibold uppercase mt-10 px-2">
        Series #{userPadAssignmentById?.data ? userPadAssignmentById?.data.seriesFrom : "----"} - #{userPadAssignmentById?.data ? userPadAssignmentById?.data.seriesTo : "----"}
      </Typography>
      <div className="flex items-center justify-between mt-10">
        <TableFilter
          searchText={searchText}
          handleSearch={handleSearch}
          resetCounter={resetCounter}
          statusOptions={padStatusOptions}
          statusFilter={status}
          handleStatusChange={handleStatusChange}
          dateFrom={dateFrom}
          handleDateFromChange={handleDateFromChange}
          dateTo={dateTo}
          handleDateToChange={handleDateToChange}
          handleClearAll={handleClearAll}
        />
      </div>
      <Table
        className="!min-h-[100%] h-[500px] border-[1px] border-zinc-300 mt-2"
        columns={columns}
        data={filteredAndSortedPadDetails}
        searchable={false}
        multiSelect={false}
        selectable={false}
        paginationTotalRows={userPadAssignmentById?.data?.padSeriesDetails ? userPadAssignmentById?.data?.padSeriesDetails.length : 0}
        paginationServer={true}
        // loading={loading}
        // onChangeRowsPerPage={(newPageSize, newPage) => {
        //   setPageSize(newPageSize);
        //   setPage(newPage);
        // }}
        // onPaginate={(newPage) => setPage(newPage)}
      />
    </div>
  );
};

export default PRTable;
