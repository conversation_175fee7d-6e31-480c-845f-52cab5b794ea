import Button from "@components/common/Button";
import Table from "@components/common/Table";
import Typography from "@components/common/Typography";
import Select from "@components/form/Select";
import TextField from "@components/form/TextField";
import { createDateChangeHand<PERSON>, createSelectChangeHand<PERSON> } from "@helpers/handlers";
import { useFetchWithParams } from "@hooks/useFetchWithParams";
import { useSelectOptions } from "@hooks/useSelectOptions";
import { IPadAssignments } from "@interface/form-inventory.interface";
import { RootState } from "@state/reducer";
import { useIncomingReceivedFormActions } from "@state/reducer/form-inventory-incoming-received-form";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useReleasedMethodActions } from "@state/reducer/form-inventory-utilities-released-methods";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { getColumns } from "../table/column/completed-column";
import { useDebouncedSearch } from "@hooks/useDebouncedSearch";
import TableFilter from "../table/filter/TableFilter";
import { FaEye, FaPlus, FaTrash } from "react-icons/fa";
import CompletedPadsModal from "../modal/compeleted-pads-modal";
import { ROUTES } from "@constants/routes";
import { IActions } from "@interface/common.interface";
import ViewPadDetailsModal from "../modal/view-pad-details-modal";
import { formatWordDateDDMMYYY } from "@helpers/date";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { FormStatus, ReleasedMethods } from "@enums/form-status";
import { TCreateReturnedPayload, TFormTransmittalTrailPayload } from "@state/types/form-inventory-transmittal";
import { useFormik } from "formik";
import { ReturnPadsValidationSchema } from "@services/form-inventory-transmittal/form-inventory-transmittal.schema";
import { useUserManagementActions } from "@state/reducer/users-management";
import { Combobox } from "@components/common-v2/Combobox";

const TransmittalReturnedForm = () => {
  const navigate = useNavigate();

  // state
  const [viewPadAssignmentId, setViewPadAssignmentId] = useState<number | undefined>();

  // view Modal
  const [isViewOpen, setIsViewOpen] = useState<boolean>(false);

  // Add Modal
  const [isFormOpen, setIsFormOpen] = useState<boolean>(false);
  const [resetCounterModal, setResetCounterModal] = useState(0);
  const [divisionFilterModal, setDivisionFilterModal] = useState<number>(0);
  const [typeModal, setTypeModal] = useState<number>(0);
  const [areaModal, setAreaModal] = useState<number>(0);

  const [dateFromModal, setDateFromModal] = useState<string>("");
  const [dateToModal, setDateToModal] = useState<string>("");
  const [pageModal, setPageModal] = useState(1);
  const [pageSizeModal, setPageSizeModal] = useState(10);
  const [localSelectedPads, setLocalSelectedPads] = useState<IPadAssignments[]>([]);

  // Global state
  const { selectedCompletedPads } = useSelector((state: RootState) => state.formInventoryIncomingReceivedForms);
  const ReleasedMethod = useSelector((state: RootState) => state.formInventoryUtilitiesReleasedMethods.releasedMethods);
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const types = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const areas = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const { data: completedPads, loading: loadingModal } = useSelector((state: RootState) => state.formInventoryIncomingReceivedForms.getCompletedPads);
  const { data } = useSelector((state: RootState) => state.auth.user);
  const users = useSelector((state: RootState) => state.usersManagement.users);
  const { postReturnedPads: returnedPads } = useSelector((state: RootState) => state.formInventoryTransmittal);

  // actions
  const { getReleasedMethods } = useReleasedMethodActions();
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const { getCompletedPads, bulkAddCompletedPads, removeCompletedPads } = useIncomingReceivedFormActions();
  const { postReturnedPads, resetPostReturnedPads } = useTransmittalFormActions();
  const { getUsers } = useUserManagementActions();

  const formik = useFormik({
    initialValues: {
      releasedTo: 0,
      releasedMethodId: 0,
      deliveredBy: "",
      trackingNo: "",
    },
    validationSchema: ReturnPadsValidationSchema,
    onSubmit: (values) => {
      const padAssignmentPayload = selectedCompletedPads.map(({ id, seriesFrom, seriesTo, formId, formTransmittalId, status, padNumber }: IPadAssignments) => ({
        id,
        seriesFrom,
        seriesTo,
        formId,
        formTransmittalId,
        status,
        assignedUser: data?.id,
        padNumber,
      }));

      const formTransmittalPayload: TFormTransmittalTrailPayload = {
        status: FormStatus.RETURNED,
        releasedTo: values.releasedTo,
        releasedMethodId: values.releasedMethodId,
        deliveredBy: values.deliveredBy,
      };

      // add tracking number if not empty and
      if (values.trackingNo !== "" && checkMethodUsed()) {
        formTransmittalPayload.trackingNo = values.trackingNo;
      }

      const payload: TCreateReturnedPayload = {
        releasedArea: 1,
        releasedTo: values.releasedTo,
        remarks: "",
        status: FormStatus.RETURNED,
        padAssignment: padAssignmentPayload,
        formTransmittalTrail: [formTransmittalPayload],
      };

      postReturnedPads(payload);
    },
  });

  // custom hooks
  const { value: searchUser, handleChange: handleSearchUser } = useDebouncedSearch();
  const { value: searchText, handleChange: handleSearch, setValue: setSearchText } = useDebouncedSearch();
  const { value: searchTextModal, handleChange: handleSearchModal, setValue: setSearchTextModal } = useDebouncedSearch();

  const releasedMethodOptions = useSelectOptions({
    data: ReleasedMethod,
    firstOptionText: "Select Method",
    valueKey: "id",
    textKey: "releasedMethodName",
  });

  const divisionOptions = useSelectOptions({
    data: divisions,
    firstOptionText: "Select Division",
    valueKey: "id",
    textKey: "divisionName",
  });

  const typeOptions = useSelectOptions({
    data: types,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: "formTypeName",
  });

  const areaOptions = useSelectOptions({
    data: areas,
    firstOptionText: "Select Area",
    valueKey: "id",
    textKey: "areaName",
  });

  // modal handlers
  const handleDivisionModalChange = createSelectChangeHandler(setDivisionFilterModal);
  const handleTypeModalChange = createSelectChangeHandler(setTypeModal);
  const handleAreaModalChange = createSelectChangeHandler(setAreaModal);

  // modal date handlers
  const handleDateFromModalChange = createDateChangeHandler(setDateFromModal);
  const handleDateToModalChange = createDateChangeHandler(setDateToModal);

  const handleCheckboxChange = (row: IPadAssignments, isChecked: boolean) => {
    if (isChecked) {
      // Add to local state if not already there
      setLocalSelectedPads((prev) => {
        const exists = prev.some((p) => p.id === row.id);
        return exists ? prev : [...prev, row];
      });
    } else {
      // Remove from local state
      setLocalSelectedPads((prev) => prev.filter((p) => p.id !== row.id));
    }
  };

  useFetchWithParams([getDivisions, getFormTypes, getAreas], { filter: "" }, [], false);

  useFetchWithParams(
    getCompletedPads,
    {
      page: pageModal,
      pageSize: pageSizeModal,
      filter: searchText,
      divisionFilter: divisionFilterModal,
    },
    [searchTextModal, divisionFilterModal, typeModal, pageModal, pageSizeModal, dateFromModal, dateToModal]
  );

  // Fetch padRequests with filters and pagination
  useFetchWithParams(
    getUsers,
    {
      nameFilter: searchUser,
    },
    [searchUser],
    false
  );

  const filteredCompletedPads = completedPads?.data.filter((pad: IPadAssignments) => !selectedCompletedPads.some((selected: IPadAssignments) => selected.id === pad.id));

  const handleAddCompletedPads = () => {
    bulkAddCompletedPads([...localSelectedPads, ...selectedCompletedPads]);
    setIsFormOpen(false);
  };

  // Declare column action and add it
  const getActionEvents = (row: IPadAssignments): IActions<any>[] => {
    const actions: IActions<any>[] = [
      {
        name: "View",
        event: () => {
          setViewPadAssignmentId(row.id);
          handleToggleViewModal();
        },
        icon: FaEye,
        color: "primary",
      },
      {
        name: "Remove",
        event: () => {
          removeCompletedPads(row);
        },
        icon: FaTrash,
        color: "primary",
      },
    ];
    return actions;
  };

  const columns = getColumns({ getActionEvents });
  const modalColumns = getColumns({ isColumnModal: true, handleCheckboxChange });

  const checkMethodUsed = () => {
    const method = ReleasedMethod.find((method) => method.id === formik.values.releasedMethodId);
    return method?.releasedMethodCode === ReleasedMethods.MAIL_COURIER;
  };

  const handleToggleFormModal = () => {
    setIsFormOpen((prev) => !prev);
    setLocalSelectedPads([]);
  };

  const handleToggleViewModal = () => {
    setIsViewOpen((prev) => !prev);
    setLocalSelectedPads([]);
  };

  const handleModalClearAll = () => {
    setSearchTextModal("");
    setDivisionFilterModal(0);
    setTypeModal(0);
    setDateFromModal("");
    setDateToModal("");
    setResetCounterModal((prev) => prev + 1);
  };

  const dateNow = () => {
    const now = new Date();
    return formatWordDateDDMMYYY(now.toISOString(), true);
  };

  const getReturnedAreaId = () => {
    return areas.find((area) => area.userAreaCode === "CLIFSA")?.id;
  };

  useEffect(() => {
    getReleasedMethods({ filter: "" });
  }, []);

  useEffect(() => {
    if (returnedPads.success) {
      navigate(ROUTES.CAC.cacNewForm.key);
      resetPostReturnedPads();
    }
  }, [returnedPads.success]);

  useEffect(() => {
    if (selectedCompletedPads.length === 0) {
      navigate(ROUTES.CAC.cacNewForm.key);
    }
  }, [selectedCompletedPads]);

  return (
    <>
      {selectedCompletedPads.length && (
        <div>
          <Button classNames="btn bg-slate-600 btn-sm" onClick={() => navigate(ROUTES.CAC.cacNewForm.key)}>
            Back
          </Button>
          <div className="mx-6">
            <Typography className="mt-6 text-primary font-poppins-semibold">TRANSMITTAL RETURN</Typography>

            <form onSubmit={formik.handleSubmit}>
              <div className="mt-8 gap-4 flex justify-center">
                <div className="w-full flex flex-col gap-10">
                  <div className="flex w-full flex-col">
                    <div className="divider divider-start font-semibold">RETURN DETAILS</div>
                  </div>

                  {/* First row */}
                  <div className="grid grid-cols-4 gap-4">
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">Area</span>
                      <Select size="sm" options={areaOptions} value={getReturnedAreaId()} readOnly />
                    </div>
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">Return To</span>
                      {/* Ayaw sa walaa ni maam */}
                      {/* <CustomTextField
                        suggestionOptions={users} // Array of the data
                        getOptionLabel={(u) => `${u.firstname} ${u.lastname}`} // displayed in the textfield
                        getOptionValue={(u) => u.id} // Value of selected data
                        name="releasedTo"
                        value={formik.values.releasedTo !== 0 ? formik.values.releasedTo : ""}
                        onChange={formik.handleChange}
                        onInputChange={handleSearchUser}
                        placeholder="Search User"
                        variant="primary"
                        size="sm"
                      /> */}
                      <Combobox
                        suggestionOptions={users}
                        optionLabel={(u) => `${u.firstname} ${u.lastname}`}
                        optionValue={(u) => u.id}
                        placeholder="Select User"
                        onInputChange={handleSearchUser}
                        setData={(u) => {
                          formik.setFieldValue("releasedTo", u.id);
                        }}
                        onClear={() => {
                          formik.setFieldValue("releasedTo", 0);
                        }}
                      />
                      {formik.touched.releasedTo && formik.errors.releasedTo && <span className="text-red-500">{formik.errors.releasedTo}</span>}
                    </div>
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">Date Returned</span>
                      <TextField size="sm" placeholder="Date Returned" disabled value={dateNow()} />
                    </div>
                  </div>
                  {/* Second row */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">Returned Via</span>

                      <Select
                        name="releasedMethodId"
                        size="sm"
                        options={releasedMethodOptions}
                        value={formik.values.releasedMethodId}
                        onChange={(e) => formik.setFieldValue("releasedMethodId", parseInt(e.target.value))}
                      />
                      {formik.touched.releasedMethodId && formik.errors.releasedMethodId && <span className="text-red-500">{formik.errors.releasedMethodId}</span>}
                    </div>
                    {/* check selected method is not in the array */}
                    {![0, NaN].includes(formik.values.releasedMethodId) && (
                      <>
                        <div className=" flex flex-col gap-2">
                          <span className="text-sm text-grey-500">{checkMethodUsed() ? "Courier Service Name" : "Handed by"}</span>
                          <TextField
                            name="deliveredBy"
                            size="sm"
                            placeholder={checkMethodUsed() ? "Courier Service Name" : "Handed by"}
                            value={formik.values.deliveredBy}
                            onChange={formik.handleChange}
                          />
                          {formik.touched.deliveredBy && formik.errors.deliveredBy && <span className="text-red-500">{formik.errors.deliveredBy}</span>}
                        </div>

                        {checkMethodUsed() && (
                          <div className=" flex flex-col gap-2">
                            <span className="text-sm text-grey-500">Tracking No.</span>
                            <TextField name="trackingNo" size="sm" placeholder="Tracking No." value={formik.values.trackingNo} onChange={formik.handleChange} />
                            {formik.touched.trackingNo && formik.errors.trackingNo && <span className="text-red-500">{formik.errors.trackingNo}</span>}
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </div>
              <div className="mt-8 gap-4 flex justify-center">
                <div className="w-full flex flex-col gap-10">
                  <div className="flex w-full flex-col">
                    <div className="divider divider-start font-semibold">SERIES OVERVIEW</div>
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">Division</span>
                      <Select size="sm" options={divisionOptions} value={selectedCompletedPads[0].form?.divisionId} readOnly />
                    </div>
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">Type</span>
                      <Select size="sm" options={typeOptions} value={selectedCompletedPads[0].form?.formTypeId} readOnly />
                    </div>
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">Area</span>
                      <Select size="sm" options={areaOptions} value={selectedCompletedPads[0].form?.areaId} readOnly />
                    </div>
                  </div>

                  <div className="w-full">
                    <div className="flex flex-row items-center justify-between">
                      <TableFilter searchText={searchText} handleSearch={handleSearch} handleClearAll={() => setSearchText("")} />
                      <Button variant="primary" classNames="flex items-center gap-2" onClick={handleToggleFormModal}>
                        <FaPlus />
                        Add New
                      </Button>
                    </div>
                    {/* First row */}
                    <div className="w-full">
                      <Table
                        className="!min-h-[100%] h-[300px] border-[1px] border-zinc-300 mt-2"
                        columns={columns}
                        data={selectedCompletedPads}
                        searchable={false}
                        multiSelect={false}
                        selectable={false}
                        paginationTotalRows={selectedCompletedPads.length || 0}
                        paginationServer={true}
                      />
                    </div>
                  </div>
                </div>
              </div>
              {/* Add Modal */}
              <CompletedPadsModal
                handleToggleFormModal={handleToggleFormModal}
                isFormOpen={isFormOpen}
                handleAddCompletedPads={handleAddCompletedPads}
                divisionFilter={divisionFilterModal}
                divisionOptions={divisionOptions}
                handleClearAll={handleModalClearAll}
                handleDivisionChange={handleDivisionModalChange}
                searchText={searchTextModal}
                handleSearch={handleSearchModal}
                handleTypeChange={handleTypeModalChange}
                areaFilter={areaModal}
                areaOptions={areaOptions}
                handleAreaChange={handleAreaModalChange}
                resetCounter={resetCounterModal}
                type={typeModal}
                typeOptions={typeOptions}
                dateFrom={dateFromModal}
                handleDateFromChange={handleDateFromModalChange}
                dateTo={dateToModal}
                handleDateToChange={handleDateToModalChange}
                columns={modalColumns}
                data={filteredCompletedPads || []}
                totalCount={completedPads?.meta?.total || [].length}
                loading={loadingModal}
                onChangeRowsPerPage={(newPageSize, newPage) => {
                  setPageSizeModal(newPageSize);
                  setPageModal(newPage);
                }}
                onPaginate={(newPage) => setPageModal(newPage)}
              />
              {/* View Modal */}
              <ViewPadDetailsModal isViewOpen={isViewOpen} handleToggleViewModal={handleToggleViewModal} padAssignment={selectedCompletedPads.find((pad) => pad.id === viewPadAssignmentId)} />
              <div className="flex justify-center gap-2 mt-14">
                <Button classNames="bg-[#33333399] w-80 font-medium rounded-lg px-5 py-2.5 text-center mb-2 hover:">Cancel</Button>
                <Button classNames="bg-sky-500 hover:bg-sky-700 w-80 font-medium rounded-lg text-sm px-5 py-2.5 text-center mb-2" type="submit" isSubmitting={returnedPads.loading}>
                  Request
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  );
};

export default TransmittalReturnedForm;
