import { useEffect, useState } from "react";
import ViewIssued<PERSON> from "./view-issued-pr";
import <PERSON><PERSON>ancelled<PERSON> from "./view-cancelled-pr";
import { useNavigate, useParams } from "react-router-dom";
import { ROUTES } from "@constants/routes";
import Button from "@components/common/Button";
import PrPagination from "../../pr-pagination";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { PadStatus } from "@enums/form-status";
import Typography from "@components/common/Typography";
import { getData, hasKey } from "@helpers/storage";

const ViewPR = () => {
  const navigate = useNavigate();

  // Get ID in URL
  const { id } = useParams();

  // State
  const [prTableId, setPrTableId] = useState<number | null>(null);

  // Global states
  const { userPadSeriesDetails } = useSelector((state: RootState) => state.formInventoryTransmittal);

  // Actions
  const { getUserPadSeriesDetails } = useTransmittalFormActions();

  useEffect(() => {
    if (id) {
      getUserPadSeriesDetails({ id: parseInt(id) });
    }
  }, [id]);

  const handleBack = () => {
    if (parseInt(userPadSeriesDetails?.data?.seriesNo) !== parseInt(userPadSeriesDetails?.data?.seriesFrom)) {
      navigate(ROUTES.ADMINSATELLITE.viewPR.parse((userPadSeriesDetails?.data.id - 1).toString()));
    }
  };

  const handleForward = () => {
    if (parseInt(userPadSeriesDetails?.data?.seriesNo) !== parseInt(userPadSeriesDetails?.data?.seriesTo)) {
      navigate(ROUTES.ADMINSATELLITE.viewPR.parse(userPadSeriesDetails?.data.id + 1).toString());
    }
  };

  const redirect = () => {
    return prTableId !== null ? navigate(ROUTES.ADMINSATELLITE.viewPrTable.parse(prTableId.toString())) : navigate(ROUTES.ADMINSATELLITE.adminSatelliteAdminNewForm.key);
  };

  useEffect(() => {
    if (hasKey("prTableId")) {
      setPrTableId(getData("prTableId"));
    }
  }, []);

  return (
    <div className="px-4">
      {userPadSeriesDetails?.data && userPadSeriesDetails?.data?.status !== PadStatus.UNUSED && (
        <div className="flex justify-start">
          <Button variant="secondary" onClick={redirect}>
            Back
          </Button>
        </div>
      )}

      {/* Render this if status is USED */}
      {userPadSeriesDetails?.data && userPadSeriesDetails?.data?.status === PadStatus.USED && <ViewIssuedPR data={userPadSeriesDetails.data} />}

      {/* Render this if status is CANCELLED */}
      {userPadSeriesDetails?.data && userPadSeriesDetails?.data?.status === PadStatus.CANCELLED && <ViewCancelledPR data={userPadSeriesDetails.data} />}

      {/* Render this if status is UNUSED */}
      {userPadSeriesDetails?.data && userPadSeriesDetails?.data?.status === PadStatus.UNUSED && id !== undefined && (
        <div className="mt-10 flex justify-center items-center">
          <div className="w-1/2 flex flex-col items-center gap-2">
            <Typography size="2xl">No Available data for this Series Number</Typography>

            <Button variant="secondary" onClick={redirect}>
              Back
            </Button>
          </div>
        </div>
      )}

      {/* Pagination */}
      {/* Render only if the status is USED or CANCELLED */}
      {userPadSeriesDetails?.data && (userPadSeriesDetails?.data?.status === PadStatus.USED || userPadSeriesDetails?.data?.status === PadStatus.CANCELLED) && (
        <div className="mt-8">
          <PrPagination handleBack={handleBack} handleForward={handleForward} seriesNo={userPadSeriesDetails?.data?.seriesNo} seriesFrom={userPadSeriesDetails?.data?.padAssignment?.seriesFrom} />
        </div>
      )}
    </div>
  );
};

export default ViewPR;
