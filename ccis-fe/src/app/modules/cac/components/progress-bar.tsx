import React from "react";

interface ProgressBarProps {
  value: number;
  max?: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ value, max = 50 }) => {
  const percent = Math.min((value / max) * 100, 100);

  return (
    <div className="w-full max-w-md mx-auto bg-base-200 rounded-lg h-8 relative overflow-hidden">
      {/* Filled part */}
      <div className="bg-success h-full" style={{ width: `${percent}%` }}></div>

      {/* Centered text */}
      <div className={`absolute inset-0 flex items-center justify-center font-semibold  select-none ${percent >= 60 ? "text-white" : "text-black"}`}>
        {value}/{max}
      </div>
    </div>
  );
};

export default ProgressBar;
