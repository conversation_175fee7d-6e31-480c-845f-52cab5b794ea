import React from "react";
import logo from "../../../../../assets/CRYSTAL-LOGO.png";

const UnderMaintenance: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col justify-center gap-6 items-center bg-gray-50 text-gray-800 bg-gradient-to-br from-black via-black to-primary">
      <div className="-translate-x-20">
        <iframe src="https://lottie.host/embed/638a072e-7892-46ff-854e-95ffad9732d7/MyYGeguKYC.lottie" style={{ height: "500px", width: "1000px" }}></iframe>
      </div>

      <div className="text-lg text-gray-600 text-center w-full leading-relaxed text-white">
        <div className="flex justify-center items-center mb-4">
          {" "}
          <img src={logo} alt="CCIS Logo" className="h-20 " />
        </div>
        <h1 className="text-3xl font-poppins-semibold mb-2 text-blue-700 text-white">
          <span className="text-amber-500">CLIMBS CRYSTAL</span> will be back soon!
        </h1>
        Sorry for the inconvenience.
        <br />
        Our site is currently undergoing scheduled maintenance.
        <br />
        Thank you for your patience.
        <br /> <br />
        <br />
        <style>
          {`
    @keyframes rgbText {
      0% { background-position: 0% 50%; }
      100% { background-position: 400% 50%; }
    }
  `}
        </style>
        <span
          className="text-lg font-poppins-semibold bg-gradient-to-r from-pink-500 via-cyan-500 to-pink-500 bg-clip-text text-transparent"
          style={{
            backgroundSize: "400% 400%", // bigger area to cycle smoothly
            animation: "rgbText 4s linear infinite",
          }}
        >
          InsurTech Department
        </span>
      </div>
    </div>
  );
};

export default UnderMaintenance;
