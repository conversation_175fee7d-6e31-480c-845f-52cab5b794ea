import Avatar from "@components/common/Avatar";
import Modal from "@components/common/Modal";
import Progress from "@components/common/Progress";
import Typography from "@components/common/Typography";
import { useProfileActions } from "@state/reducer/profile";
import { RootState } from "@state/store";
import { FC, Fragment, useEffect, useState } from "react";
import { FaCamera } from "react-icons/fa";
import { LuMail, LuPhone } from "react-icons/lu";
import { useSelector } from "react-redux";
import UploadAvatarForm from "../Forms/UploadAvatarForm";
import { usePositionsManagementActions } from "@state/reducer/utilities-positions";
const UserCard: FC = () => {
  const [editAvatar, setEditAvatar] = useState<boolean>(false);
  const profile = useSelector((state: RootState) => state.profile.profile);
  const profilePercentage = useSelector((state: RootState) => state.profile.profilePercent) ?? 0;
  const positions = useSelector((state: RootState) => state.utilitiesPositions.getPosition?.data) as Array<{ id: number; positionName: string }>;
  const { getProfilePercentage } = useProfileActions();
  const [filterPosition] = useState("");

  const { getPosition } = usePositionsManagementActions();
  const handleModal = () => {
    setEditAvatar((prev) => !prev);
  };

  useEffect(() => {
    getProfilePercentage();
    getPosition({ params: { filter: filterPosition } });
  }, []);

  return (
    <Fragment>
      <div className="card lg:card-side bg-base-100 mb-0 p-0">
        <figure className="bg-slate-300 !rounded-none p-2 pr-3">
          <Avatar size="md" src={profile.profilePicturePath} classNames="w-1/6 h-1/6 sm:w-20 sm:h-20  lg:w-full lg:h-full">
            {/* Fallback display if no image is set */}
            <Typography size="5xl">
              {profile?.firstname[0]}
              {profile?.lastname[0]}
            </Typography>
          </Avatar>
          <div role="button" className="btn btn-circle z-20 bottom-0 -ml-12 mt-24" onClick={handleModal}>
            <FaCamera />
          </div>
        </figure>
        <div className="card-body mb-0 p-0 lg:p-4 mt-4">
          <h1 className="card-title text-xl justify-center lg:justify-normal lg:text-1xl">{`${profile?.firstname} ${profile?.lastname}`}</h1>
          <div className="lg:flex lg:flex-1 lg:flex-row  text-accent">
            <div className="lg:flex flex-1 text-sm lg:text-base text-center">{positions.find((pos) => pos.id === Number(profile.positionId))?.positionName || "N/A"}</div>
            <div className="flex flex-1 flex-row lg:justify-end  text-sm justify-center lg:text-base">
              <LuMail size={18} className="mr-2 mt-1" /> {profile?.email}
            </div>
            <div className="flex flex-1 flex-row lg:justify-end justify-center text-sm lg:text-base">
              <LuPhone size={18} className="mr-2 mt-1" /> {profile?.contactNumber}
            </div>
          </div>
          <div className="block sm:hidden divider text-slate-600 my-2"></div>
          <div></div>
          <div className="lg:flex flex-1 flex-col mt-0">
            <Typography className=" text-xs lg:text-base">
              Profile Completed: <span className="ml-2">{`${parseFloat(profilePercentage.toString()).toFixed(2)}%`}</span>
            </Typography>
            <div className="lg:mt-2 w-96">
              <Progress value={profilePercentage} variant="success" className="!progress-success w-80 lg:w-full" />
            </div>
          </div>
        </div>
      </div>
      {editAvatar && (
        <Modal title="Upload Profile Avatar" className="fixed inset-0 flex justify-center items-center lg:w-3/5 mx-auto" onClose={handleModal} isOpen={editAvatar}>
          <UploadAvatarForm handleModal={handleModal} />
        </Modal>
      )}
    </Fragment>
  );
};

export default UserCard;
