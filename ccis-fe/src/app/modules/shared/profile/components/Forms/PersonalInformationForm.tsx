import httpClient from "@clients/httpClient";
import LoadingButton from "@components/common/LoadingButton";
import Typography from "@components/common/Typography";
import Select from "@components/form/Select";
import TextField from "@components/form/TextField";
import { UserRoles } from "@interface/routes.interface";
import { EditProfileSchema } from "@services/profile/profile.schema";
import { useProfileActions } from "@state/reducer/profile";
import { RootState } from "@state/store";
import { FormikProvider, Form, useFormik } from "formik";
import { FC, Fragment, useEffect, useState } from "react";
import { useSelector } from "react-redux";

const PersonalInformationForm: FC = () => {
  const roles = useSelector((state: RootState) => state.auth.user.data?.roles ?? []);
  const profile = useSelector((state: RootState) => state.profile.profile);
  const { updateProfile } = useProfileActions();

  const [departments, setDeparments] = useState([]);
  const [positions, setPositions] = useState([]);

  const formik = useFormik({
    initialValues: profile,
    validationSchema: EditProfileSchema,
    onSubmit: async (values) => {
      updateProfile({ ...values, email: profile.email });
    },
  });

  // temporary implementation since departments reducers action is in other task
  const getDepartments = async () => {
    const { data } = await httpClient.get("departments");
    const formatted = data.map((data: any) => {
      return { text: data.departmentName, value: data.id };
    });
    setDeparments(formatted);
  };

  // temporary implementation since positions reducers action is in other task
  const getPositions = async () => {
    const { data } = await httpClient.get("positions");
    const formatted = data.map((data: any) => {
      return { text: data.positionName, value: data.id };
    });
    setPositions(formatted);
  };

  useEffect(() => {
    // temporary
    if (roles.some((e: any) => (e as any).name.toLowerCase() === UserRoles.admin)) {
      getDepartments();
      getPositions();
    }
  }, [roles]);

  return (
    <Fragment>
      <FormikProvider value={formik}>
        <Form>
          {/* <div className='grid grid-cols-3 gap-3 gap-y-8'> */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 gap-y-6">
            <div>
              <Typography text="First name" />
              <TextField
                size="sm"
                placeholder="First name"
                value={formik.values.firstname}
                name="firstname"
                onChange={formik.handleChange}
                error={!!formik.errors.firstname && formik.touched.firstname}
                errorText={formik.errors.firstname}
                required
              />
            </div>
            <div>
              <Typography text="Middle name" />
              <TextField
                type="text"
                size="sm"
                value={formik.values.middlename}
                placeholder="Middle name"
                name="middlename"
                onChange={formik.handleChange}
                error={!!formik.errors.middlename && formik.touched.middlename}
                errorText={formik.errors.middlename}
              />
            </div>
            <div>
              <Typography text="Last name" />
              <TextField
                type="text"
                size="sm"
                value={formik.values.lastname}
                placeholder="Last name"
                name="lastname"
                onChange={formik.handleChange}
                error={!!formik.errors.lastname && formik.touched.lastname}
                errorText={formik.errors.lastname}
                required
              />
            </div>
            <div>
              <Typography text="Gender" />
              <Select
                value={formik.values.gender ?? ""}
                options={[
                  { value: "male", text: "Male" },
                  { value: "female", text: "Female" },
                ]}
                className="select-sm border-primary"
                name="gender"
                onChange={formik.handleChange}
                error={!!formik.errors.gender && formik.touched.gender}
                errorText={formik.errors.gender}
                required
              />
            </div>
            <div>
              <Typography text="Date of Birth" />
              <TextField
                type="date"
                size="sm"
                value={formik.values.birthDate}
                placeholder="Date of Birth"
                name="birthDate"
                onChange={formik.handleChange}
                error={!!formik.errors.birthDate && formik.touched.birthDate}
                errorText={formik.errors.birthDate}
                required
              />
            </div>
            <div>
              <Typography text="Phone Number" />
              <TextField
                type="text"
                size="sm"
                value={formik.values.contactNumber}
                placeholder="Phone Number"
                name="contactNumber"
                onChange={formik.handleChange}
                error={!!formik.errors.contactNumber && formik.touched.contactNumber}
                errorText={formik.errors.contactNumber}
                required
              />
            </div>
            <div>
              <Typography text="Email" />
              <TextField
                type="email"
                size="sm"
                value={formik.values.email}
                placeholder="Email"
                name="email"
                onChange={formik.handleChange}
                error={!!formik.errors.email && formik.touched.email}
                errorText={formik.errors.email}
                readOnly
                required
              />
            </div>
            {/* <div className='col-span-2'> */}
            <div className="lg:col-span-1 sm:col-span-2 md:w-96 lg:w-full">
              <Typography text="Address" />
              <TextField
                type="text"
                size="sm"
                value={formik.values.address}
                placeholder="Address"
                name="address"
                onChange={formik.handleChange}
                error={!!formik.errors.address && formik.touched.address}
                errorText={formik.errors.address}
                required
              />
            </div>
            <div>
              <Typography text="Company ID" />
              <TextField
                size="sm"
                value={formik.values.companyId}
                placeholder="Company ID"
                name="companyId"
                onChange={formik.handleChange}
                error={!!formik.errors.companyId && formik.touched.companyId}
                errorText={formik.errors.companyId}
                required
              />
            </div>
            {roles.some((e: any) => (e as any).name.toLowerCase() === "super-admin") && (
              <Fragment>
                <div>
                  <Typography text="Position" />
                  <Select
                    value={formik.values.positionId?.toString() ?? ""}
                    options={positions}
                    className="select-sm border-primary"
                    name="positionId"
                    onChange={(e) => {
                      formik.setFieldValue("positionId", parseInt(e.target.value, 10));
                    }}
                    error={!!formik.errors.positionId && formik.touched.positionId}
                    errorText={formik.errors.positionId}
                    required
                  />
                </div>
                <div>
                  <Typography text="Department" />
                  <Select
                    value={formik.values.departmentId?.toString() ?? ""}
                    options={departments}
                    className="select-sm border-primary"
                    name="departmentId"
                    onChange={(e) => {
                      formik.setFieldValue("departmentId", parseInt(e.target.value, 10));
                    }}
                    error={!!formik.errors.departmentId && formik.touched.departmentId}
                    errorText={formik.errors.departmentId}
                    required
                  />
                </div>
              </Fragment>
            )}
          </div>
          <div className="flex flex-row flex-1 md:justify-center mt-6 justify-center lg:justify-end ">
            <LoadingButton type="submit" variant="primary" className="!w-52">
              Save
            </LoadingButton>
          </div>
        </Form>
      </FormikProvider>
    </Fragment>
  );
};

export default PersonalInformationForm;
