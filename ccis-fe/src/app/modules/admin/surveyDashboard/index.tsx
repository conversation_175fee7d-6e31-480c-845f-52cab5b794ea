import { useState, useEffect } from "react";
import { Plus, Trash2, Edit3, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Off } from "lucide-react";
import Button from "@components/common/Button";
import httpClient from "@clients/httpClient";
import { toast } from "react-toastify";
import { <PERSON>a<PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON> } from "react-icons/fa";
import { MdDelete } from "react-icons/md";
import { confirmDelete } from "@helpers/prompt";
import { INewQuestionForm, IQuestion, ITransactionType, IUserFeedbackApiResponse } from "@interface/users-feedback.interface";
import SurveyCharts from "./modal/SurveyCharts";

export const SurveyDashboard = () => {
  const [questions, setQuestions] = useState<IQuestion[]>([]);
  const [transactionTypes, setTransactionTypes] = useState<ITransactionType[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [editingQuestion, setEditingQuestion] = useState<number | null>(null);
  const [newQuestion, setNewQuestion] = useState<INewQuestionForm>({
    question: "",
    status: 1,
    choices: [{ name: "" }],
  });
  const [editQuestion, setEditQuestion] = useState<INewQuestionForm>({
    question: "",
    status: 1,
    choices: [{ name: "" }],
  });
  const [newTransactionType, setNewTransactionType] = useState<string>("");
  const [showTransactionForm, setShowTransactionForm] = useState<boolean>(false);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  //
  const [editingTransactionType, setEditingTransactionType] = useState<number | null>(null);
  const [editTransactionTypeName, setEditTransactionTypeName] = useState<string>("");

  const [viewSurveyChartsModal, setViewSurveyChartsModal] = useState<boolean>(false);

  // Load data on component mount
  useEffect(() => {
    loadAllData();
  }, []);

  const loadAllData = async (): Promise<void> => {
    setLoading(true);
    try {
      await Promise.all([loadQuestions(), loadTransactionTypes()]);
    } catch (error: any) {
      toast.error("Error loading data:", error);
    } finally {
      setLoading(false);
    }
  };

  // API calls for Questions
  const loadQuestions = async (): Promise<void> => {
    try {
      const response = await httpClient.get<IUserFeedbackApiResponse<IQuestion[]>>("/feedback/question/?relations=choices|createdBy");
      if (response?.data && Array.isArray(response.data)) {
        setQuestions(response.data);
      } else {
        setQuestions([]);
      }
    } catch (error: any) {
      toast.error("Error loading questions:", error);
      setQuestions([]);
    }
  };

  const saveQuestion = async (): Promise<void> => {
    if (!newQuestion.question.trim()) return;

    try {
      // Show confirmation prompt before saving
      const result = await confirmDelete(
        "question",
        undefined,
        "Save Question",
        "Are you sure you want to save this question?",
        true,
        "Save",
        "Cancel",
        "#4CAF50", // Green color for save button
        "#d1d1d1",
        "question",
        "#4CAF50",
        "#FFFFFF"
      );

      // Proceed only if user confirms
      if (result.isConfirmed) {
        const formData = new FormData();
        formData.append("question", newQuestion.question);
        formData.append("status", newQuestion.status.toString());

        const response = await httpClient.post("/feedback/question/save-question", formData);

        // Check if response contains the new question ID
        const newQuestionId = response?.data?.id;

        if (newQuestionId && newQuestion.choices.length > 0) {
          // Save choices using the returned question ID
          for (const choice of newQuestion.choices) {
            if (choice.name && choice.name.trim()) {
              await saveChoice(newQuestionId, choice.name);
            }
          }
        }

        // Reset form and reload data
        setNewQuestion({
          question: "",
          status: 1,
          choices: [{ name: "" }],
        });
        await loadQuestions();
      }
    } catch (error: any) {
      toast.error("Error saving question:", error);
    }
  };

  const saveChoice = async (questionId: number, choiceName: string): Promise<void> => {
    try {
      const formData = new FormData();
      formData.append("questionId", questionId.toString());
      formData.append("name", choiceName);

      await httpClient.post("/feedback/choice/save-choice", formData);
    } catch (error: any) {
      toast.error("Error saving choice:", error);
    }
  };

  // API calls for Transaction Types
  const loadTransactionTypes = async (): Promise<void> => {
    try {
      const response = await httpClient.get<IUserFeedbackApiResponse<ITransactionType[]>>("/feedback/transaction-type");
      if (response?.data && Array.isArray(response.data)) {
        setTransactionTypes(response.data);
      } else {
        setTransactionTypes([]);
      }
    } catch (error: any) {
      toast.error("Error loading transaction types:", error);
      setTransactionTypes([]);
    }
  };

  const saveTransactionType = async (): Promise<void> => {
    if (!newTransactionType.trim()) return;

    try {
      const formData = new FormData();
      formData.append("name", newTransactionType);

      await httpClient.post("/feedback/transaction-type/save-transaction", formData);
      toast.success("Transaction type saved successfully");
      setNewTransactionType("");
      setShowTransactionForm(false);
      await loadTransactionTypes();
    } catch (error: any) {
      toast.error("Error saving transaction type:", error);
    }
  };
  const updateTransactionType = async (): Promise<void> => {
    if (!editTransactionTypeName.trim() || !editingTransactionType) return;

    try {
      const payload = {
        name: editTransactionTypeName,
      };

      await httpClient.put(`/feedback/transaction-type/update-transaction/${editingTransactionType}`, payload);

      toast.success("Transaction type updated successfully");
      setEditingTransactionType(null);
      setEditTransactionTypeName("");
      await loadTransactionTypes();
    } catch (error: any) {
      toast.error("Error updating transaction type:", error);
      toast.error("Failed to update transaction type");
    }
  };

  // Helper functions for form management
  const addChoice = (): void => {
    setNewQuestion((prev) => ({
      ...prev,
      choices: [...prev.choices, { name: "" }],
    }));
  };

  const updateChoice = (index: number, value: string): void => {
    setNewQuestion((prev) => ({
      ...prev,
      choices: prev.choices.map((choice, i) => (i === index ? { ...choice, name: value } : choice)),
    }));
  };

  const deleteChoice = (index: number): void => {
    if (newQuestion.choices.length > 1) {
      setNewQuestion((prev) => ({
        ...prev,
        choices: prev.choices.filter((_, i) => i !== index),
      }));
    }
  };

  // Add this function after saveChoice function
  const updateQuestion = async (): Promise<void> => {
    if (!editQuestion.question.trim() || !editingQuestion) return;

    try {
      // Show confirmation prompt before updating
      const result = await confirmDelete(
        "question",
        undefined,
        "Update Question",
        "Are you sure you want to update this question?",
        true,
        "Update",
        "Cancel",
        "#4CAF50", // Green color for update button
        "#d1d1d1",
        "question",
        "#4CAF50",
        "#FFFFFF"
      );

      // Proceed only if user confirms
      if (result.isConfirmed) {
        const payload = {
          id: editingQuestion,
          question: editQuestion.question,
          status: editQuestion.status,
          feedbackChoices: editQuestion.choices.map((choice) => ({
            questionId: editingQuestion,
            name: choice.name,
          })),
        };

        await httpClient.put(`/feedback/question/feedback-questions/${editingQuestion}`, payload);

        // Reset edit mode and reload questions
        setIsEditMode(false);
        await loadQuestions();
        toast.success("Question updated successfully");
      }
    } catch (error: any) {
      toast.error("Error updating question:", error);
      toast.error("Failed to update question");
    }
  };

  const handleDeleteQuestion = async (questionId: number): Promise<void> => {
    try {
      const result = await confirmDelete(
        "question",
        async () => {
          await httpClient.delete(`/feedback/question/${questionId}`);
        },
        "Delete Question",
        "Are you sure you want to delete this question? This action cannot be undone.",
        true,
        "Yes, Delete",
        "Cancel"
      );

      if (result.isConfirmed) {
        setEditingQuestion(null);
        setIsEditMode(false);
        await loadQuestions();
        toast.success("Question deleted successfully");
      }
    } catch (error: any) {
      toast.error("Error deleting question:", error);
      toast.error("Failed to delete question");
    }
  };

  const deleteTransactionType = async (transactionTypeId: number): Promise<void> => {
    try {
      // Show confirmation prompt before deleting
      const result = await confirmDelete(
        "transaction type",
        undefined,
        "Delete Transaction Type",
        "Are you sure you want to delete this transaction type?",
        true,
        "Delete",
        "Cancel",
        "#FF5151", // Red color for delete button
        "#d1d1d1",
        "warning",
        "#FF5151",
        "#FFFFFF"
      );

      // Proceed only if user confirms
      if (result.isConfirmed) {
        await httpClient.delete(`/feedback/transaction-type/${transactionTypeId}`);
        toast.success("Transaction type deleted successfully");
        await loadTransactionTypes();
      }
      await loadQuestions();
    } catch (error: any) {
      toast.error("Error deleting transaction type:", error);
      toast.error("Failed to delete transaction type");
    }
  };

  const handleCopyLink = async () => {
    try {
      // Dynamically construct the URL using window.location
      const baseUrl = `${window.location.protocol}//${window.location.host}`;
      const link = `${baseUrl}/users-feedback`;

      await navigator.clipboard.writeText(link);
      toast.success("Link copied to clipboard!");
    } catch (error: any) {
      toast.error("Error copying link:", error);
      toast.error("Failed to copy link");
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <BarChart3 className="w-12 h-12 mx-auto text-gray-400 mb-4 animate-spin" />
          <p className="text-gray-600">Loading survey data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Admin Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-blue-600 rounded-lg">
                <BarChart3 className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">Survey Admin Panel</h1>
                <p className="text-gray-600">Manage customer satisfaction surveys</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button onClick={() => setViewSurveyChartsModal(true)} className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-blue-700">
                <FaChartBar className="w-4 h-4 inline mr-2" />
                View Charts
              </button>
              <button onClick={handleCopyLink} className="px-4 py-2 bg-green-600 hover:bg-green-800 cursor-pointer text-white rounded-lg hover:bg-blue-700">
                <FaCopy className="w-4 h-4 inline mr-2" />
                Copy Link
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Transaction Types */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-800">Transaction Types</h2>
                <button
                  onClick={() => {
                    setShowTransactionForm(!showTransactionForm);
                    setEditingTransactionType(null);
                    setEditTransactionTypeName("");
                  }}
                  className="p-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>

              {showTransactionForm && (
                <div className="mb-4 p-4 border border-gray-200 rounded-lg">
                  <input
                    type="text"
                    value={newTransactionType}
                    onChange={(e) => setNewTransactionType(e.target.value)}
                    placeholder="Enter transaction type name..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg mb-2"
                  />
                  <div className="flex space-x-2">
                    <Button onClick={saveTransactionType} disabled={!newTransactionType.trim()} variant="success" classNames="flex-1 px-3 py-1 text-sm">
                      Save
                    </Button>
                    <Button
                      variant="customWhite"
                      onClick={() => {
                        setShowTransactionForm(false);
                        setNewTransactionType("");
                      }}
                      classNames="flex-1 px-3 py-1 text-sm"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}

              {editingTransactionType && (
                <div className="mb-4 p-4 border border-blue-200 bg-blue-50 rounded-lg">
                  <input
                    type="text"
                    value={editTransactionTypeName}
                    onChange={(e) => setEditTransactionTypeName(e.target.value)}
                    placeholder="Enter transaction type name..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg mb-2"
                  />
                  <div className="flex space-x-2">
                    <Button onClick={updateTransactionType} disabled={!editTransactionTypeName.trim()} variant="success" classNames="flex-1 px-3 py-1 text-sm">
                      Update
                    </Button>
                    <Button
                      variant="customWhite"
                      onClick={() => {
                        setEditingTransactionType(null);
                        setEditTransactionTypeName("");
                      }}
                      classNames="flex-1 px-3 py-1 text-sm"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                {transactionTypes && transactionTypes.length > 0 ? (
                  transactionTypes.map((type) => (
                    <div key={type.id} className="p-3 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium text-gray-800">{type.name}</h3>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => {
                              setEditingTransactionType(type.id);
                              setEditTransactionTypeName(type.name);
                              setShowTransactionForm(false);
                            }}
                            className="cursor-pointer text-sm font-semibold text-primary hover:text-primary-dark rounded-lg"
                          >
                            <Edit3 className="w-4 h-4 inline" />
                          </button>
                          <button onClick={() => deleteTransactionType(type.id)} className="cursor-pointer text-sm font-semibold text-danger hover:text-danger-dark rounded-lg">
                            <MdDelete className="w-4 h-4 inline" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center text-gray-500 py-4">No transaction types found</div>
                )}
              </div>
            </div>

            {/* Questions List */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-800 mb-4">Questions</h2>
                <button onClick={() => setEditingQuestion(null)} className="p-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                  <Plus className="w-4 h-4" />
                </button>
              </div>
              <div className="space-y-3">
                {questions && questions.length > 0 ? (
                  questions.map((question) => (
                    <div
                      key={question.id}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${editingQuestion === question.id ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:bg-gray-50"}`}
                    >
                      <div className="flex items-start justify-between">
                        <div
                          className="flex-1"
                          onClick={() => {
                            setEditingQuestion(question.id);
                            setIsEditMode(false);
                            setEditQuestion({
                              question: question.question,
                              status: question.status,
                              choices: question.choices.map((c) => ({ name: c.name })),
                            });
                          }}
                        >
                          <h3 className="font-medium text-gray-800 mb-1">{question.question}</h3>
                          <p className="text-sm text-gray-600">{question.choices?.length || 0} choices</p>
                          <div className="flex items-center mt-2">
                            <span className={`px-2 py-1 text-xs rounded ${question.status === 1 ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}`}>
                              {question.status === 1 ? "Active" : "Hidden"}
                            </span>
                          </div>
                        </div>
                        <button className="p-1 text-gray-500 hover:bg-gray-100 rounded" title={question.status === 1 ? "Hide question" : "Show question"}>
                          {question.status === 1 ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                        </button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center text-gray-500 py-4">No questions found</div>
                )}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-2">
            {editingQuestion ? (
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-lg font-semibold text-gray-800">
                    <Edit3 className="w-5 h-5 inline mr-2" />
                    {isEditMode ? "Edit Question" : "Question Details"}
                  </h2>
                  <div className="flex items-center gap-2">
                    <button onClick={() => handleDeleteQuestion(editingQuestion!)} className="bg-red-600 hover:bg-red-700 cursor-pointer text-sm font-semibold text-white px-3 py-1 rounded-lg">
                      <MdDelete className="w-3 h-3 inline mr-2" />
                      Delete
                    </button>
                    {!isEditMode && (
                      <button onClick={() => setIsEditMode(true)} className="bg-primary hover:bg-primary-dark cursor-pointer text-sm font-semibold text-white px-3 py-1 rounded-lg">
                        <Edit3 className="w-3 h-3 inline mr-2" />
                        Edit
                      </button>
                    )}
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Question Text</label>
                    <input
                      type="text"
                      value={editQuestion.question}
                      onChange={(e) => setEditQuestion((prev) => ({ ...prev, question: e.target.value }))}
                      placeholder="Enter your question..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      readOnly={!isEditMode}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select
                      value={editQuestion.status}
                      onChange={(e) => setEditQuestion((prev) => ({ ...prev, status: parseInt(e.target.value) }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      disabled={!isEditMode}
                    >
                      <option value={1}>Active (Visible to users)</option>
                      <option value={0}>Hidden</option>
                    </select>
                  </div>

                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <label className="block text-sm font-medium text-gray-700">Answer Choices</label>
                      {isEditMode && (
                        <button onClick={() => setEditQuestion((prev) => ({ ...prev, choices: [...prev.choices, { name: "" }] }))} className="text-sm text-blue-600 hover:text-blue-700">
                          + Add Choice
                        </button>
                      )}
                    </div>

                    <div className="space-y-2">
                      {editQuestion.choices.map((choice, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={choice.name}
                            onChange={(e) =>
                              setEditQuestion((prev) => ({
                                ...prev,
                                choices: prev.choices.map((c, i) => (i === index ? { name: e.target.value } : c)),
                              }))
                            }
                            placeholder="Choice name..."
                            className="flex-1 px-3 py-2 border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                            readOnly={!isEditMode}
                          />
                          {isEditMode && (
                            <button
                              onClick={() =>
                                setEditQuestion((prev) => ({
                                  ...prev,
                                  choices: prev.choices.filter((_, i) => i !== index),
                                }))
                              }
                              disabled={editQuestion.choices.length === 1}
                              className="p-2 text-red-500 hover:bg-red-50 rounded disabled:text-gray-400"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {isEditMode && (
                    <div className="flex space-x-2">
                      <Button onClick={updateQuestion} disabled={!editQuestion.question.trim() || editQuestion.choices.every((c) => !c.name?.trim())} variant="success" classNames="flex-1">
                        Save Changes
                      </Button>
                      <Button
                        onClick={() => {
                          setIsEditMode(false);
                          const question = questions.find((q) => q.id === editingQuestion);
                          if (question) {
                            setEditQuestion({
                              question: question.question,
                              status: question.status,
                              choices: question.choices.map((c) => ({ name: c.name })),
                            });
                          }
                        }}
                        variant="customWhite"
                        classNames="flex-1"
                      >
                        Cancel
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold text-gray-800 mb-6">
                  <Plus className="w-5 h-5 inline mr-2" />
                  Add New Question
                </h2>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Question Text</label>
                    <input
                      type="text"
                      value={newQuestion.question}
                      onChange={(e) =>
                        setNewQuestion((prev) => ({
                          ...prev,
                          question: e.target.value,
                        }))
                      }
                      placeholder="Enter your question..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select
                      value={newQuestion.status}
                      onChange={(e) =>
                        setNewQuestion((prev) => ({
                          ...prev,
                          status: parseInt(e.target.value),
                        }))
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value={1}>Active (Visible to users)</option>
                      <option value={0}>Hidden</option>
                    </select>
                  </div>

                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <label className="block text-sm font-medium text-gray-700">Answer Choices</label>
                      <button onClick={addChoice} className="text-sm text-blue-600 hover:text-blue-700">
                        + Add Choice
                      </button>
                    </div>

                    <div className="space-y-2">
                      {newQuestion.choices.map((choice, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={choice.name}
                            onChange={(e) => updateChoice(index, e.target.value)}
                            placeholder="Choice name..."
                            className="flex-1 px-3 py-2 border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                          />
                          <button onClick={() => deleteChoice(index)} disabled={newQuestion.choices.length === 1} className="p-2 text-red-500 hover:bg-red-50 rounded disabled:text-gray-400">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Button onClick={saveQuestion} disabled={!newQuestion.question.trim() || newQuestion.choices.every((c) => !c.name?.trim())} variant="success" classNames="w-full">
                    Save Question
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      <SurveyCharts isOpen={viewSurveyChartsModal} onClose={() => setViewSurveyChartsModal(false)} />
    </div>
  );
};
