import Modal from "@components/common/Modal";
import { useEffect, useState } from "react";
import httpClient from "@clients/httpClient";
import { toast } from "react-toastify";
import { BarChart3 } from "lucide-react";
import ColumnChart from "@modules/dashboard/IncomingOutgoingCashierDashboard.tsx/charts/columnChart";
import { IGetFeedbackResponse, IQuestion, ITransactionType, IUserFeedbackResponse } from "@interface/users-feedback.interface";

interface ChartData {
  questionId: number;
  questionText: string;
  categories: string[];
  series: Array<{
    name: string;
    data: number[];
  }>;
  totalResponses: number;
}

interface FeedbackDetail {
  name: string;
  remark: string;
  userTransactionTypeId: number;
  transactionTypeName: string;
}

interface SurveyChartsProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ApiResponse<T> {
  data: T;
}

export default function SurveyCharts({ isOpen, onClose }: SurveyChartsProps) {
  const [loading, setLoading] = useState<boolean>(true);
  const [chartsData, setChartsData] = useState<ChartData[]>([]);
  const [feedbackDetails, setFeedbackDetails] = useState<FeedbackDetail[]>([]);

  useEffect(() => {
    if (isOpen) {
      void loadChartData();
    }
  }, [isOpen]);

  const loadChartData = async (): Promise<void> => {
    setLoading(true);
    try {
      const [questionsRes, responsesRes, userFeedbackRes, transactionTypeRes] = await Promise.all([
        httpClient.get<ApiResponse<IQuestion[]>>("/feedback/question/?relations=choices|createdBy"),
        httpClient.get<ApiResponse<IGetFeedbackResponse[]>>("/feedback/response/"),
        httpClient.get<ApiResponse<IUserFeedbackResponse[]>>("/feedback/user/"),
        httpClient.get<ApiResponse<ITransactionType[]>>("/feedback/transaction-type"),
      ]);

      // @ts-ignore
      const questions: IQuestion[] = questionsRes.data ?? [];
      // @ts-ignore
      const responses: IGetFeedbackResponse[] = responsesRes.data ?? [];
      // @ts-ignore
      const userFeedbacks: UserFeedback[] = userFeedbackRes.data ?? [];
      // @ts-ignore
      const transactionTypes: TransactionType[] = transactionTypeRes.data ?? [];

      // Collect unique feedback details (name, remark, userTransactionTypeId, transactionTypeName) by userFeedbackId
      const feedbackMap = new Map<number, FeedbackDetail>();
      responses.forEach((response) => {
        const feedback = userFeedbacks.find((f) => f.id === response.userFeedbackId);
        if (feedback?.remark && !feedbackMap.has(feedback.id)) {
          const transactionType = transactionTypes.find((t) => t.id === feedback.userTransactionTypeId);
          feedbackMap.set(feedback.id, {
            name: feedback.name ?? "---",
            remark: feedback.remark,
            userTransactionTypeId: feedback.userTransactionTypeId ?? 0,
            transactionTypeName: transactionType?.name ?? "---",
          });
        }
      });

      const feedbackDetails: FeedbackDetail[] = Array.from(feedbackMap.values());

      const processedData: ChartData[] = questions
        .filter((q) => q.status === 1)
        .map((question) => {
          const questionResponses = responses.filter((r) => r.questionId === question.id);
          const totalResponses = questionResponses.length;

          const choiceCounts = question.choices.map((choice) => {
            const count = questionResponses.filter((r) => r.choiceId === choice.id).length;
            return {
              choiceName: choice.name ?? "",
              count,
              percentage: totalResponses > 0 ? Math.round((count / totalResponses) * 100) : 0,
            };
          });

          return {
            questionId: question.id,
            questionText: question.question ?? "",
            categories: choiceCounts.map((c) => c.choiceName),
            series: [
              {
                name: "Responses",
                data: choiceCounts.map((c) => c.percentage),
              },
            ],
            totalResponses,
          };
        });

      setChartsData(processedData);
      setFeedbackDetails(feedbackDetails);
    } catch (error: any) {
      toast.error("Error loading chart data:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Survey Response Charts">
      <div className="space-y-6">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <BarChart3 className="w-12 h-12 mx-auto text-gray-400 mb-4 animate-spin" />
              <p className="text-gray-600">Loading chart data...</p>
            </div>
          </div>
        ) : chartsData.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-600">No survey responses found</p>
          </div>
        ) : (
          <>
            {chartsData.map((chartData) => (
              <div key={chartData.questionId} className="bg-gray-50 p-4 rounded-lg">
                <div className="mb-2">
                  <h3 className="text-sm font-semibold text-gray-700 mb-1">{chartData.questionText}</h3>
                  <p className="text-xs text-gray-500">Total Responses: {chartData.totalResponses}</p>
                </div>
                <ColumnChart
                  categories={chartData.categories}
                  series={chartData.series}
                  colors={["#042882", "#4385F5", "#28A845", "#E3C000", "#FF3548"]}
                  title=""
                  percentage={false}
                  slantText={chartData.categories.length > 5}
                />
              </div>
            ))}
            {feedbackDetails.length > 0 && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="text-lg font-semibold text-gray-700 mb-2">Additional Comments:</h4>
                <ul className="text-sm text-gray-600 list-disc pl-5">
                  {feedbackDetails.map((feedback, index) => (
                    <li key={index}>
                      {feedback.name} ({feedback.transactionTypeName}): {feedback.remark}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </>
        )}
      </div>
    </Modal>
  );
}
