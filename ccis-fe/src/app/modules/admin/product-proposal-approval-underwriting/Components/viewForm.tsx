import { FC, useState, Fragment, useRef, useEffect } from "react";
import Button from "@components/common/Button";
import { BiExport } from "react-icons/bi";
import Typography from "@components/common/Typography";
import { Form, FormikProvider, useFormik } from "formik";
import dayjs from "dayjs";
import { IGuidelineContent, IGuidelineContentTable } from "@interface/guidelines.interface";
import { showSuccess } from "@helpers/prompt";
import { AxiosResponse } from "axios";
import Modal from "@components/common/Modal";
import { HiOutlineUserGroup } from "react-icons/hi2";
import Signatories from "./signatories";
import Select from "@components/form/Select";
import { postUnderwritingProductProposalProvisionService, getUnderwritingProductProposalProvisionByIdService } from "@services/product-proposal/product-proposal.service";
import { useParams } from "react-router-dom";
import { IProductProposalApprovalUnderwriting } from "@interface/product-proposal.interface";
import Loader from "@components/Loader";
import { FaChevronLeft } from "react-icons/fa";
import { useUserManagementActions } from "@state/reducer/users-management";
// import { RootState } from "@state/reducer";
// import { useSelector } from "react-redux";
import { usePositionsManagementActions } from "@state/reducer/utilities-positions";

const ProductProposal: FC = () => {
  const { id } = useParams<{ id: string }>();
  const [data, setData] = useState<IProductProposalApprovalUnderwriting | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);
  const [selectedStatus, setSelectedStatus] = useState<string>("");
  const [remarks, setRemarks] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(true);
  const handleStatusChange = (value: string) => {
    setSelectedStatus(value);
    if (value !== "Disapprove") {
      setRemarks(""); // Clear remarks if status changes from Disapprove
    }
  };
  const [cooperativeCreatedBy] = useState("");
  const { getUsers } = useUserManagementActions();
  // const { users } = useSelector((state: RootState) => state.usersManagement);
  const [filterPosition] = useState("");
  const { getPosition } = usePositionsManagementActions();
  const customize =
    data?.proposable?.commission?.commissionDetails?.filter((row: any) => {
      return row.commissionAgeType?.name?.toLocaleLowerCase() !== "customize";
    }) ?? [];

  const sectionRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [titleCurrIndex, setTitleCurrIndex] = useState<number>(0);
  const [lastIndex, setLastIndex] = useState<number>(0);
  const [commissionView, setCommissionStructureView] = useState<boolean>(false);

  const formik = useFormik({
    initialValues: {
      productProposalId: 0,
      status: "",
      remarks: "",
    },

    onSubmit: async (values) => {
      try {
        const status: AxiosResponse = await postUnderwritingProductProposalProvisionService({
          ...values,
          status: selectedStatus === "APPROVED" ? "APPROVED" : "DISAPPROVED",
        });

        if (status) {
          const successMessage = selectedStatus === "APPROVED" ? "Product Proposal Approval has been approved!" : "Product Proposal Approval has been disapproved.";

          showSuccess("Success", successMessage).then((result) => {
            if (result.isConfirmed) {
              window.location.reload();
            }
          });
        }
      } catch (error) {
        console.error(error);
      }
    },
  });

  const handleScrollToSection = (index: number) => {
    sectionRefs.current[index]?.scrollIntoView({ behavior: "smooth" });
    setLastIndex(data?.proposable?.productGuidelines?.length ?? 0);
    setTitleCurrIndex(index);
    setCommissionStructureView(false);
  };

  const handleSetCommissionStructureView = () => {
    handleScrollToSection(lastIndex - 1);
    setTitleCurrIndex(data?.proposable?.productGuidelines ? data.proposable.productGuidelines.length + 1 : 0);
    setCommissionStructureView(true);
  };

  const fetchProposals = async () => {
    try {
      setLoading(true);
      if (id) {
        const { data } = await getUnderwritingProductProposalProvisionByIdService(Number(id));

        // Check if data is an array and has at least one item
        if (Array.isArray(data) && data.length > 0) {
          setData(data[0]);
        } else if (data) {
          setData(data);
        }
      }
    } catch (error) {
      console.error("Failed to load proposal data.", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      fetchProposals();
      formik.setFieldValue("productProposalId", Number(id));
    }
    getUsers({ filter: cooperativeCreatedBy });
    getPosition({ params: { filter: filterPosition } });
  }, [id]); // Ensure id is available before making API call

  return loading ? (
    <Loader />
  ) : (
    <div>
      <Button classNames="btn btn-sm" variant="primary" onClick={() => window.history.back()}>
        <FaChevronLeft /> Back
      </Button>
      <div className="p-4">
        {" "}
        <div className="w-full flex flex-col gap-4 mb-4">
          <div className="grid grid-cols-2 gap-4 justify-center">
            {" "}
            <div className="flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Product Name</div>
              <div className="w-2/3 text-black"> {data?.product?.name ?? ""}</div>
            </div>
            <div className="flex items-center justify-center">
              <div className="w-1/3 text-zinc-400"> Created by</div>
              <div className="w-2/3 text-black">
                {data?.createdBy?.firstname} {data?.createdBy?.middlename?.charAt(0)}. {data?.createdBy?.lastname}
              </div>
            </div>
            <div className="flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Cooperative</div>
              <div className="w-2/3 text-black">{data?.cooperative?.coopName ?? ""}</div>
            </div>
            <div className="flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Creation Date</div>
              <div className="w-2/3 text-black">{data?.createdAt ? dayjs(data.createdAt).format("MMMM DD, YYYY") : ""}</div>
            </div>
            <div className="w-full flex items-center justify-end">
              <div className="w-1/3 text-zinc-400">Status</div>
              <div className="w-2/3 text-black flex gap-4">
                <span
                  className={`rounded p-2 text-xs ${
                    data?.underwritingStatus === "APPROVED"
                      ? "bg-green-100 text-green-600 text-xs"
                      : data?.underwritingStatus === "DISAPPROVED"
                        ? "bg-red-100 text-red-600 text-xs"
                        : "bg-amber-100 text-amber-600 text-xs"
                  }`}
                >
                  {data?.underwritingStatus
                    ?.replace(/_/g, " ")
                    .toLowerCase()
                    .replace(/\b\w/g, (char) => char.toUpperCase()) ?? ""}
                </span>
                {data?.underwritingStatus !== "APPROVED" && data?.underwritingStatus !== "DISAPPROVED" && (
                  <Button classNames="bg-primary btn btn-sm text-xs" onClick={openModal}>
                    Change Status
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      <Modal isOpen={isModalOpen} onClose={closeModal} title="Change Product Status" titleClass="text-lg font-bold" modalContainerClassName="max-w-lg">
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <p>Select Status</p>
            <Select
              options={[
                { value: "APPROVED", text: "Approve" },
                { value: "DISAPPROVED", text: "Disapprove" },
              ]}
              value={selectedStatus}
              onChange={(e) => handleStatusChange(e.target.value)}
              required
            />

            {selectedStatus === "DISAPPROVED" && (
              <div className="mt-4">
                <Typography className="text-sm font-medium text-gray-700">Remarks</Typography>

                <textarea
                  className="rounded-md px-2 border border-zinc-400 w-full"
                  value={remarks}
                  name="remarks"
                  required
                  onChange={(e) => {
                    setRemarks(e.target.value);
                    formik.setFieldValue("remarks", e.target.value);
                  }}
                />
              </div>
            )}
            <div className="flex justify-center p-4">
              <Button type="submit" classNames="btn btn-primary bg-gradient-to-r from-indigo-800 to-blue-900">
                Update Status
              </Button>
            </div>
          </Form>
        </FormikProvider>
      </Modal>
      <div className="flex items-center justify-end mb-2">
        <div className="flex gap-2">
          {" "}
          <Button type="button" variant="primary" outline classNames=" text-xs flex items-center justify-center  gap-2">
            <BiExport size={18} className="flex items-center justify-center" />
            Export
          </Button>
        </div>
      </div>

      <div className="min-h-[50rem] w-full flex  justify-center gap-4   ">
        {/* COL1 */}
        <div className="w-[30rem] px-4 h-[50rem] border border-zinc-200 hidden  xl:flex flex-col text-start text-sm relative">
          <div className="w-full text-xl font-poppins-semibold pt-4 mb-4 ">Table of Contents</div>
          <div className="overflow-y-auto max-h-[50rem]">
            {data?.proposable?.productGuidelines?.map((item: any, gIndex?: any) => (
              <div
                className={`w-full font-poppins-semibold hover:bg-zinc-100 p-4 rounded-md cursor-pointer 
                ${titleCurrIndex === gIndex ? "text-primary bg-sky-50" : "text-zinc-500"}`}
                onClick={() => handleScrollToSection(gIndex)}
              >
                {item.label}
              </div>
            ))}

            {data?.proposable && (
              <div
                onClick={handleSetCommissionStructureView}
                className={`w-full font-poppins-semibold hover:bg-zinc-100 p-4 rounded-md cursor-pointer ${commissionView ? "text-primary bg-sky-50" : "text-zinc-500"} `}
              >
                Commission Structure
              </div>
            )}
          </div>
        </div>
        {/* COL2 */}
        <div className="w-full min-h-[50rem] h-96 border border-zinc-200 p-4 pt-6 text-justify overflow-y-auto scroll-mt-96">
          <div className="p-12">
            <p>
              {" "}
              <span>{dayjs(data?.createdAt).format("MMMM DD, YYYY") ?? ""}</span>{" "}
            </p>
            <br />
            <p> Dear Cooperators,</p>
            <br /> <br />
            <p> Greetings from CLIMBS!</p>
            <br />
            <strong className="font-poppins-semibold">{data?.cooperative?.coopName}</strong> has come a long way from where it started in 1971. From our humble beginnings, we have firmly established a
            strong financial position which make us a P3.68 billion pesos with a net worth of P 1.97 billion insurance cooperative today.
            <br /> <br /> <br />
            As a group of companies with insurance as our core business, we grew into one of the country's leading players in the insurance industry and the top Cooperative Insurer being licensed both
            by the Cooperative Development Authority and insurance Commission. It is owned by more than 4,000 primary cooperatives in the Philippines.
            <br /> <br /> <br />
            with an array of insurance products both Life and Non-life, we propose to offer you the <strong className="font-poppins-semibold">{data?.product?.name}</strong> insurance plan for all
            Cooperative members' protection with an affordable premium. Please find in the following pages the salient features and schedule of benefits of this insurance plan.
            <br /> <br /> For further inquiries or clarification regarding this proposal, please do not hesitate to get in touch with me thru my {""}
            {data?.createdBy?.contactNumber} or email at {data?.createdBy?.email ?? ""} We would be happy to do a product presentation on one of your board/management meetings. Thank you very much.
            <br /> <br /> <br />
            <p> </p>
            <strong className="font-poppins-semibold">
              {data?.createdBy?.firstname} {data?.createdBy?.middlename?.charAt(0)}. {data?.createdBy?.lastname}
            </strong>
            <p>
              {data?.createdBy?.position?.positionName ?? "Position not available"} <br />
            </p>
          </div>
          <hr className="my-8 border-t border-zinc-300" />
          <div className=" w-full min-h-screen  ">
            {" "}
            <div className="w-full font-poppins-semibold xl:text-xl text-base text-center ">{data?.product?.name}</div>
            <div className="flex flex-col justify-between mt-2 mb-16">
              <div className="flex mt-2">
                <Typography className="text-justify">{data?.product?.description}</Typography>
              </div>
            </div>
            {/* PRODUCT GUIDELINES CONTENT START */}
            <div className="xl:p-8 p-4">
              <div className="flex flex-col">
                {/* FIRST PRODUCT GUIDELINE*/}
                <div className="flex flex-col xl:text-base text-xs ">
                  {data?.proposable?.productGuidelines?.map((value: any, gIndex: any) => {
                    return (
                      <div key={`guideline-${gIndex}`} className="scroll-mt-16 flex flex-1 flex-col mb-2 xl:mb-10" ref={(el) => (sectionRefs.current[gIndex] = el)}>
                        <Typography className="xl:text-xl text-sm font-poppins-semibold text-primary">{value.label}</Typography>
                        <p>{data?.proposable?.description ?? ""}</p>
                        {value.productGuideline.map((pgValue: any, pgIndex: any) => {
                          let listValue;
                          let tableValue;
                          if (pgValue.type === "list") {
                            listValue = pgValue.value as IGuidelineContent[];
                          }

                          if (pgValue.type === "table") {
                            tableValue = pgValue.value as IGuidelineContentTable;
                          }

                          return (
                            <div key={`pg-${pgIndex}`} className="p-2">
                              {pgValue.type === "textfield" && (
                                <Fragment>
                                  <Typography className="ml-4 xl:mt-4  mt-0 text-justify xl:text-base text-xs">{pgValue.value as string}</Typography>
                                </Fragment>
                              )}
                              {pgValue.type === "list" && (
                                <Fragment>
                                  <Typography className="ml-4 xl:mt-4  mt-0 xl:text-justify text-start xl:text-base text-xs">{pgValue.label}</Typography>
                                  <ul className="list-disc xl:ml-12 ml-6">
                                    {listValue &&
                                      listValue.map((listValue, listIndex) => {
                                        return (
                                          <li key={`listItem-${listIndex}`} className="xl:mt-4 mt-0">
                                            <Typography className="text-justify xl:text-base text-xs">{listValue.value as string}</Typography>
                                          </li>
                                        );
                                      })}
                                  </ul>
                                </Fragment>
                              )}
                              {pgValue.type === "texteditor" && (
                                <Fragment>
                                  <div
                                    className="ml-4 mt-2 xl:text-base text-xs "
                                    dangerouslySetInnerHTML={{
                                      __html: (pgValue as any).value ?? "",
                                    }}
                                  ></div>
                                </Fragment>
                              )}
                              {pgValue.type === "table" && (
                                <Fragment>
                                  <div className="flex flex-1 mt-2 mx-6 overflow-x-scroll xl:text-base text-xs">
                                    <table className="table border-[1px]">
                                      <thead className="table-header-group">
                                        <tr>
                                          {tableValue?.columns?.map((cValue, cIndex) => {
                                            return (
                                              <td key={`col-${cIndex}`} className="table-cell border-[1px] xl:text-base text-xs">
                                                <Typography className="font-semibold text-xs">{cValue.value as string}</Typography>
                                              </td>
                                            );
                                          })}
                                        </tr>
                                      </thead>
                                      <tbody>
                                        {tableValue?.rows?.map((rValue, rIndex) => {
                                          return (
                                            <tr key={`row-${rIndex}`}>
                                              {rValue.map((cell, cellIndex) => {
                                                return (
                                                  <td className="border-[1px] xl:text-base text-xs" key={`cell-${cellIndex}`}>
                                                    <Typography size="xs">{cell.value as string}</Typography>
                                                  </td>
                                                );
                                              })}
                                            </tr>
                                          );
                                        })}
                                      </tbody>
                                    </table>
                                  </div>
                                </Fragment>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    );
                  })}
                </div>
                {/* SECOND PRODUCT GUIDELINE*/}
                <div className="flex flex-col xl:text-base text-xs ">
                  {data?.proposable.productGuidelines?.map((value: any, gIndex: any) => {
                    return (
                      <div key={`guideline-${gIndex}`} className="scroll-mt-16 flex flex-1 flex-col mb-2 xl:mb-10" ref={(el) => (sectionRefs.current[gIndex] = el)}>
                        <Typography className="xl:text-xl text-sm font-poppins-semibold text-primary">{value.label}</Typography>
                        {value.productGuideline.map((pgValue: any, pgIndex: any) => {
                          let listValue;
                          let tableValue;
                          if (pgValue.type === "list") {
                            listValue = pgValue.value as IGuidelineContent[];
                          }

                          if (pgValue.type === "table") {
                            tableValue = pgValue.value as IGuidelineContentTable;
                          }

                          return (
                            <div key={`pg-${pgIndex}`} className="p-2">
                              {pgValue.type === "textfield" && (
                                <Fragment>
                                  <Typography className="ml-4 xl:mt-4  mt-0 text-justify xl:text-base text-xs">{pgValue.value as string}</Typography>
                                </Fragment>
                              )}
                              {pgValue.type === "list" && (
                                <Fragment>
                                  <Typography className="ml-4 xl:mt-4  mt-0 xl:text-justify text-start xl:text-base text-xs">{pgValue.label}</Typography>
                                  <ul className="list-disc xl:ml-12 ml-6">
                                    {listValue &&
                                      listValue.map((listValue, listIndex) => {
                                        return (
                                          <li key={`listItem-${listIndex}`} className="xl:mt-4 mt-0">
                                            <Typography className="text-justify xl:text-base text-xs">{listValue.value as string}</Typography>
                                          </li>
                                        );
                                      })}
                                  </ul>
                                </Fragment>
                              )}
                              {pgValue.type === "texteditor" && (
                                <Fragment>
                                  <div
                                    className="ml-4 mt-2 xl:text-base text-xs "
                                    dangerouslySetInnerHTML={{
                                      __html: (pgValue as any).value ?? "",
                                    }}
                                  ></div>
                                </Fragment>
                              )}
                              {pgValue.type === "table" && (
                                <Fragment>
                                  <div className="flex flex-1 mt-2 mx-6 overflow-x-scroll xl:text-base text-xs">
                                    <table className="table border-[1px]">
                                      <thead className="table-header-group">
                                        <tr>
                                          {tableValue?.columns?.map((cValue, cIndex) => {
                                            return (
                                              <td key={`col-${cIndex}`} className="table-cell border-[1px] xl:text-base text-xs">
                                                <Typography className="font-semibold text-xs">{cValue.value as string}</Typography>
                                              </td>
                                            );
                                          })}
                                        </tr>
                                      </thead>
                                      <tbody>
                                        {tableValue?.rows?.map((rValue, rIndex) => {
                                          return (
                                            <tr key={`row-${rIndex}`}>
                                              {rValue.map((cell, cellIndex) => {
                                                return (
                                                  <td className="border-[1px] xl:text-base text-xs" key={`cell-${cellIndex}`}>
                                                    <Typography size="xs">{cell.value as string}</Typography>
                                                  </td>
                                                );
                                              })}
                                            </tr>
                                          );
                                        })}
                                      </tbody>
                                    </table>
                                  </div>
                                </Fragment>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    );
                  })}
                </div>
                {/* LAST PRODUCT GUIDELINE*/}

                {data?.proposable?.commission && (
                  <Fragment>
                    <Typography size="md" className="font-poppins-semibold text-primary">
                      Commission Structure
                    </Typography>
                    <Fragment>
                      <Typography className="ml-4 mt-4 xl:text-base text-xs">
                        {parseFloat(data?.proposable?.commission.maximumDisposableRate ?? "").toFixed(2)}% Maximum Disposable Commission - Standard Rate
                      </Typography>

                      <div className="flex-flex-1 mt-6 mx-6 overflow-x-scroll">
                        <table className="table overflow-scroll">
                          <thead>
                            <tr>
                              <td className="table-cell border-1 text-center text-xs">Type</td>
                              <td className="table-cell border-1 text-center text-xs">Age Type</td>
                              {customize.length > 0 && (
                                <Fragment>
                                  <td className="table-cell border-1 text-center text-xs">Age From</td>
                                  <td className="table-cell border-1 text-center text-xs">Age To</td>
                                </Fragment>
                              )}
                              <td className="table-cell border-1 text-center text-xs">Rate</td>
                            </tr>
                          </thead>
                          <tbody>
                            {data?.productProposal?.productRevision?.commission?.commissionDetails?.map((rowValue: any, rowIndex: any) => {
                              return (
                                <tr key={`commissionDetailsRow-${rowIndex}`}>
                                  <td className="table-cell border-1 text-xs text-center  font-poppins-semibold">{rowValue?.commissionType?.commissionName}</td>
                                  <td className="table-cell border-1 text-xs text-center">{rowValue?.commissionAgeType?.name}</td>
                                  {customize.length > 0 && (
                                    <Fragment>
                                      <td className="table-cell border-1 text-center text-xs">{rowValue.ageFrom}</td>
                                      <td className="table-cell border-1 text-center text-xs">{rowValue.ageTo}</td>
                                    </Fragment>
                                  )}
                                  <td className="table-cell border-1 text-center text-xs">{rowValue.rate ? parseFloat(rowValue.rate.toString()).toFixed(0) : ""}%</td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    </Fragment>
                  </Fragment>
                )}
              </div>
            </div>
            {/* PRODUCT GUIDELINES CONTENT END */}
          </div>
        </div>
        {/* COL3 */}
        <div className="w-1/3 flex flex-col border border-zinc-200 p-4">
          <div className="flex justify-center border-b border-zinc-200 pb-2 mb-4">
            <HiOutlineUserGroup /> <Typography> SIGNEES</Typography>
          </div>

          <Signatories signatories={data?.provisionApproval} />
        </div>
      </div>
    </div>
  );
};

export default ProductProposal;
