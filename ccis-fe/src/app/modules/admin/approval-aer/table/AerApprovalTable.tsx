import ActionDropdown from "@components/common/ActionDropdown";
import Table from "@components/common/Table";
import Typography from "@components/common/Typography";
import { formatWordDateDDMMYYY } from "@helpers/date";
import { capitalizeFirstLetterWords, formatStringAtoZ0to9, getTextStatusColor } from "@helpers/text";
import { IActions, ISelectOptions } from "@interface/common.interface";
import { IQuotationTable } from "@interface/quotation.interface";
import { RootState } from "@state/reducer";
import { useQuotationActions } from "@state/reducer/quotations";
import { ChangeEvent, useEffect, useState } from "react";
import { TableColumn } from "react-data-table-component";
import { GoVersions } from "react-icons/go";
import { useSelector } from "react-redux";
import { useDebounce, useDebouncedCallback } from "use-debounce";
import { useNavigate } from "react-router-dom";
import { ROUTES } from "@constants/routes";
import { extractFirstPathSegment } from "@helpers/navigatorHelper";
import { useUserId } from "@helpers/data";
import { toast } from "react-toastify";
import Filter from "@components/common/Filter";
import TextField from "@components/form/TextField";
import Select from "@components/form/Select";
import { Statuses } from "@constants/global-constant-value";

const AerApprovalTable: React.FC = () => {
  const navigate = useNavigate();
  const { getQuotations } = useQuotationActions();
  const userId = useUserId();
  const aerData = useSelector((state: RootState) => state.quotation.getQuotations?.data);
  const loading = useSelector((state: RootState) => state.quotation.getQuotations?.loading);
  const [pathBase, setPathBase] = useState<string>("");
  const [search, setSearch] = useState<string>("");
  const [debouncedSearch] = useDebounce(search, 1000);
  const [dateFrom, setDateFrom] = useState<string>("");
  const [dateTo, setDateTo] = useState<string>("");
  const [statusFilter, setStatusFilter] = useState<string | undefined>("");
  const [typeFilter, _setTypeFilter] = useState<number | undefined>(undefined);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [resetCounter, setResetCounter] = useState(0);
  const addCondtion = "&approval.signatories.userId[eq]";
  useEffect(() => {
    getQuotations({
      params: {
        page: page,
        pageSize: pageSize,
        dateFrom: dateFrom,
        dateTo: dateTo,
        statusFilter: statusFilter,
        productTypeFilter: typeFilter,
        filter: debouncedSearch,
        condition: addCondtion + "=" + userId,
      },
    });
  }, [page, pageSize, debouncedSearch, dateFrom, dateTo, statusFilter, typeFilter, addCondtion]);
  const handleSearch = useDebouncedCallback((event: ChangeEvent<HTMLInputElement>) => {
    // Extract the current input value from the event
    const value = event.target.value;
    setSearch(value);
  }, 500);
  const handleClearAll = () => {
    setSearch("");
    setStatusFilter("");
    setDateFrom("");
    setDateTo("");
    setResetCounter((prev) => prev + 1);
  };
  const statusOptions: ISelectOptions[] = [
    { text: Statuses.FORAPPROVAL, value: Statuses.FOR_APPROVAL },
    { text: Statuses.REJECTED, value: Statuses.REJECTED },
  ];
  const handleStatusChange = (event: ChangeEvent<HTMLSelectElement>) => {
    const selectedText = event.target.options[event.target.selectedIndex].value;
    setStatusFilter(selectedText);
  };
  const handleDateFromChange = (event: ChangeEvent<HTMLInputElement>) => {
    setDateFrom(event.target.value);
  };
  const handleDateToChange = (event: ChangeEvent<HTMLInputElement>) => {
    setDateTo(event.target.value);
  };
  const handlePaginate = (pagination: number) => {
    setPage(pagination);
  };
  const handleRowsChange = (newPerPage: number, pagination: number) => {
    setPageSize(newPerPage);
    setPage(pagination);
  };
  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  useEffect(() => {
    const firstSegment = extractFirstPathSegment(window.location.pathname, 0);
    if (firstSegment) {
      setPathBase(formatStringAtoZ0to9(firstSegment).toUpperCase());
    }
  }, []);
  const toastWarning = () => {
    toast.warning("Quotation ID is undefined");
  };

  const handleRowClick = (data: IQuotationTable) => {
    switch ((data as any).product?.productCode?.toLowerCase()) {
      case "gyrt":
        if (data?.quotationId) {
          navigate((ROUTES[pathBase as keyof typeof ROUTES] as any)?.quotationGyrtQuotationView.parse(data.quotationId), {
            state: { isShowSignatureStatusLocal: true, showActions: false },
          });
        } else {
          toastWarning();
        }

        break;
      case "clsp":
        if (data?.quotationId) {
          navigate((ROUTES[pathBase as keyof typeof ROUTES] as any)?.quotationClspQuotationView.parse(data.quotationId), {
            state: { isShowSignatureStatusLocal: true, showActions: false },
          });
        } else {
          toastWarning();
        }

        break;
      case "clpp":
        if (data?.quotationId) {
          navigate((ROUTES[pathBase as keyof typeof ROUTES] as any)?.quotationClppQuotationView.parse(data.quotationId), {
            state: { isShowSignatureStatusLocal: true, showActions: false },
          });
        } else {
          toastWarning();
        }
        break;
      case "fip":
        if (data?.quotationId) {
          navigate((ROUTES[pathBase as keyof typeof ROUTES] as any)?.quotationFipAerView.parse(data.quotationId), {
            state: { isShowSignaturStatus: true, isShowStatus: false },
          });
        } else {
          toastWarning();
        }

        break;

      default:
        toast.error("Invalid product type");
    }
  };
  const actionEvents: IActions<IQuotationTable>[] = [
    {
      name: "View",
      event: (data: IQuotationTable, _index: number) => {
        handleRowClick(data);
      },
      icon: GoVersions,
      color: "primary",
    },
  ];

  const columns: TableColumn<IQuotationTable>[] = [
    {
      name: "Product",
      selector: (row) => (row as any)?.product?.name,
      ...commonSetting,
    },
    {
      name: "Cooperative",
      cell: (row) => (row as any)?.quotation?.cooperative?.coopName,
    },
    {
      name: "Date Created",
      cell: (row) => {
        try {
          return formatWordDateDDMMYYY(new Date((row as any).createdAt).toISOString());
        } catch (error) {
          return formatWordDateDDMMYYY(new Date().toISOString());
        }
      },
    },
    {
      name: "Status",
      cell: (row) => <span className={`${getTextStatusColor(row.status)}`}>{capitalizeFirstLetterWords(row.status || "N/A", "_")}</span>,
    },
    {
      name: <Typography className="flex justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => (
        <div className="flex justify-center">
          <ActionDropdown actions={actionEvents} data={row} rowIndex={rowIndex} />
        </div>
      ),
    },
  ];

  return (
    <div className="p-4">
      <div>Actuary Evaluation Report</div>
      <div className="mt-8">
        <div className="flex flex-row justify-between">
          <Filter search={search} onChange={handleSearch}>
            <div className="flex justify-end">
              <button className="text-primary text-xs btn-sm" type="button" onClick={handleClearAll}>
                Clear All
              </button>
            </div>
            <div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <div className="text-xs">Date From</div>
                  <TextField className="" type="date" size="sm" value={dateFrom} onChange={handleDateFromChange} />
                </div>
                <div>
                  <div className="text-xs">Date To</div>
                  <TextField className="" type="date" size="sm" value={dateTo} onChange={handleDateToChange} />
                </div>
              </div>
              <div className="text-xs">Status</div>
              <Select key={`status-${resetCounter}`} size="sm" placeholder="Status" options={statusOptions} value={statusFilter} onChange={handleStatusChange} />
            </div>
          </Filter>
        </div>

        <Table
          className="h-[600px]"
          columns={columns}
          data={aerData?.data}
          loading={loading}
          multiSelect={false}
          paginationServer={true}
          paginationTotalRows={aerData?.meta?.total}
          onPaginate={handlePaginate}
          onChangeRowsPerPage={handleRowsChange}
          hideButton="invisible"
        />
      </div>
    </div>
  );
};
export default AerApprovalTable;
