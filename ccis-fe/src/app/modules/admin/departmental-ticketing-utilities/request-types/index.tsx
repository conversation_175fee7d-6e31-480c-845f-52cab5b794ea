import { FC, Fragment, useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { TableColumn } from "react-data-table-component";
import { RootState } from "@state/store";
import { CiEdit, CiTrash } from "react-icons/ci";
import { IActions } from "@interface/common.interface";
import Table from "@components/common/Table";
import Modal from "@components/common/Modal";
import ActionButtons from "@components/common/ActionButtons";
import { confirmDelete, confirmSaveOrEdit } from "@helpers/prompt";
import { useRequestTypeActions } from "@state/reducer/departmental-ticketing-request-type";
import { IRequestTypeInterface } from "@interface/departmental-ticketing-interface";
import { Form, FormikProvider, useFormik } from "formik";
import TextField from "@components/form/TextField";
import Button from "@components/common/Button";
import { useDepartmentsManagementActions } from "@state/reducer/utilities-departments";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import CustomTextField from "@components/common/CustomTextFieldWithSearch";
// import { CreateRequestTypeSchema, EditRequestTypeSchema } from "@services/form-inventory-utilities-form-types/form-inventory-utilities-form-types.schema";

const RequestTypes: FC = () => {
  const [create, setCreate] = useState<boolean>(false);
  const [edit, setEdit] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>("");
  const { getDepartment } = useDepartmentsManagementActions();
  const { getDivisions } = useDivisionActions();
  const divisions = useSelector((state: RootState) => state?.formInventoryUtilitiesDivisions?.divisions);
  const departments = useSelector((state: RootState) => state.utilitiesDepartments?.departments || []);
  const RequestTypes = useSelector((state: RootState) => state.departmentalTicketingRequestType?.getRequestTypes?.data || []);
  const postSuccess = useSelector((state: RootState) => state.departmentalTicketingRequestType?.postRequestType?.success);
  const putSuccess = useSelector((state: RootState) => state.departmentalTicketingRequestType?.putRequestType?.success);
  const deleteSuccess = useSelector((state: RootState) => state.departmentalTicketingRequestType?.destroyRequestType?.success);

  const loading = useSelector((state: RootState) => state?.departmentalTicketingRequestType?.getRequestTypes?.loading);
  const { setSelectedRequestType, getRequestTypes, postRequestType, putRequestType, destroyRequestType } = useRequestTypeActions();
  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const actionEvents: IActions<IRequestTypeInterface>[] = [
    {
      name: "Edit",
      event: (row: IRequestTypeInterface, index: number) => {
        // Set data to edit
        const data = {
          id: row.id ?? 0,
          name: row.name,
          departmentId: Number(row.departmentId), // Convert departmentId to number
          description: row.description,
        };

        setSelectedRequestType({ data: data, index: index });
        formikEdit.setValues({ ...data, departmentId: String(data.departmentId) });
        handleToggleEditModal();
      },
      icon: CiEdit,
      color: "primary",
    },
    {
      name: "Delete",
      event: (row: IRequestTypeInterface, index: number) => {
        const action = confirmDelete(row.name);
        action.then((value) => {
          if (value.isConfirmed) {
            destroyRequestType({ id: row.id ?? 0, index: index });
          }
        });
      },
      icon: CiTrash,
      color: "danger",
    },
  ];

  const columns: TableColumn<IRequestTypeInterface>[] = [
    {
      name: "Request Type",
      selector: (row) => row.name,
      ...commonSetting,
    },
    {
      name: "Department",
      selector: (row) => {
        const department = departments.find((dept) => dept.id === Number(row.departmentId));
        return department ? department.departmentName : "N/A";
      },
      ...commonSetting,
    },
    {
      name: "Description",
      cell: (row) => row.description,
    },
    {
      name: "Action",
      cell: (row, rowIndex) => <ActionButtons data={row} rowIndex={rowIndex} actions={actionEvents} />,
    },
  ];

  const handleToggleCreateModal = () => {
    setCreate((prev) => !prev);
    formik.resetForm();
  };

  const handleToggleEditModal = () => {
    setEdit((prev) => !prev);
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  useEffect(() => {
    getRequestTypes({ filter: searchText });
    getDepartment({ filter: "" });
    getDivisions({ filter: "" });
  }, [searchText]);

  const formik = useFormik({
    initialValues: {
      id: 0,
      name: "",
      departmentId: "",
      description: "",
    },
    // validationSchema: CreateRequestTypeSchema,
    onSubmit: async (values, { resetForm }) => {
      const isConfirmed = await confirmSaveOrEdit(`Do you want to save the formType: "${values.name} ?"`);
      if (isConfirmed) {
        postRequestType({ ...values, departmentId: Number(values.departmentId) });
        handleToggleCreateModal();
        resetForm();
        formik.setFieldValue("departmentId", ""); // Reset departmentId after submission
      }
    },
  });
  const formikEdit = useFormik({
    initialValues: {
      id: 0,
      name: "",
      departmentId: "",
      description: "",
    },
    // validationSchema: EditRequestTypeSchema,
    onSubmit: async (values) => {
      const isConfirmed = await confirmSaveOrEdit(`Do you want to edit the formType: "${values.departmentId} ?"`);
      if (isConfirmed) {
        putRequestType(values as any);
        handleToggleEditModal();
      }
    },
  });

  useEffect(() => {
    getRequestTypes({ filter: searchText });
  }, [postSuccess, putSuccess, deleteSuccess]);



  return (
    <Fragment>
      <div className="text-xl font-semibold uppercase my-4">Request Type</div>
      <Table
        className="h-[600px] "
        columns={columns}
        data={RequestTypes}
        createLabel="Add Request Type"
        onCreate={handleToggleCreateModal}
        loading={loading}
        onSearch={handleSearch}
        searchable
        multiSelect={false}
      />
      {create && (
        <Modal title="Add new Request Type" modalContainerClassName="max-w-3xl " titleClass="text-primary text-lg uppercase" isOpen={create} onClose={handleToggleCreateModal}>
          <>
            <FormikProvider value={formik}>
              <Form className="flex flex-col my-4 gap-4">
                <div>
                  <label>Request Type</label>
                  <TextField
                    name="name"
                    placeholder="Enter Request Type"
                    type="text"
                    className="bg-white"
                    error={formik.touched.name && !!formik.errors.name}
                    errorText={formik.errors.name}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.name}
                    required
                  />
                </div>
                <div>
                  {" "}
                  <label>Department</label>
                  <CustomTextField
                    suggestionOptions={departments}
                    getOptionLabel={(dept) => {
                      const division = divisions?.find((div) => div.id === dept.divisionId);
                      return division ? `${dept.departmentName} - ${division.divisionName}` : dept.departmentName;
                    }}
                    getOptionValue={(dept) => dept.id || ""}
                    name="departmentId"
                    placeholder="Search and select department"
                    className="bg-white"
                    error={formik.touched.departmentId && !!formik.errors.departmentId}
                    errorText={formik.errors.departmentId}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.departmentId}
                    required
                  />
                </div>
                <div>
                  <label>Description</label>
                  <TextField
                    name="description"
                    type="text"
                    placeholder="Enter Description"
                    className="bg-white"
                    error={formik.touched.description && !!formik.errors.description}
                    errorText={formik.errors.description}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.description}
                  />
                </div>

                <Button type="submit" variant="primary" classNames="btn rounded-xl">
                  Save
                </Button>
              </Form>
            </FormikProvider>
          </>
        </Modal>
      )}
      {edit && (
        <Modal title="Edit Product Request Type" modalContainerClassName="max-w-3xl" titleClass="text-primary text-lg uppercase" isOpen={edit} onClose={handleToggleEditModal}>
          <>
            <FormikProvider value={formikEdit}>
              <Form className="flex flex-col my-4 gap-4">
                <div>
                  <label>Request Type</label>
                  <TextField
                    name="name"
                    placeholder="Enter RequestType Code"
                    type="text"
                    className="bg-white"
                    error={formikEdit.touched.name && !!formikEdit.errors.name}
                    errorText={formikEdit.errors.name}
                    onChange={formikEdit.handleChange}
                    onBlur={formikEdit.handleBlur}
                    value={formikEdit.values.name}
                    required
                  />
                </div>
                <div>
                  {" "}
                  <label> Department</label>
                  <CustomTextField
                    suggestionOptions={departments}
                    getOptionLabel={(dept) => {
                      const division = divisions?.find((div) => div.id === dept.divisionId);
                      return division ? `${dept.departmentName} - ${division.divisionName}` : dept.departmentName;
                    }}
                    getOptionValue={(dept) => dept.id || ""}
                    name="departmentId"
                    placeholder="Search and select department"
                    className="bg-white"
                    error={formikEdit.touched.departmentId && !!formikEdit.errors.departmentId}
                    errorText={formikEdit.errors.departmentId}
                    onChange={formikEdit.handleChange}
                    onBlur={formikEdit.handleBlur}
                    value={formikEdit.values.departmentId}
                    required
                  />
                </div>
                <div>
                  <label>Description</label>
                  <TextField
                    name="description"
                    type="text"
                    placeholder="Enter Description"
                    className="bg-white"
                    error={formikEdit.touched.description && !!formikEdit.errors.description}
                    errorText={formikEdit.errors.description}
                    onChange={formikEdit.handleChange}
                    onBlur={formikEdit.handleBlur}
                    value={formikEdit.values.description}
                  />
                </div>

                <Button type="submit" variant="primary" classNames="btn rounded-xl">
                  Update
                </Button>
              </Form>
            </FormikProvider>
          </>
        </Modal>
      )}
    </Fragment>
  );
};

export default RequestTypes;
