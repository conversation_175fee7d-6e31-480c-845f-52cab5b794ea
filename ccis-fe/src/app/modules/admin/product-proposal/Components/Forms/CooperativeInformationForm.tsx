import LoadingButton from "@components/common/LoadingButton";
import Typography from "@components/common/Typography";
import TextField from "@components/form/TextField";
import { ICooperative, IAffiliation, ICooperativeOfficer } from "@interface/product-proposal.interface";
import { useProductProposalActions } from "@state/reducer/product-proposal";
import { RootState } from "@state/store";
import { Form, FormikProvider, useFormik } from "formik";
import { FC, Fragment, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { CreateCooperativeSchema } from "@services/product-proposal/product-proposal.schema";
import { useCooperativeCategorysManagementActions } from "@state/reducer/utilities-cooperative-category";
import { useCooperativeTypeManagementActions } from "@state/reducer/utilities-cooperative-types";
import { formatSelectOptions } from "@helpers/array";
import Select from "@components/form/Select";
import AffiliationsTable from "../Tables/AffiliationsTable";
import CoopOfficersTable from "../Tables/CoopOfficersTable";
import Modal from "@components/common/Modal";
import { FaPlus } from "react-icons/fa";
import AffiliationForm from "./AffiliationForm";
import CoopOfficerForm from "./CoopOfficerForm";
import { postCooperativeService, putCooperativeService } from "@services/product-proposal/product-proposal.service";
import { toast } from "react-toastify";
import { CooperativeDataSourceType } from "@enums/enums";
import { CoopStatus } from "@enums/coop-status";

type TProps = {
  handleSaveAsDraft: (id?: string) => void;
  isSubmitting: boolean;
};

const CooperativeInformationForm: FC<TProps> = ({ handleSaveAsDraft, isSubmitting = false }) => {
  const { getCooperatives, setCooperative, setStep } = useProductProposalActions();
  const { getCooperativeCategory } = useCooperativeCategorysManagementActions();
  const { getCooperativeType } = useCooperativeTypeManagementActions();

  const cooperativeCategory = useSelector((state: RootState) => state.utilitiesCooperativeCategory.cooperativeCategory);
  const cooperativeType = useSelector((state: RootState) => state.utilitiesCooperativeType.cooperativeType);
  const cooperative = useSelector((state: RootState) => state.productProposal.cooperative);

  const cooperativeCategoryOptions = formatSelectOptions(cooperativeCategory, "coopCategoryName");
  const cooperativeTypeOptions = formatSelectOptions(cooperativeType, "coopTypeName");

  const [affiliationModal, setAffiliationModal] = useState<boolean>(false);
  const toggleAffiliationModal = () => setAffiliationModal(!affiliationModal);
  const [coopOfficerModal, setCoopOfficerModal] = useState<boolean>(false);
  const toggleCoopOfficerModal = () => setCoopOfficerModal(!coopOfficerModal);

  const [selectedAffiliation, setSelectedAffiliation] = useState<(IAffiliation & { index: number }) | undefined>(undefined);
  const [selectedCoopOfficer, setSelectedCoopOfficer] = useState<(ICooperativeOfficer & { index: number }) | undefined>(undefined);

  const [isSubmit, setIsSubmit] = useState<boolean>(false);

  const handleCancel = () => {
    setCooperative(undefined as unknown as ICooperative);
    getCooperatives({ page: 1, pageSize: 10 });
  };

  const saveAsDraft = async () => {
    try {
      setIsSubmit(true);
      if (formik.values.source === CooperativeDataSourceType.CDA) {
        const { data } = await postCooperativeService(formik.values);
        if (data) {
          setCooperative({
            ...data,
            id: data.id,
            source: CooperativeDataSourceType.LOCAL,
            status: CoopStatus.active,
          });
          formik.setFieldValue("id", data.id);
          formik.setFieldValue("source", CooperativeDataSourceType.LOCAL);
          formik.setFieldValue("status", CoopStatus.active);
          toast.success("Cooperative information has been saved successfully");
        }
        handleSaveAsDraft(data.id);
      } else {
        const result = await handleCoopData(formik.values);
        if (result === true) {
          handleSaveAsDraft();
        }
      }
    } catch (error: any) {
      toast.error(error?.response?.data?.message);
    } finally {
      setIsSubmit(false);
    }
  };

  const handleCoopData = async (values: ICooperative) => {
    if (Object.keys(formik.errors).length > 0) {
      toast.error("Please fill up all required fields with correct values");
      return false;
    }

    if (values.source === CooperativeDataSourceType.CDA) {
      const { data } = await postCooperativeService(values);
      if (data) {
        setCooperative({
          ...data,
          id: data.id,
          source: CooperativeDataSourceType.LOCAL,
          status: CoopStatus.active,
        });
        toast.success("Cooperative information has been saved successfully");
        return true;
      }
    } else {
      if (cooperative?.id) {
        const { data } = await putCooperativeService(values);
        if (data) {
          setCooperative({
            ...data,
            id: data.id,
            source: CooperativeDataSourceType.LOCAL,
            status: CoopStatus.active,
          });
          toast.success("Cooperative information has been saved successfully");
          return true;
        }
      }
    }

    return false;
  };

  const formik = useFormik({
    initialValues: cooperative ?? {
      source: CooperativeDataSourceType.LOCAL,
      coopName: "",
      coopAcronym: "",
      coopCode: "",
      streetAddress: "",
      barangay: "",
      city: "",
      province: "",
      zipCode: "",
      emailAddress: "",
      websiteAddress: "",
      telephoneNumber: "",
      cdaRegistrationNumber: "",
      cdaRegistrationDate: "",
      cdaCocNumber: "",
      cdaCocDate: "",
      taxIdNumber: "",
      taxIdDate: "",
      taxCteNumber: "",
      taxCteExpiryDate: "",
      coopBranchesCount: 0,
      coopMembersCount: 0,
      coopTotalAssets: 0,
      coopMembersFemaleCount: 0,
      coopMembersMaleCount: 0,
      status: CoopStatus.active,
      mainBranchId: undefined,
      cooperativeTypeId: "",
      cooperativeCategoryId: "",
      cooperativeAffiliations: [] as IAffiliation[],
      cooperativeOfficers: [] as ICooperativeOfficer[],
    },
    validationSchema: CreateCooperativeSchema,
    onSubmit: async (values) => {
      try {
        setIsSubmit(true);
        const result = await handleCoopData(values);
        if (result === true) {
          setStep(2);
        }
      } catch (error: any) {
        toast.error(error?.response?.data?.message);
      } finally {
        setIsSubmit(false);
      }
    },
  });

  const resetForm = () => {
    setSelectedAffiliation(undefined);
    setSelectedCoopOfficer(undefined);
  };

  const handleAddAffiliation = (values: IAffiliation) => {
    formik.setFieldValue("cooperativeAffiliations", [
      ...(formik.values.cooperativeAffiliations ?? []),
      {
        status: values.status,
        affiliationName: values.affiliationName,
        effectivityDate: values.effectivityDate,
        affiliationId: values.affiliationId,
      },
    ]);
  };

  const toggleEditAffiliationModal = (affiliation: IAffiliation & { index: number }) => {
    setSelectedAffiliation(affiliation);
    toggleAffiliationModal();
  };

  const handleEditAffiliation = (affiliation: IAffiliation & { index: number }) => {
    const prevAffiliations = formik.values.cooperativeAffiliations ?? [];
    const newData = {
      status: affiliation.status,
      affiliationName: affiliation.affiliationName,
      effectivityDate: affiliation.effectivityDate,
      affiliationId: affiliation.affiliationId,
    };

    prevAffiliations[affiliation.index] = { ...newData };
    formik.setFieldValue("cooperativeAffiliations", [...prevAffiliations]);
    toggleAffiliationModal();
  };

  const handleDeleteAffiliation = (index: number) => {
    const affiliations = formik.values.cooperativeAffiliations ?? [];
    affiliations.splice(index, 1);
    formik.setFieldValue("cooperativeAffiliations", affiliations);
  };

  const handleAddCoopOfficer = (values: ICooperativeOfficer) => {
    formik.setFieldValue("cooperativeOfficers", [
      ...(formik.values.cooperativeOfficers ?? []),
      {
        title: values.title,
        firstName: values.firstName,
        lastName: values.lastName,
        middleName: values.middleName,
        generation: values.generation,
        gender: values.gender,
        emailAddress: values.emailAddress,
        contactNumber: values.contactNumber,
        effectivityDate: values.effectivityDate,
        status: values.status,
        positionId: values.positionId,
        positionName: values.positionName,
        signatory: values.signatory,
      },
    ]);
  };

  const toggleEditCoopOfficerModal = (officer: ICooperativeOfficer & { index: number }) => {
    setSelectedCoopOfficer(officer);
    toggleCoopOfficerModal();
  };

  const handleEditCoopOfficer = (officer: ICooperativeOfficer & { index: number }) => {
    const prevCoopOfficer = formik.values.cooperativeOfficers ?? [];
    const newData = {
      title: officer.title,
      firstName: officer.firstName,
      lastName: officer.lastName,
      middleName: officer.middleName,
      generation: officer.generation,
      gender: officer.gender,
      emailAddress: officer.emailAddress,
      contactNumber: officer.contactNumber,
      effectivityDate: officer.effectivityDate,
      status: officer.status,
      positionId: officer.positionId,
      positionName: officer.positionName,
      signatory: officer.signatory,
    };

    prevCoopOfficer[officer.index] = { ...newData };
    formik.setFieldValue("cooperativeOfficers", [...prevCoopOfficer]);
    toggleCoopOfficerModal();
  };

  const handleDeleteCoopOfficer = (index: number) => {
    const prevOfficers = formik.values.cooperativeOfficers ?? [];
    prevOfficers.splice(index, 1);
    formik.setFieldValue("cooperativeOfficers", [...prevOfficers]);
  };

  useEffect(() => {
    getCooperativeCategory({ filter: "" });
    getCooperativeType({ filter: "" });
  }, []);

  const affiliationsError = Object.values(Object.values(formik.errors.cooperativeAffiliations ?? {})[0] ?? {});
  const officersError = Object.values(Object.values(formik.errors.cooperativeOfficers ?? {})[0] ?? {});

  return (
    <Fragment>
      {affiliationModal && (
        <Modal isOpen={affiliationModal} onClose={toggleAffiliationModal} title={selectedAffiliation ? "Edit Affiliation" : "Add New Affiliation"} modalContainerClassName="max-w-3xl">
          <AffiliationForm
            resetForm={resetForm}
            selectedAffiliation={selectedAffiliation}
            affiliationState={formik.values.cooperativeAffiliations ?? []}
            toggleModal={toggleAffiliationModal}
            addAffiliation={handleAddAffiliation}
            editAffiliation={handleEditAffiliation}
          />
        </Modal>
      )}
      {coopOfficerModal && (
        <Modal isOpen={coopOfficerModal} onClose={toggleCoopOfficerModal} title={selectedCoopOfficer ? "Edit Cooperative Officer" : "Add New Cooperative Officer"} modalContainerClassName="max-w-3xl">
          <CoopOfficerForm
            selectedCoopOfficer={selectedCoopOfficer}
            resetForm={resetForm}
            toggleModal={toggleCoopOfficerModal}
            addCoopOfficer={handleAddCoopOfficer}
            editCoopOfficer={handleEditCoopOfficer}
          />
        </Modal>
      )}
      <FormikProvider value={formik}>
        <Form>
          <div className="flex flex-1 flex-col justify-between -mt-10">
            <Typography className="text-accent !text-md">COOPERATIVE INFORMATION</Typography>
            <Typography className="!text-sm">Provide the necessary details about the cooperative to proceed</Typography>
          </div>
          <div className="divider"></div>
          <div className="flex flex-1 flex-col">
            <div className="flex flex-1">
              <Typography className="text-accent !text-lg">BASIC INFORMATION</Typography>
            </div>
            <div className="flex flex-1 space-y-2">
              <div className="flex flex-col w-1/2 space-y-4 mt-4 ml-4">
                <div className="flex flex-1 flex-col">
                  <Typography>Cooperative Name (In full)</Typography>
                  <TextField
                    name="coopName"
                    value={cooperative?.coopName ?? cooperative?.name}
                    onChange={formik.handleChange}
                    error={!!formik.errors.coopName}
                    errorText={formik.errors.coopName}
                    size="sm"
                    placeholder="Enter Cooperative Name"
                    className="mt-2"
                    required
                    disabled
                  />
                </div>
                <div className="flex flex-1 flex-col">
                  <Typography>Cooperative Acronym</Typography>
                  <TextField
                    name="coopAcronym"
                    value={formik.values.coopAcronym}
                    onChange={formik.handleChange}
                    error={!!formik.errors.coopAcronym}
                    errorText={formik.errors.coopAcronym}
                    size="sm"
                    placeholder="Enter Cooperative Acronym"
                    className="mt-2"
                    required
                    disabled={!!formik.values.coopAcronym}
                  />
                </div>
                <div className="flex flex-1 flex-col">
                  <Typography>Cooperative Code</Typography>
                  <TextField name="coopCode" onChange={formik.handleChange} size="sm" placeholder="Enter Cooperative Code" value={formik.values.coopCode} className="mt-2" required disabled />
                </div>
                <div className="flex flex-1 flex-col">
                  <Typography>Coop Category</Typography>
                  <Select
                    options={cooperativeCategoryOptions}
                    name="cooperativeCategoryId"
                    size="sm"
                    value={formik.values.cooperativeCategoryId}
                    onChange={formik.handleChange}
                    error={!!formik.errors.cooperativeCategoryId && formik.touched.cooperativeCategoryId}
                    errorText={formik.errors.cooperativeCategoryId}
                    required
                  />
                </div>
                <div className="flex flex-1 flex-col">
                  <Typography>Cooperative Type</Typography>
                  <Select
                    variant="primary"
                    options={cooperativeTypeOptions}
                    name="cooperativeTypeId"
                    size="sm"
                    value={formik.values.cooperativeTypeId}
                    onChange={formik.handleChange}
                    error={!!formik.errors.cooperativeTypeId && formik.touched.cooperativeTypeId}
                    errorText={formik.errors.cooperativeTypeId}
                    required
                  />
                </div>
                <div className="flex flex-1 flex-col">
                  <Typography>No. of Branches</Typography>
                  <TextField
                    type="number"
                    name="coopBranchesCount"
                    onChange={formik.handleChange}
                    error={!!formik.errors.coopBranchesCount}
                    errorText={formik.errors.coopBranchesCount}
                    size="sm"
                    placeholder="Enter no. of branches"
                    value={parseInt(formik.values.coopBranchesCount?.toString() ?? "0")}
                    className="mt-2"
                    min={0}
                    required
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="divider"></div>
          <div className="flex flex-1 flex-col">
            <div className="flex flex-1">
              <Typography className="text-accent !text-lg">BUSINESS ADDRESS</Typography>
            </div>
            <div className="flex flex-1 space-y-2">
              <div className="flex flex-col w-1/2 space-y-4 mt-4 ml-4">
                <div className="flex flex-1 flex-col">
                  <Typography>Province</Typography>
                  <TextField
                    name="province"
                    onChange={formik.handleChange}
                    error={!!formik.errors.province}
                    errorText={formik.errors.province}
                    size="sm"
                    placeholder="Enter Province"
                    value={formik.values.province}
                    className="mt-2"
                    required
                  />
                </div>
                <div className="flex flex-1 flex-col">
                  <Typography>Municipality/City</Typography>
                  <TextField
                    name="city"
                    onChange={formik.handleChange}
                    error={!!formik.errors.city}
                    errorText={formik.errors.city}
                    size="sm"
                    placeholder="Enter Municipality/City"
                    value={formik.values.city}
                    className="mt-2"
                    required
                  />
                </div>
                <div className="flex flex-1 flex-col">
                  <Typography>Barangay</Typography>
                  <TextField
                    name="barangay"
                    onChange={formik.handleChange}
                    error={!!formik.errors.city}
                    errorText={formik.errors.barangay}
                    size="sm"
                    placeholder="Enter Barangay"
                    value={formik.values.barangay}
                    className="mt-2"
                    required
                  />
                </div>
                <div className="flex flex-1 flex-col">
                  <Typography>Street Address</Typography>
                  <TextField
                    name="streetAddress"
                    onChange={formik.handleChange}
                    error={!!formik.errors.streetAddress}
                    errorText={formik.errors.streetAddress}
                    size="sm"
                    placeholder="Enter Street Address"
                    value={formik.values.streetAddress}
                    className="mt-2"
                    required
                  />
                </div>
                <div className="flex flex-1 flex-col">
                  <Typography>Zipcode</Typography>
                  <TextField
                    name="zipCode"
                    onChange={formik.handleChange}
                    error={!!formik.errors.zipCode}
                    errorText={formik.errors.zipCode}
                    size="sm"
                    placeholder="Enter Zipcode"
                    value={formik.values.zipCode}
                    className="mt-2"
                    required
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="divider"></div>
          <div className="flex flex-1 flex-col">
            <div className="flex flex-1">
              <Typography className="text-accent !text-lg">CONTACT INFORMATION</Typography>
            </div>
            <div className="flex flex-1 space-y-2">
              <div className="flex flex-col w-1/2 space-y-4 mt-4 ml-4">
                <div className="flex flex-1 flex-col">
                  <Typography>Email Address</Typography>
                  <TextField
                    type="email"
                    name="emailAddress"
                    onChange={formik.handleChange}
                    error={!!formik.errors.emailAddress}
                    errorText={formik.errors.emailAddress}
                    size="sm"
                    placeholder="Enter email address"
                    value={formik.values.emailAddress}
                    className="mt-2"
                    required
                  />
                </div>
                <div className="flex flex-1 flex-col">
                  <Typography>Website</Typography>
                  <TextField
                    name="websiteAddress"
                    onChange={formik.handleChange}
                    error={!!formik.errors.websiteAddress}
                    errorText={formik.errors.websiteAddress}
                    size="sm"
                    placeholder="Enter website"
                    value={formik.values.websiteAddress}
                    className="mt-2"
                  />
                </div>
                <div className="flex flex-1 flex-col">
                  <Typography>Telephone No. / Fax No.</Typography>
                  <TextField
                    type="tel"
                    name="telephoneNumber"
                    onChange={formik.handleChange}
                    error={!!formik.errors.telephoneNumber}
                    errorText={formik.errors.telephoneNumber}
                    size="sm"
                    placeholder="Enter Telephone No. / Fax No."
                    value={formik.values.telephoneNumber}
                    className="mt-2"
                    required
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="divider"></div>
          <div className="flex flex-1 flex-col">
            <div className="flex flex-1">
              <Typography className="text-accent !text-lg">REGISTRATION AND COMPLIANCE</Typography>
            </div>
            <div className="flex flex-1 space-y-2">
              <div className="flex flex-col w-full space-y-4 mt-4 ml-4">
                <div className="flex flex-1 flex-row space-x-8">
                  <div className="flex flex-1 flex-col">
                    <Typography>CDA Registraion No.</Typography>
                    <TextField
                      name="cdaRegistrationNumber"
                      onChange={formik.handleChange}
                      error={!!formik.errors.cdaRegistrationNumber}
                      errorText={formik.errors.cdaRegistrationNumber}
                      size="sm"
                      placeholder="Enter CDA Reg. No."
                      value={formik.values.cdaRegistrationNumber}
                      className="mt-2"
                    />
                  </div>
                  <div className="flex flex-1 flex-col">
                    <Typography>Date Registered</Typography>
                    <TextField
                      type="date"
                      name="cdaRegistrationDate"
                      onChange={formik.handleChange}
                      error={!!formik.errors.cdaRegistrationDate}
                      errorText={formik.errors.cdaRegistrationDate}
                      size="sm"
                      placeholder="mm/dd/yyyy"
                      value={formik.values.cdaRegistrationDate}
                      className="mt-2"
                    />
                  </div>
                </div>
                <div className="flex flex-1 flex-row space-x-8">
                  <div className="flex flex-1 flex-col">
                    <Typography>CDA Certificate of Compliance (COC No.)</Typography>
                    <TextField
                      name="cdaCocNumber"
                      onChange={formik.handleChange}
                      error={!!formik.errors.cdaCocNumber}
                      errorText={formik.errors.cdaCocNumber}
                      size="sm"
                      placeholder="Enter CDA Certificate of Compliance (COC) No."
                      value={formik.values.cdaCocNumber}
                      className="mt-2"
                    />
                  </div>
                  <div className="flex flex-1 flex-col">
                    <Typography>Date Registered</Typography>
                    <TextField
                      type="date"
                      name="cdaCocDate"
                      onChange={formik.handleChange}
                      error={!!formik.errors.cdaCocDate}
                      errorText={formik.errors.cdaCocDate}
                      size="sm"
                      placeholder="mm/dd/yyyy"
                      value={formik.values.cdaCocDate}
                      className="mt-2"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="divider"></div>
          <div className="flex flex-1 flex-col">
            <div className="flex flex-1">
              <Typography className="text-accent !text-lg">TAX INFORMATION</Typography>
            </div>
            <div className="flex flex-1 space-y-2">
              <div className="flex flex-col w-full space-y-4 mt-4 ml-4">
                <div className="flex flex-1 flex-row space-x-8">
                  <div className="flex flex-1 flex-col">
                    <Typography>Tax Identification No.</Typography>
                    <TextField
                      name="taxIdNumber"
                      onChange={formik.handleChange}
                      error={!!formik.errors.taxIdNumber}
                      errorText={formik.errors.taxIdNumber}
                      size="sm"
                      placeholder="Enter TIN No."
                      value={formik.values.taxIdNumber}
                      className="mt-2"
                    />
                  </div>
                  <div className="flex flex-1 flex-col">
                    <Typography>Date Issued</Typography>
                    <TextField
                      type="date"
                      name="taxIdDate"
                      onChange={formik.handleChange}
                      error={!!formik.errors.taxIdDate}
                      errorText={formik.errors.taxIdDate}
                      size="sm"
                      placeholder="mm/dd/yyyy"
                      value={formik.values.taxIdDate}
                      className="mt-2"
                    />
                  </div>
                </div>
                <div className="flex flex-1 flex-row space-x-8">
                  <div className="flex flex-1 flex-col">
                    <Typography>Certificate of Tax Exemption (CTE) No.</Typography>
                    <TextField
                      name="taxCteNumber"
                      onChange={formik.handleChange}
                      error={!!formik.errors.taxCteNumber}
                      errorText={formik.errors.taxCteNumber}
                      size="sm"
                      placeholder="Enter Certificate of Tax Exemption (CTE) No."
                      value={formik.values.taxCteNumber}
                      className="mt-2"
                    />
                  </div>
                  <div className="flex flex-1 flex-col">
                    <Typography>Certificate of Tax Exemption (CTE) Expiration Date</Typography>
                    <TextField
                      type="date"
                      name="taxCteExpiryDate"
                      onChange={formik.handleChange}
                      error={!!formik.errors.taxCteExpiryDate}
                      errorText={formik.errors.taxCteExpiryDate}
                      size="sm"
                      placeholder="mm/dd/yyyy"
                      value={formik.values.taxCteExpiryDate}
                      className="mt-2"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="divider"></div>
          <div className="flex flex-1 flex-col">
            <div className="flex flex-1">
              <Typography className="text-accent !text-lg">MEMBER AND ASSET INFORMATION</Typography>
            </div>
            <div className="flex flex-1 space-y-2">
              <div className="flex flex-col w-full space-y-4 mt-4 ml-4">
                <div className="flex flex-1 flex-row space-x-8">
                  <div className="flex flex-1 flex-col">
                    <Typography>Total Asset</Typography>
                    <TextField
                      type="number"
                      name="coopTotalAssets"
                      onChange={formik.handleChange}
                      error={!!formik.errors.coopTotalAssets}
                      errorText={formik.errors.coopTotalAssets}
                      size="sm"
                      placeholder="Enter Total Asset"
                      value={parseInt(formik.values.coopTotalAssets?.toString() ?? "0")}
                      className="mt-2"
                      min={0}
                      required
                    />
                  </div>
                  <div className="flex flex-1 flex-col">
                    <Typography>Total Number of Members</Typography>
                    <TextField
                      type="number"
                      name="coopMembersCount"
                      onChange={formik.handleChange}
                      error={!!formik.errors.coopMembersCount}
                      errorText={formik.errors.coopMembersCount}
                      size="sm"
                      placeholder="Enter Total Number of Members"
                      value={formik.values.coopMembersCount}
                      className="mt-2"
                      min={0}
                      required
                    />
                  </div>
                </div>
                <div className="flex flex-1 flex-row space-x-8">
                  <div className="flex flex-1 flex-col">
                    <Typography>Total Number of Female Member</Typography>
                    <TextField
                      type="number"
                      name="coopMembersFemaleCount"
                      onChange={formik.handleChange}
                      error={!!formik.errors.coopMembersFemaleCount}
                      errorText={formik.errors.coopMembersFemaleCount}
                      size="sm"
                      placeholder="Enter Total Number of Female Member"
                      value={formik.values.coopMembersFemaleCount}
                      className="mt-2"
                      min={0}
                      required
                    />
                  </div>
                  <div className="flex flex-1 flex-col">
                    <Typography>Total Number of Male Member</Typography>
                    <TextField
                      type="number"
                      name="coopMembersMaleCount"
                      onChange={formik.handleChange}
                      error={!!formik.errors.coopMembersMaleCount}
                      errorText={formik.errors.coopMembersMaleCount}
                      size="sm"
                      placeholder="Enter Total Number of Male Member"
                      value={formik.values.coopMembersMaleCount}
                      className="mt-2"
                      min={0}
                      required
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="divider"></div>
          <div className="flex flex-1 flex-col">
            <div className="flex flex-1 flex-col">
              <Typography className="text-accent !text-lg">AFFILIATIONS</Typography>
              {affiliationsError.length > 0 && (
                <ul className="ml-10">
                  {affiliationsError.map((error: string, index: number) => {
                    return (
                      <li>
                        <Typography key={index} className="!text-red-500 !text-xs">
                          {error}
                        </Typography>
                      </li>
                    );
                  })}
                </ul>
              )}
            </div>
            <div className="flex flex-1 space-y-2">
              <div className="flex flex-col w-full space-y-4 mt-4 ml-4">
                <AffiliationsTable
                  affiliations={formik.values.cooperativeAffiliations ?? []}
                  editAffiliation={(affiliation: IAffiliation & { index: number }) => toggleEditAffiliationModal(affiliation)}
                  deleteAffiliation={(index: number) => handleDeleteAffiliation(index)}
                />
                <button type="button" className="btn btn-sm w-32 mt-2" onClick={toggleAffiliationModal}>
                  <FaPlus />
                  Add New
                </button>
              </div>
            </div>
          </div>
          <div className="divider"></div>
          <div className="flex flex-1 flex-col">
            <div className="flex flex-1 flex-col">
              <Typography className="text-accent !text-lg">COOP OFFICERS</Typography>
              {officersError.length > 0 && (
                <ul className="ml-10">
                  {officersError.map((error: string, index: number) => {
                    return (
                      <li>
                        <Typography key={index} className="!text-red-500 !text-xs">
                          {error}
                        </Typography>
                      </li>
                    );
                  })}
                </ul>
              )}
            </div>
            <div className="flex flex-1 space-y-2">
              <div className="flex flex-col w-full space-y-4 mt-4 ml-4">
                <CoopOfficersTable
                  officers={formik.values.cooperativeOfficers ?? []}
                  editCoopOfficer={(officer: ICooperativeOfficer & { index: number }) => toggleEditCoopOfficerModal(officer)}
                  deleteOfficer={(index: number) => handleDeleteCoopOfficer(index)}
                />
                <button type="button" className="btn btn-sm w-32 mt-2" onClick={toggleCoopOfficerModal}>
                  <FaPlus />
                  Add New
                </button>
              </div>
            </div>
          </div>
          <div className="flex flex-1 flex-row mt-10">
            <div className="flex flex-1 flex-row w-full items-center justify-between space-x-2">
              <div className="flex justify-end">
                <LoadingButton isLoading={isSubmit || isSubmitting} onClick={handleCancel} className="btn !bg-white !text-primary hover:!bg-zinc-300 rounded-lg !w-48 mr-4 mt-4" type="button">
                  Cancel
                </LoadingButton>
              </div>
              <div className="flex justify-end">
                <LoadingButton
                  isLoading={isSubmit || isSubmitting}
                  onClick={saveAsDraft}
                  className="btn btn-outline !bg-white !text-primary rounded-lg hover:bg-zinc-200 hover:text-primary !w-48 mr-4 mt-4"
                  type="button"
                >
                  Save as draft
                </LoadingButton>
                <LoadingButton isLoading={isSubmit || isSubmitting} className="btn rounded-lg hover:bg-zinc-200 hover:text-primary !w-48 mr-4 mt-4" type="submit">
                  Continue
                </LoadingButton>
              </div>
            </div>
          </div>
        </Form>
      </FormikProvider>
    </Fragment>
  );
};

export default CooperativeInformationForm;
