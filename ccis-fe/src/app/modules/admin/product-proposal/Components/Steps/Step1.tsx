import Modal from "@components/common/Modal";
import Typography from "@components/common/Typography";
import { FC, Fragment, useEffect, useState } from "react";
import { FaFileAlt, FaPercent } from "react-icons/fa";
import { FaFilePen } from "react-icons/fa6";
import SelectStandardGuidelinesForm from "../Forms/SelectStandardGuidelinesForm";
import SelectedProductDetails from "../SelectedProductDetails";
import { RootState } from "@state/store";
import { useSelector } from "react-redux";
import TextField from "@components/form/TextField";
import { toast } from "react-toastify";
import { useProductProposalActions } from "@state/reducer/product-proposal";
import { ProposalStatus } from "@enums/proposal-status";
import { IoIosPaper } from "react-icons/io";
import { IoNewspaper } from "react-icons/io5";
import EditSelectProductDetails from "../EditSelectedProductDetails";
import SelectAERDetailsForm from "../Forms/SelectAERDetailsForm";
import SelectedAERDetails from "../SelectedAERDetails";
import { useQuotationActions } from "@state/reducer/quotations";
import { CustomizeType } from "@enums/enums";
import { useLocation, useParams } from "react-router-dom";
import { getQuotationByIdService } from "@services/quotation/quotation.service";

type TStep1Props = {
  handleSaveAsDraft?: () => void;
};

const Step1: FC<TStep1Props> = ({ handleSaveAsDraft }) => {
  const [standardModal, setStandardModal] = useState<boolean>(false);
  const toggleStandardModal = () => setStandardModal(!standardModal);
  const [customizeModal, setCustomizeModal] = useState<boolean>(false);
  const toggleCustomizeModal = () => setCustomizeModal(!customizeModal);
  const [feeModal, setFeeModal] = useState<boolean>(false);
  const toggleFeeModal = () => setFeeModal(!feeModal);
  const proposedProduct = useSelector((state: RootState) => state.productProposal.proposedProduct);
  const revisionDetails = useSelector((state: RootState) => state.products.revisionDetails);
  const managementPercentFee = useSelector((state: RootState) => state.productProposal.managementPercentFee);
  const { setStep, setManagementFee, setCustomType } = useProductProposalActions();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [selectedCustomization, setSelectedCustomization] = useState<string>("");
  const [isEditProvisions, setIsEditProvisions] = useState<boolean>(false);
  const [mode, setMode] = useState<"create" | "edit">("create");
  const [proposalId, setProposalId] = useState<number | undefined>(undefined);

  const toggleIsEditProvisions = () => setIsEditProvisions(!isEditProvisions);

  const [aerModal, setAerModal] = useState<boolean>(false);
  const toggleAerModal = () => setAerModal(!aerModal);
  const approvedAER = useSelector((state: RootState) => state.quotation.quotations);
  const { setApprovedAER } = useQuotationActions();
  const [hasSelectedAER, setHasSelectedAER] = useState(false);
  const { id: aerIdFromParams } = useParams();
  const location = useLocation();

  const handleAERSelected = (aerData: any) => {
    setCustomType(CustomizeType.AER);
    setApprovedAER(aerData);
    setHasSelectedAER(true);
    setAerModal(false); // Close the modal
  };

  const handleStepAER = () => {
    if (approvedAER === null) {
      toast.error("Please select an AER");
      return;
    }
    setStep(2);
  };

  const handleToggleStandardModal = () => {
    setCustomType(undefined);
    toggleStandardModal();
    if (revisionDetails !== undefined) {
      toggleFeeModal();
    }
  };

  const handleToggleCustomizeModal = () => {
    toggleCustomizeModal();
    if (selectedCustomization !== "") {
      setSelectedCustomization("");
    }
  };

  const handleContinueCustomize = () => {
    setCustomType(selectedCustomization);
    if (selectedCustomization === CustomizeType.PROVISIONS) {
      toggleCustomizeModal();
      toggleStandardModal();
    } else if (selectedCustomization === CustomizeType.AER) {
      toggleCustomizeModal();
      toggleAerModal();
    }
  };

  const handleChange = () => {
    toggleStandardModal();
  };

  const handleChangeAER = () => {
    toggleAerModal();
  };

  const handleCheckManagementPercentFee = () => {
    if (proposedProduct?.status === ProposalStatus.approved) {
      return true;
    }
    const fee = parseFloat((managementPercentFee ?? 0).toString());

    if (fee < 0) {
      toast.error("Please enter a valid management fee");
      return false;
    }

    return true;
  };

  const handleSetManagementFee = (fee: number) => {
    const managementFee = parseFloat((fee ?? 0).toString());

    if (fee < 0) {
      toast.error("Please enter a valid management fee");
      return;
    }

    setManagementFee(managementFee);
  };

  const handleStep = () => {
    if (!handleCheckManagementPercentFee()) {
      return;
    }

    setStep(1);
  };

  const handleSaveManagementFee = () => {
    const fee = parseFloat((managementPercentFee ?? 0).toString());
    if (isNaN(fee) || fee < 0) {
      toast.error("Please enter a valid management fee");
      setManagementFee(0);
      return;
    }

    setManagementFee(fee);
    toggleFeeModal();
  };

  const handleCancelManagementFee = () => {
    toggleFeeModal();
  };

  const handleSave = async () => {
    try {
      setSubmitting(true);

      if (managementPercentFee === undefined || managementPercentFee < 0) {
        toast.error("Please enter a valid management fee");
        return;
      }

      handleSaveAsDraft && handleSaveAsDraft();
    } catch (error: any) {
      toast.error(error?.response?.data?.message);
    } finally {
      setSubmitting(false);
    }
  };

  useEffect(() => {
    const cameFromStep3 = sessionStorage.getItem("cameFromStep3WithAER");
    if (cameFromStep3 === "true" && approvedAER) {
      setHasSelectedAER(true);
      sessionStorage.removeItem("cameFromStep3WithAER");
    }
  }, []);

  useEffect(() => {
    const fetchAERById = async () => {
      if (!aerIdFromParams) return;

      try {
        const { data } = await getQuotationByIdService(aerIdFromParams);

        if (Array.isArray(data) && data.length > 0) {
          const aer = data[0];
          setApprovedAER(aer);
          setCustomType(CustomizeType.AER);
          setProposalId(Number(aerIdFromParams));
          const locationState = location.state as {
            proposal?: any;
            proposalId?: number;
            cameFromStep3WithAER?: boolean;
          };

          if (locationState?.proposalId) {
            setProposalId(locationState.proposalId);
            setMode("edit");
            if (locationState?.cameFromStep3WithAER && locationState?.proposal) {
              setApprovedAER(locationState.proposal);
              setCustomType(CustomizeType.AER);
              setHasSelectedAER(true);
            }
          }
          setHasSelectedAER(true);
          setMode("edit");
        } else {
          toast.error("No AER found for this ID");
        }
      } catch (error) {
        toast.error("Failed to fetch AER from URL");
      }
    };

    fetchAERById();
  }, [aerIdFromParams]);

  return (
    <Fragment>
      {standardModal && (
        <Modal isOpen={standardModal} onClose={handleToggleStandardModal} modalContainerClassName="!max-w-7xl" title="Select Product Guidelines">
          <SelectStandardGuidelinesForm toggleModal={toggleStandardModal} toggleFeeModal={toggleFeeModal} />
        </Modal>
      )}
      {customizeModal && (
        <Modal isOpen={customizeModal} onClose={handleToggleCustomizeModal} modalContainerClassName="!max-w-2xl" justifyContent="justify-end">
          <div className="flex flex-col items-center justify-center">
            <Typography size="3xl" className="mb-10">
              Customize Type
            </Typography>
            <Typography>How would like to customize this proposal?</Typography>
            <div className="flex flex-row justify-between gap-2 mt-8">
              <div
                onClick={() => setSelectedCustomization(CustomizeType.PROVISIONS)}
                className={`                                   
                                    group
                                    card flex flex-col items-center gap-2 
                                    border-2 min-w-[250px] 
                                    hover:cursor-pointer hover:border-primary  
                                     ${selectedCustomization === CustomizeType.PROVISIONS ? "border-primary" : "border-zinc-100"}
                                `}
              >
                <IoIosPaper className="text-primary" size={50} />
                <Typography className="text-primary">Product Provisions</Typography>
              </div>
              <div
                onClick={() => setSelectedCustomization(CustomizeType.AER)}
                className={`                                   
                                    group
                                    card flex flex-col items-center gap-2 
                                    border-2 min-w-[250px] 
                                    hover:cursor-pointer hover:border-primary  
                                     ${selectedCustomization === CustomizeType.AER ? "border-primary" : "border-zinc-100"}
                                `}
              >
                <IoNewspaper className="text-primary" size={50} />
                <Typography className="text-primary">AER</Typography>
              </div>
            </div>
            <div className="mt-8 flex flex-row w-full items-center justify-center px-10">
              <button onClick={handleContinueCustomize} className="btn bg-primary-dark text-white w-full hover:bg-primary" disabled={selectedCustomization === ""}>
                Continue
              </button>
            </div>
          </div>
        </Modal>
      )}
      {aerModal && (
        <Modal isOpen={aerModal} onClose={toggleAerModal} modalContainerClassName="!max-w-4xl" title="Select Approved AER">
          <SelectAERDetailsForm toggleModal={toggleAerModal} onAERSelected={handleAERSelected} />
        </Modal>
      )}
      {feeModal && (
        <Modal isOpen={feeModal} onClose={toggleFeeModal} modalContainerClassName="!max-w-xl" title="Coop Management Fee">
          <div>
            <Typography>Enter Coop Management Fee</Typography>
            <TextField value={managementPercentFee} onChange={(e) => handleSetManagementFee(parseFloat(e.target.value))} type="number" rightIcon={<FaPercent />} />
            <div className="flex flex-1 flex-row mt-8 justify-end space-x-2">
              <button onClick={handleCancelManagementFee} className="btn bg-outline bg-zinc-300 min-w-32">
                Cancel
              </button>
              <button onClick={handleSaveManagementFee} className="btn bg-accent text-white min-w-32">
                Save
              </button>
            </div>
          </div>
        </Modal>
      )}
      {revisionDetails == undefined && !hasSelectedAER && (
        <Fragment>
          <div className="flex flex-1 justify-center mt-72">
            <div className="flex flex-row justify-center justify-items-center w-3/4">
              <div className="flex flex-1 flex-col items-center">
                <div className="flex flex-1 flex-col shadow-md shadow-accent border-[1px] border-zinc-500 w-3/4 rounded-lg p-4 pt-10 justify-center items-center">
                  <FaFileAlt className="text-5xl text-accent" />
                  <Typography className="mt-8" size="xl">
                    Standard
                  </Typography>
                  <Typography className="text-center mt-4">create a standardized process for your product proposal</Typography>
                  <div className="mt-5">
                    <button onClick={toggleStandardModal} className="btn bg-accent text-white min-w-32">
                      Select
                    </button>
                  </div>
                </div>
              </div>
              <div className="flex flex-1 flex-col items-center">
                <div className="flex flex-1 flex-col shadow-md shadow-accent border-[1px] border-zinc-500 w-3/4 rounded-lg p-4 pt-10 justify-center items-center">
                  <FaFilePen className="text-5xl text-accent" />
                  <Typography className="mt-8" size="xl">
                    Customize
                  </Typography>
                  <Typography className="text-center mt-4">create a product proposal with credible and comprehensive actuarial data</Typography>
                  <div className="mt-5">
                    <button onClick={() => toggleCustomizeModal()} className="btn bg-accent text-white min-w-32">
                      Select
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Fragment>
      )}
      {revisionDetails !== undefined && !standardModal && !feeModal && !isEditProvisions && (
        <div className="flex flex-1 flex-col p-2 mt-24">
          <SelectedProductDetails
            setStep={handleStep}
            handleChange={handleChange}
            handleSaveAsDraft={handleSave}
            toggleManagementFee={toggleFeeModal}
            submitting={submitting}
            toggleIsEditProvisions={toggleIsEditProvisions}
          />
        </div>
      )}
      {revisionDetails !== undefined && isEditProvisions && (
        <div className="flex flex-1 flex-col p-2 mt-24">
          <EditSelectProductDetails setStep={handleStep} handleChange={handleChange} toggleManagementFee={toggleFeeModal} submitting={submitting} toggleIsEditProvisions={toggleIsEditProvisions} />
        </div>
      )}
      {!aerModal && hasSelectedAER && approvedAER && mode && <SelectedAERDetails setStep={handleStepAER} handleChange={handleChangeAER} submitting={submitting} mode={mode} proposalId={proposalId} />}
    </Fragment>
  );
};

export default Step1;
