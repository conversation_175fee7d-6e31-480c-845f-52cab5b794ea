import { FC, Fragment, useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { TableColumn } from "react-data-table-component";
import { RootState } from "@state/store";
import { CiEdit, CiTrash } from "react-icons/ci";
import { IActions } from "@interface/common.interface";
import Table from "@components/common/Table";
import Modal from "@components/common/Modal";
import ActionButtons from "@components/common/ActionButtons";
import { confirmDelete, confirmSaveOrEdit } from "@helpers/prompt";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { IUserArea } from "@interface/utilities.interface";
import { Form, FormikProvider, useFormik } from "formik";
import TextField from "@components/form/TextField";
import Button from "@components/common/Button";
import { CreateAreaSchema, EditAreaSchema } from "@services/utilities-areas/utilities-areas.schema";

const UtilitiesArea: FC = () => {
  const [create, setCreate] = useState<boolean>(false);
  const [edit, setEdit] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>("");
  const Areas = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const loading = useSelector((state: RootState) => state?.utilitiesAreas?.getArea?.loading);
  const { setSelectedArea, getAreas, postArea, putArea, destroyArea } = useAreaActions();

  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const actionEvents: IActions<IUserArea>[] = [
    {
      name: "Edit",
      event: (row: IUserArea, index: number) => {
        // Set data to edit
        const data = {
          id: row.id,
          userAreaCode: row.userAreaCode,
          areaName: row.areaName,
          description: row.description,
        };

        setSelectedArea({ data: data, index: index });
        formikEdit.setValues(data);
        handleToggleEditModal();
      },
      icon: CiEdit,
      color: "primary",
    },
    {
      name: "Delete",
      event: (row: IUserArea, index: number) => {
        const action = confirmDelete(row.areaName);
        action.then((value) => {
          if (value.isConfirmed) {
            destroyArea({ id: row.id, index: index });
          }
        });
      },
      icon: CiTrash,
      color: "danger",
    },
  ];

  const columns: TableColumn<IUserArea>[] = [
    {
      name: "Area Code",
      selector: (row) => row.userAreaCode,
      ...commonSetting,
    },
    {
      name: "Area Name",
      selector: (row) => row.areaName,
      ...commonSetting,
    },
    {
      name: "Description",
      cell: (row) => row.description,
    },
    {
      name: "Action",
      cell: (row, rowIndex) => <ActionButtons data={row} rowIndex={rowIndex} actions={actionEvents} />,
    },
  ];

  const handleToggleCreateModal = () => {
    setCreate((prev) => !prev);
    formik.resetForm();
  };

  const handleToggleEditModal = () => {
    setEdit((prev) => !prev);
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  useEffect(() => {
    getAreas({ filter: searchText });
  }, [searchText]);

  const formik = useFormik({
    initialValues: {
      id: 0,
      userAreaCode: "",
      areaName: "",
      description: "",
    },
    validationSchema: CreateAreaSchema,
    onSubmit: async (values, { resetForm }) => {
      const isConfirmed = await confirmSaveOrEdit(`Do you want to save the area: "${values.areaName} ?"`);
      if (isConfirmed) {
        postArea(values);
        handleToggleCreateModal();
        resetForm();
      }
    },
  });
  const formikEdit = useFormik({
    initialValues: {
      id: 0,
      userAreaCode: "",
      areaName: "",
      description: "",
    },
    validationSchema: EditAreaSchema,
    onSubmit: async (values) => {
      const isConfirmed = await confirmSaveOrEdit(`Do you want to edit the area: "${values.areaName} ?"`);
      if (isConfirmed) {
        putArea(values as any);
        handleToggleEditModal();
      }
    },
  });

  return (
    <Fragment>
      <div className="text-xl font-semibold uppercase my-4">System Utilities / Areas</div>
      <Table
        className="h-[400px] "
        columns={columns}
        data={Areas}
        createLabel="Add new Area"
        onCreate={handleToggleCreateModal}
        loading={loading}
        onSearch={handleSearch}
        searchable
        multiSelect={false}
      />
      {create && (
        <Modal title="Add new Area" modalContainerClassName="max-w-3xl " titleClass="text-primary text-lg uppercase" isOpen={create} onClose={handleToggleCreateModal}>
          <>
            <FormikProvider value={formik}>
              <Form className="flex flex-col my-4 gap-4">
                <div>
                  <label>Area Code</label>
                  <TextField
                    name="userAreaCode"
                    placeholder="Enter Area Code"
                    type="text"
                    className="bg-white"
                    error={formik.touched.userAreaCode && !!formik.errors.userAreaCode}
                    errorText={formik.errors.userAreaCode}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.userAreaCode}
                    required
                  />
                </div>
                <div>
                  {" "}
                  <label>Area Name</label>
                  <TextField
                    name="areaName"
                    placeholder="Enter Area Name"
                    type="text"
                    className="bg-white"
                    error={formik.touched.areaName && !!formik.errors.areaName}
                    errorText={formik.errors.areaName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.areaName}
                    required
                  />
                </div>
                <div>
                  <label>Description</label>
                  <TextField
                    name="description"
                    type="text"
                    placeholder="Enter Description"
                    className="bg-white"
                    error={formik.touched.description && !!formik.errors.description}
                    errorText={formik.errors.description}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.description}
                    required
                  />
                </div>

                <Button type="submit" variant="primary" classNames="btn rounded-xl">
                  Save
                </Button>
              </Form>
            </FormikProvider>
          </>
        </Modal>
      )}
      {edit && (
        <Modal title="Edit Product Area" modalContainerClassName="max-w-3xl" titleClass="text-primary text-lg uppercase" isOpen={edit} onClose={handleToggleEditModal}>
          <>
            <FormikProvider value={formikEdit}>
              <Form className="flex flex-col my-4 gap-4">
                <div>
                  <label>Area Code</label>
                  <TextField
                    name="userAreaCode"
                    placeholder="Enter Area Code"
                    type="text"
                    className="bg-white"
                    error={formikEdit.touched.userAreaCode && !!formikEdit.errors.userAreaCode}
                    errorText={formikEdit.errors.userAreaCode}
                    onChange={formikEdit.handleChange}
                    onBlur={formikEdit.handleBlur}
                    value={formikEdit.values.userAreaCode}
                    required
                  />
                </div>
                <div>
                  {" "}
                  <label> Area Name</label>
                  <TextField
                    name="areaName"
                    placeholder="Enter Product Area Name"
                    type="text"
                    className="bg-white"
                    error={formikEdit.touched.areaName && !!formikEdit.errors.areaName}
                    errorText={formikEdit.errors.areaName}
                    onChange={formikEdit.handleChange}
                    onBlur={formikEdit.handleBlur}
                    value={formikEdit.values.areaName}
                    required
                  />
                </div>
                <div>
                  <label>Description</label>
                  <TextField
                    name="description"
                    type="text"
                    placeholder="Enter Description"
                    className="bg-white"
                    error={formikEdit.touched.description && !!formikEdit.errors.description}
                    errorText={formikEdit.errors.description}
                    onChange={formikEdit.handleChange}
                    onBlur={formikEdit.handleBlur}
                    value={formikEdit.values.description}
                    required
                  />
                </div>

                <Button type="submit" variant="primary" classNames="btn rounded-xl">
                  Update
                </Button>
              </Form>
            </FormikProvider>
          </>
        </Modal>
      )}
    </Fragment>
  );
};

export default UtilitiesArea;
