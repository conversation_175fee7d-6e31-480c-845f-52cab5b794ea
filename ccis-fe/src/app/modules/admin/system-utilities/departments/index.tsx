import { FC, Fragment, useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { TableColumn } from "react-data-table-component";
import { RootState } from "@state/store";
import { CiEdit, CiTrash } from "react-icons/ci";
import { IActions } from "@interface/common.interface";
import Table from "@components/common/Table";
import Modal from "@components/common/Modal";
import ActionButtons from "@components/common/ActionButtons";
import { confirmDelete, showSuccess } from "@helpers/prompt";
import { useDepartmentsManagementActions } from "@state/reducer/utilities-departments";
import { IUtilitiesDepartment } from "@interface/utilities.interface";
import { Form, FormikProvider, useFormik } from "formik";
import TextField from "@components/form/TextField";
import Button from "@components/common/Button";
import { CreateDepartmentSchema, EditDepartmentSchema } from "@services/utilities-departments/utilities-departments.schema";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { IDivisionPayload } from "@state/types/form-inventory-utilities-divisions";
import CustomTextField from "@components/common/CustomTextFieldWithSearch";

const DepartmentsTable: FC = () => {
  const [create, setCreate] = useState<boolean>(false);
  const [edit, setEdit] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>("");

  const SuccessPostDepartment = useSelector((state: RootState) => state.utilitiesDepartments.postDepartment?.success);
  const SuccessPutDepartment = useSelector((state: RootState) => state.utilitiesDepartments.putDepartment?.success);
  const SuccessDestroyDepartment = useSelector((state: RootState) => state.utilitiesDepartments.destroyDepartment?.success);

  const departments = useSelector((state: RootState) => state.utilitiesDepartments.departments);
  const loading = useSelector((state: RootState) => state?.utilitiesDepartments?.getDepartment?.loading);
  const { getDepartment, postDepartment, putDepartment, destroyDepartment, setSelectedDepartment } = useDepartmentsManagementActions();
  const { getDivisions } = useDivisionActions();

  const divisions = useSelector((state: RootState) => state?.formInventoryUtilitiesDivisions?.divisions);

  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const actionEvents: IActions<IUtilitiesDepartment>[] = [
    {
      name: "Edit",
      event: (row: IUtilitiesDepartment, index: number) => {
        const data = {
          id: row.id,
          departmentCode: row.departmentCode,
          departmentName: row.departmentName,
          description: row.description ?? "",
          divisionId: row.divisionId ?? 0,
        };
        formikEdit.setValues({
          id: data.id,
          departmentCode: data.departmentCode,
          departmentName: data.departmentName,
          description: data.description ?? "",
          divisionId: data.divisionId ?? 0,
        });
        setSelectedDepartment({ data: data, index: index });
        handleToggleEditModal();
      },
      icon: CiEdit,
      color: "primary",
    },
    {
      name: "Delete",
      event: (row: IUtilitiesDepartment, index: number) => {
        const action = confirmDelete(row.departmentName);
        action.then((value) => {
          if (value.isConfirmed) {
            destroyDepartment({ id: row.id, index: index });
          }
        });
      },
      icon: CiTrash,
      color: "danger",
    },
  ];

  const columns: TableColumn<IUtilitiesDepartment>[] = [
    {
      name: "Code",
      selector: (row) => row.departmentCode,
      ...commonSetting,
    },
    {
      name: "Deparment",
      cell: (row) => row.departmentName,
    },
    {
      name: "Description",
      cell: (row) => row.description,
    },
    {
      name: "Division",
      cell: (row) => {
        const division = divisions?.find((div: IDivisionPayload) => div.id === row.divisionId);
        return division ? division.divisionName : "";
      },
    },
    {
      name: "Action",
      cell: (row, rowIndex) => <ActionButtons data={row} rowIndex={rowIndex} actions={actionEvents} />,
    },
  ];

  const handleToggleCreateModal = () => {
    setCreate((prev) => !prev);
    formik.resetForm();
  };

  const handleToggleEditModal = () => {
    setEdit((prev) => !prev);
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  const handlePaginate = (value: any) => {
    console.log(value);
  };

  useEffect(() => {
    getDepartment({ filter: searchText });
    getDivisions({ filter: "" });
  }, [searchText]);

  const formik = useFormik({
    initialValues: {
      departmentCode: "",
      departmentName: "",
      description: "",
      divisionId: 0,
    },
    validationSchema: CreateDepartmentSchema,
    onSubmit: async (values, { resetForm }) => {
      postDepartment(values);
      handleToggleCreateModal();
      resetForm();
    },
  });

  const formikEdit = useFormik({
    initialValues: {
      id: 0,
      departmentCode: "",
      departmentName: "",
      description: "",
      divisionId: 0,
    },
    validationSchema: EditDepartmentSchema,
    onSubmit: async (values) => {
      putDepartment(values as any);
      handleToggleEditModal();
    },
  });

  useEffect(() => {
    if (SuccessPostDepartment) {
      showSuccess("Success", "Department has been added");
    }
  }, [SuccessPostDepartment]);

  useEffect(() => {
    if (SuccessPutDepartment) {
      showSuccess("Success", "Department has been updated");
    }
  }, [SuccessPutDepartment]);

  useEffect(() => {
    if (SuccessDestroyDepartment) {
      showSuccess("Success", "Department has been deleted").then((result) => {
        if (result.isConfirmed) {
          window.location.reload();
        }
      });
    }
  }, [SuccessDestroyDepartment]);

  return (
    <Fragment>
      <div className="text-xl font-semibold uppercase my-4">System Utilities / Deparments</div>
      <Table
        className="h-[400px] "
        columns={columns}
        data={departments}
        createLabel="Create New Department"
        onCreate={handleToggleCreateModal}
        loading={loading}
        onSearch={handleSearch}
        onPaginate={handlePaginate}
        multiSelect={false}
        searchable
      />
      {create && (
        <Modal title="Create New Department" modalContainerClassName="max-w-3xl " titleClass="text-primary text-lg uppercase" isOpen={create} onClose={handleToggleCreateModal}>
          <>
            <FormikProvider value={formik}>
              <Form className="flex flex-col my-4 gap-4">
                <div>
                  {" "}
                  <label>Department Code</label>
                  <TextField
                    name="departmentCode"
                    placeholder="Enter Department Code"
                    type="text"
                    className="bg-white"
                    error={formik.touched.departmentCode && !!formik.errors.departmentCode}
                    errorText={formik.errors.departmentCode}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.departmentCode}
                    required
                  />
                </div>
                <div>
                  {" "}
                  <label>Department Name</label>
                  <TextField
                    name="departmentName"
                    placeholder="Enter Department Name"
                    type="text"
                    className="bg-white"
                    error={formik.touched.departmentName && !!formik.errors.departmentName}
                    errorText={formik.errors.departmentName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.departmentName}
                    required
                  />
                </div>
                <div>
                  <label>Description</label>
                  <TextField
                    name="description"
                    type="text"
                    placeholder="Enter Description"
                    className="bg-white"
                    error={formik.touched.description && !!formik.errors.description}
                    errorText={formik.errors.description}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.description}
                  />
                </div>

                <div>
                  <label>Division</label>
                  <CustomTextField
                    suggestionOptions={divisions || []} // Pass divisions array, fallback to empty array if undefined
                    getOptionLabel={(item: IDivisionPayload) => item.divisionName} // Display divisionName in the input and suggestions
                    getOptionValue={(item: IDivisionPayload) => item.id.toString()} // Store division id as the value
                    size="sm"
                    name="divisionId"
                    placeholder="Select division"
                    value={formik.values.divisionId}
                    onChange={formik.handleChange}
                    error={formik.touched.divisionId && !!formik.errors.divisionId}
                    errorText={formik.errors.divisionId}
                    required
                  />
                </div>

                <Button type="submit" variant="primary" classNames="btn rounded-xl">
                  Save
                </Button>
              </Form>
            </FormikProvider>
          </>
        </Modal>
      )}
      {edit && (
        <Modal title="Edit Department" modalContainerClassName="max-w-3xl" titleClass="text-primary text-lg uppercase" isOpen={edit} onClose={handleToggleEditModal}>
          <>
            <FormikProvider value={formikEdit}>
              <Form className="flex flex-col my-4 gap-4">
                <div>
                  {" "}
                  <label>Department Code</label>
                  <TextField
                    name="departmentCode"
                    placeholder="Enter Department Code"
                    type="text"
                    className="bg-white"
                    error={formikEdit.touched.departmentCode && !!formikEdit.errors.departmentCode}
                    errorText={formikEdit.errors.departmentCode}
                    onChange={formikEdit.handleChange}
                    onBlur={formikEdit.handleBlur}
                    value={formikEdit.values.departmentCode}
                    required
                  />
                </div>
                <div>
                  {" "}
                  <label>Department Name</label>
                  <TextField
                    name="departmentName"
                    placeholder="Enter Department Name"
                    type="text"
                    className="bg-white"
                    error={formikEdit.touched.departmentName && !!formikEdit.errors.departmentName}
                    errorText={formikEdit.errors.departmentName}
                    onChange={formikEdit.handleChange}
                    onBlur={formikEdit.handleBlur}
                    value={formikEdit.values.departmentName}
                    required
                  />
                </div>
                <div>
                  <label>Description</label>
                  <TextField
                    name="description"
                    placeholder="Enter Description"
                    type="text"
                    className="bg-white"
                    error={formikEdit.touched.description && !!formikEdit.errors.description}
                    errorText={formikEdit.errors.description}
                    onChange={formikEdit.handleChange}
                    onBlur={formikEdit.handleBlur}
                    value={formikEdit.values.description}
                    required
                  />
                </div>
                <div>
                  <label>Division</label>
                  <CustomTextField
                    suggestionOptions={divisions || []}
                    getOptionLabel={(item: IDivisionPayload) => item.divisionName}
                    getOptionValue={(item: IDivisionPayload) => item.id.toString()}
                    size="sm"
                    name="divisionId"
                    placeholder="Select division"
                    value={formikEdit.values.divisionId}
                    onChange={formikEdit.handleChange}
                    error={formikEdit.touched.divisionId && !!formikEdit.errors.divisionId}
                    errorText={formikEdit.errors.divisionId}
                  />
                </div>

                <Button type="submit" variant="primary" classNames="btn rounded-xl">
                  Update
                </Button>
              </Form>
            </FormikProvider>
          </>
        </Modal>
      )}
    </Fragment>
  );
};

export default DepartmentsTable;
