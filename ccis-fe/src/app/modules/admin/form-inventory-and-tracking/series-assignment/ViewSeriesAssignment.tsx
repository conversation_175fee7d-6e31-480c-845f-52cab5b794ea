import Button from "@components/common/Button";
import Typography from "@components/common/Typography";
import Loader from "@components/Loader";
import React, { useEffect, useState } from "react";
import { FaCircleDot, FaClockRotateLeft } from "react-icons/fa6";
import { LiaDotCircleSolid } from "react-icons/lia";
import { useNavigate, useParams } from "react-router-dom";
import Pagination from "../new-forms/incoming/components/pagination";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import Select from "@components/form/Select";
import { toast } from "react-toastify";
import { ROUTES } from "@constants/routes";
import { useUserManagementActions } from "@state/reducer/users-management";
import { findItem } from "@helpers/array";
import { useAreaAdminActions } from "@state/reducer/form-inventory-utilities-area-admins";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { IDefaultParams } from "@interface/common.interface";
import { IFormTransmittal } from "@interface/form-inventory.interface";
import { Position, RoleType } from "@enums/form-status";

const ViewSeriesAssignment: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [currentPage, setCurrentPage] = useState(1);

  // Get data from Redux state
  const formsResponse = useSelector((state: RootState) => state.formInventoryTransmittal.getTransmittalForm);
  const data = (formsResponse?.data) as IFormTransmittal || null;
  const formsLoading = formsResponse?.loading || false;
  const formsError = formsResponse?.error || false;

  const activityLogsResponse = useSelector((state: RootState) => state.formInventoryTransmittal.getFormActivityLogs);
  const activityLogs = activityLogsResponse?.data || [];
  const activityLogsLoading = activityLogsResponse?.loading || false;

  const itemsPerPage = 1; // Number of items per page
  const totalPages = data && data.padAssignments ? Math.ceil(data.padAssignments.length / itemsPerPage) : 0;

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const users = useSelector((state: RootState) => state.usersManagement.users);
  const paginatedPadAssignments = data?.padAssignments?.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage) || [];

  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const formTypes = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);

  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  // const areaAdmins = useSelector((state: RootState) => state.formInventoryUtilitiesAreaAdmins.areaAdmins);
  const [filterDivision] = useState("");
  const [filterFormType] = useState("");
  const [filterArea] = useState("");
  const { getFormActivityLogs, getTransmittalForm } = useTransmittalFormActions();
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const { getUsers } = useUserManagementActions();
  const { getAreaAdmins } = useAreaAdminActions();

  const fetchForm = () => {
    if (id) {
      getTransmittalForm({ id: Number(id) });
    }
  };

  useEffect(() => {
    fetchForm();
  }, [id]);

  useEffect(() => {
    getDivisions({ filter: filterDivision });
    getFormTypes({ filter: filterFormType });
    getAreas({ filter: filterArea });
    getUsers({ relations: `${Position.CASHIER},${Position.AREA_ADMIN},${RoleType.ADMINOUTGOING},${RoleType.CLIFSA}` });
    getAreaAdmins({ filter: "" });
  }, []);

  useEffect(() => {
    if (data && data.padAssignments && data.padAssignments.length > 0) {
      const currentPadAssignment = data.padAssignments[currentPage - 1];
      if (currentPadAssignment) {
        const padAssignmentId = currentPadAssignment.id;
        getFormActivityLogs({ id: Number(padAssignmentId) } as IDefaultParams);
      }
    }
  }, [currentPage, data]);

  // Handle error state
  useEffect(() => {
    if (formsError) {
      toast.error("Failed to load proposal data. Please try again later.");
    }
  }, [formsError]);

  return formsLoading || activityLogsLoading ? (
    <div className="flex justify-center items-center h-screen">
      <Loader />
    </div>
  ) : (
    <div>
      <Button classNames="btn bg-slate-600 btn-sm" onClick={() => navigate(ROUTES.CHIEFCASHIER.seriesAssignment.key)}>
        Back
      </Button>
      <div className="mx-6">
        <Typography className="mt-6 text-primary font-poppins-semibold">RELEASED SERIES DETAILS</Typography>
        <div className=" mt-8 gap-4 flex justify-center">
          <div className="w-5/6 ">
            <div className="flex justify-end gap-2">
              <div>
                <p className="mt-1">Filter by:</p>
              </div>
              <div className="">
                <Select
                  className="select select-bordered select-sm w-full max-w-xs"
                  value={currentPage}
                  options={data?.padAssignments?.map((assignment, index) => ({
                    text: `Pad #: ${assignment.padNumber}`,
                    value: index + 1,
                  }))}
                  onChange={(e) => handlePageChange(Number(e.target.value))}
                />
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="divider divider-start">Series Overview</div>
            </div>
            <div className="border rounded border-slate-300 p-6">
              <div className="grid grid-rows-2 grid-flow-col gap-4">
                <div className="p-2">
                  <p className="text-sm">Transmittal No.</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    {" "}
                    <p>{data?.transmittalNumber}</p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Division</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    {" "}
                    <p>{String(findItem(divisions, "id", Number(data?.padAssignments?.[0]?.form?.divisionId), "divisionName") || "N/A")}</p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Type</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    {" "}
                    <p>{String(findItem(formTypes, "id", Number(data?.padAssignments?.[0]?.form?.formTypeId), "formTypeCode") || "N/A")}</p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Released By</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    {" "}
                    <p>
                      {data?.padAssignments?.[0]?.form?.createdBy
                        ? `${data.padAssignments[0].form.createdBy.firstname} ${data.padAssignments[0].form.createdBy.middlename || ""} ${data.padAssignments[0].form.createdBy.lastname}`
                        : data?.createdBy
                          ? `${data.createdBy.firstname} ${data.createdBy.middlename || ""} ${data.createdBy.lastname}`
                          : "N/A"}
                    </p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Area Released</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    {" "}
                    <p>{String(findItem(area, "id", Number(data?.releasedAreaId), "areaName") || "N/A")}</p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Released To</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    <p>{users.length > 0 ? users.find((user) => user.id === data?.releasedToId)?.firstname + " " + users.find((user) => user.id === data?.releasedToId)?.lastname : "N/A"}</p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Area</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    <p>{area.length > 0 ? area.find((a) => a.id === data?.padAssignments?.[0]?.form?.areaId)?.areaName : "N/A"}</p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">ATP No.</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    {" "}
                    <p>{data?.padAssignments?.[0]?.form?.atpNumber}</p>
                  </div>
                </div>
              </div>
              <div className="p-2">
                <p className="text-sm">Released Date</p>
                <div className="border-b-2 border-slate-300 w-32 text-sm">
                  {" "}
                  <p>{data?.createdAt ? new Date(data.createdAt).toLocaleDateString("en-US") : "N/A"}</p>
                </div>
              </div>
            </div>
            <div className="border rounded-md border-slate-300 p-2 mt-4 mb-4 flex w-full">
              <table className="w-full">
                <thead className="bg-gradient-to-r from-zinc-50 to-indigo-50 p-4">
                  <tr>
                    <th className="p-4 text-sm border-zinc-100">Pad Number</th>
                    <th className="p-4 text-sm">Series From</th>
                    <th className="p-4 text-sm">Series To</th>
                  </tr>
                </thead>
                <tbody>
                  {paginatedPadAssignments.map((assignment) => (
                    <tr key={assignment.id}>
                      <td className="p-4 text-sm border border-slate-100 text-center">{assignment.padNumber}</td>
                      <td className="p-4 text-sm border border-slate-100 text-center">{assignment.seriesFrom}</td>
                      <td className="p-4 text-sm border border-slate-100 text-center">{assignment.seriesTo}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
              <div className="divider divider-horizontal"></div>
              <div className="bg-slate-50 p-4 min-w-96 w-full">
                <div className="p-2 flex justify-center bg-white rounded mb-2">Remarks</div>
                <div className="bg-white p-4 text-sm rounded">
                  {data?.remarks?.split("\n").map((line, index) => (
                    <p key={index}>-{line}</p>
                  ))}
                </div>
              </div>
            </div>
            <Pagination currentPage={currentPage} totalPages={totalPages} onPageChange={handlePageChange} />
          </div>

          <div className="col-span-1 rounded border border-slate-300 max-w-3/5 p-4 max-h-screen overflow-y-auto">
            <div className="border-b-2 border-slate-200  p-4 justify-center flex gap-2 mb-2">
              <FaClockRotateLeft className="text-sm mt-1" /> <p>Status</p>
            </div>
            {Array.isArray(activityLogs) &&
              activityLogs
                .slice()
                .reverse()
                .map((log, index) => (
                  <React.Fragment key={log.id}>
                    <div className="flex gap-4">
                      <div className="flex gap-4">
                        <div>
                          <div className="text-xs text-end">{new Date(log.created_at).toLocaleDateString("en-US")}</div>
                          <div className="text-xs text-slate-400 text-end">
                            {new Date(log.created_at).toLocaleTimeString("en-US", {
                              hour: "2-digit",
                              minute: "2-digit",
                            })}
                          </div>
                        </div>
                        <div>
                          <div>
                            {index === 0 ? <FaCircleDot className="text-primary size-6 mt-1" /> : <LiaDotCircleSolid className="text-zinc-400 size-6 mt-1" />}
                            {index !== activityLogs.length - 1 && (
                              <div className="flex mt-1 flex-col items-center">
                                <div className="h-8 border-l-2 border-slate-300"></div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      <div>
                        <div>{log.description}</div>
                        <div className="text-xs">
                          {log.causer.firstname} {log.causer.lastname} | {log.causer.position.positionName}
                        </div>
                      </div>
                    </div>
                  </React.Fragment>
                ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewSeriesAssignment;
