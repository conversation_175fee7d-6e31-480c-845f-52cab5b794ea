import Table from "@components/common/Table";
import React, { useEffect, useState } from "react";
import { TableColumn } from "react-data-table-component";
import Typography from "@components/common/Typography";
import ActionDropdown from "@components/common/ActionDropdown";
import { IActions, IDefaultParams } from "@interface/common.interface";
import { GoVersions } from "react-icons/go";
import { useNavigate } from "react-router-dom";
import { RootState } from "@state/reducer";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { useSelector } from "react-redux";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { getTextStatusColor } from "@helpers/text";
import { ROUTES } from "@constants/routes";
import { IFormTransmittal } from "@interface/form-inventory.interface";
import { FormStatus } from "@enums/form-status";

interface IncomingTableProps {
  searchText?: string;
  divisionFilter?: number;
  statusFilter?: string;
  type?: any;
}
const SeriesAssignmentTable: React.FC<IncomingTableProps> = ({ searchText, divisionFilter, statusFilter, type }) => {
  const navigate = useNavigate();

  const { getTransmittalForms } = useTransmittalFormActions();

  const loading = useSelector((state: RootState) => state.formInventoryTransmittal.getTransmittalForms.loading);
  const { data: FORMS } = useSelector((state: RootState) => state.formInventoryTransmittal.getTransmittalForms);
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const formTypes = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const userId = useSelector((state: RootState) => state?.auth?.user.data?.id);
  const [filterDivision] = useState("");
  const [filterFormType] = useState("");
  const [filterArea] = useState("");
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const getActionEvents = (SeriesAssignment: any): IActions<any>[] => {
    const actions: IActions<any>[] = [
      {
        name: "View",
        event: () => {
          navigate(ROUTES.CHIEFCASHIER.viewSeriesAssignment.parse(SeriesAssignment.id));
        },
        icon: GoVersions,
        color: "primary",
      },
    ];

    return actions;
  };
  // Get data with approved status from  the product proposal table
  const columns: TableColumn<any>[] = [
    {
      name: "No.",
      cell: (row) => row.id,
      width: "80px",
      ...commonSetting,
    },
    {
      name: "Transmittal No.",
      cell: (row) => row.transmittalNumber,
      ...commonSetting,
    },
    {
      name: "Division",
      cell: (row) => {
        const division = divisions.find((division) => division.id === row.padAssignments?.[0]?.form?.divisionId);
        return division ? division.divisionName : "N/A";
      },
      ...commonSetting,
    },
    {
      name: "Type",
      cell: (row) => {
        const formType = formTypes.find((type) => type.id === row.padAssignments?.[0]?.form?.formTypeId);
        return formType ? formType.formTypeCode : "N/A";
      },
      ...commonSetting,
    },
    {
      name: "Released Area",
      cell: (row) => {
        const releasedArea = area.find((area) => area.id === row.padAssignments?.[0]?.form?.areaId);
        return releasedArea ? releasedArea.areaName : "N/A";
      },
      ...commonSetting,
    },
    {
      name: "No. of Pads",
      cell: (row) => row.padAssignments?.length,
    },
    {
      name: "Status",
      cell: (row) => {
        const rowStatus = row?.status;
        const trail = row?.formTransmittalTrails?.find((item: any) => item.createdBy?.id === userId);
        const status = trail ? trail.status : "No Status";

        let displayStatus = status;

        if (rowStatus !== FormStatus.APPROVED) {
          displayStatus = rowStatus;
        } else if (status === FormStatus.RECEIVED) {
          displayStatus = FormStatus.RECEIVED;
        } else {
          displayStatus = FormStatus.NOT_YET_RECEIVED;
        }

        return (
          <Typography className={displayStatus === FormStatus.RECEIVED ? getTextStatusColor(FormStatus.RECEIVED) : getTextStatusColor(FormStatus.NOT_YET_RECEIVED)}>
            {displayStatus
              ?.replace(/_/g, " ")
              .toLowerCase()
              .replace(/\b\w/g, (char: string) => char.toUpperCase())}
          </Typography>
        );
      },
    },
    // {
    //   name: "Status",
    //   cell: (row) => (
    //     <Typography size="xs" className={getTextStatusColor(row.status)}>
    //       {row.status !== FormStatus.APPROVED
    //         ? capitalizeFirstLetterWords(row.status, "_") :
    //         capitalizeFirstLetterOnly(
    //             FormStatus.NOT_YET_RECEIVED.replace(/_/g, " ")
    //           )}
    //     </Typography>
    //   ),
    // },
    {
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />,
    },
  ];
  const handleRowsChange = (newPerPage: number, pagination: number) => {
    setPageSize(newPerPage);
    setPage(pagination);
  };

  const handlePaginate = (page: number) => {
    setPage(page);
  };

  const sortedTransmittal = FORMS?.data?.slice().sort((a: IFormTransmittal, b: IFormTransmittal) => {
    return Number(b.id) - Number(a.id); // Explicitly convert to number
  });

  const fetchForms = () => {
    const payload = {
      page: page,
      pageSize: pageSize,
      filter: searchText,
      divisionFilter: divisionFilter,
      type: type,
      statusFilter: statusFilter,
      exludeReturned: true,
    } as IDefaultParams;
    getTransmittalForms(payload);
  };

  useEffect(() => {
    const getTransmittalForms = setTimeout(() => {
      fetchForms();
    }, 500);
    return () => clearTimeout(getTransmittalForms);
  }, [searchText, divisionFilter, statusFilter, type, page, pageSize]);

  useEffect(() => {
    getDivisions({ filter: filterDivision });
    getFormTypes({ filter: filterFormType });
    getAreas({ filter: filterArea });
    fetchForms();
  }, []);

  return (
    <div>
      <Table
        className="!min-h-[100%] h-[500px] border-[1px] border-zinc-300 mt-8"
        columns={columns}
        data={sortedTransmittal}
        paginationServer={true}
        paginationTotalRows={FORMS?.meta?.total}
        loading={loading}
        onChangeRowsPerPage={handleRowsChange}
        onPaginate={handlePaginate}
        searchable={false}
        multiSelect={false}
      />
    </div>
  );
};

export default SeriesAssignmentTable;
