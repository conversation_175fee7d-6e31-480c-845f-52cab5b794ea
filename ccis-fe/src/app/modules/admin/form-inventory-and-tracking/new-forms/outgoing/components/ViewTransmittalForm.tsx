import Button from "@components/common/Button";
import Typography from "@components/common/Typography";
import TextField from "@components/form/TextField";
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useAreaAdminActions } from "@state/reducer/form-inventory-utilities-area-admins";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import Loader from "@components/Loader";
import { Form, FormikProvider, useFormik } from "formik";
import { confirmSaveOrEdit } from "@helpers/prompt";
import { toast } from "react-toastify";
import { FormStatus, RoleType, Position } from "@enums/form-status";
import { usePositionsManagementActions } from "@state/reducer/utilities-positions";
import { useUserManagementActions } from "@state/reducer/users-management";
import { findItem } from "@helpers/array";
import { navigateBack } from "@helpers/navigatorHelper";
import TransmittalLetterDisplay from "./transmittal-letter-display";
import { FaPrint } from "react-icons/fa6";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { TFormTransmittalOutgoingPayload } from "@state/types/form-inventory-transmittal";
import { IFormTransmittal } from "@interface/form-inventory.interface";
import { UserArea } from "@enums/users-management";

const ViewTransmittalForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [formContainers, setFormContainers] = useState<any[]>([]);
  const [showLetterModal, setShowLetterModal] = useState<boolean>(false);

  // Redux state
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const formTypes = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const users = useSelector((state: RootState) => state.usersManagement.users);
  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const areaAdmins = useSelector((state: RootState) => state.formInventoryUtilitiesAreaAdmins.areaAdmins);
  const formsResponse = useSelector((state: RootState) => state.formInventoryTransmittal.getTransmittalForm);
  const data = (formsResponse?.data as IFormTransmittal) || null;
  const formsLoading = formsResponse?.loading || false;
  const formsError = formsResponse?.error || false;
  const profile = useSelector((state: RootState) => state.profile.profile);
  const postTrailSuccess = useSelector((state: RootState) => state.formInventoryTransmittal.postFormTransmittalTrail?.success);

  // Actions
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const { getAreaAdmins } = useAreaAdminActions();
  const { getUsers } = useUserManagementActions();
  const { getPosition } = usePositionsManagementActions();
  const { getTransmittalForm, postFormTransmittalTrail, clearSelectedTransmittalForm } = useTransmittalFormActions();

  // Check if the released area is HEADQUARTER
  const isHeadquarterArea = data?.releasedAreaId && area.find((a) => a.id === data.releasedAreaId)?.areaName?.toUpperCase() === UserArea.HEADQUARTER;

  const getReleasedToUser = () => {
    if (isHeadquarterArea) {
      // For HEADQUARTER, use the area admin
      const areaAdmin = areaAdmins.find((admin) => admin.userAreaId === data?.releasedAreaId);
      return users.find((user) => user.id === areaAdmin?.userId);
    } else {
      // For other areas, use ADMINOUTGOING
      return users.find((user) => user.position?.positionName === RoleType.ADMINOUTGOING);
    }
  };

  const releasedToUser = getReleasedToUser();

  // Formik setup
  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      id: Number(id) || 0,
      releasedTo: 0,
      status: FormStatus.RELEASED,
    },
    onSubmit: async (values, { resetForm }) => {
      const isConfirmed = await confirmSaveOrEdit("Are you sure you want to release this transmittal?");

      if (isConfirmed) {
        try {
          if (!id) {
            toast.error("Missing form transmittal ID.");
            return;
          }

          // Ensure we have the latest releasedTo value
          const finalValues = {
            ...values,
            id: Number(id),
            releasedTo: values.releasedTo || releasedToUser?.id || data?.releasedToId || 0,
          };

          await postFormTransmittalTrail(finalValues as TFormTransmittalOutgoingPayload);
          resetForm();
        } catch (error) {
          toast.error("Failed to release transmittal. Please try again.");
        }
      }
    },
  });

  // Effects
  useEffect(() => {
    if (postTrailSuccess) {
      navigateBack();
      clearSelectedTransmittalForm();
    }
  }, [postTrailSuccess]);

  const fetchForm = () => {
    if (id) {
      getTransmittalForm({ id: Number(id) });
    }
  };

  useEffect(() => {
    getUsers({ relations: `${Position.CASHIER},${Position.AREA_ADMIN},${RoleType.ADMINOUTGOING},${RoleType.CLIFSA}` });
    fetchForm();
  }, [id]);

  useEffect(() => {
    if (formsError) {
      toast.error("Failed to load proposal data. Please try again later.");
    }
  }, [formsError]);

  useEffect(() => {
    if (data && !formContainers.some((f) => f.id === data.id)) {
      setFormContainers((prev) => [...prev, data]);
    }
  }, [data]);

  useEffect(() => {
    getDivisions({ filter: "" });
    getFormTypes({ filter: "" });
    getAreas({ filter: "" });
    getAreaAdmins({ filter: "" });
    getPosition({ params: { filter: "" } });
  }, []);

  // Update formik values when data is loaded
  useEffect(() => {
    if (data && releasedToUser?.id) {
      formik.setFieldValue("releasedTo", releasedToUser.id);
      formik.setFieldValue("id", data.id);
    } else if (data?.releasedToId) {
      formik.setFieldValue("releasedTo", data.releasedToId);
      formik.setFieldValue("id", data.id);
    }
  }, [data, releasedToUser]);

  const handlePrintPDF = () => {
    if (!data?.id) {
      toast.error("Missing Form Transmittal ID.");
      return;
    }
    setShowLetterModal(true);
  };

  // Helper function to format user name
  const formatUserName = (user: any) => {
    return user ? `${user.firstname || "N/A"} ${user.middlename || ""} ${user.lastname || ""}`.trim() : "N/A";
  };

  return formsLoading ? (
    <div className="flex justify-center items-center h-screen">
      <Loader />
    </div>
  ) : (
    <div>
      <Button classNames="btn bg-slate-600 btn-sm" onClick={() => navigateBack()}>
        Back
      </Button>
      <div className="mx-6">
        <Typography className="mt-6 text-primary uppercase font-poppins-semibold-">Transmittal Details</Typography>
        <div className="flex justify-end gap-2">
          <Button classNames="btn btn-sm bg-slate-600 text-slate-900" onClick={handlePrintPDF}>
            <FaPrint /> Print
          </Button>
        </div>
        <div className="mt-8">
          <FormikProvider value={formik}>
            <Form>
              <div className="flex w-full flex-col">
                <div className="divider divider-start uppercase">Assignee Details</div>
              </div>

              <div>
                <div className="p-6">
                  <div className="flex flex-wrap gap-4">
                    <div className="p-2 flex-1">
                      <p className="text-sm">Released by</p>
                      <div className="w-60 text-sm">
                        <TextField disabled className="w-auto" size="sm" value={profile ? formatUserName(profile) : "N/A"} />
                      </div>
                    </div>
                    <div className="p-2 flex-1">
                      <p className="text-sm">Release To</p>
                      <div className="w-60 text-sm">
                        <TextField className="w-48" disabled size="sm" name="releasedTo" value={releasedToUser ? formatUserName(releasedToUser) : "N/A"} />
                        {/* Hidden field to store the actual value */}
                        <input type="hidden" name="releasedTo" value={formik.values.releasedTo} />
                      </div>
                    </div>
                    <div className="p-2 flex-1">
                      <p className="text-sm">Date Released</p>
                      <div className="w-60 text-sm">
                        <TextField
                          className="w-48"
                          value={new Date().toLocaleDateString("en-US", {
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          })}
                          size="sm"
                          disabled
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex w-full flex-col">
                <div className="divider divider-start uppercase">Series Overview</div>
              </div>

              <div>
                <div className="p-6">
                  <div className="flex flex-wrap gap-4">
                    <div className="p-2 flex-1">
                      <p className="text-sm">Released by</p>
                      <div className="w-60 text-sm">
                        <TextField disabled className="w-auto" size="sm" value={data?.createdBy ? formatUserName(data.createdBy) : "N/A"} />
                      </div>
                    </div>
                    <div className="p-2 flex-1">
                      <p className="text-sm">Area Released</p>
                      <div className="w-60 text-sm">
                        <TextField className="w-48" disabled size="sm" value={area.find((a) => a.id === data?.releasedAreaId)?.areaName || "N/A"} />
                      </div>
                    </div>
                    <div className="p-2 flex-1">
                      <p className="text-sm">Released To</p>
                      <div className="w-60 text-sm">
                        <TextField className="w-48" disabled size="sm" value={areaAdmins.find((admin) => admin.userId === data?.releasedToId)?.adminName || "N/A"} />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="border rounded-md border-slate-300 p-2">
                  <div className="p-6">
                    <div className="grid grid-cols-5 gap-4">
                      <div className="p-2">
                        <p className="text-sm">Transmittal No.</p>
                        <div className="border-b-2 border-slate-300 w-32 text-sm">
                          <p>{data?.transmittalNumber}</p>
                        </div>
                      </div>
                      <div className="p-2">
                        <p className="text-sm">Division</p>
                        <div className="border-b-2 border-slate-300 w-32 text-sm">
                          <p>{String(findItem(divisions, "id", Number(data?.padAssignments?.[0]?.form?.divisionId), "divisionName") || "N/A")}</p>
                        </div>
                      </div>
                      <div className="p-2">
                        <p className="text-sm">Type</p>
                        <div className="border-b-2 border-slate-300 w-32 text-sm">
                          <p>{String(findItem(formTypes, "id", Number(data?.padAssignments?.[0]?.form?.formTypeId), "formTypeCode") || "N/A")}</p>
                        </div>
                      </div>
                      <div className="p-2">
                        <p className="text-sm">Area</p>
                        <div className="border-b-2 border-slate-300 w-32 text-sm">
                          <p>{String(findItem(area, "id", Number(data?.padAssignments?.[0]?.form?.areaId), "areaName") || "N/A")}</p>
                        </div>
                      </div>
                      <div className="p-2">
                        <p className="text-sm">ATP No.</p>
                        <div className="border-b-2 border-slate-300 w-32 text-sm">
                          <p>{data?.padAssignments?.[0]?.form?.atpNumber}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 mb-4 flex w-full">
                    <div className="overflow-auto max-h-64 w-full">
                      <table className="w-full">
                        <thead className="bg-gradient-to-r from-zinc-50 to-indigo-50 p-4 sticky top-0 z-10">
                          <tr>
                            <th className="p-4 text-sm border-zinc-100">Pad Number</th>
                            <th className="p-4 text-sm">Series From</th>
                            <th className="p-4 text-sm">Series To</th>
                          </tr>
                        </thead>
                        <tbody>
                          {data?.padAssignments?.map((assignment) => (
                            <tr key={assignment.id}>
                              <td className="p-4 text-sm border border-slate-100 text-center">{assignment.padNumber}</td>
                              <td className="p-4 text-sm border border-slate-100 text-center">{assignment.seriesFrom}</td>
                              <td className="p-4 text-sm border border-slate-100 text-center">{assignment.seriesTo}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                    <div className="divider divider-horizontal"></div>
                    <div className="bg-slate-50 p-4 min-w-96 w-full">
                      <div className="p-2 flex justify-center bg-white rounded mb-2">Remarks</div>
                      <div className="bg-white p-4 text-sm rounded">
                        {data?.remarks?.split("\n").map((line, index) => (
                          <p key={index}>-{line}</p>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-center mt-6">
                <Button classNames="btn bg-slate-400 btn-sm mr-2 w-48" onClick={() => navigateBack()} type="button">
                  Cancel
                </Button>
                <Button classNames="btn btn-sm w-48 bg-primary" type="submit" disabled={!formik.values.releasedTo || formik.values.releasedTo === 0}>
                  Release
                </Button>
              </div>
            </Form>
          </FormikProvider>
        </div>
      </div>

      <TransmittalLetterDisplay formTransmittalId={data} isOpen={showLetterModal} onClose={() => setShowLetterModal(false)} />
    </div>
  );
};

export default ViewTransmittalForm;
