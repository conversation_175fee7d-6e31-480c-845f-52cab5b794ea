import httpClient from "@clients/httpClient";
import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import { Document, Page, pdfjs } from "react-pdf";

// Configure PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = new URL("pdfjs-dist/build/pdf.worker.min.mjs", import.meta.url).toString();
interface TransmittalReportDisplayProps {
  formTransmittalId: any;
  isOpen: boolean;
  onClose: () => void;
}

export default function TransmittalLetterDisplay({ formTransmittalId, isOpen, onClose }: TransmittalReportDisplayProps) {
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [numPages, setNumPages] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch the PDF as a blob and create a URL
  const fetchLReportData = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!formTransmittalId?.id) {
        toast.error("Missing Form Transmittal ID.");
        return;
      }
      const formTransmittalIds = Array.isArray(formTransmittalId?.id) ? formTransmittalId.id : [formTransmittalId?.id];

      const response: any = await httpClient.post("/form-transmittal/export-report/outgoing-cashier", { formTransmittalIds }, { responseType: "blob" });
      // @ts-ignore
      const blob = new Blob([response], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);

      setPdfUrl(url);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load letter");
    } finally {
      setLoading(false);
    }
  };


  //FUTURE USE
  // const handlePrintReportPDF = async () => {
  //   try {
  //     if (!data?.id) {
  //       toast.error("Missing Form Transmittal ID.");
  //       return;
  //     }
  //     const formTransmittalIds = Array.isArray(data?.id) ? data.id : [data?.id];

  //     const response: any = await httpClient.post("/form-transmittal/export-report/outgoing-cashier", { formTransmittalIds }, { responseType: "blob" });

  //     // Use the same blob creation pattern as the working component
  //     const blob = new Blob([response], { type: "application/pdf" });
  //     const url = window.URL.createObjectURL(blob);
  //     window.open(url, "_blank");

  //     // Clean up the URL after a delay
  //     setTimeout(() => window.URL.revokeObjectURL(url), 1000);
  //   } catch (error) {
  //     console.error("Error generating PDF:", error);
  //     toast.error(`PDF export failed: ${String(error)}`);
  //   }
  // };

  // Print the PDF
  const handlePrint = () => {
    if (pdfUrl) {
      const printWindow = window.open(pdfUrl, "_blank");
      if (printWindow) {
        printWindow.onload = () => printWindow.print();
      }
    }
  };

  // Download the PDF
  // const handleDownload = () => {
  //   if (pdfUrl) {
  //     const a = document.createElement("a");
  //     a.href = pdfUrl;
  //     a.download = `transmittal-letter-${formTransmittalId?.id || "document"}.pdf`;
  //     document.body.appendChild(a);
  //     a.click();
  //     document.body.removeChild(a);
  //   }
  // };

  // Close modal and cleanup
  const handleClose = () => {
    if (pdfUrl) {
      window.URL.revokeObjectURL(pdfUrl);
      setPdfUrl(null);
    }
    setError(null);
    onClose();
  };

  // Auto-fetch when modal opens
  useEffect(() => {
    if (isOpen && !pdfUrl && !loading) {
      fetchLReportData();
    }
  }, [isOpen]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (pdfUrl) {
        window.URL.revokeObjectURL(pdfUrl);
      }
    };
  }, [pdfUrl]);

  // Close modal on Escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        handleClose();
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
      <div className="relative w-[95%] max-w-6xl bg-white rounded-lg shadow-xl overflow-hidden flex">
        {/* LEFT PANEL - PDF Viewer */}
        <div className="flex-1 p-6 bg-gray-50 overflow-y-auto">
          {loading && (
            <div className="flex justify-center items-center h-64">
              <div className="loading loading-spinner loading-lg"></div>
              <span className="ml-3 text-gray-600">Loading document...</span>
            </div>
          )}

          {error && (
            <div className="flex flex-col items-center justify-center h-64">
              <p className="text-red-600 mb-4">{error}</p>
              <button className="px-4 py-2 bg-purple-600 text-black rounded-lg hover:bg-purple-700" onClick={fetchLReportData}>
                Retry
              </button>
            </div>
          )}

          {pdfUrl && (
            <div className="border rounded-lg bg-white shadow-sm overflow-hidden p-4">
              <Document file={pdfUrl} onLoadSuccess={({ numPages }) => setNumPages(numPages)} className="flex flex-col items-center shadow-mb">
                {Array.from({ length: numPages }, (_, index) => (
                  <Page key={`page_${index + 1}`} pageNumber={index + 1} renderTextLayer={false} renderAnnotationLayer={false} className="mb-4" />
                ))}
              </Document>
            </div>
          )}
        </div>

        {/* RIGHT PANEL - Print Settings */}
        <div className="w-[30%] border-l border-gray-200 p-6 flex flex-col justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Print</h3>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Destination</label>
              <select className="w-full border rounded-md px-3 py-2 text-sm">
                <option>Microsoft Print to PDF</option>
                <option>Save as PDF</option>
                <option>Other Printer</option>
              </select>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Orientation</label>
              <div className="flex gap-4">
                <button className="flex-1 border rounded-md px-3 py-2 hover:bg-gray-100 text-sm">Portrait</button>
                <button className="flex-1 border rounded-md px-3 py-2 hover:bg-gray-100 text-sm">Landscape</button>
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Pages</label>
              <input type="text" placeholder="All" className="w-full border rounded-md px-3 py-2 text-sm" />
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Color Mode</label>
              <select className="w-full border rounded-md px-3 py-2 text-sm">
                <option>Color</option>
                <option>Black & White</option>
              </select>
            </div>
          </div>

          {/* Bottom Buttons */}
          <div className="flex justify-end gap-3">
            <button className="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-100" onClick={handleClose}>
              Cancel
            </button>
            <button className="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-700" onClick={handlePrint}>
              Print
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
