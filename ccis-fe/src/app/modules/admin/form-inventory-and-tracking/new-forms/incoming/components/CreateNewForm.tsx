import Button from "@components/common/Button";
import Select from "@components/form/Select";
import TextField from "@components/form/TextField";
import { RoleType } from "@enums/form-status";
import { confirmSaveOrEdit } from "@helpers/prompt";
import Attachments from "@modules/admin/products/components/Common/Attachments";
import { showProcessingToast } from "@modules/dashboard/RequestorDashboard/components/prompts/DepartmentalTicketingPrompts";
import { CreateIncomingReceivedSchema } from "@services/form-inventory-incoming-received-form/form-inventory-incoming-received-form.schema";
import { deleteAttachment, postIncomingReceivedFormService } from "@services/form-inventory-incoming-received-form/form-inventory-incoming-received-form.service";
import { RootState } from "@state/reducer";
import { removeAttachment } from "@state/reducer/form-inventory-incoming-received-form";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { Form, FormikProvider, useFormik } from "formik";
import React, { FC, Fragment, useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { toast } from "react-toastify";
import Swal from "sweetalert2";

type FormProps = {
  handleModal: () => void;
};

const CreateNewForm: FC<FormProps> = ({ handleModal }) => {
  const [seriesFrom, setSeriesFrom] = useState<number | string>("");
  const [seriesTo, setSeriesTo] = useState<number | string>("");
  const [numberOfPads, setNumberOfPads] = useState<number>(0);
  const [authorityToPrintFiles, setAuthorityToPrintFiles] = useState<Array<File>>([]);
  const [taxUsersSwornStatementFiles, setTaxUsersSwornStatementFiles] = useState<Array<File>>([]);
  const [copyOfFirstAndLastStubFiles, setCopyOfFirstAndLastStubFiles] = useState<Array<File>>([]);
  const [printersCertificateFiles, setPrintersCertificateFiles] = useState<Array<File>>([]);
  const [reset, setReset] = useState(0);

  // const formTypes = useSelector(
  //   (state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes
  // );
  // const areas = useSelector((state: RootState) => state.utilitiesAreas.areas);


  // Refs for scrolling to errors
  const divisionRef = useRef<HTMLDivElement>(null);
  const formTypeRef = useRef<HTMLDivElement>(null);
  const areaRef = useRef<HTMLDivElement>(null);
  const dateRef = useRef<HTMLDivElement>(null);
  const seriesFromRef = useRef<HTMLDivElement>(null);
  const seriesToRef = useRef<HTMLDivElement>(null);
  const atpRef = useRef<HTMLDivElement>(null);
  const attachmentsRef = useRef<HTMLDivElement>(null);

  const incomingReceivedAttachment = useSelector((state: RootState) => state.uatDetails.selectedUat.attachments);

  const formTypeOptions = [
    // { value: 0, text: "Select Form Type", disabled: true },
    ...useSelector(
      (state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes
    ).map((value) => {
      return { value: value.id, text: value.formTypeName };
    }),
  ];

  // TEMPORARILY HIDE OTHER FORM TYPES
  // const formTypeOptions = [
  //   { value: 0, text: "Select Form Type" },
  //   ...useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes)
  //     // ✅ Filter to only include "Provisional Receipt"
  //     .filter((value) => value.formTypeName === "Provisional Receipt")
  //     .map((value) => {
  //       return { value: value.id, text: value.formTypeName };
  //     }),
  // ];

  // const divisionOptions = formatSelectOptions(divisions, "divisionName");

  // HIDE OTHER DIVISIONS TEMPORARILY
  // const divisionOptions = [
  //   { value: 0, text: "Select Division" },
  //   ...useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions).map((value) => {
  //     return { value: value.id, text: value.divisionName };
  //   }),
  // ];

  const divisionOptions = [
    // { value: 0, text: "Select Division", disabled: true },
    ...useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions)
      // Filter to only include "Life"
      .filter((value) => value.divisionName === "Life")
      .map((value) => {
        return { value: value.id, text: value.divisionName };
      }),
  ];

  const areaOptions = [
    // { value: 0, text: "Select Area", disabled: true },
    ...useSelector((state: RootState) => state.utilitiesAreas.areas)
      .filter((value) => value.areaName !== RoleType.EXCLUDE_CLIFSA)
      .map((value) => {
        return { value: value.id, text: value.areaName };
      }),
  ];

  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const [attachmentOnProcess, setUatProcess] = useState<boolean>(false);

  // Function to scroll to the first error field
  const scrollToError = (errors: any) => {
    if (errors.divisionId && divisionRef.current) {
      divisionRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
      divisionRef.current.focus();
    } else if (errors.formTypeId && formTypeRef.current) {
      formTypeRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
    } else if (errors.areaId && areaRef.current) {
      areaRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
    } else if (errors.receivedDate && dateRef.current) {
      dateRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
    } else if (errors.seriesFrom && seriesFromRef.current) {
      seriesFromRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
    } else if (errors.seriesTo && seriesToRef.current) {
      seriesToRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
    } else if (errors.atpNumber && atpRef.current) {
      atpRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
    } else if (errors.attachments && attachmentsRef.current) {
      attachmentsRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  const formik = useFormik({
    initialValues: {
      id: 0,
      divisionId: 0,
      formTypeId: 0,
      areaId: 0,
      receivedDate: "",
      seriesFrom: 0,
      seriesTo: 0,
      atpNumber: "",
      noPads: 0,
      attachments: [],
    },
    validationSchema: CreateIncomingReceivedSchema,
    onSubmit: async (values, { resetForm }) => {
      // Validate form before submission
      const errors = await formik.validateForm();
      if (Object.keys(errors).length > 0) {
        scrollToError(errors);
        toast.error("Please fix the validation errors before submitting");
        return;
      }

      const isConfirmed = await confirmSaveOrEdit(`Are you sure you want to save this form?`);
      if (isConfirmed) {
        try {
          showProcessingToast("Creating form...");
          await postIncomingReceivedFormService(values);
          Swal.close();
          toast.success("Form successfully created");
          resetForm();
          handleModal();
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        } catch (error: any) {
          Swal.close();
          if (error?.response?.data?.status === "error" && error?.response?.data?.message) {
            toast.error(error.response.data.message);
          } else if (error?.response?.status === 413) {
            toast.error("File too large");
          } else {
            toast.error("Failed to submit form");
          }
        }
      }
    },
  });

  // Auto-scroll to error when validation errors change
  useEffect(() => {
    if (Object.keys(formik.errors).length > 0 && formik.submitCount > 0) {
      scrollToError(formik.errors);
    }
  }, [formik.errors, formik.submitCount]);

  useEffect(() => {
    getDivisions({ filter: "" });
    getFormTypes({ filter: "" });
    getAreas({ filter: "" });
  }, []);

  useEffect(() => {
    if (seriesFrom && seriesTo) {
      const from = Number(seriesFrom);
      const to = Number(seriesTo);
      if (!isNaN(from) && !isNaN(to)) {
        setNumberOfPads(calculateNumberOfPads(from, to));
      }
    }
  }, [seriesFrom, seriesTo]);

  const calculateNumberOfPads = (seriesFrom: number, seriesTo: number): number => {
    return Math.ceil((seriesTo - seriesFrom + 1) / 50);
  };

  useEffect(() => {
    if (seriesFrom && seriesTo) {
      const from = Number(seriesFrom);
      const to = Number(seriesTo);
      if (!isNaN(from) && !isNaN(to)) {
        const pads = calculateNumberOfPads(from, to);
        setNumberOfPads(pads);
        formik.setFieldValue("noPads", pads);
      }
    }
  }, [seriesFrom, seriesTo]);

  const handleFile = (acceptedFiles: Array<File>, setFiles: React.Dispatch<React.SetStateAction<Array<File>>>, attachmentType: string) => {
    setFiles(acceptedFiles);
    const fileArray = acceptedFiles.map((file) => ({
      file: file,
      label: `${attachmentType} File`,
      description: `${attachmentType} File`,
    }));
    formik.setFieldValue("attachments", [...formik.values.attachments, ...fileArray]);
  };

  const handleRemoveAttachmentFile = async (index: number, _files: Array<File>, setFiles: React.Dispatch<React.SetStateAction<Array<File>>>, _attachmentType: string) => {
    try {
      setUatProcess(true);
      const prevState = [...(incomingReceivedAttachment ?? [])];
      const targetFile = prevState[index];

      if (!targetFile) {
        setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
        return;
      }

      if (targetFile.id === undefined) {
        setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
        return;
      }
      const { data } = await deleteAttachment(targetFile.id ?? "");
      if (data) {
        removeAttachment(index);
        setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
        toast.success("Instruction file removed successfully");
      }
    } catch (error: any) {
      toast.error(error?.response?.data?.message || "An error occurred");
    } finally {
      setUatProcess(false);
    }
  };

  const handleCancel = () => {
    formik.resetForm();
    setSeriesFrom("");
    setSeriesTo("");
    setNumberOfPads(0);
    setAuthorityToPrintFiles([]);
    setTaxUsersSwornStatementFiles([]);
    setCopyOfFirstAndLastStubFiles([]);
    setPrintersCertificateFiles([]);
    setReset((prev) => prev + 1);
    handleModal();
  };

  return (
    <Fragment>
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit}>
          <div>
            <div className="max-h-96 overflow-y-auto">
              <p className="text-lg text-start font-bold mb-2">General Details</p>
              <div className="grid gap-4 grid-cols-2">
                {/* Division Field */}
                <div ref={divisionRef}>
                  <div className="flex justify-evenly gap-4">
                    <div className="w-1/3 text-sm">Division:</div>
                    <Select
                      key={`division-${reset}`}
                      placeholder="Select division"
                      options={divisionOptions}
                      size="sm"
                      name="divisionId"
                      value={formik.values.divisionId || 0}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.divisionId && !!formik.errors.divisionId}
                    />
                  </div>
                  {formik.touched.divisionId && formik.errors.divisionId && (
                    <div className="text-red-500 text-xs mt-1">{formik.errors.divisionId}</div>
                  )}
                </div>

                {/* Form Type Field */}
                <div ref={formTypeRef}>
                  <div className="flex justify-evenly gap-4">
                    <div className="w-1/3 text-sm">Type:</div>
                    <Select
                      key={`formType-${reset}`}
                      placeholder="Select form type"
                      options={formTypeOptions}
                      size="sm"
                      name="formTypeId"
                      value={formik.values.formTypeId}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.formTypeId && !!formik.errors.formTypeId}
                    />
                  </div>
                  {formik.touched.formTypeId && formik.errors.formTypeId && (
                    <div className="text-red-500 text-xs mt-1">{formik.errors.formTypeId}</div>
                  )}
                </div>

                {/* Area Field */}
                <div ref={areaRef}>
                  <div className="flex justify-evenly gap-4">
                    <div className="w-1/3 text-sm">Area:</div>
                    <Select
                      key={`area-${reset}`}
                      placeholder="Select area"
                      options={areaOptions}
                      size="sm"
                      name="areaId"
                      value={formik.values.areaId}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.areaId && !!formik.errors.areaId}
                    />
                  </div>
                  {formik.touched.areaId && formik.errors.areaId && (
                    <div className="text-red-500 text-xs mt-1">{formik.errors.areaId}</div>
                  )}
                </div>

                {/* Date Received Field */}
                <div ref={dateRef} className="flex justify-evenly gap-4">
                  <div className="w-1/3 text-sm">Date Received:</div>
                  <TextField
                    type="date"
                    className="w-full"
                    size="sm"
                    placeholder="Select Date"
                    name="receivedDate"
                    value={formik.values.receivedDate}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.receivedDate && !!formik.errors.receivedDate}
                    errorText={formik.errors.receivedDate}
                  />
                </div>
              </div>

              <p className="text-lg text-start font-bold mb-2 mt-4">Series Details</p>
              <div className="grid gap-4 grid-cols-2">
                {/* Series From Field */}
                <div ref={seriesFromRef} className="gap-4">
                  <div className="w-1/3 text-sm">Series From:</div>
                  <TextField
                    className="w-full"
                    size="sm"
                    placeholder="Enter Series From"
                    name="seriesFrom"
                    value={seriesFrom}
                    onChange={(e) => {
                      setSeriesFrom(e.target.value);
                      formik.handleChange(e);
                    }}
                    onBlur={formik.handleBlur}
                    error={formik.touched.seriesFrom && !!formik.errors.seriesFrom}
                    errorText={formik.errors.seriesFrom}
                  />
                </div>

                {/* Series To Field */}
                <div ref={seriesToRef}>
                  <div className="w-1/3 text-sm">Series To:</div>
                  <TextField
                    className="w-full"
                    size="sm"
                    placeholder="Enter Series To"
                    name="seriesTo"
                    value={seriesTo}
                    onChange={(e) => {
                      setSeriesTo(e.target.value);
                      formik.handleChange(e);
                    }}
                    onBlur={formik.handleBlur}
                    error={formik.touched.seriesTo && !!formik.errors.seriesTo}
                    errorText={formik.errors.seriesTo}
                  />
                </div>

                {/* ATP No Field */}
                <div ref={atpRef}>
                  <div className="w-1/3 text-sm">ATP No:</div>
                  <TextField
                    className="w-full"
                    size="sm"
                    placeholder="Enter ATP No."
                    name="atpNumber"
                    value={formik.values.atpNumber}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.atpNumber && !!formik.errors.atpNumber}
                    errorText={formik.errors.atpNumber}
                  />
                </div>

                <div>
                  <div className="w-1/3 text-sm">No. of Pads:</div>
                  <TextField className="w-full" size="sm" value={numberOfPads} disabled />
                </div>
              </div>

              <div className="divider"></div>
              
              {/* Attachments Section */}
              <div ref={attachmentsRef}>
              <p className="text-lg text-start font-bold mb-4">Scan and Upload Attachments</p>
                
              <div className="mt-2">
                <p className="text-sm mb-2">Authority to print:</p>
                <div className="border rounded border-dashed border-primary-100">
                  <Attachments
                    fileType={["pdf"]}
                    attachments={
                      authorityToPrintFiles.length > 0
                        ? authorityToPrintFiles.map((file) => ({
                            file,
                            label: "Authority to Print",
                            description: "Authority to Print File",
                          }))
                        : []
                    }
                    setFiles={(files) => handleFile(files, setAuthorityToPrintFiles, "authorityToPrint")}
                    removeFile={(index) => handleRemoveAttachmentFile(index, authorityToPrintFiles, setAuthorityToPrintFiles, "authorityToPrint")}
                    isUploading={attachmentOnProcess}
                    showAddMoreButton={false}
                  />
                </div>
              </div>

              <div className="mt-4">
                <p className="text-sm mb-2">Tax Users Sworn Statement:</p>
                <div className="border rounded border-dashed border-primary-100">
                  <Attachments
                    fileType={["pdf"]}
                    attachments={
                      taxUsersSwornStatementFiles.length > 0
                        ? taxUsersSwornStatementFiles.map((file) => ({
                            file,
                            label: "Tax Users Sworn Statement",
                            description: "Tax Users Sworn Statement",
                          }))
                        : []
                    }
                    setFiles={(files) => handleFile(files, setTaxUsersSwornStatementFiles, "taxUsersSwornStatement")}
                    removeFile={(index) => handleRemoveAttachmentFile(index, taxUsersSwornStatementFiles, setTaxUsersSwornStatementFiles, "taxUsersSwornStatement")}
                    isUploading={attachmentOnProcess}
                    showAddMoreButton={false}
                  />
                </div>
              </div>

              <div className="mt-4">
                <p className="text-sm mb-2">Printer's Certificate:</p>
                <div className="border rounded border-dashed border-primary-100">
                  <Attachments
                    fileType={["pdf"]}
                    attachments={
                      printersCertificateFiles.length > 0
                        ? printersCertificateFiles.map((file) => ({
                            file,
                            label: "Printer's Certificate",
                            description: "Printer's Certificate",
                          }))
                        : []
                    }
                    setFiles={(files) => handleFile(files, setPrintersCertificateFiles, "printersCertificate")}
                    removeFile={(index) => handleRemoveAttachmentFile(index, printersCertificateFiles, setPrintersCertificateFiles, "printersCertificate")}
                    isUploading={attachmentOnProcess}
                    showAddMoreButton={false}
                  />
                </div>
              </div>

              <div className="mt-4">
                <p className="text-sm mb-2">Copy of First and Last Stub:</p>
                <div className="border rounded border-dashed border-primary-100">
                  <Attachments
                    fileType={["pdf"]}
                    attachments={
                      copyOfFirstAndLastStubFiles.length > 0
                        ? copyOfFirstAndLastStubFiles.map((file) => ({
                            file,
                            label: "Copy of First and Last Stub",
                            description: "Copy of First and Last Stub",
                          }))
                        : []
                    }
                    setFiles={(files) => handleFile(files, setCopyOfFirstAndLastStubFiles, "copyOfFirstAndLastStub")}
                    removeFile={(index) => handleRemoveAttachmentFile(index, copyOfFirstAndLastStubFiles, setCopyOfFirstAndLastStubFiles, "copyOfFirstAndLastStub")}
                    isUploading={attachmentOnProcess}
                    showAddMoreButton={false}
                  />
                </div>
                </div>

                {/* General attachments error */}
                {formik.touched.attachments && formik.errors.attachments && (
                  <div className="text-red-500 text-sm mt-2 p-2 bg-red-50 rounded border border-red-200">
                    {formik.errors.attachments}
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-4 mt-4">
            <Button classNames="btn bg-slate-300 w-32 btn-sm" type="button" onClick={handleCancel} disabled={formik.isSubmitting}>
              Cancel
            </Button>
            <Button classNames="btn btn-primary w-32 btn-sm" type="submit" disabled={formik.isSubmitting}>
              {formik.isSubmitting ? "Submitting..." : "Add"}
            </Button>
          </div>
        </Form>
      </FormikProvider>
    </Fragment>
  );
};

export default CreateNewForm;