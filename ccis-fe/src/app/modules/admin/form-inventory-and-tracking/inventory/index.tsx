// import React from "react";
// import { Outlet } from "react-router-dom";

// const Inventory: React.FC = () => {
//   return (
//     <div className="w-full h-full">
//       <Outlet />
//     </div>
//   );
// };

// export default Inventory;




import React from "react";
import GenericInventoryTab from "@components/template/GenericInventoryTab";
import { RoleType } from "@enums/form-status";
// import { useInventoryMetrics } from "@components/template/InventoryMetricType";

const Inventory: React.FC = () => {
  // const { headCashierMetrics } = useInventoryMetrics();
  return (
    <GenericInventoryTab
      userRole={RoleType.CHIEFCASHIER}
      title="INVENTORY (ON-HAND)"
      description="This page lists all forms across all received series. Use filters to narrow down results by division, or type."
      // customMetrics={headCashierMetrics}
    />
  );
};

export default Inventory;

