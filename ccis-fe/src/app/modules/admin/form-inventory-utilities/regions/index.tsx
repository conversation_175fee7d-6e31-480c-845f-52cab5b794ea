import { FC, Fragment, useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { TableColumn } from "react-data-table-component";
import { RootState } from "@state/store";
import { CiEdit, CiTrash } from "react-icons/ci";
import { IActions } from "@interface/common.interface";
import Table from "@components/common/Table";
import Modal from "@components/common/Modal";
import ActionButtons from "@components/common/ActionButtons";
import { confirmDelete, confirmSaveOrEdit } from "@helpers/prompt";
//Reducer
import { useUtilitiesRegionActions } from "@state/reducer/utilities-region";
import { IUtilitiesRegion } from "@interface/utilities.interface";

import { Form, FormikProvider, useFormik } from "formik";
import TextField from "@components/form/TextField";
import Button from "@components/common/Button";

//Schema
import {
  CreateRegionSchema,
  EditRegionSchema,
} from "@services/utilities-region/utilities-region.schema";

const Regions: FC = () => {
  const [create, setCreate] = useState<boolean>(false);
  const [edit, setEdit] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>("");
  
  //Change the region selector to getRegion
  const Regions = useSelector(
    (state: RootState) => state.utilitiesRegion.getRegion.data?.data
  );
  //Loading var here
  const loading = useSelector(
    (state: RootState) =>
      state?.utilitiesRegion.getRegion.loading
  );
  //All functions from the reducer

  const {
    setSelectedRegion,
    getRegion,
    postRegion,
    putRegion,
    destroyRegion,
  } = useUtilitiesRegionActions();

  //Settings for the table
  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  //Action events for the table

  const actionEvents: IActions<IUtilitiesRegion>[] = [
    {
      name: "Edit",
      event: (row: IUtilitiesRegion, index: number) => {
        const data = {
          id: row.id,
          regionName: row.regionName,
        };

        setSelectedRegion({ data: data, index: index });
        formikEdit.setValues(data);
        handleToggleEditModal();
      },
      icon: CiEdit,
      color: "primary",
    },
    {
      name: "Delete",
      event: (row: IUtilitiesRegion, index: number) => {
        const action = confirmDelete(row.regionName);
        action.then((value) => {
          if (value.isConfirmed) {
            destroyRegion({ id: row.id, index: index });
          }
        });
      },
      icon: CiTrash,
      color: "danger",
    },
  ];

  //Table columns
  const columns: TableColumn<IUtilitiesRegion>[] = [
    {
      name: "Region ID",
      selector: (row) => row.id,
      ...commonSetting,
    },
    {
      name: "Region Name",
      selector: (row) => row.regionName,
      ...commonSetting,
    },
    {
      name: "Action",
      cell: (row, rowIndex) => (
        <ActionButtons data={row} rowIndex={rowIndex} actions={actionEvents} />
      ),
    },
  ];

  //Create Modal
  const handleToggleCreateModal = () => {
    setCreate((prev) => !prev);
    formik.resetForm();
  };

  //Edit Modal
  const handleToggleEditModal = () => {
    setEdit((prev) => !prev);
  };
  //Search
  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  //Get data

  useEffect(() => {
    getRegion({ params: { filter: searchText } });
  }, [searchText]);
  
  //Initial values in formik

  const formik = useFormik({
    initialValues: {
      id: 0,
      regionName: "",
    },
    validationSchema: CreateRegionSchema,
    onSubmit: async (values, { resetForm }) => {
      const isConfirmed = await confirmSaveOrEdit(
        `Do you want to save the region: "${values.regionName} ?"`
      );
      if (isConfirmed) {
        postRegion(values);
        handleToggleCreateModal();
        resetForm();
      }
    },
  });
  const formikEdit = useFormik({
    initialValues: {
      id: 0,
      regionName: "",
    },
    validationSchema: EditRegionSchema,
    onSubmit: async (values) => {
      const isConfirmed = await confirmSaveOrEdit(
        `Do you want to edit the region: "${values.regionName} ?"`
      );
      if (isConfirmed) {
        putRegion(values as any);
        handleToggleEditModal();
      }
    },
  });

  return (
    <Fragment>
      <div className="text-xl font-semibold uppercase my-4">Region</div>
      <Table
        className="h-[400px] "
        columns={columns}
        data={Regions}
        createLabel="Add new Region"
        onCreate={handleToggleCreateModal}
        loading={loading}
        onSearch={handleSearch}
        searchable
        multiSelect={false}
      />
      {create && (
        <Modal
          title="Add new Region"
          modalContainerClassName="max-w-3xl "
          titleClass="text-primary text-lg uppercase"
          isOpen={create}
          onClose={handleToggleCreateModal}
        >
          <>
            <FormikProvider value={formik}>
              <Form className="flex flex-col my-4 gap-4">
                <div>
                  {" "}
                  <label>Region Name</label>
                  <TextField
                    name="regionName"
                    placeholder="Enter Region Name"
                    type="text"
                    className="bg-white"
                    error={
                      formik.touched.regionName &&
                      !!formik.errors.regionName
                    }
                    errorText={formik.errors.regionName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.regionName}
                    required
                  />
                </div>
                <Button
                  type="submit"
                  variant="primary"
                  classNames="btn rounded-xl"
                >
                  Save
                </Button>
              </Form>
            </FormikProvider>
          </>
        </Modal>
      )}
      {edit && (
        <Modal
          title="Edit Region"
          modalContainerClassName="max-w-3xl"
          titleClass="text-primary text-lg uppercase"
          isOpen={edit}
          onClose={handleToggleEditModal}
        >
          <>
            <FormikProvider value={formikEdit}>
              <Form className="flex flex-col my-4 gap-4">
                <div>
                  {" "}
                  <label>Region Name</label>
                  <TextField
                    name="regionName"
                    placeholder="Enter Region Name"
                    type="text"
                    className="bg-white"
                    error={
                      formikEdit.touched.regionName &&
                      !!formikEdit.errors.regionName
                    }
                    errorText={formikEdit.errors.regionName}
                    onChange={formikEdit.handleChange}
                    onBlur={formikEdit.handleBlur}
                    value={formikEdit.values.regionName}
                    required
                  />
                </div>

                <Button
                  type="submit"
                  variant="primary"
                  classNames="btn rounded-xl"
                >
                  Update
                </Button>
              </Form>
            </FormikProvider>
          </>
        </Modal>
      )}
    </Fragment>
  );
};

export default Regions;
