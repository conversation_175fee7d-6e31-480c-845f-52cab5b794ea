import { FC, useState, useRef, Fragment } from "react";
import Button from "@components/common/Button";
import { BiExport } from "react-icons/bi";
//import { FaEllipsisV } from "react-icons/fa";
import { GrGroup } from "react-icons/gr";
import user_avatar from "@assets/user_avatar.svg";
import { CiCircleCheck } from "react-icons/ci";
import { useSelector } from "react-redux";
import { IProductRevisions } from "@interface/products.interface";
import Typography from "@components/common/Typography";
import { RootState } from "@state/store";
import dayjs from "dayjs";
import { IGuidelineContent, IGuidelineContentTable } from "@interface/guidelines.interface";
//import { exportService } from "@services/compliance/compliance.service";
import httpClient from "@clients/httpClient";
import Modal from "@components/common/Modal";
import Loader from "@components/Loader";
import { MdOutlineDraw } from "react-icons/md";

const ComplianceProductDetails: FC = () => {
  const getProductForComplianceData = useSelector((state: RootState) => state.productsForCompliance.getProductForCompliance?.data);
  const productGuidelines = useSelector((state: RootState) => state.productsForCompliance.getProductForCompliance?.data?.productRevision?.productGuidelines);
  const commissionStructure = useSelector((state: RootState) => state.productsForCompliance.getProductForCompliance?.data?.productRevision?.commission ?? "");

  const signatories = (getProductForComplianceData?.productRevision as IProductRevisions)?.signatories ?? undefined;

  const standard =
    commissionStructure.commissionDetails?.filter((row: any) => {
      return row.commissionAgeType?.name?.toLocaleLowerCase() !== "standard";
    }) ?? [];

  const sectionRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [titleCurrIndex, setTitleCurrIndex] = useState<number>(0);
  const [lastIndex, setLastIndex] = useState<number>(0);
  const [commissionStructureView, setCommissionStructureView] = useState<boolean>(false);
  const [pdfFile, setPdfFile] = useState<string>();
  const [processing, setProcessing] = useState<boolean>(false);
  const [processModal, setProcessModal] = useState<boolean>(false);
  const [signatoryModal, setSignatoryModal] = useState<boolean>(false);

  const handleSignatoryModal = () => {
    setSignatoryModal((prev) => !prev);
  };

  const toggleProcess = () => {
    setProcessModal((prev) => !prev);
  };

  const handleScrollToSection = (index: number) => {
    sectionRefs.current[index]?.scrollIntoView({ behavior: "smooth" });
    setLastIndex(productGuidelines.length);
    setTitleCurrIndex(index);
    setCommissionStructureView(false);
  };

  const handleSetCommissionStructureView = () => {
    handleScrollToSection(lastIndex - 1);
    setTitleCurrIndex(productGuidelines.length + 1);
    setCommissionStructureView(true);
  };

  const handleExport = async () => {
    try {
      setProcessing(true);
      toggleProcess();

      const response: any = await httpClient.get(
        `products/${getProductForComplianceData?.productRevision?.product?.id}/product-revisions/${getProductForComplianceData?.productRevision?.id}/export/pdf`,
        {
          responseType: "blob",
        }
      );

      const pdfBlob = new Blob([response], { type: "application/pdf" });

      const url = window.URL.createObjectURL(pdfBlob);
      setPdfFile(url);
    } catch (error) {
      console.log(error);
    } finally {
      setProcessing(false);
    }
  };

  const getStatus = (item: any) => {
    const statusClass = item === "APPROVED" ? "bg-green-100 text-green-500 " : item === "PENDING" ? "bg-yellow-100 text-yellow-500 " : item === "DISAPPROVED" ? "bg-red-100 text-red-500 " : " ";

    const convertedStatus = item === "APPROVED" ? "Approved" : item === "PENDING" ? "Pending" : item === "DISAPPROVED" ? "Disapproved" : "";

    return (
      //<div className="w-full flex items-center justify-center">
      <div className={`${statusClass}  text-center px-2 py-1 rounded-md `}>{item ? convertedStatus : "No Status"}</div>
      //</div>
    );
  };
  return (
    <div>
      {signatoryModal && (
        <Modal isOpen={signatoryModal} onClose={handleSignatoryModal} showCloseButton={true} modalContainerClassName="w-full">
          <div className="  bg-sky-50">
            {" "}
            <div className="w-full font-poppins-semibold text-xl flex items-center justify-center gap-2 mb-8 text-primary">
              {" "}
              <GrGroup size={25} /> SIGNEES
            </div>
            {signatories?.map((item: any, index: number) => (
              <div key={index}>
                <div className="w-full h-24  flex  justify-center ">
                  <div className="w-1/6 min-h-24  text-green-500  flex flex-col  items-center ">
                    <span className="p-1 bg-green-100 rounded-full">
                      {" "}
                      <CiCircleCheck size={20} />
                    </span>

                    <div className="w-[0.01rem] mt-1 bg-zinc-300 h-1/2 border border-zinc-300"></div>
                  </div>
                  <div className="w-1/6 min-h-24 flex justify-center text-primary ">
                    {" "}
                    <img src={user_avatar} className="w-full h-max max-w-10" />
                  </div>
                  <div className="w-2/3 min-h-24 items-start flex flex-col gap-1">
                    <div className="text-sm">{item?.user?.firstname + " " + item?.user?.middlename + " " + item?.user?.lastname}</div>
                    <div className="text-xs text-zinc-400">{item?.user?.position?.positionName}</div>
                    <div className="flex gap-2 text-[0.6rem] text-zinc-400 items-center justify-center">
                      <span>{getStatus(item?.approvalStatus)}</span>
                      {item?.datetimeSigned !== null && <span>{dayjs(item?.datetimeSigned).format("MM/DD/YY h:m A")}</span>}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Modal>
      )}

      {processModal && (
        <Modal isOpen={processModal} onClose={toggleProcess} showCloseButton={!processing} modalContainerClassName={`${processing ? "!max-w-xs" : "w-screen"}`}>
          {processing && (
            <div className="flex flex-1 flex-col justify-center items-center ">
              <Loader />
              <Typography>Fetching Details . . .</Typography>
            </div>
          )}
          {!processing && (
            <div className="flex flex-1 flex-col justify-center items-center">
              <embed src={pdfFile} className="min-h-[52rem]" width="100%" />
            </div>
          )}
        </Modal>
      )}

      <div className="h-full font-poppins-semibold flex xl:justify-end justify-between gap-4 text-primary">
        <Button
          type="button"
          onClick={handleSignatoryModal}
          classNames="bg-white text-center border border-zinc-300 rounded0-md xl:hidden flex gap-2 items-center justify-between text-xs"
          outline
          variant="primary"
        >
          <MdOutlineDraw size={18} className="flex items-center justify-center " />
          Show Signatories
        </Button>{" "}
        <Button type="button" onClick={handleExport} classNames="bg-white text-center border border-zinc-300 rounded0-md flex gap-2 items-center justify-center text-xs" outline variant="primary">
          <BiExport size={18} className="flex items-center justify-center " />
          Export
        </Button>{" "}
        {/* <Button type="button" classNames="bg-white border border-zinc-300 rounded0-md flex items-center justify-center bg-info " outline variant="primary">
          <FaEllipsisV size={15} />
        </Button> */}
      </div>

      {/* RESPONSIVE COL 1 */}
      <div>
        <div className="w-full text-sm font-poppins-semibold pt-4 mb-2 xl:hidden flex  ">Table of Contents</div>
        <div className="w-full  flex xl:hidden flex-wrap justify-start text-start text-sm ">
          {productGuidelines?.map((item: any, gIndex?: any) => (
            <div
              className={`w-max text-[.7rem]  font-poppins-semibold hover:bg-zinc-100 p-1 px-2  rounded-md cursor-pointer ${titleCurrIndex === gIndex ? "text-primary bg-sky-50" : "text-zinc-500"}`}
              onClick={() => handleScrollToSection(gIndex)}
            >
              {item.label}
            </div>
          ))}

          {commissionStructure && (
            <div
              onClick={handleSetCommissionStructureView}
              className={`w-max text-xs font-poppins-semibold hover:bg-zinc-100 p-1  px-2 rounded-md cursor-pointer ${commissionStructureView ? "text-primary bg-sky-50" : "text-zinc-500"} `}
            >
              Commission Structure
            </div>
          )}
        </div>
      </div>

      <div className="flex w-full min-h-screen mt-4 gap-2 ">
        {/* COL1 */}
        <div className="w-72 min-h-screen hidden  xl:flex flex-col text-start text-sm relative">
          <div className="w-full text-xl font-poppins-semibold pt-4 mb-4 ">Table of Contents</div>

          {productGuidelines?.map((item: any, gIndex?: any) => (
            <div
              className={`w-full  font-poppins-semibold hover:bg-zinc-100 p-4 rounded-md cursor-pointer ${titleCurrIndex === gIndex ? "text-primary bg-sky-50" : "text-zinc-500"}`}
              onClick={() => handleScrollToSection(gIndex)}
            >
              {item.label}
            </div>
          ))}

          {commissionStructure && (
            <div
              onClick={handleSetCommissionStructureView}
              className={`w-full font-poppins-semibold hover:bg-zinc-100 p-4 rounded-md cursor-pointer ${commissionStructureView ? "text-primary bg-sky-50" : "text-zinc-500"} `}
            >
              Commission Structure
            </div>
          )}
        </div>
        {/* COL2 */}

        <div className="xl:w-2/3 w-full min-h-screen border border-zinc-200 pt-4">
          {" "}
          <div className="w-full font-poppins-semibold xl:text-xl text-base text-center ">{getProductForComplianceData?.productRevision?.product?.name}</div>
          {/* PRODUCT GUIDELINES CONTENT START */}
          <div className="xl:p-8 p-4">
            <div className="flex flex-col">
              {/* FIRST PRODUCT GUIDELINE*/}
              <div className="flex flex-col xl:text-base text-xs ">
                {productGuidelines?.map((value: any, gIndex: any) => {
                  return (
                    <div key={`guideline-${gIndex}`} className="scroll-mt-16 flex flex-1 flex-col mb-2 xl:mb-10" ref={(el) => (sectionRefs.current[gIndex] = el)}>
                      <Typography className="xl:text-xl text-sm font-poppins-semibold text-primary">{value.label}</Typography>
                      {value.productGuideline.map((pgValue: any, pgIndex: any) => {
                        let listValue;
                        let tableValue;
                        if (pgValue.type === "list") {
                          listValue = pgValue.value as IGuidelineContent[];
                        }

                        if (pgValue.type === "table") {
                          tableValue = pgValue.value as IGuidelineContentTable;
                        }

                        return (
                          <div key={`pg-${pgIndex}`} className="p-2">
                            {pgValue.type === "textfield" && (
                              <Fragment>
                                <Typography className="ml-4 xl:mt-4  mt-0 text-justify xl:text-base text-xs">{pgValue.value as string}</Typography>
                              </Fragment>
                            )}
                            {pgValue.type === "list" && (
                              <Fragment>
                                <Typography className="ml-4 xl:mt-4  mt-0 xl:text-justify text-start xl:text-base text-xs">{pgValue.label}</Typography>
                                <ul className="list-disc xl:ml-12 ml-6">
                                  {listValue &&
                                    listValue.map((listValue, listIndex) => {
                                      return (
                                        <li key={`listItem-${listIndex}`} className="xl:mt-4 mt-0">
                                          <Typography className="text-justify xl:text-base text-xs">{listValue.value as string}</Typography>
                                        </li>
                                      );
                                    })}
                                </ul>
                              </Fragment>
                            )}
                            {pgValue.type === "texteditor" && (
                              <Fragment>
                                <div
                                  className="ml-4 mt-2 xl:text-base text-xs "
                                  dangerouslySetInnerHTML={{
                                    __html: (pgValue as any).value ?? "",
                                  }}
                                ></div>
                              </Fragment>
                            )}
                            {pgValue.type === "table" && (
                              <Fragment>
                                <div className="flex flex-1 mt-2 mx-6 overflow-x-scroll xl:text-base text-xs">
                                  <table className="table border-[1px]">
                                    <thead className="table-header-group">
                                      <tr>
                                        {tableValue?.columns?.map((cValue, cIndex) => {
                                          return (
                                            <td key={`col-${cIndex}`} className="table-cell border-[1px] xl:text-base text-xs">
                                              <Typography className="font-semibold text-xs">{cValue.value as string}</Typography>
                                            </td>
                                          );
                                        })}
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {tableValue?.rows?.map((rValue, rIndex) => {
                                        return (
                                          <tr key={`row-${rIndex}`}>
                                            {rValue.map((cell, cellIndex) => {
                                              return (
                                                <td className="border-[1px] xl:text-base text-xs" key={`cell-${cellIndex}`}>
                                                  <Typography size="xs">{cell.value as string}</Typography>
                                                </td>
                                              );
                                            })}
                                          </tr>
                                        );
                                      })}
                                    </tbody>
                                  </table>
                                </div>
                              </Fragment>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  );
                })}
              </div>
              {/* LAST PRODUCT GUIDELINE*/}

              {commissionStructure && (
                <Fragment>
                  <Typography size="md" className="font-poppins-semibold text-primary">
                    Commission Structure
                  </Typography>
                  <Fragment>
                    <Typography className="ml-4 mt-4 xl:text-base text-xs">
                      {parseFloat(commissionStructure.maximumDisposableRate ?? "").toFixed(2)}% Maximum Disposable Commission - Standard Rate
                    </Typography>

                    <div className="flex-flex-1 mt-6 mx-6 overflow-x-scroll">
                      <table className="table overflow-scroll">
                        <thead>
                          <tr>
                            <td className="table-cell border-1 text-center text-xs">Type</td>
                            <td className="table-cell border-1 text-center text-xs">Age Type</td>
                            {standard.length > 0 && (
                              <Fragment>
                                <td className="table-cell border-1 text-center text-xs">Age From</td>
                                <td className="table-cell border-1 text-center text-xs">Age To</td>
                              </Fragment>
                            )}
                            <td className="table-cell border-1 text-center text-xs">Rate</td>
                          </tr>
                        </thead>
                        <tbody>
                          {commissionStructure.commissionDetails?.map((rowValue: any, rowIndex: any) => {
                            return (
                              <tr key={`commissionDetailsRow-${rowIndex}`}>
                                <td className="table-cell border-1 text-xs text-center  font-poppins-semibold">{rowValue?.commissionType?.commissionName}</td>
                                <td className="table-cell border-1 text-xs text-center">{rowValue?.commissionAgeType?.name}</td>
                                {standard.length > 0 && (
                                  <Fragment>
                                    <td className="table-cell border-1 text-center text-xs">{rowValue.ageFrom}</td>
                                    <td className="table-cell border-1 text-center text-xs">{rowValue.ageTo}</td>
                                  </Fragment>
                                )}
                                <td className="table-cell border-1 text-center text-xs">{rowValue.rate ? parseFloat(rowValue.rate.toString()).toFixed(0) : ""}%</td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                  </Fragment>
                </Fragment>
              )}
            </div>
          </div>
          {/* PRODUCT GUIDELINES CONTENT END */}
        </div>

        {/* COL3 */}
        <div className="w-96 h-max pb-4 bg-sky-50 pt-4 xl:block hidden">
          {" "}
          <div className="w-full font-poppins-semibold text-xl flex items-center justify-center gap-2 mb-8 text-primary">
            {" "}
            <GrGroup size={25} /> SIGNEES
          </div>
          {signatories?.map((item: any, index: number) => (
            <div key={index}>
              <div className="w-full h-24  flex  justify-center ">
                <div className="w-1/6 min-h-24  text-green-500  flex flex-col  items-center ">
                  <span className="p-1 bg-green-100 rounded-full">
                    {" "}
                    <CiCircleCheck size={20} />
                  </span>

                  <div className="w-[0.01rem] mt-1 bg-zinc-300 h-1/2 border border-zinc-300"></div>
                </div>
                <div className="w-1/6 min-h-24 flex justify-center text-primary ">
                  {" "}
                  <img src={user_avatar} className="w-full h-max max-w-10" />
                </div>
                <div className="w-2/3 min-h-24 items-start flex flex-col gap-1">
                  <div className="text-sm">{item?.user?.firstname + " " + item?.user?.middlename + " " + item?.user?.lastname}</div>
                  <div className="text-xs text-zinc-400">{item?.user?.position?.positionName}</div>
                  <div className="flex gap-2 text-[0.6rem] text-zinc-400 items-center justify-center">
                    <span>{getStatus(item?.approvalStatus)}</span>
                    {item?.datetimeSigned !== null && <span>{dayjs(item?.datetimeSigned).format("MM/DD/YY h:m A")}</span>}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ComplianceProductDetails;
