import { FC, useState, useEffect } from "react";
import { useFormik, FormikProvider } from "formik";
import { createUATFormSchema, editUATFormSchema } from "@services/uat-details/uat.schema";
import DatePicker from "../Datepicker";
import TextField from "@components/form/TextField";
import Table from "@components/common/Table";
import Button from "@components/common/Button";
import { CiTrash } from "react-icons/ci";
import { removeUatAttachment, useUatActions } from "@state/reducer/uat-details";
import { IUatRequirements, IUatInstructions, IUatDetails } from "@interface/uat.interface";
import LoadingButton from "@components/common/LoadingButton";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import Select from "@components/form/Select";
import { formatSelectOptions } from "@helpers/array";
import { usePositionsManagementActions } from "@state/reducer/utilities-positions";
import { useUserManagementActions } from "@state/reducer/users-management";
import Loader from "@components/Loader";
import { confirmSaveOrEdit } from "@helpers/prompt";
import { deleteUatAttachment } from "@services/uat-details/uat.service";
import Attachments from "@modules/admin/products/components/Common/Attachments";
import { toast } from "react-toastify";

type UATFormProps = {
  handleModal: () => void;
  mode: "create" | "edit";
};

const UATForm: FC<UATFormProps> = ({ handleModal, mode }) => {
  const [requirements, setUATRequirements] = useState<IUatRequirements[]>([]);
  const [instructions, setInstructions] = useState<IUatInstructions[]>([]);
  const positions = useSelector((state: RootState) => state.utilitiesPositions?.getPosition?.data ?? []);
  const users = useSelector((state: RootState) => state.usersManagement.users);
  const [files, setFiles] = useState<Array<File>>([]);
  const [filterPosition, setPosition] = useState("");
  const positionOptions = formatSelectOptions(positions, "positionName");
  const [filteredUsers, setFilteredUsers] = useState(users);
  const { postUat, getUats, putUat } = useUatActions();
  const [loading] = useState<boolean>(false);
  const uatDetails = useSelector((state: RootState) => state.uatDetails.selectedUat);
  const { getPosition } = usePositionsManagementActions();
  const { getUsers } = useUserManagementActions();
  const uatAttachment = useSelector((state: RootState) => state.uatDetails.selectedUat.attachments);
  const [uatOnProcess, setUatProcess] = useState<boolean>(false);
  const initialValues =
    mode === "create"
      ? {
          // selectedUat: '',
          name: "",
          scheduleDate: "",
          status: "PENDING",
          acceptanceRequirements: "",
          respondentName: "",
          respondentEmail: "",
          instructionTitle: "",
          instructionDescription: "",
          attachments: [],
          uatRequirements: [],
          uatInstruction: [],
        }
      : {
          id: uatDetails.id,
          selectedUat: "",
          name: uatDetails.name,
          scheduleDate: uatDetails.scheduleDate,
          status: uatDetails.status,
          acceptanceRequirements: "",
          instructionTitle: "",
          instructionDescription: "",
          attachments: [],
          respondentName: uatDetails.respondentName,
          respondentEmail: uatDetails.respondentEmail,
          // uatRequirements: uatDetails.uatRequirements || [],
          // uatInstruction: uatDetails.uatInstruction || [],
          uatRequirements: requirements.map((element) => ({
            question: element.question,
          })),
          uatInstruction: instructions.map((element) => ({
            instructionTitle: element.instructionTitle,
            instructionDescription: element.instructionDescription,
          })),
        };

  const formik = useFormik({
    initialValues,
    validationSchema: mode === "create" ? createUATFormSchema : editUATFormSchema,
    enableReinitialize: true,
    onSubmit: async (values: IUatDetails) => {
      if (requirements.length === 0) {
        formik.setErrors({
          acceptanceRequirements: "At least add one acceptance requirement.",
        });
        return;
      }
      // Check if there are instructions or an uploaded file
      if (instructions.length === 0 && !files) {
        formik.setErrors({
          instructionTitle: "At least upload an instruction file or add one instruction.",
          instructionDescription: "At least upload an instruction file or add one instruction.",
        });
        return;
      }

      if (mode === "create") {
        values.uatRequirements = requirements;
        values.uatInstruction = instructions;
        const data = {
          ...values,
          attachments: values.attachments.map((file) => ({
            attachableType: "UAT",
            file: file.file,
            label: file.label,
            description: file.description,
            attachableId: "0",
          })),
        };
        await postUat(data);
      } else {
        const isConfirmed = await confirmSaveOrEdit(`Do you want to save changes in the UAT: "${values.name}"?`);
        if (isConfirmed) {
          values.uatRequirements = requirements;
          values.uatInstruction = instructions;
          await putUat(values);
          await getUats({ filter: "" });
        }
      }
      handleModal();
    },
  });

  const handleAddRequirement = () => {
    formik.validateField("acceptanceRequirements");
    if (formik.values.acceptanceRequirements.trim() !== "") {
      const newRequirement: IUatRequirements = {
        question: formik.values.acceptanceRequirements,
      };
      const updatedRequirements = [...requirements, newRequirement];
      setUATRequirements(updatedRequirements);

      // Update only the specific fields without touching other form values
      formik.setFieldValue("uatRequirements", updatedRequirements);
      formik.setFieldValue("acceptanceRequirements", "");
    }
  };

  const handleDeleteRequirement = (index: number) => {
    setUATRequirements((prevRequirements) => prevRequirements.filter((_, i) => i !== index));
  };

  const handleAddInstruction = () => {
    formik.validateField("instructionTitle");
    formik.validateField("instructionDescription");

    if (formik.values.instructionTitle.trim() !== "" && formik.values.instructionDescription.trim() !== "") {
      const newInstruction: IUatInstructions = {
        instructionTitle: formik.values.instructionTitle,
        instructionDescription: formik.values.instructionDescription,
      };

      setInstructions((prevInstructions) => [...prevInstructions, newInstruction]);

      // Update the form values with the new instructions array
      formik.setFieldValue("uatInstruction", [...instructions, newInstruction]);

      // Clear the individual instruction fields
      formik.setFieldValue("instructionTitle", "");
      formik.setFieldValue("instructionDescription", "");
    }
  };

  const handleDeleteInstruction = (index: number) => {
    setInstructions((prevInstructions) => prevInstructions.filter((_, i) => i !== index));
  };

  const handleFile = (acceptedFiles: Array<File>) => {
    setFiles(acceptedFiles);
    const fileArray = acceptedFiles.map((file) => ({
      file: file,
      label: file.name,
      description: "Instruction File",
      attachableType: "UAT",
    }));
    formik.setFieldValue("attachments", fileArray);
  };

  const handleRemoveAttachmentFile = async (index: number) => {
    try {
      setUatProcess(true); // Indicate that the process is ongoing
      const prevState = [...(uatAttachment ?? [])];
      const targetFile = prevState[index];

      if (!targetFile) {
        setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
        return;
      }

      if (targetFile.id === undefined) {
        setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
        return;
      }

      const { data } = await deleteUatAttachment(targetFile.id ?? "");
      if (data) {
        removeUatAttachment(index);
        setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
        toast.success("Instruction file removed successfully");
      }
    } catch (error: any) {
      toast.error(error?.response?.data?.message || "An error occurred");
    } finally {
      setUatProcess(false); // Indicate that the process is complete
    }
  };
  const uatRequirementsColumns = [
    {
      name: "Requirement",
      selector: (row: IUatRequirements) => row.question,
      sortable: true,
    },
    {
      name: "Actions",
      width: "20%",
      cell: (_row: IUatRequirements, index: number) => (
        <Button onClick={() => handleDeleteRequirement(index)}>
          <CiTrash className="w-4 h-4 text-danger" />
        </Button>
      ),
    },
  ];
  const instructionsColumns = [
    {
      name: "Title",
      selector: (row: IUatInstructions) => row.instructionTitle,
      sortable: true,
    },
    {
      name: "Description",
      selector: (row: IUatInstructions) => row.instructionDescription,
      sortable: true,
    },
    {
      name: "Actions",
      width: "20%",
      cell: (_row: IUatInstructions, index: number) => (
        <Button onClick={() => handleDeleteInstruction(index)}>
          <CiTrash className="w-4 h-4 text-danger" />
        </Button>
      ),
    },
  ];
  useEffect(() => {
    getPosition({ params: { filter: "" } });
    getUsers({ filter: "" });
  }, []);

  useEffect(() => {
    const filteredUsers = users.filter((user) => user.position?.id.toString() === filterPosition.toString());
    setFilteredUsers(filteredUsers);
  }, [filterPosition, users]);

  useEffect(() => {
    const selectedUser = users.find((user) => user.id.toString() === formik.values.respondentName.toString());
    if (selectedUser) {
      formik.setFieldValue("respondentEmail", selectedUser.email);
    }
  }, [formik.values.respondentName, users]);

  useEffect(() => {
    if (mode === "edit") {
      setInstructions(uatDetails.uatInstruction);
      setUATRequirements(uatDetails.uatRequirements);
      if (uatDetails.attachments) {
        const mappedFiles = uatDetails.attachments.map((attachment: any) => {
          const fileUrl = `${import.meta.env.VITE_AWS_S3_ENDPOINT}/${attachment.filepath}`;
          return new File([fileUrl], attachment.label);
        });
        setFiles(mappedFiles);
      }
      const respondentId = uatDetails.respondentName;
      const selectedUser = users.find((user) => user.id.toString() === respondentId.toString());
      if (selectedUser && selectedUser.position) {
        setFilteredUsers([selectedUser, ...users.filter((user) => user.id.toString() !== respondentId.toString())]);
        formik.setFieldValue("respondentName", respondentId);
        formik.setFieldValue("respondentEmail", selectedUser.email);
      }
    }
  }, [mode, uatDetails, users]);

  return loading ? (
    <Loader />
  ) : (
    <div>
      <div className="fixed inset-0 flex items-center justify-center z-50">
        <div className="relative w-full max-w-3xl mx-auto p-8 bg-white rounded-lg shadow-lg max-h-screen overflow-y-auto">
          <button onClick={handleModal} className="absolute top-2 right-2 text-gray-600 hover:text-gray-800 text-3xl">
            &times;
          </button>
          <FormikProvider value={formik}>
            <form onSubmit={formik.handleSubmit}>
              <div>
                <div className="w-full" id="UATRequirement">
                  <div>
                    <h1 className="text-xl font-bold text-primary">{mode === "create" ? "A. Create User Acceptance Test" : "A. Edit User Acceptance Test"}</h1>
                    <h1 className="bold text-red italic mb-3">*Fill out all Fields</h1>
                  </div>
                  <div className="grid grid-cols-3 gap-3 mb-2">
                    <div className="flex flex-1 items-center">User Acceptance Test Title</div>
                    <div className="col-span-2">
                      <TextField
                        type="text"
                        value={formik.values.name}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={formik.touched.name && !!formik.errors.name}
                        errorText={formik.errors.name}
                        name="name"
                        placeholder="Enter UAT Title..."
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-3 mb-2">
                    <div className="flex flex-1 items-center">Set Schedule for Test</div>
                    <div className="col-span-2">
                      <DatePicker
                        name="scheduleDate"
                        value={formik.values.scheduleDate}
                        onChange={(value) => formik.setFieldValue("scheduleDate", value)}
                        onBlur={formik.handleBlur}
                        hasError={!!(formik.touched.scheduleDate && formik.errors.scheduleDate)}
                      />
                      {formik.touched.scheduleDate && formik.errors.scheduleDate ? <div className="text-red-500 mb-4 text-sm">{formik.errors.scheduleDate}</div> : null}
                    </div>
                  </div>
                  <h2 className="text-xl font-bold text-primary mb-2">B. Assign Respondent</h2>
                  <div className="grid grid-cols-3 gap-3 mb-2">
                    <div className="flex flex-1 items-center">Filter by Position</div>
                    <div className="col-span-2">
                      <Select
                        options={positionOptions}
                        size="md"
                        value={filterPosition}
                        onChange={(e) => {
                          setPosition(e.target.value);
                        }}
                        placeholder="Select respondent position"
                      />
                    </div>
                    <div className="flex flex-1 items-center">Respondent name</div>
                    <div className="col-span-2">
                      {mode === "edit" ? (
                        filterPosition ? (
                          <Select
                            options={filteredUsers.map((user) => ({
                              value: `${user.firstname} ${user.middlename} ${user.lastname}`,
                              text: `${user.firstname} ${user.middlename} ${user.lastname}`,
                            }))}
                            size="md"
                            name="respondentName"
                            placeholder="Select respondent"
                            value={formik.values.respondentName}
                            onChange={(e) => {
                              formik.setFieldValue("respondentName", e.target.value);
                              const selectedUser = filteredUsers.find((user) => `${user.firstname} ${user.middlename} ${user.lastname}` === e.target.value);
                              if (selectedUser) {
                                formik.setFieldValue("respondentEmail", selectedUser.email);
                              }
                            }}
                            error={formik.touched.respondentName && !!formik.errors.respondentName}
                            errorText={formik.errors.respondentName}
                            required
                          />
                        ) : (
                          <div>
                            <TextField value={uatDetails.respondentName} disabled name="respondentName" placeholder="Respondent Name" />
                          </div>
                        )
                      ) : (
                        <Select
                          options={filteredUsers.map((user) => ({
                            value: `${user.firstname} ${user.middlename} ${user.lastname}`,
                            text: `${user.firstname} ${user.middlename} ${user.lastname}`,
                          }))}
                          size="md"
                          name="respondentName"
                          placeholder="Select respondent"
                          value={formik.values.respondentName}
                          onChange={(e) => {
                            formik.setFieldValue("respondentName", e.target.value);
                            const selectedUser = filteredUsers.find((user) => `${user.firstname} ${user.middlename} ${user.lastname}` === e.target.value);
                            if (selectedUser) {
                              formik.setFieldValue("respondentEmail", selectedUser.email);
                            }
                          }}
                          error={formik.touched.respondentName && !!formik.errors.respondentName}
                          errorText={formik.errors.respondentName}
                          required
                        />
                      )}
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-3 mb-2">
                    <div className="flex flex-1 items-center">Respondent email</div>
                    <div className="col-span-2">
                      <TextField
                        type="email"
                        name="respondentEmail"
                        placeholder="Enter respondent email..."
                        value={formik.values.respondentEmail}
                        error={formik.touched.respondentEmail && !!formik.errors.respondentEmail}
                        errorText={formik.errors.respondentEmail}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        disabled
                      />
                    </div>
                  </div>
                </div>
                <div className="w-full">
                  <h1 className="text-xl font-bold text-primary mb-2">C. Set Up Requirements</h1>
                  <div>
                    <div>Acceptance Requirements</div>
                    <div>
                      <textarea
                        value={formik.values.acceptanceRequirements}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        name="acceptanceRequirements"
                        placeholder="Enter Acceptance Requirements..."
                        className="input w-full border border-primary"
                      />
                      {formik.touched.acceptanceRequirements && formik.errors.acceptanceRequirements ? <div className="text-red-500 mb-2 text-sm">{formik.errors.acceptanceRequirements}</div> : null}
                      <div className="flex justify-end mt-2">
                        <Button variant="primary" onClick={handleAddRequirement}>
                          Add Requirement
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h2 className="text-xl mt-2 font-bold text-primary mb-4">Acceptance Requirements</h2>
                    <Table className="h-[200px]" columns={uatRequirementsColumns} data={requirements} pagination={false} searchable={false} selectable={false} />
                  </div>
                </div>
              </div>
              <h1 className="text-xl font-bold text-primary">D. Set Up Instructions</h1>
              <div className="italic">*You can upload an instruction file, input manually, or use both. </div>
              <div className="w-full" id="UATInstruction">
                <div className="w-full border rounded border-slate-300 p-4">
                  <Attachments
                    fileType={["pdf"]}
                    maxSize={5242880}
                    maxFiles={1}
                    attachments={
                      files.length > 0
                        ? files.map((file) => ({
                            attachableType: "UAT",
                            file,
                            label: file.name,
                            description: "Instruction File",
                          }))
                        : []
                    }
                    setFiles={handleFile}
                    removeFile={(index) => handleRemoveAttachmentFile(index)}
                    isUploading={uatOnProcess}
                    showAddMoreButton={false}
                  />
                </div>
                <div className="text-center font-semibold p-4">OR</div>
                <div className="w-full">
                  <div className="grid grid-cols-3 gap-3 mb-2">
                    <div className="flex flex-1 items-center">Instruction Title</div>
                    <div className="col-span-2">
                      <TextField
                        type="text"
                        value={formik.values.instructionTitle}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        name="instructionTitle"
                        placeholder="Enter Instruction Title..."
                        error={formik.touched.instructionTitle && !!formik.errors.instructionTitle}
                        errorText={formik.errors.instructionTitle}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-3 mb-2">
                    <div className="flex flex-1 items-center">Description</div>
                    <div className="col-span-2">
                      <TextField
                        value={formik.values.instructionDescription}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        name="instructionDescription"
                        placeholder="Enter Description..."
                        className="input w-full border border-primary mt-2"
                        error={formik.touched.instructionDescription && !!formik.errors.instructionDescription}
                        errorText={formik.errors.instructionDescription}
                      />
                    </div>
                  </div>
                  <div className="flex justify-end">
                    <Button variant="primary" onClick={handleAddInstruction} classNames="mt-4">
                      Add Instruction
                    </Button>
                  </div>
                  <Table className="h-[200px] mt-2" columns={instructionsColumns} data={instructions} pagination={false} searchable={false} selectable={false} />
                </div>
              </div>
              <div className="flex flex-1 flex-row items-center justify-end mt-4 ">
                <LoadingButton type="submit" variant="primary" className="text-white btn btn-sm !w-1/4">
                  Save UAT
                </LoadingButton>
              </div>
            </form>
          </FormikProvider>
        </div>
      </div>
    </div>
  );
};

export default UATForm;
