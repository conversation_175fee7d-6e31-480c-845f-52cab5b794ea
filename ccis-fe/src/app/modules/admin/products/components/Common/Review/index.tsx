import Typography from "@components/common/Typography";
import { formatToRomanNumeral } from "@helpers/text";
import { IGuidelineContent, IGuidelineContentTable } from "@interface/guidelines.interface";
import { IProduct } from "@interface/products.interface";
import { getProductService } from "@services/products/products.service";
import { RootState } from "@state/store";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import { toast } from "react-toastify";
import { Fragment } from "react/jsx-runtime";
const Review = () => {
  const location = useLocation();
  const isRevision = location?.state?.isRevision;
  const productFromState = location?.state?.product;
  const forClone = location?.state?.forClone;

  const product = useSelector((state: RootState) => state.products.product);
  const productType = useSelector((state: RootState) => state.utilitiesProductType.productType);
  const productCategory = useSelector((state: RootState) => state.utilitiesProductCategory.productCategory);
  const targetMarket = useSelector((state: RootState) => state.utilitiesTargetMarket.targetMarkets);
  const guidelines = useSelector((state: RootState) => state.guidelines.appliedGuidelines);
  const pType = productType.filter((obj) => obj.id.toString() == product?.productTypeId)[0];
  const pCatType = productCategory.filter((obj) => obj.id.toString() == product?.productCategoryId)[0];
  const pTargetMarket = targetMarket.filter((obj) => obj.id.toString() == product?.targetMarketId)[0];
  const commissionStructure = useSelector((state: RootState) => state.commissionStructure.commissionStructures);
  const guidelineHeaders = useSelector((state: RootState) => state.guidelines.guidelineHeaders);

  const [fetchProduct, setFetchProduct] = useState<IProduct>();
  const [isFetching, setIsFetching] = useState<boolean>(false);

  const getProduct = async () => {
    try {
      setIsFetching(true);
      const response = await getProductService(product?.parentId ?? "");
      if (response) {
        const { data } = response;
        if (data) {
          setFetchProduct(data);
        }
      }
    } catch (error: any) {
      toast.error(error?.response?.data?.message);
    } finally {
      setIsFetching(false);
    }
  };

  useEffect(() => {
    getProduct();
  }, []);

  return (
    <Fragment>
      <div className="flex flex-1">
        {!isRevision && !forClone && (
          <div className="flex flex-1 flex-col">
            <div className="p-4 flex flex-row">
              <Typography className="min-w-96">Product Type</Typography>
              <Typography>{pType?.productType ?? productFromState?.productType?.productType ?? "Undefined"}</Typography>
            </div>
            <div className="p-4 mt-2 flex flex-row">
              <Typography className="min-w-96">Product Category</Typography>
              <Typography>{pCatType?.productCategory ?? productFromState?.productCategory?.productCategory ?? "Undefined"}</Typography>
            </div>
            <div className="p-4 mt-2 flex flex-row">
              <Typography className="min-w-96">Target Market</Typography>
              <Typography>{pTargetMarket?.marketName ?? productFromState?.targetMarket?.marketName ?? "Undefined"}</Typography>
            </div>
            {product?.parentId !== null && (
              <div className="p-4  mt-2 flex flex-row">
                <Typography className="min-w-96">Standard Product Name</Typography>
                {isFetching && <Typography>Loading...</Typography>}
                {!isFetching && <Typography>{fetchProduct?.name ?? fetchProduct?.name ?? "N/A"}</Typography>}
              </div>
            )}
            <div className="p-4  mt-2 flex flex-row">
              <Typography className="min-w-96">Product Name</Typography>
              <Typography>{product?.name ?? productFromState?.name ?? "Undefined"}</Typography>
            </div>
            <div className="p-4  mt-2 flex flex-row">
              <Typography className="min-w-96">Product Code</Typography>
              <Typography>{product?.productCode ?? productFromState?.productCode ?? "Undefined"}</Typography>
            </div>
            <div className="p-4  mt-2 flex flex-row">
              <Typography className="min-w-96">Product Description</Typography>
              <div
                className="text-justify"
                dangerouslySetInnerHTML={{
                  __html: product?.description ?? productFromState?.description ?? "Undefined",
                }}
              ></div>
            </div>
          </div>
        )}
      </div>
      <div className="flex flex-1 flex-col mt-10 ml-4">
        {guidelineHeaders.map((header, index) => {
          const guidelineTagId = header.id.toString();
          return (
            <div key={`guideline-header-${index}`} className="flex flex-1 flex-col mb-10">
              <Typography className="text-[18px] mb-4">
                {formatToRomanNumeral(index + 1)}. <span className="ml-2">{header.productRevisionGuidelineTag}</span>{" "}
              </Typography>
              <div className="ml-4">
                {guidelines.map((value, gIndex) => {
                  if (value.productGuidelineTagId?.toString() !== guidelineTagId) {
                    return null;
                  }
                  return (
                    <div key={`guideline-${gIndex}`} className="flex flex-1 flex-col mb-10">
                      <Typography className="text-[18px] mb-4">{value.label}</Typography>
                      {value.productGuideline.map((pgValue, pgIndex) => {
                        let listValue;
                        let tableValue;
                        if (pgValue.type === "list") {
                          listValue = pgValue.value as IGuidelineContent[];
                        }
                        if (pgValue.type === "table") {
                          tableValue = pgValue.value as IGuidelineContentTable;
                        }
                        return (
                          <div key={`pg-${pgIndex}`}>
                            {pgValue.type === "textfield" && (
                              <Fragment>
                                <Typography className="ml-4 mt-4 text-justify">{pgValue.value as string}</Typography>
                              </Fragment>
                            )}
                            {pgValue.type === "list" && (
                              <Fragment>
                                <Typography className="ml-4 mt-4 text-justify">{pgValue.label}</Typography>
                                <ul className="list-disc ml-12">
                                  {listValue &&
                                    listValue.map((listValue, listIndex) => {
                                      return (
                                        <li key={`listItem-${listIndex}`} className="mt-4">
                                          <Typography className="text-justify">{listValue.value as string}</Typography>
                                        </li>
                                      );
                                    })}
                                </ul>
                              </Fragment>
                            )}
                            {pgValue.type === "texteditor" && (
                              <Fragment>
                                <div
                                  className="ml-10 mt-10"
                                  dangerouslySetInnerHTML={{
                                    __html: (pgValue as any).value ?? "",
                                  }}
                                ></div>
                              </Fragment>
                            )}
                            {pgValue.type === "table" && (
                              <Fragment>
                                <div className="flex flex-1 mt-10 mx-6">
                                  <table className="table border-[1px]">
                                    <thead className="table-header-group">
                                      <tr>
                                        {tableValue?.columns?.map((cValue, cIndex) => {
                                          return (
                                            <td key={`col-${cIndex}`} className="table-cell border-[1px]">
                                              <Typography className="font-semibold text-[16px]">{cValue.value as string}</Typography>
                                            </td>
                                          );
                                        })}
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {tableValue?.rows?.map((rValue, rIndex) => {
                                        return (
                                          <tr key={`row-${rIndex}`}>
                                            {rValue.map((cell, cellIndex) => {
                                              return (
                                                <td className="border-[1px]" key={`cell-${cellIndex}`}>
                                                  <Typography>{cell.value as string}</Typography>
                                                </td>
                                              );
                                            })}
                                          </tr>
                                        );
                                      })}
                                    </tbody>
                                  </table>
                                </div>
                              </Fragment>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>
      <div className="flex flex-1 flex-col">
        {commissionStructure.length > 0 && <Typography className="pt-10 !text-lg">Commission Structure</Typography>}
        {commissionStructure.map((value, index) => {
          const isStandard = value.commissionDetails?.filter((rowValue) => rowValue.commissionAgeTypeString?.toLowerCase() !== "standard") ?? [];
          return (
            <Fragment key={`commissionStructure-${index}`}>
              <Typography className="ml-4 mt-4">{value.maximumDisposableRate}% Maximum Disposable Commission - Standard Rate</Typography>
              <div className="flex-flex-1 mt-6 mx-6">
                <table className="table">
                  <thead>
                    <tr>
                      <td className="table-cell border-[1px] text-center text-nowrap">Type</td>
                      <td className="table-cell border-[1px] text-center text-nowrap">Age Type</td>
                      {isStandard.length > 0 && (
                        <Fragment>
                          <td className="table-cell border-[1px] text-center text-nowrap">Age From</td>
                          <td className="table-cell border-[1px] text-center text-nowrap">Age To</td>
                        </Fragment>
                      )}
                      <td className="table-cell border-[1px] text-center text-nowrap">Rate</td>
                    </tr>
                  </thead>
                  <tbody>
                    {value.commissionDetails?.map((rowValue, rowIndex) => {
                      return (
                        <tr key={`commissionDetailsRow-${rowIndex}`}>
                          <td className="table-cell border-[1px]">{rowValue?.commissionType?.commissionName || rowValue?.commissionTypeString}</td>
                          <td className="table-cell border-[1px]">{rowValue?.commissionAgeType?.name || rowValue?.commissionAgeTypeString}</td>
                          {isStandard.length > 0 && (
                            <Fragment>
                              <td className="table-cell border-[1px] text-center">{rowValue.ageFrom}</td>
                              <td className="table-cell border-[1px] text-center">{rowValue.ageTo}</td>
                            </Fragment>
                          )}
                          <td className="table-cell border-[1px] text-center">{rowValue.rate ? parseFloat(rowValue.rate.toString()).toFixed(2) : ""}%</td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </Fragment>
          );
        })}
      </div>
    </Fragment>
  );
};
export default Review;
