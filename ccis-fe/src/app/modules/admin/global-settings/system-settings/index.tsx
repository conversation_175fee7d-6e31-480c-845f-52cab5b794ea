import { useState, useEffect } from "react";
import { Plus, Save, X, Edit2, Check, RotateCcw, Trash2 } from "lucide-react";
import { useGlobalSettingsManagementActions } from "@state/reducer/global-settings";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import Button from "@components/common/Button";
import { toast } from "react-toastify";
import { IoChevronBack } from "react-icons/io5";
import { useNavigate } from "react-router-dom";
import CustomTextField from "@components/common/CustomTextFieldWithSearch";

const SystemSettingsManager = () => {
  const { putGlobalSettings, clearPutGlobalSettings } = useGlobalSettingsManagementActions();
  const systemSettings = useSelector((state: RootState) => state?.globalSettings?.globalSettings);

  const [selectedKey, setSelectedKey] = useState("");
  const [inputValue, setInputValue] = useState("");
  const [workingArray, setWorkingArray] = useState<number[]>([]);
  const [editingIndex, setEditingIndex] = useState(-1);
  const [editValue, setEditValue] = useState("");
  const [hasChanges, setHasChanges] = useState(false);
  const putGlobalSettingsState = useSelector((state: RootState) => state?.globalSettings?.putGlobalSettings);
  const navigate = useNavigate();

  // Use systemSettings directly instead of storing in state
  const originalSettings = systemSettings || [];

  useEffect(() => {
    if (putGlobalSettingsState?.success) {
      clearPutGlobalSettings();
      toast.success("Settings updated successfully!");
    }
  }, [putGlobalSettingsState?.success]);

  // Parse array from string value
  const parseArrayValue = (value: string) => {
    try {
      return JSON.parse(value);
    } catch {
      return [];
    }
  };

  // Get original array for selected key
  const getOriginalArray = () => {
    const setting = originalSettings?.find((s) => s?.key === selectedKey);
    if (!setting) return [];
    return parseArrayValue(String(setting?.value ?? ""));
  };

  // Initialize working array when key changes or when originalSettings loads
  useEffect(() => {
    if (selectedKey && originalSettings?.length > 0) {
      const originalArray = getOriginalArray();
      setWorkingArray([...originalArray]);
      setHasChanges(false);
      setEditingIndex(-1);
    }
  }, [selectedKey, originalSettings]);

  // Check if there are changes
  useEffect(() => {
    if (selectedKey && originalSettings?.length > 0) {
      const originalArray = getOriginalArray();
      const hasChanged = JSON.stringify(originalArray) !== JSON.stringify(workingArray);
      setHasChanges(hasChanged);
    }
  }, [workingArray, selectedKey, originalSettings]);

  // Handle adding new ID to the working array
  const handleAddId = () => {
    if (!inputValue?.trim()) return;

    const numericValue = parseInt(inputValue.trim());
    if (isNaN(numericValue)) {
      toast.info("Please enter a valid numeric ID");
      return;
    }

    // Check if ID already exists
    if (workingArray.includes(numericValue)) {
      toast.info("ID already exists in the array");
      return;
    }

    setWorkingArray([...workingArray, numericValue]);
    setInputValue("");
  };

  // Handle removing ID from working array
  const handleRemoveId = (index: number) => {
    const newArray = workingArray.filter((_, i) => i !== index);
    setWorkingArray(newArray);
  };

  // Handle editing an individual value
  const handleEditStart = (index: number) => {
    setEditingIndex(index);
    setEditValue(workingArray[index]?.toString() || "");
  };

  const handleEditSave = () => {
    const numericValue = parseInt(editValue);
    if (isNaN(numericValue)) {
      toast.info("Please enter a valid numeric ID");
      return;
    }

    // Check if the new value already exists elsewhere in the array
    const existingIndex = workingArray.findIndex((val) => val === numericValue);
    if (existingIndex !== -1 && existingIndex !== editingIndex) {
      toast.info("This ID already exists in the array");
      return;
    }

    const newArray = [...workingArray];
    newArray[editingIndex] = numericValue;
    setWorkingArray(newArray);
    setEditingIndex(-1);
    setEditValue("");
  };

  const handleEditCancel = () => {
    setEditingIndex(-1);
    setEditValue("");
  };

  // Handle bulk input (comma-separated values)
  const handleBulkAdd = () => {
    if (!inputValue?.trim()) return;

    const values = inputValue
      .split(",")
      .map((v) => v?.trim())
      .filter((v) => v);
    const numericValues = [];

    for (const value of values) {
      const numericValue = parseInt(value);
      if (isNaN(numericValue)) {
        toast.info(`"${value}" is not a valid numeric ID`);
        return;
      }
      numericValues.push(numericValue);
    }

    // Check for duplicates within the new values
    const uniqueValues = [...new Set(numericValues)];
    if (uniqueValues.length !== numericValues.length) {
      toast.info("Duplicate values found in input");
      return;
    }

    // Check for duplicates with existing array
    const duplicates = uniqueValues.filter((val) => workingArray.includes(val));
    if (duplicates.length > 0) {
      toast.info(`These IDs already exist: ${duplicates.join(", ")}`);
      return;
    }

    setWorkingArray([...workingArray, ...uniqueValues]);
    setInputValue("");
  };

  // Reset to original values
  const handleReset = () => {
    const originalArray = getOriginalArray();
    setWorkingArray([...originalArray]);
    setEditingIndex(-1);
  };

  // Clear all values
  const handleClearAll = () => {
    if (confirm("Are you sure you want to clear all values?")) {
      setWorkingArray([]);
    }
  };

  // Generate POST data
  const generatePostData = () => {
    if (!selectedKey || !hasChanges) return null;

    return {
      key: selectedKey,
      value: JSON.stringify(workingArray),
    };
  };

  const selectedSetting = originalSettings?.find((s) => s?.key === selectedKey);
  const postData = generatePostData();

  const handleSaveChanges = () => {
    const postData = generatePostData();
    if (!postData) {
      toast.info("No changes to save");
      return;
    }

    // Then make the actual API call
    putGlobalSettings(postData);
  };

  return (
    <>
      <Button classNames="bg-white border-0 flex items-center justify-center" outline variant="primary" onClick={() => navigate(-1)}>
        <IoChevronBack />
        Back
      </Button>
      <div className="max-w-5xl mx-auto p-6 bg-white">
        <h1 className="text-2xl font-bold text-gray-800 mb-6">System Settings Batch Editor</h1>

        <div className="mb-4 p-3 bg-blue-50 border-l-4 border-blue-400 text-blue-700">
          <p className="text-sm">
            <strong>Batch Editing:</strong> Make multiple changes (add, edit, remove) to the array, then save all changes with one POST request.
          </p>
        </div>

        {/* Loading state */}
        {!originalSettings?.length && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <p className="text-gray-600">Loading settings...</p>
          </div>
        )}

        {/* Key Selection */}
        {originalSettings?.length > 0 && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">Select Setting Key:</label>
            <CustomTextField
              value={selectedKey}
              onChange={(e) => setSelectedKey(e.target.value)}
              suggestionOptions={originalSettings}
              getOptionLabel={(setting) => `${setting?.key || "Unknown"} (ID: ${setting?.id || "N/A"})`}
              getOptionValue={(setting) => setting?.key || ""}
              placeholder="Search and select a setting..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        )}

        {/* Working Array Display */}
        {selectedKey && originalSettings?.length > 0 && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-medium text-gray-800">
                Working Array:
                <span className="text-sm font-normal text-gray-600 ml-2">
                  ({workingArray?.length || 0} items)
                  {hasChanges && <span className="text-orange-600 font-medium"> • Modified</span>}
                </span>
              </h3>
              <div className="flex gap-2">
                <button
                  onClick={handleReset}
                  disabled={!hasChanges}
                  className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center gap-1"
                >
                  <RotateCcw size={14} />
                  Reset
                </button>
                <button onClick={handleClearAll} className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 flex items-center gap-1">
                  <Trash2 size={14} />
                  Clear All
                </button>
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              {workingArray?.length > 0 ? (
                workingArray.map((value, index) => (
                  <div key={index} className="inline-flex items-center">
                    {editingIndex === index ? (
                      <div className="flex items-center bg-yellow-100 rounded-full px-2 py-1">
                        <input
                          type="number"
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="w-16 px-1 text-sm border rounded"
                          onKeyPress={(e) => e.key === "Enter" && handleEditSave()}
                        />
                        <button onClick={handleEditSave} className="ml-1 text-green-600 hover:text-green-800">
                          <Check size={14} />
                        </button>
                        <button onClick={handleEditCancel} className="ml-1 text-red-600 hover:text-red-800">
                          <X size={14} />
                        </button>
                      </div>
                    ) : (
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-grey-400 text-white">
                        {value}
                        <button onClick={() => handleEditStart(index)} className="ml-2 text-blue-600 hover:text-green-600" title="Edit this value">
                          <Edit2 size={14} />
                        </button>
                        <button onClick={() => handleRemoveId(index)} className="ml-1 text-blue-600 hover:text-red-600" title="Remove this value">
                          <X size={14} />
                        </button>
                      </span>
                    )}
                  </div>
                ))
              ) : (
                <span className="text-gray-500 italic">No values in array - start by adding some IDs</span>
              )}
            </div>
          </div>
        )}

        {/* Add New IDs */}
        {selectedKey && originalSettings?.length > 0 && (
          <div className="mb-6 p-4 border rounded-lg">
            <h3 className="text-lg font-medium text-gray-800 mb-3">Add New IDs:</h3>
            <div className="flex gap-2 mb-2">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="Enter ID (or comma-separated IDs: 1,2,3)..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                onKeyPress={(e) => e.key === "Enter" && (inputValue?.includes(",") ? handleBulkAdd() : handleAddId())}
              />
              <Button onClick={inputValue?.includes(",") ? handleBulkAdd : handleAddId} disabled={!inputValue?.trim()} classNames="flex items-center gap-1" variant="primary">
                <Plus size={16} />
                Add
              </Button>
            </div>
            <div className="text-xs text-gray-500">Tip: You can add multiple IDs at once by separating them with commas (e.g., "1,2,3,4")</div>
          </div>
        )}

        {/* POST Data Preview */}
        {postData && hasChanges && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h3 className="text-lg font-medium text-green-800 mb-2 flex items-center gap-2">
              <Save size={20} />
              POST Data Ready - Batch Update:
            </h3>
            <pre className="bg-white p-3 rounded border text-sm overflow-x-auto">{JSON.stringify(postData, null, 2)}</pre>
            <div className="mt-3 text-sm text-green-700">
              This contains all your changes and is ready to be sent to your API endpoint.
              <br />
              Original: {JSON.stringify(getOriginalArray())} → Modified: {JSON.stringify(workingArray)}
            </div>
            <div className="flex gap-2 mt-2">
              <button onClick={() => navigator.clipboard.writeText(JSON.stringify(postData))} className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700">
                Copy POST Data
              </button>
              <button onClick={handleSaveChanges} className="px-3 py-1 bg-primary text-white text-sm rounded hover:bg-blue-700 flex items-center gap-1">
                <Save size={14} />
                Save Changes
              </button>
            </div>
          </div>
        )}

        {/* Setting Details */}
        {selectedSetting && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="text-lg font-medium text-blue-800 mb-2">Setting Details:</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
              <div>
                <strong>ID:</strong> {selectedSetting?.id || "N/A"}
              </div>
              <div>
                <strong>Key:</strong> {selectedSetting?.key || "N/A"}
              </div>
              <div>
                <strong>Created:</strong> {selectedSetting?.createdAt || "N/A"}
              </div>
              <div>
                <strong>Updated:</strong> {selectedSetting?.updatedAt || "N/A"}
              </div>
              <div className="md:col-span-2">
                <strong>Original Value:</strong> {selectedSetting?.value || "N/A"}
              </div>
              {hasChanges && (
                <div className="md:col-span-2">
                  <strong>New Value:</strong> {JSON.stringify(workingArray)}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default SystemSettingsManager;
