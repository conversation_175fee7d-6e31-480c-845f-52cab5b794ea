import { FC, Fragment, useEffect, useState } from "react";
import { TableColumn } from "react-data-table-component";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import { CiEdit } from "react-icons/ci";
import { GrValidate } from "react-icons/gr";
import { IActions } from "@interface/common.interface";
import Table from "@components/common/Table";
import ActionDropdown from "@components/common/ActionDropdown";
import { ViewSharesModal } from "./Forms/ViewShares";
import { FaLock } from "react-icons/fa";
import { IShares } from "@interface/shares.interface";
import { useSharesManagementActions } from "@state/reducer/shares";
import Typography from "@components/common/Typography";
import { capitalizeFirstLetterWords, getTextStatusColor, formatStringAtoZ0to9 } from "@helpers/text";
import { IUserRPermission } from "@interface/user.interface";
import { canPerformOperation } from "@helpers/permissions";
import { showAlert } from "@helpers/prompt";
import { PermissionType, UserRoles } from "@interface/routes.interface";
import { RiProgress6Line } from "react-icons/ri";
import { GoVersions } from "react-icons/go";
import { Status, membershipName } from "@constants/global-constant-value";
import { toast } from "react-toastify";
import { useSharesPaymentsManagementActions } from "@state/reducer/share-payments";

import { printFile } from "@helpers/print";
import { sortByKey } from "@helpers/array";
type ChangeViewProps = {
  changeView: (view: "ChooseCooperative" | "CooperativeInformation" | "Requirements" | "Review" | "Validate" | "Table" | "ReviewInformations") => void;
  setDisableRequirementTemplate: (value: boolean) => void;
  pageLocation?: string;
  setIsEditShares?: (value: boolean) => void;
};

const SharesTable: FC<ChangeViewProps> = ({ changeView, setDisableRequirementTemplate, pageLocation, setIsEditShares }) => {
  const [searchText, setSearchText] = useState<string>("");
  const [isModalViewShares, setIsModalViewShares] = useState<boolean>(false);
  const [existing, setExisting] = useState<boolean>(false);
  const [membershipTypeName, setMembershipTypeName] = useState<string>("");
  const [_page, setPage] = useState<number>(1);
  const [_pageSize, setPageSize] = useState<number>(10);
  const shares = useSelector((state: RootState) => state.shares.shares);
  const selectedShares = useSelector((state: RootState) => state?.shares?.selectedShares?.data);
  const loading = useSelector((state: RootState) => state?.shares?.getShares?.loading);
  const loadingDataSharesStatus = useSelector((state: RootState) => state?.shares?.getSharesByStatus?.loading);
  const userId = useSelector((state: RootState) => state.auth.user.data?.id);
  const { getShares, getSharesByUser, setSelectedShares, clearSelectedShares, getSharesByStatus } = useSharesManagementActions();
  const { getSharePaymentOr, clearSelectedSharesPayment } = useSharesPaymentsManagementActions();

  const user: IUserRPermission = useSelector((state: RootState) => {
    const userData = state?.auth?.user?.data;
    const roles = userData?.roles ?? [];
    return {
      ...userData,
      roles,
    } as IUserRPermission; // Type assertion to match IUserRPermission
  });
  const pdfUrl = useSelector((state: RootState) => state?.sharesPayments?.getSharePaymentOr?.pdfUrl);
  const getPaymentSuccess = useSelector((state: RootState) => state?.sharesPayments?.getSharePaymentOr?.success);
  const [selectedShareEditColumn, setSelectedShareEditColumn] = useState<IShares | undefined>(undefined);

  const [selectedShareIndex, setSelectedShareIndex] = useState<number | undefined>(undefined);
  const [selectedShareData, setSelectedShareData] = useState<IShares | undefined>(undefined);
  const [_selectedData, setSelectedData] = useState<any[]>([]);
  const handleSelectedRowsChange = (rows: any[]) => {
    setSelectedData(rows);
  };
  //for future use
  // const showPermissionError = () => {
  //   toast.error("You do not have permission to display those actions.");
  // };
  useEffect(() => {
    const membershipTypeName = selectedShares?.cooperativeMembershipType?.membershipTypeName;
    setMembershipTypeName(membershipTypeName);
    setExisting(membershipTypeName === membershipName.existing);
  }, [selectedShares]);

  // for future use
  // useEffect(() => {
  //   if (
  //     actionButtonCheck(
  //       user,
  //       statusShare,
  //       [UserRoles.sales],
  //       [PermissionType.SHARES_EDIT],
  //       [Status.forOrIssuance]
  //     )
  //   ) {
  //     showPermissionError();
  //   }
  // }, [selectedShareEditColumn]);

  const onSelectedShareData = (sharesData: IShares, index: number) => {
    setSelectedShareIndex(index);
    setSelectedShareData(sharesData);
  };

  useEffect(() => {
    canPerformOperation(user, PermissionType.SHARES_VIEW, [UserRoles.sales])
      ? getSharesByUser({ filter: searchText })
      : canPerformOperation(user, PermissionType.SHARES_VIEW, [UserRoles.treasury])
        ? getSharesByStatus({
            filter: searchText,
            condition: "status[eq]=" + Status.completed,
            //Note: page & pageSize are commented out for future use
            // page: page,
            // pageSize: pageSize,
          })
        : canPerformOperation(user, PermissionType.SHARES_EDIT, [UserRoles.marketing])
          ? getSharesByStatus({
              filter: searchText,
              condition: "status[eq]=" + Status.forApproval,
              // page: page,
              // pageSize: pageSize,
            })
          : canPerformOperation(user, PermissionType.SHARES_EDIT, [UserRoles.cashier])
            ? getSharesByStatus({
                // page: page,
                // pageSize: pageSize,
                filter: searchText,
                id: userId,
                type: membershipName.existing,
              })
            : getShares({ filter: searchText });
  }, [searchText, membershipTypeName]);
  const handleRowClick = (row: IShares) => {
    setSelectedShareEditColumn(row);
    // Perform any additional actions with the share data
  };

  useEffect(() => {
    if (!membershipTypeName) return;
    const hasPermission = (roles: UserRoles[]) => canPerformOperation(user, PermissionType.REQUIREMENT_TEMPLATE_UPDATE && PermissionType.REQUIREMENT_UPDATE, roles);

    const isStatusAre =
      selectedShares?.status === Status.invalidRequirement ||
      selectedShares?.status === Status.forApproval ||
      selectedShares?.status === Status.forOrIssuance ||
      selectedShares?.status === Status.completed ||
      selectedShares?.status === Status.draft;

    const handlePermissionDenied = () => {
      showAlert("Permission Denied", "You do not have permission to edit this share capital - Only for Marketing Access.", <FaLock size="50" color="red" />);
      handleViewChange("Table");
    };

    const handleValidateOrReview = (view: string) => {
      handleViewChange(view);
    };

    const hasCashierPermission = hasPermission([UserRoles.cashier]);
    const hasMarketingPermission = hasPermission([UserRoles.marketing]);

    if (hasCashierPermission || hasMarketingPermission) {
      if (membershipTypeName === membershipName.existing && isStatusAre && existing) {
        handleViewChange("Review");
      } else if (membershipTypeName.includes("New") && isStatusAre && !existing) {
        if ((hasCashierPermission && hasMarketingPermission) || hasMarketingPermission || hasCashierPermission) {
          handleValidateOrReview("Validate");
        } else {
          handlePermissionDenied();
          clearSelectedShares();
        }
      } else {
        handlePermissionDenied();
        handleViewChange("Table");
      }
    } else {
      handleViewChange("Table");
    }

    if (!isStatusAre) {
      showAlert("Status Alert", "Action not allowed: You cannot validate or issue an OR when the status is marked as completed.", <FaLock size="50" color="red" />);
      clearSelectedShares();
    }
  }, [membershipTypeName, isModalViewShares, existing]);

  useEffect(() => {
    let printed = false;
    if (getPaymentSuccess) {
      handlePrint(pdfUrl);
      printed = true;
    }
    if (printed) {
      setTimeout(() => {
        clearSelectedSharesPayment();
      }, 1000); // 1 seconds delay
    }
  }, [getPaymentSuccess]);
  const handlePrint = async (pdfUrl: Blob | string | null | undefined) => {
    await printFile(pdfUrl);
  };
  const showModalViewShares = (data: IShares) => {
    if (data.status === Status.completed && canPerformOperation(user, PermissionType.SHARES_PAYMENT_CREATE, [UserRoles.treasury, UserRoles.sales, UserRoles.cashier])) {
      setIsModalViewShares(true);
    } else {
      showAlert("Action Required", "Please provide an Official Receipt to complete the shares transaction.", <RiProgress6Line size="50" color="red" />);
    }
  };

  const editCos = (data: IShares) => {
    const { status } = data;

    const editableStatuses = [Status.invalidRequirement, Status.draft, Status.forApproval];
    const isEditable = editableStatuses.includes(status as Status);

    const handleEdit = () => {
      setDisableRequirementTemplate(true);
      status === Status.forApproval ? setIsEditShares?.(false) : setIsEditShares?.(true);

      const view = status === Status.invalidRequirement || status === Status.draft ? "Requirements" : "ReviewInformations";

      changeView(view);
    };

    const handleActionDenied = () => {
      clearSelectedShares();
      showAlert("Action Denied", "This share capital can be edited when the status is either 'Draft' or 'Invalid Requirement'.", <RiProgress6Line size="50" color="red" />);
    };

    const handleActionRequired = () => {
      clearSelectedShares();
      showAlert("Action Required", "Editing/Viewing is allowed only for the user who created the share capital.", <RiProgress6Line size="50" color="red" />);
    };

    if (isEditable) {
      handleEdit();
    } else if (!isEditable) {
      handleActionDenied();
    } else {
      handleActionRequired();
    }
  };
  const isTreasuryRole = user?.roles?.some((role) => formatStringAtoZ0to9(role.name) === formatStringAtoZ0to9(UserRoles.treasury)) || false;
  const isCashierRole = user?.roles?.some((role) => formatStringAtoZ0to9(role.name) === formatStringAtoZ0to9(UserRoles.cashier)) || false;
  const checkCertificatePrint = (data: IShares, index?: number) => {
    const { cosDatePrinted } = data;
    if (cosDatePrinted === null && !isTreasuryRole) {
      toast.error("The Certificate of Stocks has not been printed yet. Please contact the treasurer to print it.");
    } else {
      onSelectedShareData(data, index as number);
      showModalViewShares(data);
    }
  };
  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const isMembershipTypeExcluded = (membershipTypeName: string) => {
    const excludedTypes = [membershipName.newWithRemittance, membershipName.newWithoutRemittance];
    return excludedTypes.some((type) => formatStringAtoZ0to9(membershipTypeName) === formatStringAtoZ0to9(type));
  };

  const membershipTypeNames = selectedShareEditColumn?.cooperativeMembershipType?.membershipTypeName ?? "";
  const statusShare = selectedShareEditColumn?.status ?? "";
  const isStatusEqualCheck = (status: string, statusEnum: Status): boolean => {
    return formatStringAtoZ0to9(status) === formatStringAtoZ0to9(statusEnum);
  };
  const actionButtonCheck = (
    user: IUserRPermission,
    statusShare: string,
    userRoles: UserRoles[],
    permissions: PermissionType[], // Accept an array of permissions
    statusEnums: Status[], // Use the union type for the array
    excludedMembershipTypes?: (membershipTypeNames: string) => boolean,
    createdBy?: number // Optional parameter for createdBy
  ): boolean => {
    return (
      permissions.some((permission) => canPerformOperation(user, permission, userRoles)) && // Check if the user has any of the provided permissions
      statusEnums.some((statusEnum) => isStatusEqualCheck(statusShare, statusEnum)) && // Check if statusShare matches any status in the array
      (!excludedMembershipTypes || !excludedMembershipTypes(membershipTypeNames)) && // Check excluded membership types
      (createdBy === undefined || createdBy === userId) // Optional check for createdBy
    );
  };
  const validateAndIssueORAction = {
    name: isCashierRole ? "Validate / Issue OR" : "Validate",
    event: (data: IShares, index: number) => {
      setSelectedShares({ data, index });
    },
    icon: GrValidate,
    color: "primary",
    disabled: false,
  };
  const sortedShares = sortByKey(shares, "id", "desc");
  const actionEvents: IActions<IShares>[] = [
    ...(canPerformOperation(user, PermissionType.REQUIREMENT_TEMPLATE_UPDATE && PermissionType.REQUIREMENT_UPDATE, [UserRoles.marketing]) ||
    actionButtonCheck(
      user,
      statusShare,
      [UserRoles.cashier],
      [PermissionType.REQUIREMENT_TEMPLATE_UPDATE, PermissionType.REQUIREMENT_UPDATE], // Pass dynamic permissions
      [Status.forApproval],
      isMembershipTypeExcluded
    ) ||
    actionButtonCheck(
      user,
      statusShare,
      [UserRoles.cashier],
      [PermissionType.REQUIREMENT_TEMPLATE_UPDATE, PermissionType.REQUIREMENT_UPDATE], // Pass dynamic permissions
      [Status.forOrIssuance]
    )
      ? [validateAndIssueORAction]
      : []),

    ...(canPerformOperation(user, PermissionType.SHARES_PAYMENT_EDIT, [UserRoles.treasury]) ||
    actionButtonCheck(
      user,
      statusShare,
      [UserRoles.sales, UserRoles.cashier],
      [PermissionType.SHARES_PAYMENT_EDIT],
      [Status.completed]
      // (membershipTypeName) => !isMembershipTypeExcluded(membershipTypeName) // Negate the function
    )
      ? [
          {
            name: "Certificate",
            event: (data: IShares, index: number) => {
              checkCertificatePrint(data, index);
            },
            icon: GoVersions,
            color: "primary",
          },
        ]
      : []),

    ...(actionButtonCheck(
      user,
      statusShare,
      [UserRoles.sales, UserRoles.cashier, UserRoles.treasury, UserRoles.marketing],
      [PermissionType.SHARES_CREATE, PermissionType.SHARES_EDIT],
      [Status.draft, Status.forApproval, Status.forOrIssuance, Status.completed]
    )
      ? [
          {
            name: "View Shares Details",
            event: (data: IShares, index: number) => {
              setSelectedShares({ data, index });

              const view = data.status === Status.draft ? "Requirements" : "ReviewInformations";
              changeView(view);
            },
            icon: GoVersions,
            color: "primary",
          },
        ]
      : []),
    ...(actionButtonCheck(user, statusShare, [UserRoles.sales], [PermissionType.REQUIREMENT_TEMPLATE_UPDATE, PermissionType.REQUIREMENT_UPDATE], [Status.invalidRequirement]) ||
    actionButtonCheck(user, statusShare, [UserRoles.cashier], [PermissionType.SHARES_EDIT], [Status.invalidRequirement], undefined, (selectedShareEditColumn as any)?.createdBy)
      ? [
          {
            name: "Edit",
            event: (data: IShares, index: number) => {
              setSelectedShares({ data, index });
              editCos(data);
            },
            icon: CiEdit,
            color: "primary",
          },
        ]
      : []),

    ...(actionButtonCheck(
      user,
      statusShare,
      [UserRoles.sales, UserRoles.cashier, UserRoles.treasury],
      [PermissionType.REQUIREMENT_TEMPLATE_UPDATE, PermissionType.REQUIREMENT_UPDATE, PermissionType.SHARES_VIEW],
      [Status.completed]
    )
      ? [
          {
            name: "View OR",
            event: (data: IShares) => {
              const shareID = data?.id;
              getSharePaymentOr({ shareID });
            },
            icon: CiEdit,
            color: "primary",
          },
        ]
      : []),
  ];

  const columns: TableColumn<IShares>[] = [
    {
      name: "Cooperative",
      selector: (row) => row?.cooperative?.coopName || "N/A",
      ...commonSetting,
    },
    {
      name: "Client Type",
      selector: (row) => row?.cooperativeMembershipType?.membershipTypeName || "N/A",
      ...commonSetting,
    },

    {
      name: "Subscriber Share Capital",
      selector: (row) => row.shareType?.shareTypeName || "N/A",
      ...commonSetting,
    },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => <span className={`${getTextStatusColor(row.status)}`}>{capitalizeFirstLetterWords(row.status || "N/A", " ")}</span>,
      ...commonSetting,
    },

    ...(!canPerformOperation(user, PermissionType.SHARES_VIEW, [UserRoles.admin])
      ? [
          {
            name: <Typography className="flex justify-center !text-black !text-xs">Actions</Typography>,
            cell: (row, rowIndex) => (
              <div className="flex justify-center" onClick={() => handleRowClick(row)}>
                <ActionDropdown actions={actionEvents} data={row} rowIndex={rowIndex} />
              </div>
            ),

            ...commonSetting,
          } as TableColumn<IShares>,
        ]
      : []),
  ];

  const handleViewChange = (view: any) => {
    changeView(view);
  };
  const handlePaginate = (pagination: number) => {
    setPage(pagination);
  };
  const handleRowsChange = (newPerPage: number, pagination: number) => {
    setPageSize(newPerPage);
    setPage(pagination);
  };

  return (
    <Fragment>
      <div className="text-xl font-poppins-semibold  my-4 text-primary">{pageLocation ? pageLocation : "Dashboard / Shares"}</div>
      <Table
        className="h-[600px]"
        columns={columns}
        data={sortedShares}
        createLabel="Add Share Capital"
        onCreate={() => {
          setExisting(true);
          handleViewChange("ChooseCooperative");
          clearSelectedShares();
        }}
        loading={
          canPerformOperation(user, PermissionType.SHARES_VIEW, [UserRoles.sales])
            ? loading
            : canPerformOperation(user, PermissionType.SHARES_VIEW, [UserRoles.treasury])
              ? loadingDataSharesStatus
              : loading
        }
        onSearch={setSearchText}
        onPaginate={handlePaginate}
        onChangeRowsPerPage={handleRowsChange}
        multiSelect={true}
        searchable
        hideButton={canPerformOperation(user, PermissionType.SHARES_CREATE, [UserRoles.sales, UserRoles.cashier]) ? "visible" : "invisible"}
        onSelectedRowsChange={handleSelectedRowsChange}
        selectable={true}
      />
      <ViewSharesModal
        modalOpenShares={isModalViewShares}
        onSelectedShareData={selectedShareData}
        selectedIndex={selectedShareIndex}
        onClose={() => {
          setIsModalViewShares(false);
          clearSelectedShares();
        }}
      />
    </Fragment>
  );
};

export default SharesTable;
