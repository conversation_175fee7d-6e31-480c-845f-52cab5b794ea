import React, { useEffect, useState, ChangeEvent, useRef } from "react";
import Button from "@components/common/Button";
import { IoChevronBack } from "react-icons/io5";
import TextField from "@components/form/TextField";
import { IoIosSearch } from "react-icons/io";
import Modal from "@components/common/Modal";
import Folder_Search from "/assets/rafiki.png";
import { PiSmileySadBold } from "react-icons/pi";
import { useCooperativesManagementActions } from "@state/reducer/cooperatives";
import { useCooperativesCdaManagementActions } from "@state/reducer/cooperatives-cda";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { useDebouncedCallback } from "use-debounce";
import { showAlert } from "@helpers/prompt";
import { ISharesCoopInformation as ISharesCoopInformationType } from "@interface/shares.interface";
import { ICda } from "@interface/cooperatives-cda";
import { canPerformOperation } from "@helpers/permissions";
import { IUserRPermission } from "@interface/user.interface";
import { PermissionType, UserRoles } from "@interface/routes.interface";
import { toast } from "react-toastify";
import Typography from "@components/common/Typography";
import LoadingButton from "@components/common/LoadingButton";
import { useNavigate } from "react-router-dom";

type ViewType = "ChooseCooperative" | "CooperativeInformation" | "Requirements" | "Review" | "Validate" | "Table";

type ChangeViewProps = {
  changeView: (view: ViewType) => void;
  onSelectCoop: (id?: number, coopData?: ISharesCoopInformationType | ICda) => void; // Update this line
  setIsEditCoop: (value: boolean) => void; // Add this line
  setIsAddtionalPayment: (value: boolean) => void;
};

const ChooseCooperative: React.FC<ChangeViewProps> = ({
  changeView,
  onSelectCoop,
  setIsEditCoop, // Receive the setter function as a prop
  setIsAddtionalPayment,
}) => {
  const [searchText, setSearchText] = useState<{ [key: string]: string }>({
    searchCoop: "",
    searchCda: "",
  });
  const [selectedCoopId, setSelectedCoopId] = useState<number | undefined>(undefined);
  const [isExist, setIsExist] = useState<boolean>(false);
  const [isClicked, setIsClicked] = useState<boolean>(false);
  const [isCoopClicked, setIsCoopClicked] = useState(false);
  const [selectedCoopData, setSelectedCoopData] = useState<ISharesCoopInformationType | ICda | undefined>(undefined);
  const { getCooperatives, clearSelectedCooperatives } = useCooperativesManagementActions();
  const { getCooperativesCda } = useCooperativesCdaManagementActions();
  const cooperatives = useSelector((state: RootState) => state?.cooperatives?.cooperatives);
  const cooperativesCda = useSelector((state: RootState) => state?.cooperativesCda?.cooperativesCda);
  const loadingGetCooperatives = useSelector((state: RootState) => state?.cooperatives?.getCooperatives?.loading);
  const loadingCda = useSelector((state: RootState) => state?.cooperativesCda?.getCooperativesCda?.loading);
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const user: IUserRPermission = useSelector((state: RootState) => {
    const userData = state?.auth?.user?.data;
    const roles = userData?.roles ?? [];
    return {
      ...userData,
      roles,
    } as IUserRPermission; // Type assertion to match IUserRPermission
  });
  const buttonRefContinue = useRef<HTMLButtonElement>(null);
  const navigate = useNavigate();
  const handleCoopClick = (id?: number, coopData?: ISharesCoopInformationType | ICda) => {
    if (id !== undefined && coopData !== undefined) {
      setSelectedCoopId(id); // Set the selected cooperative ID
      setIsCoopClicked(true);
      setSelectedCoopData(coopData); // Set the selected cooperative data
      getCooperatives({ payload: { filter: coopData?.coopName as string } });
    }
  };
  useEffect(() => {
    if (cooperatives && cooperatives.length > 0) {
      setIsExist(true);
    } else {
      clearSelectedCooperatives();
      setIsExist(false);
    }
  }, [cooperatives, selectedCoopData]);

  const handleModal = () => {
    setModalOpen((prev) => !prev);
  };

  const handleViewChange = (view: ViewType) => {
    changeView(view);
  };

  const handleSearchCooperatives = useDebouncedCallback(
    (value: string) => {
      getCooperatives({ payload: { filter: value } });
    },
    500 // 500ms debounce delay
  );

  const handleSearchCooperativesCda = useDebouncedCallback(
    (value: string) => {
      getCooperativesCda({ filter: value });
    },
    500 // 500ms debounce delay
  );

  const onSearchChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { value, name } = event.target;
    setSearchText((prev) => ({ ...prev, [name]: value }));
    handleSearchCooperatives(value);
    handleSearchCooperativesCda(value);
    setIsClicked(false);
  };
  useEffect(() => {
    getCooperatives({ payload: { filter: searchText.searchCoop, page: 1, pageSize: 15 } });
    getCooperatives({ payload: { filter: searchText.searchCda, page: 1, pageSize: 15 } });
  }, [searchText.searchCoop, searchText.searchCda]);

  useEffect(() => {
    if (modalOpen) {
    }
    getCooperativesCda({ filter: searchText.searchCda, page: 1, pageSize: 15 });
  }, [modalOpen]);
  const handleContinueClick = (coopDataType: "cooperative" | "cooperativeCda") => {
    // Set isEditCoop to true using the setter function
    if (coopDataType === "cooperative") {
      if (selectedCoopId !== undefined) {
        const coopData = cooperatives.find((coop) => coop.id === selectedCoopId);
        if (coopData) {
          setIsEditCoop(true); // Set isEditCoop to true using the setter function
          onSelectCoop(selectedCoopId, coopData); // Pass both ID and data

          handleViewChange("CooperativeInformation");
        }
      }
    } else {
      if (selectedCoopId !== undefined) {
        const coopData = cooperativesCda.find((coop) => coop.coopCdaId === selectedCoopId);
        if (coopData) {
          setIsEditCoop(false); // Set isEditCoop to false using the setter function
          onSelectCoop(selectedCoopId, coopData);
          handleViewChange("CooperativeInformation");
        } else {
          showAlert("Warning", "No cooperative Cda data found. Please select a valid cooperative Cda before continuing .", <PiSmileySadBold color="red" size={100} />);
        }
      } else {
        toast.error("Please select a cooperative before continuing.");
      }
    }
  };

  useEffect(() => {
    if (isCoopClicked && selectedCoopId !== undefined && buttonRefContinue.current) {
      buttonRefContinue.current.click();
      setIsAddtionalPayment(true);
      setIsCoopClicked(false);
    } else if (isCoopClicked && selectedCoopId === undefined) {
      showAlert("Warning", "No cooperative data found. Please select a valid cooperative before continuing from useffect.", <PiSmileySadBold color="red" size={100} />);
      setIsCoopClicked(false);
    }
  }, [isCoopClicked, selectedCoopId]);
  const noRecords = !loadingCda && cooperativesCda.length === 0;
  const handleAddCoopPage = () => {
    const pathRole = location.pathname.split("/")[1]; // e.g., 'admin'
    navigate(`/${pathRole}/utilities/cooperatives/create`, { replace: true });
  };
  return (
    <>
      {modalOpen && (
        <Modal
          isOpen={modalOpen}
          onClose={() => {
            handleModal();
            setIsExist(false);
          }}
          title="Choose Cooperatives from CDA List"
          modalContainerClassName="max-w-4xl"
        >
          <div className="w-full pt-6 border-t border-zinc-200">
            <TextField type="text" leftIcon={<IoIosSearch color="primary" size={20} />} placeholder="searchCda" name="search" className="bg-white py-6 text-sm" onChange={onSearchChange} required />
          </div>
          <div className="w-full h-full">
            <div className="w-full font-poppins-semibold text-base rounded-md bg-indigo-50 text-primary p-4">Cooperatives</div>
            <div className="flex flex-row items-center justify-between w-full">
              {isExist && isClicked && (
                <div className="flex justify-center my-4">
                  <Typography className="!text-danger">Cooperative is already registered</Typography>
                </div>
              )}
            </div>

            <div className="divide-y divide-zinc-200 flex flex-col text-zinc-600 h-[30rem] overflow-auto">
              {cooperativesCda.map((item) => (
                <div
                  key={item?.coopCdaId}
                  onClick={() => {
                    handleCoopClick(item?.coopCdaId, item);
                    setIsClicked(true);
                  }}
                  className={`
        group
        flex flex-1 cursor-pointer px-2 items-center border-[1px] border-zinc-200 min-h-12 max-h-12 my-[1px]
        ${item?.coopCdaId === selectedCoopId ? "bg-primary text-white" : "hover:bg-primary hover:text-white"}
      `}
                >
                  {item.coopName}
                </div>
              ))}
            </div>
            <div className="w-full flex items-center justify-between text-sm mt-6">
              <Button type="button" classNames="px-12 bg-zinc-400 btn" onClick={handleModal}>
                Close
              </Button>
              {!noRecords ? (
                <LoadingButton
                  type="button"
                  disabled={isExist}
                  isLoading={loadingGetCooperatives}
                  className="btn bg-primary text-white !w-32"
                  onClick={() => {
                    setIsAddtionalPayment(false);
                    handleContinueClick("cooperativeCda");
                  }}
                >
                  Select
                </LoadingButton>
              ) : (
                <LoadingButton type="button" className="btn bg-primary text-white !w-32" onClick={handleAddCoopPage}>
                  Add New
                </LoadingButton>
              )}
            </div>
          </div>
        </Modal>
      )}

      <div className="w-full flex-col flex items-start h-1/5">
        <div className="border-b border-zinc-200 w-full py-4">
          <Button
            classNames="flex items-center justify-center border-none"
            type="button"
            variant="primary"
            outline
            onClick={() => {
              handleViewChange("Table");
            }}
          >
            <IoChevronBack />
            Back
          </Button>
        </div>
        <div className="w-full p-6 text-primary">
          <div className="font-poppins-semibold mb-2 text-2xl">Select Cooperative</div>
          <div className="text-zinc-500 mb-8 text-sm">Choose a coop that intends to make share capital investments.</div>
          <div className="w-full">
            <TextField type="text" leftIcon={<IoIosSearch color="primary" size={20} />} placeholder="Search" name="searchCoop" className="bg-white py-6 text-sm" onChange={onSearchChange} required />
          </div>
          <div className="w-full h-[50vh] pt-6">
            <div className="w-full font-poppins-semibold text-base rounded-md bg-indigo-50 p-4">Cooperatives</div>
            {cooperatives.length !== 0 && (
              <div className="divide-y divide-zinc-200 flex flex-col text-zinc-200 border b-4 h-full overflow-y-auto">
                {cooperatives.map((item) => (
                  <div
                    key={item.id}
                    onClick={() => handleCoopClick(item.id, item)} // Pass cooperative data
                    className={`
                    group flex flex-row cursor-pointer px-2 items-center border-zinc-500 min-h-12 my-[0.5px] hover:bg-primary !hover:text-white  ${
                      item.id === selectedCoopId ? "bg-primary text-white" : "hover:bg-primary hover:text-white"
                    }
                  `}
                  >
                    <span
                      className={`text-zinc-500 group-hover:text-white
                      item?.id === coop.id ? "!text-white" : ""
                    }`}
                    >
                      {" "}
                      {item.coopName}
                    </span>
                  </div>
                ))}
              </div>
            )}

            {canPerformOperation(user, PermissionType.SHARES_CREATE, [UserRoles.sales]) && cooperatives.length === 0 && (
              <div className="w-full p-6 flex flex-col items-center justify-center gap-4">
                <img src={Folder_Search} className="w-max h-40 object-cover" />
                <div className="text-center">
                  <div className="text-xl text-zinc-700 font-poppins-semibold">No results found.</div>
                  <div className="text-xs text-zinc-400">We couldn't find any cooperatives that match your search.</div>
                </div>

                <Button
                  type="button"
                  variant="primary"
                  onClick={() => {
                    handleModal();
                  }}
                  classNames="btn px-12"
                >
                  Search Coop
                </Button>
              </div>
            )}
          </div>
          <div className="w-full flex justify-between items-center text-sm mt-20">
            <Button type="button" classNames="px-12 bg-zinc-400 btn" onClick={() => handleViewChange("Table")}>
              Cancel
            </Button>
            <Button type="button" variant="primary" classNames="px-12 btn hidden" ref={buttonRefContinue} onClick={() => handleContinueClick("cooperative")}>
              Continue
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};

export default ChooseCooperative;
