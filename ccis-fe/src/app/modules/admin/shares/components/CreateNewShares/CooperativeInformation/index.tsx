import React, { useState, useEffect } from "react";
import dayjs from "dayjs";
import Button from "@components/common/Button";
import { IoChevronBack } from "react-icons/io5";
import { PiSmileySadBold } from "react-icons/pi";
import { Form, FormikProvider, useFormik } from "formik";
import TextField from "@components/form/TextField";
import { formatSelectOptions } from "@helpers/array";
import { RootState } from "@state/store";
import { useSelector } from "react-redux";
import Select from "@components/form/Combo-box";
import Modal from "@components/common/Modal";
import { toast } from "react-toastify";
import { CreateCooperativesSchema, EditCooperativesSchema } from "@services/cooperatives/cooperatives.schema";
import { confirmDelete, showSuccess, confirmSaveOrEdit } from "@helpers/prompt";
import { IShareType, ISharesCoopInformation as ISharesCoopInformationType, coopAffiliation, coopOfficer } from "@interface/shares.interface";

import { showAlert } from "@helpers/prompt";
import { TableColumn } from "react-data-table-component";
import { IActions } from "@interface/common.interface";
import Table from "@components/common/Table";
import ActionButtons from "@components/common/ActionButtons";
import { CiEdit, CiTrash } from "react-icons/ci";
import { IUtilitiesCooperativeType } from "@interface/utilities.interface";
import { useCooperativeTypeManagementActions } from "@state/reducer/utilities-cooperative-types";
import { useAffiliationManagementActions } from "@state/reducer/utilities-affiliation";
import { useCooperativeCategorysManagementActions } from "@state/reducer/utilities-cooperative-category";
import { useCooperativesManagementActions } from "@state/reducer/cooperatives";
import { usePositionsManagementActions } from "@state/reducer/utilities-positions";
import testData from "@constants/json-file/data.json";
import { GoPlus } from "react-icons/go";
import { useSharesManagementActions } from "@state/reducer/shares";
import { useSelectOptions } from "@hooks/useSelectOptions";
import TextArea from "@components/form/TextArea";
import CheckBox from "@components/form/CheckBox";

type ViewType = "ChooseCooperative" | "CooperativeInformation" | "Requirements" | "Review" | "Validate" | "Table";
type ChangeViewProps = {
  changeView: (view: ViewType) => void;
  coopId?: number; // Add this line
  coopData?: ISharesCoopInformationType; // Add this line
  sharesData?: (id?: number, updateSharesData?: IShareType) => void;
  isEditCoop?: boolean;
  setIsEditCoop: (value: boolean) => void;
  onSaveOrUpdate?: (updatedData: ISharesCoopInformationType) => void;
  setDisableRequirementTemplate: (value: boolean) => void;
};

const CooperativeInformation: React.FC<ChangeViewProps> = ({
  changeView,
  // coopId,
  coopData = {} as ISharesCoopInformationType,
  isEditCoop,
  onSaveOrUpdate,
  setIsEditCoop,
  setDisableRequirementTemplate,
}) => {
  const handleViewChange = (view: ViewType) => {
    changeView(view);
  };
  const { clearSelectedShares } = useSharesManagementActions();
  const { postCooperatives, getCooperatives, putCooperatives, setSelectedCooperatives } = useCooperativesManagementActions();
  const postCooperativeError = useSelector((state: RootState) => state?.cooperatives?.postCooperatives?.error);
  const putCooperativeError = useSelector((state: RootState) => state?.cooperatives?.putCooperatives?.error);
  const positions = useSelector((state: RootState) => state.utilitiesPositions.getPosition?.data);
  const latestId = useSelector((state: RootState) => state.cooperatives.latestId);

  const cooperativeType = useSelector((state: RootState) => state.utilitiesCooperativeType.cooperativeType);
  const Affiliation = useSelector((state: RootState) => state.utilitiesAffiliation.Affiliation);
  const cooperativeCategory = useSelector((state: RootState) => state.utilitiesCooperativeCategory.cooperativeCategory);
  const selectedShares = useSelector((state: RootState) => state?.shares?.selectedShares?.data);
  const [addAffiliations, setAddAffiliations] = useState<boolean>(false);
  const [addOfficers, setAddOfficers] = useState<boolean>(false);

  const { getCooperativeCategory } = useCooperativeCategorysManagementActions();
  const { getCooperativeType } = useCooperativeTypeManagementActions();
  const { getAffiliation } = useAffiliationManagementActions();
  const { getPosition } = usePositionsManagementActions();
  const [edit, setEdit] = useState<boolean>(false);
  const [editAffliation, setEditAffliation] = useState<boolean>(false);
  const [newAffiliation, setNewAffiliation] = useState<coopAffiliation>({} as coopAffiliation);
  const [newCoopOfficers, setNewCoopOfficers] = useState<coopOfficer>({} as coopOfficer);

  const incrementedId = latestId + 1;
  type FieldType = keyof coopAffiliation | keyof coopOfficer;
  const [fieldErrors, setFieldErrors] = useState<{
    [key in FieldType]?: string;
  }>({});

  const handleCloseModalCoopOfficer = (type: "officer" | "affiliation") => {
    if (type === "officer") {
      setAddOfficers(false);
      setEdit(false);
    } else {
      setAddAffiliations(false);
      setEditAffliation(false);
    }
  };
  // const cooperativePositionOptions = formatSelectOptions(positions, "positionName");
  const cooperativePositionOptions = useSelectOptions({
    data: positions,
    firstOptionText: "Select Position",
    valueKey: "id",
    textKey: ["positionName"],
  });
  const affiliationOptions = formatSelectOptions(Affiliation, "affiliationName");
  const cooperativeCategoryOptions = formatSelectOptions(cooperativeCategory, "coopCategoryName" || "");
  const defaultOption = { value: "", text: "Please select a value" };
  const cooperativeCategoryOptionsWithDefault = [defaultOption, ...cooperativeCategoryOptions];

  const coopTypes = [
    defaultOption,
    ...cooperativeType?.map((type: IUtilitiesCooperativeType) => ({
      value: type.id.toString(),
      text: type.coopTypeName,
    })),
  ];

  const handleDeleteData = (dataType: string, indexToDelete: number | undefined) => {
    if (dataType === "coopOfficer") {
      const updatedCooperativeOfficers = formik.values.cooperativeOfficers.filter((officer) => officer.index !== indexToDelete);
      formik.setFieldValue("cooperativeOfficers", updatedCooperativeOfficers);
      showSuccess();
    } else if (dataType === "coopAffiliation") {
      const updatedCooperativeAffiliations = formik.values.cooperativeAffiliations.filter((affiliation) => affiliation.index !== indexToDelete);
      formik.setFieldValue("cooperativeAffiliations", updatedCooperativeAffiliations);
      showSuccess();
    }
  };

  const handleOpenOfficerModal = (type: "officer" | "affiliation", data?: coopOfficer | coopAffiliation) => {
    if (type === "officer") {
      if (data) {
        setEdit(true);
        setAddOfficers(false);
        setNewCoopOfficers(data as coopOfficer); // Cast to coopOfficer
      } else {
        setEdit(false);
        setAddOfficers(true);
        setNewCoopOfficers({
          index: undefined || 0,
          title: "",
          firstName: "",
          middleName: "",
          lastName: "",
          generation: "",
          gender: "",
          emailAddress: "",
          contactNumber: "",
          effectivityDate: "",
          status: "",
          positionId: 0,
          maritalStatus: "",
          address: "",
          signatory: false,
        });
      }
    } else if (type === "affiliation") {
      if (data) {
        // setEdit(true);
        setEditAffliation(true);
        setAddAffiliations(false);
        setNewAffiliation(data as coopAffiliation); // Cast to coopAffiliation
      } else {
        setEditAffliation(false);
        setAddAffiliations(true);
        setNewAffiliation({
          index: undefined || 0,
          affiliationId: 0,
          status: "",
          effectivityDate: "",
          cooperativeId: Number(coopData.id),
        });
      }
    }
  };

  const getTextFromValue = (value: string | number, options: { value: string; text: string }[]): string => {
    const option = options.find((opt) => opt.value === value);
    return option ? option.text : "";
  };

  const getValueFromText = (text: string, options: { value: string | number; text: string }[]): string | number | null => {
    const option = options.find((opt) => opt.text === text);
    return option ? option.value : null; // Return the value or null if not found
  };

  const actionEvents: IActions<coopOfficer>[] = [
    {
      name: "Edit",
      event: (row: coopOfficer) => {
        handleOpenOfficerModal("officer", row);
      },

      icon: CiEdit,
      color: "primary",
      buttonType: "button",
    },
    {
      name: "Delete",
      event: async (row: coopOfficer) => {
        try {
          const result = await confirmDelete(row.firstName);
          if (result.isConfirmed) {
            if (row.index !== undefined) {
              handleDeleteData("coopOfficer", row.index);
            } else {
              console.warn("Cannot delete item: index is undefined.");
            }
          }
        } catch (error) {
          console.error("Error during deletion confirmation:", error);
        }
      },

      icon: CiTrash,
      color: "danger",
      buttonType: "button",
    },
  ];
  const actionEventsAffliation: IActions<coopAffiliation>[] = [
    {
      name: "Edit",

      event: (row: coopAffiliation) => {
        handleOpenOfficerModal("affiliation", row);
      },

      icon: CiEdit,
      color: "primary",
      buttonType: "button",
    },
    {
      name: "Delete",
      event: async (row: coopAffiliation) => {
        try {
          const result = await confirmDelete(getTextFromValue(row.affiliationId, affiliationOptions));
          if (result.isConfirmed) {
            if (row.index !== undefined) {
              handleDeleteData("coopAffiliation", row.index);
            } else {
              console.warn("Cannot delete item: index is undefined.");
            }
          }
        } catch (error) {
          console.error("Error during deletion confirmation:", error);
        }
      },

      icon: CiTrash,
      color: "danger",
      buttonType: "button",
    },
  ];

  const columns: TableColumn<coopOfficer>[] = [
    {
      name: "Title",
      selector: (row) => row.title || selectedShares?.cooperative?.cooperativeOfficers?.title,
    },
    {
      name: "First Name",
      cell: (row) => row.firstName || selectedShares?.cooperative?.cooperativeOfficers?.firstName,
    },
    {
      name: "Middle Name",
      cell: (row) => row.middleName || selectedShares?.cooperative?.cooperativeOfficers?.middleName,
    },

    {
      name: "Last Name",
      cell: (row) => row.lastName || selectedShares?.cooperative?.cooperativeOfficers?.lastName,
    },
    {
      name: "Suffix",
      cell: (row) => row.generation || selectedShares?.cooperative?.cooperativeOfficers?.generation,
    },
    {
      name: "Gender",
      cell: (row) => row.gender || selectedShares?.cooperative?.cooperativeOfficers?.gender,
    },
    {
      name: "Email Address",
      cell: (row) => row.emailAddress || selectedShares?.cooperative?.emailAddress,
    },
    {
      name: "Contact",
      cell: (row) => row.contactNumber || selectedShares?.cooperative?.telephoneNumber,
    },
    {
      name: "Marital Status",
      cell: (row) => row?.maritalStatus || selectedShares?.cooperative?.cooperativeAffiliations?.maritalStatus,
    },
    {
      name: "Address",
      cell: (row) => row?.address || selectedShares?.cooperative?.cooperativeAffiliations?.address,
    },
    {
      name: "Effective Date",

      cell: (row) => dayjs(row.effectivityDate).format("YYYY-MM-DD") || selectedShares?.cooperative?.cooperativeOfficers?.effectivityDate,
    },
    {
      name: "Status",
      cell: (row) => row.status || selectedShares?.cooperative?.cooperativeOfficers?.status,
    },
    {
      name: "Position",
      cell: (row) => getTextFromValue(Number(row.positionId), cooperativePositionOptions) || selectedShares?.cooperative?.cooperativeOfficers?.positionName,
    },
    {
      name: "Action",
      cell: (row, rowIndex) => <ActionButtons data={row} rowIndex={rowIndex} actions={actionEvents} />,
    },
  ];
  const columnsAffliation: TableColumn<coopAffiliation>[] = [
    {
      name: "Affiliation Name",

      selector: (row) => getTextFromValue(Number(row.affiliationId), affiliationOptions) || selectedShares?.cooperative?.cooperativeAffiliations?.affiliation?.affiliationName,
    },

    {
      name: "Status",
      cell: (row) => row.status || selectedShares?.cooperative?.cooperativeAffiliations.status,
    },
    {
      name: "Effective Date",

      cell: (row) => dayjs(row.effectivityDate).format("YYYY-MM-DD") || selectedShares?.cooperative?.cooperativeAffiliations.effectivityDate,
    },
    {
      name: "Action",
      cell: (row, rowIndex) => <ActionButtons data={row} rowIndex={rowIndex} actions={actionEventsAffliation} />,
    },
  ];

  const handleKeyDown = (event: React.KeyboardEvent<HTMLFormElement>) => {
    if (event.key === "Enter") {
      event.preventDefault();
    }
  };

  const handleTextFieldChange = (type: string, field: string) => (event: any) => {
    const value = event.target.value;
    if (type === "affiliation") {
      setNewAffiliation({ ...newAffiliation, [field]: value });
    } else {
      setNewCoopOfficers({ ...newCoopOfficers, [field]: value });
    }
    // Clear the error for the field being changed
    setFieldErrors((prevErrors) => ({ ...prevErrors, [field]: "" }));
  };

  const handleSelectChange = (type: string, field: string) => (event: any) => {
    const value = event.target.value;
    if (type === "affiliation") {
      setNewAffiliation({ ...newAffiliation, [field]: value });
    } else {
      setNewCoopOfficers({ ...newCoopOfficers, [field]: value });
    }
    // Clear the error for the field being changed
    setFieldErrors((prevErrors) => ({ ...prevErrors, [field]: "" }));
  };
  const validateFields = (isSubmittingAffiliation: boolean) => {
    const errors: { [key in FieldType]?: string } = {};
    if (isSubmittingAffiliation) {
      if (!newAffiliation.affiliationId) {
        errors.affiliationId = "Affiliation is required.";
      }
      if (!newAffiliation.status) {
        errors.status = "Status is required.";
      }
      if (!newAffiliation.effectivityDate) {
        errors.effectivityDate = "Effective Date is required.";
      }
    } else {
      const {
        firstName,
        lastName,
        positionId,
        gender,
        contactNumber,
        emailAddress,
        status: officerStatus, // Alias for status field
        effectivityDate,
        address,
        maritalStatus,
      } = newCoopOfficers;

      // Validate coopOfficer fields
      if (!firstName) {
        errors.firstName = "First Name is required.";
      }

      if (!lastName) {
        errors.lastName = "Last Name is required.";
      }
      if (!positionId) {
        errors.positionId = "Position ID is required.";
      }
      if (!gender) {
        errors.gender = "Gender is required.";
      }
      if (!contactNumber) {
        errors.contactNumber = "Contact Number is required.";
      }
      if (!emailAddress) {
        errors.emailAddress = "Email Address is required.";
      }
      if (!officerStatus) {
        errors.status = "Status is required.";
      }
      if (!effectivityDate) {
        errors.effectivityDate = "Effective Date is required.";
      }
      if (!address) {
        errors.address = "Address is required.";
      }
      if (!maritalStatus) {
        errors.maritalStatus = "Marital Status is required.";
      }
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0; // Returns true if no errors
  };

  const submitAffiliationCoopOfficer = (isSubmittingAffiliation: boolean) => {
    const isValid = validateFields(isSubmittingAffiliation);

    if (isValid) {
      // Combine cooperativeAffiliations and cooperativeOfficers to ensure unique index
      const allItems = [...formik.values.cooperativeAffiliations, ...formik.values.cooperativeOfficers];

      const getNextIndex = () => (allItems.length > 0 ? Math.max(...allItems.map((item) => item.index ?? 0)) + 1 : 1);

      if (isSubmittingAffiliation) {
        // Handle affiliation updates
        if (editAffliation && newAffiliation.index !== undefined) {
          const updatedAffiliations = formik.values.cooperativeAffiliations.map((affiliation) => (affiliation.index === newAffiliation.index ? newAffiliation : affiliation));
          formik.setFieldValue("cooperativeAffiliations", updatedAffiliations);
        } else {
          // Handle new affiliation entry with global index
          const nextIndex = getNextIndex();

          formik.setFieldValue("cooperativeAffiliations", [...formik.values.cooperativeAffiliations, { ...newAffiliation, index: nextIndex }]);
        }

        resetAffiliationForm();
      } else {
        // Handle officer updates
        if (edit && newCoopOfficers.index !== undefined) {
          const updatedOfficers = formik.values.cooperativeOfficers.map((officer) => (officer.index === newCoopOfficers.index ? newCoopOfficers : officer));
          formik.setFieldValue("cooperativeOfficers", updatedOfficers);
        } else {
          // Handle new officer entry with global index
          const nextIndex = getNextIndex();

          formik.setFieldValue("cooperativeOfficers", [
            ...formik.values.cooperativeOfficers,
            {
              ...newCoopOfficers,
              index: nextIndex,
              positionId: Number(newCoopOfficers.positionId),
            },
          ]);
        }

        // Reset officer form and close modal
        resetOfficerForm();
      }

      showSuccess();
    } else {
      console.error("Validation errors found. Modal remains open.");
    }
  };

  // Helper functions to reset forms
  const resetAffiliationForm = () => {
    setNewAffiliation({
      index: undefined || 0,
      affiliationId: 0,
      status: "",
      effectivityDate: "",
      cooperativeId: Number(coopData.id),
    });
    handleCloseModalCoopOfficer("affiliation");
  };

  const resetOfficerForm = () => {
    setNewCoopOfficers({
      index: undefined,
      title: "",
      firstName: "",
      middleName: "",
      lastName: "",
      generation: "",
      gender: "",
      emailAddress: "",
      contactNumber: "",
      effectivityDate: "",
      status: "",
      positionId: 0,
      maritalStatus: "",
      address: "",
      signatory: false,
    });
    handleCloseModalCoopOfficer("officer");
  };
  useEffect(() => {
    getCooperativeType({ filter: "" });
    getAffiliation({ filter: "" });
    getCooperativeCategory({ filter: "" });
    getPosition({ params: { filter: "" } });
    getCooperatives({ payload: { filter: "" } });
  }, []);

  useEffect(() => {
    if (coopData.cooperativeAffiliations?.length > 0) {
      const affiliation = coopData.cooperativeAffiliations[0];
      setNewAffiliation(affiliation);

      formik.setValues((prevValues) => ({
        ...prevValues,
        cooperativeAffiliations: coopData.cooperativeAffiliations,
      }));
    }
  }, [coopData]);

  useEffect(() => {
    getCooperatives({ payload: { filter: "" } });
  }, []);
  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      id: coopData?.id || incrementedId,
      mainBranchId: coopData?.mainBranchId ?? coopData?.id ?? incrementedId,
      coopName: coopData?.coopName || selectedShares?.cooperative?.coopName,
      coopCode: coopData?.coopCode || selectedShares?.cooperative?.coopCode,
      coopAcronym: coopData?.coopAcronym || selectedShares?.cooperative?.coopAcronym,
      cooperativeTypeId: Number(coopData?.cooperativeTypeId) || (coopData?.type ? getValueFromText(coopData.type, coopTypes) : Number(selectedShares?.cooperative?.cooperativeTypeId)),

      cooperativeCategoryId:
        Number(coopData?.cooperativeCategoryId) || (coopData?.category ? getValueFromText(coopData.category, cooperativeCategoryOptions) : Number(selectedShares?.cooperative?.cooperativeCategoryId)),
      coopBranchesCount: coopData?.coopBranchesCount || selectedShares?.cooperative?.coopBranchesCount,
      province: coopData?.province || selectedShares?.cooperative?.province,
      city: coopData?.city || selectedShares?.cooperative?.city,
      barangay: coopData?.barangay || selectedShares?.cooperative?.barangay,
      streetAddress: coopData?.streetAddress || selectedShares?.cooperative?.streetAddress,
      zipCode: coopData?.zipCode || selectedShares?.cooperative?.zipCode,
      emailAddress: coopData?.emailAddress || selectedShares?.cooperative?.emailAddress,
      websiteAddress: coopData?.websiteAddress || selectedShares?.cooperative?.websiteAddress,
      telephoneNumber: coopData?.telephoneNumber || selectedShares?.cooperative?.telephoneNumber,
      cdaRegistrationNumber: coopData?.cdaRegistrationNumber || selectedShares?.cooperative?.cdaRegistrationNumber,
      cdaRegistrationDate: coopData?.cdaRegistrationDate || selectedShares?.cooperative?.cdaRegistrationDate,
      cdaCocNumber: coopData?.cdaCocNumber || selectedShares?.cooperative?.cdaCocNumber,
      cdaCocDate: coopData?.cdaCocDate || selectedShares?.cooperative?.cdaCocDate,
      taxIdNumber: coopData?.taxIdNumber || selectedShares?.cooperative?.taxIdNumber,
      taxCteExpiryDate: coopData?.taxCteExpiryDate || selectedShares?.cooperative?.taxCteExpiryDate,
      taxCteNumber: coopData?.taxCteNumber || selectedShares?.cooperative?.taxCteNumber,
      taxIdDate: coopData?.taxIdDate || selectedShares?.cooperative?.taxIdDate,
      coopTotalAssets: coopData?.coopTotalAssets || selectedShares?.cooperative?.coopTotalAssets,
      status: coopData?.status || selectedShares?.cooperative?.status || "PENDING",
      coopMembersCount: coopData?.coopMembersCount || selectedShares?.cooperative?.coopMembersCount,
      coopMembersMaleCount: coopData?.coopMembersMaleCount || selectedShares?.cooperative?.coopMembersMaleCount,
      coopMembersFemaleCount: coopData?.coopMembersFemaleCount || selectedShares?.cooperative?.coopMembersFemaleCount,
      cooperativeAffiliations: coopData?.cooperativeAffiliations || selectedShares?.cooperative?.cooperativeAffiliations || ([] as coopAffiliation[]),
      cooperativeOfficers: coopData?.cooperativeOfficers || selectedShares?.cooperative?.cooperativeOfficers || ([] as coopOfficer[]),
    },
    validationSchema: isEditCoop ? EditCooperativesSchema : CreateCooperativesSchema,
    onSubmit: async (values, { resetForm }) => {
      // Validate if cooperativeAffiliations and cooperativeOfficers are not empty
      if (formik.values.cooperativeAffiliations.length === 0 || formik.values.cooperativeOfficers.length === 0) {
        if (formik.values.cooperativeAffiliations.length === 0) {
          formik.setFieldError("cooperativeAffiliations", "Cooperative affiliations cannot be empty.");
          showAlert("Warning", "Cooperative Affiliation should not be empty.", <PiSmileySadBold color="red" size={100} />);
        }
        if (formik.values.cooperativeOfficers.length === 0) {
          formik.setFieldError("cooperativeOfficers", "Cooperative Officer cannot be empty.");
          showAlert("Warning", "Cooperative Officer should not be empty.", <PiSmileySadBold color="red" size={100} />);
        }
        if (values.cooperativeAffiliations.length === 0 && values.cooperativeOfficers.length === 0) {
          showAlert("Warning", "Both Cooperative Affiliation and Cooperative Officer should not be empty.", <PiSmileySadBold color="red" size={100} />);
          formik.setFieldError("cooperativeAffiliations", "Cooperative affiliations cannot be empty.");
          formik.setFieldError("cooperativeOfficers", "Cooperative Officer cannot be empty.");
          return;
        }
      } else {
        // Proceed with confirmation if validation passes
        const confirmationMessage = isEditCoop ? `Do you want to Update the Cooperatives details: "${values.coopName}"?` : `Do you want to Save the Cooperatives: "${values.coopName}"?`;

        const isConfirmed = await confirmSaveOrEdit(confirmationMessage);

        if (isConfirmed) {
          const dataToSubmit = {
            ...values,
            id: Number(values.id),
            mainBranchId: Number(values.mainBranchId),
            cooperativeTypeId: Number(values.cooperativeTypeId),
            cooperativeCategoryId: Number(values.cooperativeCategoryId),
            coopBranchesCount: Number(values.coopBranchesCount),
            coopMembersCount: Number(values.coopMembersCount),
            coopMembersMaleCount: Number(values.coopMembersMaleCount),
            coopMembersFemaleCount: Number(values.coopMembersFemaleCount),
            coopTotalAssets: Number(values.coopTotalAssets),
          };
          const selectedCoop = setSelectedCooperatives({
            index: dataToSubmit.id,
            data: dataToSubmit,
          });

          if (isEditCoop) {
            putCooperatives(dataToSubmit);
            {
              putCooperativeError
                ? toast.error("Cooperative Information not updated. Please try again.")
                : toast.success("Cooperative Information Successfully Updated.") && selectedCoop && setTimeout(() => handleViewChange("Requirements"), 2000);
            }
          } else {
            postCooperatives(dataToSubmit);
            if (postCooperativeError) {
              toast.error("Cooperative Information not saved. Please try again.");
              return;
            } else {
              toast.success("Cooperative Information Successfully Added.");
              setTimeout(() => handleViewChange("Requirements"), 2000);
              selectedCoop;
            }

            setIsEditCoop(true);
          }
          if (onSaveOrUpdate) {
            onSaveOrUpdate(dataToSubmit);
          }
          resetForm();
        }
      }
    },
  });
  const suffixOptions = useSelectOptions({
    data: testData.suffix,
    firstOptionText: "Select Suffix",
    valueKey: "value",
    textKey: ["text"],
  });
  const genderOptions = useSelectOptions({
    data: testData.gender,
    firstOptionText: "Select Gender",
    valueKey: "value",
    textKey: ["text"],
  });
  const statusOptions = useSelectOptions({
    data: testData.statusSameVal,
    firstOptionText: "Select Status",
    valueKey: "value",
    textKey: ["text"],
  });
  const maritalStatusOptions = useSelectOptions({
    data: testData.maritalStatus,
    firstOptionText: "Select Marital Status",
    valueKey: "value",
    textKey: ["text"],
  });

  return (
    <>
      {(addAffiliations || editAffliation) && (
        <Modal
          isOpen={addAffiliations || editAffliation}
          onClose={() => handleCloseModalCoopOfficer("affiliation")}
          title={addAffiliations ? "Add Coop Affiliation" : "Edit Coop Affiliation"}
          modalContainerClassName="max-w-3xl   "
        >
          <FormikProvider value={formik}>
            <Form onKeyDown={handleKeyDown}>
              <div className="w-full flex flex-col items-center justify-center mb-8 gap-2">
                <div className="w-full flex flex-col gap-2">
                  <div className="w-full flex justify-between">
                    <label className="w-1/2 flex items-center">Affiliation</label>
                    <div className="w-1/2">
                      <Select
                        name="affiliationId"
                        options={affiliationOptions}
                        onChange={handleSelectChange("affiliation", "affiliationId")}
                        value={newAffiliation?.affiliationId || ""}
                        error={!!fieldErrors.affiliationId}
                        errorText={fieldErrors.affiliationId}
                        onBlur={formik.handleBlur}
                        required
                      />
                    </div>
                  </div>
                  <div className="w-full flex justify-between">
                    <label className="w-1/2 flex items-center">Status</label>
                    <div className="w-1/2">
                      <Select
                        name="status"
                        options={testData.statusSameVal}
                        onChange={handleSelectChange("affiliation", "status")}
                        value={newAffiliation?.status}
                        error={!!fieldErrors.status}
                        errorText={fieldErrors.status}
                        onBlur={formik.handleBlur}
                        required
                      />
                    </div>
                  </div>
                  <div className="w-full flex justify-between">
                    <label className="w-1/2 flex items-center">Effective Date</label>
                    <div className="w-1/2">
                      <TextField
                        type="date"
                        name="effectivityDate"
                        onChange={handleTextFieldChange("affiliation", "effectivityDate")}
                        value={newAffiliation?.effectivityDate || ""}
                        error={!!fieldErrors.effectivityDate}
                        errorText={fieldErrors.effectivityDate}
                        onBlur={formik.handleBlur}
                        required
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="w-full flex items-center justify-center gap-2">
                <Button type="button" classNames="w-1/2 bg-zinc-400" onClick={() => handleCloseModalCoopOfficer("affiliation")}>
                  Cancel
                </Button>
                <Button type="button" variant="primary" classNames="w-1/2" onClick={() => submitAffiliationCoopOfficer(true)}>
                  {addAffiliations ? "Add" : "Update"}
                </Button>
              </div>
            </Form>
          </FormikProvider>
        </Modal>
      )}

      {(addOfficers || edit) && (
        <Modal
          isOpen={addOfficers || edit}
          onClose={() => handleCloseModalCoopOfficer("officer")}
          title={newCoopOfficers?.index ? "Edit Coop Officer" : "Add Coop Officer"}
          modalContainerClassName="max-w-3xl   "
        >
          <FormikProvider value={formik}>
            <Form onKeyDown={handleKeyDown}>
              <div className="w-full  flex flex-col items-center justify-center mb-8 gap-2 ">
                <div className="w-full flex justify-between ">
                  {" "}
                  <label className="w-1/2 flex items-center ">First Name</label>
                  <div className="w-1/2">
                    <TextField
                      name="firstName"
                      type="text"
                      placeholder="First Name"
                      className="w-1/2"
                      onChange={handleTextFieldChange("officer", "firstName")}
                      value={newCoopOfficers?.firstName || ""}
                      error={!!fieldErrors.firstName}
                      errorText={fieldErrors.firstName}
                      onBlur={formik.handleBlur}
                      required
                    />
                  </div>
                </div>

                <div className="w-full flex justify-between ">
                  {" "}
                  <label className="w-1/2 flex items-center ">Middle Name</label>
                  <div className="w-1/2">
                    <TextField
                      type="text"
                      name="middleName"
                      placeholder="Enter Middle Name"
                      className="w-1/2"
                      onChange={handleTextFieldChange("officer", "middleName")}
                      value={newCoopOfficers?.middleName || ""}
                      onBlur={formik.handleBlur}
                    />
                  </div>
                </div>

                <div className="w-full flex justify-between ">
                  {" "}
                  <label className="w-1/2 flex items-center ">Last Name</label>
                  <div className="w-1/2">
                    <TextField
                      type="text"
                      name="lastName"
                      placeholder="Enter Last Name"
                      className="w-1/2"
                      onChange={handleTextFieldChange("officer", "lastName")}
                      value={newCoopOfficers?.lastName || ""}
                      error={!!fieldErrors.lastName}
                      errorText={fieldErrors.lastName}
                      onBlur={formik.handleBlur}
                      required
                    />
                  </div>
                </div>

                <div className="w-full flex justify-between ">
                  <label className="w-1/2 flex items-center ">Suffix</label>
                  <div className="w-1/2">
                    <Select
                      name="generation"
                      placeholder="Select Suffix"
                      options={suffixOptions}
                      className="w-full border-primary"
                      onChange={handleSelectChange("officer", "generation")}
                      value={newCoopOfficers?.generation || ""}
                      error={!!fieldErrors.generation}
                      errorText={fieldErrors.generation}
                      onBlur={formik.handleBlur}
                    />
                  </div>
                </div>

                <div className="w-full flex justify-between ">
                  <label className="w-1/2 flex items-center ">Title</label>
                  <div className="w-1/2">
                    <TextField
                      type="text"
                      name="title"
                      placeholder="Enter the Title"
                      className="w-1/2"
                      onChange={handleTextFieldChange("officer", "title")}
                      value={newCoopOfficers?.title || ""}
                      error={!!fieldErrors.title}
                      errorText={fieldErrors.title}
                      onBlur={formik.handleBlur}
                    />
                  </div>
                </div>

                <div className="w-full flex justify-between ">
                  {" "}
                  <label className="w-1/2 flex items-center ">Position</label>
                  <div className="w-1/2">
                    <Select
                      name="positionId"
                      options={cooperativePositionOptions}
                      className="w-full border-primary"
                      onChange={handleSelectChange("officer", "positionId")}
                      value={newCoopOfficers.positionId ? Number(newCoopOfficers.positionId) : ""}
                      error={!!fieldErrors.positionId}
                      errorText={fieldErrors.positionId}
                      onBlur={formik.handleBlur}
                      required
                    />
                  </div>
                </div>

                <div className="w-full flex justify-between">
                  {" "}
                  <label className="w-1/2 flex items-center ">Gender</label>
                  <div className="w-1/2">
                    {" "}
                    <Select
                      name="gender"
                      options={genderOptions}
                      className="w-full border-primary"
                      onChange={handleSelectChange("officer", "gender")}
                      value={newCoopOfficers?.gender || ""}
                      error={!!fieldErrors.gender}
                      errorText={fieldErrors.gender}
                      onBlur={formik.handleBlur}
                      required
                    />
                  </div>
                </div>

                <div className="w-full flex justify-between ">
                  {" "}
                  <label className="w-1/2 flex items-center ">Contact No.</label>
                  <div className="w-1/2">
                    <TextField
                      type="text"
                      name="contactNumber"
                      placeholder="Enter Contact Number"
                      className="w-1/2"
                      onChange={handleTextFieldChange("officer", "contactNumber")}
                      value={newCoopOfficers?.contactNumber || ""}
                      error={!!fieldErrors.contactNumber}
                      errorText={fieldErrors.contactNumber}
                      onBlur={formik.handleBlur}
                      required
                    />
                  </div>
                </div>
                <div className="w-full flex justify-between ">
                  {" "}
                  <label className="w-1/2 flex items-center ">Email Address</label>
                  <div className="w-1/2">
                    <TextField
                      type="text"
                      name="emailAddress"
                      placeholder="Enter Email Address"
                      className="w-1/2"
                      onChange={handleTextFieldChange("officer", "emailAddress")}
                      value={newCoopOfficers?.emailAddress || ""}
                      error={!!fieldErrors.emailAddress}
                      errorText={fieldErrors.emailAddress}
                      onBlur={formik.handleBlur}
                      required
                    />
                  </div>
                </div>

                <div className="w-full flex justify-between">
                  {" "}
                  <label className="w-1/2 flex items-center ">Status</label>
                  <div className="w-1/2">
                    {" "}
                    <Select
                      name="status"
                      options={statusOptions}
                      className="w-full border-primary"
                      onChange={handleSelectChange("officer", "status")}
                      value={newCoopOfficers?.status || ""}
                      error={!!fieldErrors?.status}
                      errorText={fieldErrors?.status}
                      onBlur={formik.handleBlur}
                      required
                    />
                  </div>
                </div>
                <div className="w-full flex justify-between">
                  {" "}
                  <label className="w-1/2 flex items-center ">Marital Status</label>
                  <div className="w-1/2">
                    {" "}
                    <Select
                      name="maritalStatus"
                      options={maritalStatusOptions}
                      className="w-full border-primary"
                      onChange={handleSelectChange("officer", "maritalStatus")}
                      value={newCoopOfficers?.maritalStatus || ""}
                      error={!!fieldErrors?.maritalStatus}
                      errorText={fieldErrors?.maritalStatus}
                      onBlur={formik.handleBlur}
                      required
                    />
                  </div>
                </div>
                <div className="w-full flex justify-between">
                  {" "}
                  <label className="w-1/2 flex items-center ">Address</label>
                  <div className="w-1/2">
                    {" "}
                    <TextArea
                      className="w-full border-primary"
                      placeholder="Enter Address"
                      name="address"
                      onChange={handleTextFieldChange("officer", "address")}
                      value={newCoopOfficers?.address || ""}
                      error={!!fieldErrors?.address}
                      errorText={fieldErrors?.address}
                      onBlur={formik.handleBlur}
                    />
                  </div>
                </div>
                <div className="w-full flex justify-between">
                  {" "}
                  <label
                    className="w-1/2 flex items-center
              "
                  >
                    Effective Date
                  </label>
                  <div className="w-1/2">
                    <TextField
                      name="effectivityDate"
                      type="date"
                      className="w-1/2"
                      onChange={handleTextFieldChange("officer", "effectivityDate")}
                      value={newCoopOfficers?.effectivityDate || ""}
                      error={!!fieldErrors.effectivityDate}
                      errorText={fieldErrors.effectivityDate}
                      onBlur={formik.handleBlur}
                      required
                    />
                  </div>
                </div>
                <div className="w-full flex justify-between">
                  {" "}
                  <label className="w-1/2 flex items-center ">Signatory Officer</label>
                  <div className="w-1/2">
                    {" "}
                    {/* <CheckBox
                      name="signatory"
                      // onChange={handleTextFieldChange("officer", "signatory")}
                      checked={!!newCoopOfficers.signatory}
                      error={!!fieldErrors.signatory}
                      errorText={fieldErrors.signatory}
                    /> */}
                    <CheckBox
                      name="signatory"
                      checked={!!newCoopOfficers.signatory}
                      error={!!fieldErrors.signatory}
                      errorText={fieldErrors.signatory}
                      onChange={(e) => {
                        setNewCoopOfficers((prev) => ({
                          ...prev,
                          signatory: e.target.checked,
                        }));
                      }}
                    />
                  </div>
                </div>
              </div>

              <div className="w-full flex items-center justify-center gap-2">
                <Button type="button" variant="primary" classNames="w-32" onClick={() => submitAffiliationCoopOfficer(false)}>
                  {edit ? "Update" : addOfficers ? "Save" : ""}
                </Button>
              </div>
            </Form>
          </FormikProvider>
        </Modal>
      )}

      <FormikProvider value={formik}>
        <Form onKeyDown={handleKeyDown}>
          <div className=" w-full flex-col flex items-start h-1/5 ">
            <div className="border-b border-zinc-200 w-full py-4">
              <Button
                classNames="flex items-center justify-center border-none"
                type="button"
                variant="primary"
                outline
                onClick={() => {
                  handleViewChange("ChooseCooperative");
                  setDisableRequirementTemplate(false);
                  clearSelectedShares();
                }}
              >
                <IoChevronBack />
                Back
              </Button>
            </div>
            <div className="w-full p-6  text-primary">
              <div className="font-poppins-semibold mb-2 text-2xl">Cooperative Information</div>
              <div className="text-zinc-500 mb-8 text-sm">Fill in and check the cooperative information.</div>

              <div className="w-full h-full pt-6 text-black ">
                <div className="font-semibold text-xl"> BASIC INFORMATION</div>
                <div className="mt-6 flex flex-col">
                  <label className="mb-2">Cooperative Name (In Full)</label>
                  <TextField
                    type="text"
                    name="coopName"
                    placeholder="Enter Cooperative Name"
                    className="border border-zinc-200   "
                    value={formik.values.coopName}
                    error={formik.touched.coopName && !!formik.errors.coopName}
                    errorText={formik.errors.coopName || selectedShares?.cooperative?.coopName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    disabled
                  />

                  <label className="mb-2 mt-6">Coop Acronym</label>
                  <TextField
                    type="text"
                    name="coopAcronym"
                    placeholder="Enter Coop Acronym"
                    className="border border-zinc-200  "
                    value={formik.values.coopAcronym}
                    error={formik.touched.coopAcronym && !!formik.errors.coopAcronym}
                    errorText={formik.errors.coopAcronym || selectedShares?.cooperative?.coopAcronym}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    disabled
                  />
                  <label className="mb-2 mt-4">Select Cooperative Category :</label>
                  <Select
                    name="cooperativeCategoryId"
                    options={cooperativeCategoryOptionsWithDefault}
                    value={Number(formik.values.cooperativeCategoryId) || Number(selectedShares?.cooperative?.cooperativeCategoryId)}
                    className="w-1/2"
                    error={formik.touched.cooperativeCategoryId && !!formik.errors.cooperativeCategoryId}
                    errorText={formik.errors.cooperativeCategoryId || selectedShares?.cooperative?.cooperativeCategoryId}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    required
                  />
                  <label className="mb-2 mt-4">Select Cooperative Type :</label>
                  <Select
                    name="cooperativeTypeId"
                    options={coopTypes}
                    value={Number(formik.values.cooperativeTypeId) || Number(selectedShares?.cooperative?.cooperativeTypeId)}
                    className="w-1/2"
                    error={formik.touched.cooperativeTypeId && !!formik.errors.cooperativeTypeId}
                    errorText={formik.errors.cooperativeTypeId || selectedShares?.cooperative?.cooperativeTypeId}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    required
                  />

                  <label className="mb-2 mt-6">No. of Branches</label>
                  <TextField
                    type="number"
                    name="coopBranchesCount"
                    error={formik.touched.coopBranchesCount && !!formik.errors.coopBranchesCount}
                    errorText={formik.errors.coopBranchesCount || selectedShares?.cooperative?.coopBranchesCount}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.coopBranchesCount}
                    placeholder="Enter Number of Coop Branch"
                    className="border border-zinc-200  "
                  />
                </div>

                <div className="w-full border-t border-zinc-200 pt-6 mt-6">
                  <div className="text-2xl font-semibold text-black">Business Address</div>

                  <div className="flex flex-col  mt-6 ">
                    <label className="mb-2">Province</label>
                    <TextField
                      type="text"
                      name="province"
                      error={formik.touched.province && !!formik.errors.province}
                      value={formik.values.province || selectedShares?.cooperative?.province}
                      errorText={formik.errors.province || selectedShares?.cooperative?.province}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter Province"
                      className="border border-zinc-200  "
                    />
                    <label className="mb-2 mt-6">Municipality / City</label>
                    <TextField
                      type="text"
                      name="city"
                      error={formik.touched.city && !!formik.errors.city}
                      errorText={formik.errors.city || selectedShares?.cooperative?.city}
                      onChange={formik.handleChange}
                      value={formik.values.city || selectedShares?.cooperative?.city}
                      onBlur={formik.handleBlur}
                      placeholder="Enter Municipality/City "
                      className="border border-zinc-200  "
                    />
                    <label className="mb-2 mt-6">Barangay</label>
                    <TextField
                      type="text"
                      name="barangay"
                      error={formik.touched.barangay && !!formik.errors.barangay}
                      errorText={formik.errors.barangay || selectedShares?.cooperative?.barangay}
                      onChange={formik.handleChange}
                      value={formik.values.barangay}
                      onBlur={formik.handleBlur}
                      placeholder="Enter Barangay"
                      className="border border-zinc-200   "
                    />
                    <label className="mb-2 mt-6">Street Address</label>
                    <TextField
                      type="text"
                      name="streetAddress"
                      error={formik.touched.streetAddress && !!formik.errors.streetAddress}
                      errorText={formik.errors.streetAddress || selectedShares?.cooperative?.streetAddress}
                      onChange={formik.handleChange}
                      value={formik.values.streetAddress}
                      onBlur={formik.handleBlur}
                      placeholder="Enter Street Address"
                      className="border border-zinc-200  "
                    />
                    <label className="mb-2 mt-6">Zipcode</label>
                    <TextField
                      type="string"
                      name="zipCode"
                      error={formik.touched.zipCode && !!formik.errors.zipCode}
                      errorText={formik.errors.zipCode || selectedShares?.cooperative?.zipCode}
                      onChange={formik.handleChange}
                      value={formik.values.zipCode}
                      onBlur={formik.handleBlur}
                      placeholder="Enter zipCode"
                      className="border border-zinc-200  "
                    />
                  </div>
                </div>

                <div className="w-full border-t border-zinc-200 pt-6 mt-6">
                  <div className="text-2xl font-semibold text-black">Contact Information</div>

                  <div className="flex flex-col  mt-6 ">
                    <label className="mb-2 mt-6">Email Address</label>
                    <TextField
                      type="text"
                      name="emailAddress"
                      error={formik.touched.emailAddress && !!formik.errors.emailAddress}
                      errorText={formik.errors.emailAddress || selectedShares?.cooperative?.emailAddress}
                      value={formik.values.emailAddress}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter Email"
                      className="border border-zinc-200   "
                    />
                    <label className="mb-2 mt-6">Website</label>
                    <TextField
                      type="text"
                      name="websiteAddress"
                      error={formik.touched.websiteAddress && !!formik.errors.websiteAddress}
                      errorText={formik.errors.websiteAddress || selectedShares?.cooperative?.websiteAddress}
                      value={formik.values.websiteAddress}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter Website "
                      className="border border-zinc-200"
                    />
                    <label className="mb-2 mt-6">Telephone No. Fax No.</label>
                    <TextField
                      type="text"
                      name="telephoneNumber"
                      error={formik.touched.telephoneNumber && !!formik.errors.telephoneNumber}
                      errorText={formik.errors.telephoneNumber || selectedShares?.cooperative?.telephoneNumber}
                      onChange={formik.handleChange}
                      value={formik.values.telephoneNumber}
                      onBlur={formik.handleBlur}
                      placeholder="Enter Telephone"
                      className="border border-zinc-200  "
                    />
                  </div>
                </div>

                <div className="w-full border-t border-zinc-200 pt-6 mt-6">
                  <div className="text-2xl font-semibold text-black">Registration and Compliance</div>

                  <div className="flex flex-col mb-4 mt-4 gap-2 ">
                    <div className="flex w-full gap-4">
                      <div className="w-1/2 mt-4 mb-4">
                        <label className="mb-2">CDA Reg. No.</label>
                        <TextField
                          placeholder="Enter CDA Registered No."
                          name="cdaRegistrationNumber"
                          error={formik.touched.cdaRegistrationNumber && !!formik.errors.cdaRegistrationNumber}
                          errorText={formik.errors.cdaRegistrationNumber || selectedShares?.cooperative?.cdaRegistrationNumber}
                          value={formik.values.cdaRegistrationNumber}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          className="border border-zinc-200 "
                        />
                      </div>

                      <div className="w-1/2 mt-4 mb-4">
                        <label className="mb-2">Date Registered</label>
                        <TextField
                          type="date"
                          name="cdaRegistrationDate"
                          error={formik.touched.cdaRegistrationDate && !!formik.errors.cdaRegistrationDate}
                          errorText={formik.errors.cdaRegistrationDate || selectedShares?.cooperative?.cdaRegistrationDate}
                          value={formik.values.cdaRegistrationDate}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          className="border border-zinc-200 "
                        />
                      </div>
                    </div>
                    <div className="flex w-full gap-4">
                      <div className="w-1/2 ">
                        {" "}
                        <label className="mb-2">CDA Certificate of Compliance</label>
                        <TextField
                          placeholder="Enter CDA Registered No."
                          name="cdaCocNumber"
                          error={formik.touched.cdaCocNumber && !!formik.errors.cdaCocNumber}
                          errorText={formik.errors.cdaCocNumber || selectedShares?.cooperative?.cdaCocNumber}
                          onChange={formik.handleChange}
                          value={formik.values.cdaCocNumber}
                          onBlur={formik.handleBlur}
                          className="border border-zinc-200 "
                        />
                      </div>
                      <div className="w-1/2 ">
                        {" "}
                        <label className="mb-2">Date Registered</label>
                        <TextField
                          type="date"
                          name="cdaCocDate"
                          error={formik.touched.cdaCocDate && !!formik.errors.cdaCocDate}
                          errorText={formik.errors.cdaCocDate || selectedShares?.cooperative?.cdaCocDate}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          value={formik.values.cdaCocDate}
                          className="border border-zinc-200  "
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="w-full border-t border-zinc-200 pt-6 mt-6">
                  <div className="text-2xl font-semibold text-black">TAX INFORMATION</div>

                  <div className="flex flex-col mb-6 mt-6 gap-2 ">
                    <div className="flex w-full gap-4">
                      <div className="w-1/2 mt-4 mb-4">
                        {" "}
                        <label className="mb-2">Tax Identification No. (TIN)</label>
                        <TextField
                          placeholder="Enter Tax ID Number."
                          name="taxIdNumber"
                          error={formik.touched.taxIdNumber && !!formik.errors.taxIdNumber}
                          errorText={formik.errors.taxIdNumber || selectedShares?.cooperative?.taxIdNumber}
                          value={formik.values.taxIdNumber}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          className="border border-zinc-200 "
                        />
                      </div>

                      <div className="w-1/2 mt-4 mb-4">
                        {" "}
                        <label className="mb-2">Tax Identification No. (TIN) Issue Date</label>
                        <TextField
                          type="date"
                          name="taxIdDate"
                          error={formik.touched.taxIdDate && !!formik.errors.taxIdDate}
                          errorText={formik.errors.taxIdDate || selectedShares?.cooperative?.taxIdDate}
                          value={formik.values.taxIdDate}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          className="border border-zinc-200 "
                        />
                      </div>
                    </div>
                    <div className="flex w-full gap-4">
                      <div className="w-1/2 ">
                        {" "}
                        <label className="mb-2">Certificate of Tax Exemption (CTE) No.</label>
                        <TextField
                          placeholder="Enter Cert. of Tax Exemption."
                          name="taxCteNumber"
                          error={formik.touched.taxCteNumber && !!formik.errors.taxCteNumber}
                          errorText={formik.errors.taxCteNumber || selectedShares?.cooperative?.taxCteNumber}
                          onChange={formik.handleChange}
                          value={formik.values.taxCteNumber}
                          onBlur={formik.handleBlur}
                          className="border border-zinc-200 "
                        />
                      </div>
                      <div className="w-1/2 ">
                        {" "}
                        <label className="mb-2">Cert. Of Tax Exemption (CTE) Expiration Date</label>
                        <TextField
                          type="date"
                          name="taxCteExpiryDate"
                          error={formik.touched.taxCteExpiryDate && !!formik.errors.taxCteExpiryDate}
                          errorText={formik.errors.taxCteExpiryDate || selectedShares?.cooperative?.taxCteExpiryDate}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          value={formik.values.taxCteExpiryDate}
                          className="border border-zinc-200 "
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="w-full border-t border-zinc-200 pt-6 mt-6">
                <div className="text-2xl font-semibold text-black">Member and Asset Information</div>

                {/* <div className="flex flex-col mb-6 mt-6 gap-2 ">
                  <div className="flex w-full gap-4">
                    <div className="w-1/2 "> */}
                <div className="flex flex-col mb-6 mt-6 gap-2 ">
                  <div className="flex w-full gap-4">
                    <div className="w-1/2 mt-4 mb-4">
                      <label className="mb-2">Total Asset</label>
                      <TextField
                        type="number"
                        placeholder="Enter Total Asset"
                        name="coopTotalAssets"
                        error={formik.touched.coopTotalAssets && !!formik.errors.coopTotalAssets}
                        errorText={formik.errors.coopTotalAssets || selectedShares?.cooperative?.coopTotalAssets}
                        onChange={formik.handleChange}
                        value={formik.values.coopTotalAssets}
                        onBlur={formik.handleBlur}
                        className="border border-zinc-200  "
                      />
                    </div>
                    <div className="w-1/2 mt-4 mb-4">
                      {" "}
                      <label className="mb-2">Total Number of Members</label>
                      <TextField
                        type="number"
                        placeholder="Enter Total Number of Members"
                        name="coopMembersCount"
                        error={formik.touched.coopMembersCount && !!formik.errors.coopMembersCount}
                        errorText={formik.errors.coopMembersCount || selectedShares?.cooperative?.coopMembersCount}
                        onChange={formik.handleChange}
                        value={(Number(formik.values.coopMembersFemaleCount) + Number(formik.values.coopMembersMaleCount)).toString()}
                        disabled
                        onBlur={formik.handleBlur}
                        className="border border-zinc-200   "
                      />
                    </div>
                  </div>
                  <div className="flex w-full gap-4">
                    <div className="w-1/2 ">
                      {" "}
                      <label className="mb-2"> Total Number of Female Member</label>
                      <TextField
                        type="number"
                        placeholder="Enter Total Number of Female Members"
                        name="coopMembersFemaleCount"
                        error={formik.touched.coopMembersFemaleCount && !!formik.errors.coopMembersFemaleCount}
                        errorText={formik.errors.coopMembersFemaleCount || selectedShares?.cooperative?.coopMembersFemaleCount}
                        onChange={(e) => {
                          const value = e.target.value ? Number(e.target.value) : 0;
                          formik.setFieldValue("coopMembersFemaleCount", value);
                          const totalMembersCount = (Number(formik.values.coopMembersMaleCount) || 0) + value;
                          formik.setFieldValue("coopMembersCount", totalMembersCount);
                        }}
                        value={formik.values.coopMembersFemaleCount} // Ensure it's a string
                        onBlur={formik.handleBlur}
                        className="border border-zinc-200 "
                      />
                    </div>
                    <div className="w-1/2 ">
                      {" "}
                      <label className="mb-2"> Total Number of Male Member</label>
                      <TextField
                        type="number"
                        placeholder="Enter Total Number of Male Members"
                        name="coopMembersMaleCount"
                        error={formik.touched.coopMembersMaleCount && !!formik.errors.coopMembersMaleCount}
                        errorText={formik.errors.coopMembersMaleCount || selectedShares?.cooperative?.coopMembersMaleCount}
                        onChange={(e) => {
                          const value = e.target.value ? Number(e.target.value) : 0;
                          formik.setFieldValue("coopMembersMaleCount", value);
                          const totalMembersCount = value + (Number(formik.values.coopMembersFemaleCount) || 0);
                          formik.setFieldValue("coopMembersCount", totalMembersCount);
                        }}
                        value={formik.values.coopMembersMaleCount} // Ensure it's a string
                        onBlur={formik.handleBlur}
                        className="border border-zinc-200 "
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="w-full border-t border-zinc-200 pt-6 mt-6">
                <div className=" text-2xl font-semibold text-black mb-6">Affiliations</div>

                <div className="w-full border border-zinc-300 rounded-3xl pt-0 p-8">
                  <Table className="h-[200px] " columns={columnsAffliation} data={formik.values.cooperativeAffiliations} multiSelect={false} searchable={false} />
                  <Button
                    onClick={() => handleOpenOfficerModal("affiliation")}
                    type="button"
                    outline
                    classNames="flex btn  h-12 w-max items-center justify-center text-xs px-4 rounded-full bg-indigo-50 mt-2  "
                  >
                    {" "}
                    <GoPlus size={20} />
                    Add New
                  </Button>
                </div>
              </div>

              <div className="w-full border-t border-zinc-200 pt-6 mt-6">
                <div className=" font-semibold text-black mb-6 text-2xl">Coop Officers</div>

                <div className="w-full border border-zinc-300 rounded-3xl pt-0 p-8">
                  <Table
                    className="h-[200px]"
                    columns={columns}
                    data={formik.values.cooperativeOfficers.map((officer: any, index: number) => ({
                      ...officer,
                      uniqueKey: officer.index || index, // Generate a unique key
                    }))}
                    multiSelect={false}
                    searchable={false}
                  />
                  <Button
                    onClick={() => handleOpenOfficerModal("officer")}
                    type="button"
                    outline
                    classNames="flex btn  h-12 w-max items-center justify-center text-xs px-4 rounded-full bg-indigo-50 mt-2  "
                  >
                    {" "}
                    <GoPlus size={20} />
                    Add New
                  </Button>
                </div>
              </div>

              <div className="w-full flex items-center justify-end text-sm mt-6 ">
                <Button
                  type="submit"
                  // type="button"
                  variant="primary"
                  classNames="px-6 btn"
                >
                  Continue
                </Button>
              </div>
            </div>
          </div>
        </Form>
      </FormikProvider>
    </>
  );
};

export default CooperativeInformation;
