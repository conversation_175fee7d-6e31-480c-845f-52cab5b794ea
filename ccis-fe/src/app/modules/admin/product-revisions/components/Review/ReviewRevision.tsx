import httpClient from "@clients/httpClient";
import Modal from "@components/common/Modal";
import Typography from "@components/common/Typography";
import Loader from "@components/Loader";
import { ROUTES } from "@constants/routes";
import { RevisionStatus } from "@enums/revision-status";
import { formatToRomanNumeral, getTextStatusColor } from "@helpers/text";
import { IGuideline, IGuidelineContent, IGuidelineContentTable } from "@interface/guidelines.interface";
import ChangeStatusForm from "@modules/admin/product-revisions/components/Forms/ChangeStatusForm";
import { updateProductStatusService, updateRevisionStatusService } from "@services/products/products.service";
import { apiUrl } from "@services/variables";
import { useProductActions } from "@state/reducer/products";
import { RootState } from "@state/store";
import { TApprovalPayload } from "@state/types/users-product-approval";
import dayjs from "dayjs";
import { FC, Fragment, useEffect, useState, useRef } from "react";
import { FaChevronLeft, FaRegFilePdf, FaUsers } from "react-icons/fa";
import { useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import ApproverRemarksList from "../List/ApproverRemarksList";
import { FiDownload } from "react-icons/fi";
import Button from "@components/common/Button";
import Signatories2 from "@modules/users/approvals/review-revisions/Tabs/Signatories2";
import Tabs from "@components/common/Tabs";
import { AttachmentTags } from "@enums/attachment-tags";
import ApprovalHistory from "@modules/admin/products/components/Common/ApprovalHistory";
import { UserRoles } from "@interface/routes.interface";
import { IoChevronBackCircle } from "react-icons/io5";

const ReviewRevision: FC = () => {
  const assetUrl = `${import.meta.env.VITE_AWS_S3_ENDPOINT}`;
  const navigate = useNavigate();
  const { productid, revisionid } = useParams();
  const { getRevisionDetails } = useProductActions();
  const productRevision = useSelector((state: RootState) => state.products.revisionDetails);
  const guidelineHeaders = productRevision?.productRevisionGuidelinesTagSequence;
  const product = productRevision?.product;

  const [processing, setProcessing] = useState<boolean>(false);
  const [processModal, setProcessModal] = useState<boolean>(false);
  const [pdfFile, setPdfFile] = useState<string>();
  const toggleProcess = () => setProcessModal((prev) => !prev);

  const [statusModal, setStatusModal] = useState<boolean>(false);
  const toggleModal = () => setStatusModal((prev) => !prev);

  const [remarksModal, setRemarksModal] = useState<boolean>(false);
  const toggleRemarksModal = () => setRemarksModal((prev) => !prev);

  const [showTableOfContents, setShowTableOfContents] = useState<boolean>(false);
  const handleShowTableOfContents = () => setShowTableOfContents((prev) => !prev);

  const standard = productRevision?.commission?.commissionDetails?.filter((rowValue) => rowValue.commissionAgeType?.name?.toLowerCase() !== "standard") ?? [];

  const tableOfContentsRef = useRef<(HTMLLIElement | HTMLDivElement)[]>([]);
  const [currentIndex, setCurrentIndex] = useState<number>(0);

  const scrollToSection = (index: number) => {
    tableOfContentsRef?.current[index].scrollIntoView({ behavior: "smooth" });
    setCurrentIndex(index);
  };

  const generateListItem = (label: string, index: number) => {
    return (
      <li
        key={`toc-${index}`}
        className={`
          mt-2 list-none font-poppins-semibold hover:bg-zinc-100 p-4 rounded-md cursor-pointer
          ${currentIndex === index ? "text-primary bg-sky-50" : "text-zinc-500"}
        `}
        onClick={() => scrollToSection(index)}
      >
        <Typography size="sm">{label}</Typography>
      </li>
    );
  };

  const generateTableOfContents = (productGuidelines: IGuideline[]) => {
    const toc = productGuidelines?.map((value, index) => {
      return generateListItem(value.label, index);
    });

    const lastIndex = productGuidelines.length;

    toc[lastIndex] = generateListItem("Commission Structure", lastIndex);

    return toc;
  };

  const handlePDFReport = async () => {
    try {
      setProcessing(true);
      toggleProcess();
      const response: any = await httpClient.get(`${apiUrl}/products/${productid}/product-revisions/${revisionid}/export/pdf`, {
        responseType: "blob",
      });

      const pdfBlob = new Blob([response], { type: "application/pdf" });

      const url = window.URL.createObjectURL(pdfBlob);
      setPdfFile(url);
    } catch (error: any) {
      toast.error(error?.response?.data?.message);
    } finally {
      setProcessing(false);
    }
  };

  const handleSubmitStatus = async (_data: TApprovalPayload) => {
    try {
      setProcessing(true);
      const { data } = await updateProductStatusService(productid?.toString() ?? "", _data.approvalStatus);
      if (data) {
        const { data: newData } = await updateRevisionStatusService(revisionid?.toString() ?? "", _data.approvalStatus);
        if (newData) {
          const newRevisionId = newData.id.toString();
          getRevisionDetails({ productid, revisionid: newRevisionId });
          toast.success("Product status updated successfully");
          toggleModal();
        }
      }
    } catch (error: any) {
      toast.error(error?.response?.data?.message);
    } finally {
      setProcessing(false);
    }
  };
  useEffect(() => {
    getRevisionDetails({ productid, revisionid });
  }, []);

  return (
    <Fragment>
      <div className="flex flex-1 flex-col mx-5 overflow-x-hidden">
        <div className="flex flex-1 flex-col mx-4 overscroll-y-none">
          <div className="flex -ml-4">
            <button
              className="btn btn-sm btn-ghost"
              onClick={() => {
                const route = location.pathname.includes(UserRoles.rnd) ? ROUTES.RESEARCHANDDEVELOPMENT.revisions.parse(productid ?? "") : ROUTES.ADMIN.revisions.parse(productid ?? "");
                navigate(route);
              }}
            >
              <FaChevronLeft />
              <Typography>Back</Typography>
            </button>
          </div>
          <div className="divider"></div>
          <div className="flex flex-1 flex-col  pl-2">
            <div className="flex flex-1 flex-row">
              <div className="flex flex-1 flex-row">
                <div className="min-w-40">
                  <Typography size="md" className="!text-slate-600 mr-3">
                    PRODUCT NAME
                  </Typography>
                </div>
                <div>
                  <Typography size="md" className="font-poppins-semibold">
                    {product?.name}
                  </Typography>
                </div>
              </div>
              <div className="flex flex-1 flex-row">
                <div className="min-w-40">
                  <Typography size="md" className="!text-slate-600 mr-3">
                    REVISION No.
                  </Typography>
                </div>
                <div>
                  <Typography size="md" className="font-poppins-semibold">
                    {productRevision?.revisionNumber?.toUpperCase()}
                  </Typography>
                </div>
              </div>
            </div>
            <div className="flex flex-1 flex-row mt-4">
              <div className="flex flex-1 flex-row">
                <div className="min-w-40">
                  <Typography size="md" className="!text-slate-600 mr-3">
                    CREATED BY
                  </Typography>
                </div>
                <div>
                  <Typography size="md" className="font-poppins-semibold">{`${productRevision?.createdBy?.firstname} ${productRevision?.createdBy?.lastname}`}</Typography>
                </div>
              </div>
              <div className="flex flex-1 flex-row">
                <div className="min-w-40">
                  <Typography size="md" className="!text-slate-600 mr-3">
                    CREATION DATE
                  </Typography>
                </div>
                <div>
                  <Typography size="md" className="font-poppins-semibold">
                    {dayjs(product?.createdAt).format("D MMMM YYYY")}
                  </Typography>
                </div>
              </div>
            </div>
            <div className="flex flex-1 flex-row mt-4">
              <div className="flex flex-1 flex-row">
                <div className="min-w-40">
                  <Typography className="!text-slate-600">APPROVAL STATUS</Typography>
                </div>
                <div className="flex flex-1 flex-row items-center">
                  <Typography className={`${getTextStatusColor(productRevision?.approvalStatus ?? "")} flex items-center justify-center font-poppins-semibold`}>
                    {productRevision?.approvalStatus?.replace("_", " ")}
                  </Typography>
                  {(productRevision?.approvalStatus === RevisionStatus.for_approval || productRevision?.approvalStatus === RevisionStatus.for_revision) && (
                    <button className="btn btn-xs w-36 ml-2 btn-primary" onClick={toggleModal}>
                      Change Status
                    </button>
                  )}
                </div>
              </div>
              <div className="flex flex-1 flex-row">
                <div className="min-w-40">
                  <Typography size="md" className="!text-slate-600 mr-3">
                    DATE APPROVED
                  </Typography>
                </div>
                <div>
                  <Typography size="md" className="font-poppins-semibold">
                    {productRevision?.approvedAt ? dayjs(productRevision?.approvedAt).format("D MMMM YYYY") : ""}
                  </Typography>
                </div>
              </div>
            </div>
            <div className="flex flex-1 flex-row mt-2">
              <div className="min-w-40">
                <Typography size="md" className="!text-slate-600 mr-3">
                  TIMELINE
                </Typography>
              </div>
              <div>
                <Typography size="md" className="font-poppins-semibold">
                  {productRevision?.approvalDays}
                </Typography>
              </div>
            </div>
          </div>
          <div className="flex flex-1 justify-between my-2 pt-4">
            <div className="flex flex-1 flex-row justify-end">
              <Button
                type="button"
                variant="primary"
                outline
                onClick={() => handlePDFReport()}
                classNames="flex flex-row items-center border-0 bg-none text-primary text-sm bg-zinc-50 hover:bg-primary hover:text-white"
              >
                <FiDownload className="mr-2" />
                Export
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-12  mt-4  transition-all duration-500">
            <div className={`  h-full  bg-sky-50 overflow-auto ${showTableOfContents ? "col-span-3" : "hidden"}`}>
              <div className="w-full pb-4 h-max bg-white hidden  xl:flex flex-col text-start text-sm relative">
                <Typography className="text-center text-lg font-semibold">Table of Contents</Typography>
                <ul className="mt-5">{generateTableOfContents(productRevision?.productGuidelines ?? [])}</ul>
              </div>
            </div>

            <div className={` border-l border-zinc-300 px-6 relative ${showTableOfContents ? "col-span-6" : "col-span-9"}`}>
              <div onClick={handleShowTableOfContents} className=" -translate-x-10 transition-all duration-500 cursor-pointer text-primary ">
                <IoChevronBackCircle size={40} className={`inline mr-2 transition-all duration-500  ${showTableOfContents ? "" : "rotate-180"}`} />
              </div>
              <div className="flex flex-1 justify-center">
                <Typography size="2xl" className="text-lg font-poppins-semibold">
                  {product?.name}
                </Typography>
              </div>
              <div className="flex flex-col justify-between mt-2 mb-16">
                <div className="flex mt-2">
                  <div
                    className="ml-4 mt-2 xl:text-base text-xs "
                    dangerouslySetInnerHTML={{
                      __html: product?.description ?? "",
                    }}
                  ></div>
                </div>
              </div>
              <div className="flex flex-col xl:text-base text-xs">
                {guidelineHeaders?.map((header, index) => {
                  const guidelineTagId = header.productRevisionGuidelineTag.id.toString();
                  return (
                    <div key={`guideline-header-${index}`} className="mt-4">
                      <Typography className="xl:text-lg font-poppins-semibold text-primary text-center mb-6">
                        {formatToRomanNumeral(index + 1)}.<span className="ml-2">{header.productRevisionGuidelineTag.productRevisionGuidelineTag}</span>
                      </Typography>
                      <div>
                        {productRevision?.productGuidelines?.map((value: any, gIndex: any) => {
                          if (guidelineTagId !== value.productGuidelineTagId?.toString()) {
                            return null;
                          }
                          return (
                            <div
                              key={`guideline-${gIndex}`}
                              className="scroll-mt-16 flex flex-1 flex-col mb-2 xl:mb-10"
                              ref={(e) => {
                                if (e) {
                                  tableOfContentsRef.current[gIndex] = e;
                                }
                              }}
                            >
                              <Typography className="xl:text-lg text-sm font-poppins-semibold text-primary">{value.label}</Typography>
                              {value.productGuideline.map((pgValue: any, pgIndex: any) => {
                                let listValue;
                                let tableValue;
                                if (pgValue.type === "list") {
                                  listValue = pgValue.value as IGuidelineContent[];
                                }

                                if (pgValue.type === "table") {
                                  tableValue = pgValue.value as IGuidelineContentTable;
                                }

                                return (
                                  <div key={`pg-${pgIndex}`} className="p-2">
                                    {pgValue.type === "textfield" && (
                                      <Fragment>
                                        <Typography className="ml-4 xl:mt-4  mt-0 text-justify xl:text-base text-xs">{pgValue.value as string}</Typography>
                                      </Fragment>
                                    )}
                                    {pgValue.type === "list" && (
                                      <Fragment>
                                        <Typography className="ml-4 xl:mt-4  mt-0 xl:text-justify text-start xl:text-base text-xs">{pgValue.label}</Typography>
                                        <ul className="list-disc xl:ml-12 ml-6">
                                          {listValue &&
                                            listValue.map((listValue, listIndex) => {
                                              return (
                                                <li key={`listItem-${listIndex}`} className="xl:mt-4 mt-0">
                                                  <Typography className="text-justify xl:text-base text-xs">{listValue.value as string}</Typography>
                                                </li>
                                              );
                                            })}
                                        </ul>
                                      </Fragment>
                                    )}
                                    {pgValue.type === "texteditor" && (
                                      <Fragment>
                                        <div
                                          className="ml-4 mt-2 xl:text-base text-xs "
                                          dangerouslySetInnerHTML={{
                                            __html: (pgValue as any).value ?? "",
                                          }}
                                        ></div>
                                      </Fragment>
                                    )}
                                    {pgValue.type === "table" && (
                                      <Fragment>
                                        <div className="flex flex-1 mt-2 mx-6 overflow-x-scroll xl:text-base text-xs">
                                          <table className="table border-[1px]">
                                            <thead className="table-header-group">
                                              <tr>
                                                {tableValue?.columns?.map((cValue, cIndex) => {
                                                  return (
                                                    <td key={`col-${cIndex}`} className="table-cell border-[1px] xl:text-base text-xs">
                                                      <Typography className="font-semibold text-xs">{cValue.value as string}</Typography>
                                                    </td>
                                                  );
                                                })}
                                              </tr>
                                            </thead>
                                            <tbody>
                                              {tableValue?.rows?.map((rValue, rIndex) => {
                                                return (
                                                  <tr key={`row-${rIndex}`}>
                                                    {rValue.map((cell, cellIndex) => {
                                                      return (
                                                        <td className="border-[1px] xl:text-base text-xs" key={`cell-${cellIndex}`}>
                                                          <Typography size="xs">{cell.value as string}</Typography>
                                                        </td>
                                                      );
                                                    })}
                                                  </tr>
                                                );
                                              })}
                                            </tbody>
                                          </table>
                                        </div>
                                      </Fragment>
                                    )}
                                  </div>
                                );
                              })}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  );
                })}
              </div>
              <div className="flex flex-col">
                {productRevision?.commission && (
                  <div
                    ref={(e) => {
                      if (e) {
                        const lastIndex = productRevision?.productGuidelines?.length ?? 0;
                        tableOfContentsRef.current[lastIndex] = e;
                      }
                    }}
                  >
                    <Typography size="md" className="font-poppins-semibold text-primary">
                      Commission Structure
                    </Typography>
                    <Fragment>
                      <Typography className="ml-4 mt-4 xl:text-base text-xs">
                        {parseFloat(productRevision?.commission?.maximumDisposableRate ?? "").toFixed(2)}% Maximum Disposable Commission - Standard Rate
                      </Typography>

                      <div className="flex-flex-1 mt-6 mx-6 overflow-x-scroll border rounded-xl border-zinc-300 p-4">
                        <table className="table overflow-scroll">
                          <thead>
                            <tr>
                              <td className="table-cell border-1 text-center text-xs">Type</td>
                              <td className="table-cell border-1 text-center text-xs">Age Type</td>
                              {standard.length > 0 && (
                                <Fragment>
                                  <td className="table-cell border-1 text-center text-xs">Age From</td>
                                  <td className="table-cell border-1 text-center text-xs">Age To</td>
                                </Fragment>
                              )}
                              <td className="table-cell border-1 text-center text-xs">Rate</td>
                            </tr>
                          </thead>
                          <tbody>
                            {productRevision?.commission?.commissionDetails?.map((rowValue: any, rowIndex: any) => {
                              return (
                                <tr key={`commissionDetailsRow-${rowIndex}`}>
                                  <td className="table-cell border-1 text-xs text-center  font-poppins-semibold">{rowValue?.commissionType?.commissionName}</td>
                                  <td className="table-cell border-1 text-xs text-center">{rowValue?.commissionAgeType?.name}</td>
                                  {standard.length > 0 && (
                                    <Fragment>
                                      <td className="table-cell border-1 text-center text-xs">{rowValue.ageFrom}</td>
                                      <td className="table-cell border-1 text-center text-xs">{rowValue.ageTo}</td>
                                    </Fragment>
                                  )}
                                  <td className="table-cell border-1 text-center text-xs">{rowValue.rate ? parseFloat(rowValue.rate.toString()).toFixed(2) : ""}%</td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    </Fragment>
                  </div>
                )}
              </div>
            </div>
            <div className="col-span-12 lg:col-span-3 bg-sky-50">
              <div className="flex flex-[1.2] align-center flex-col p-2 sm:p-4">
                <Typography className="!text-black text-sm sm:text-md text-left font-semibold">Other Documents to Review</Typography>
                <div className="flex flex-col mb-4 sm:mb-8 overflow-x-hidden overflow-y-auto max-h-40 sm:max-h-none">
                  {(productRevision?.attachments?.length ?? 0) === 0 && (
                    <div className="flex justify-center">
                      <Typography className="text-zinc-400 text-xs sm:text-sm">No attachments</Typography>
                    </div>
                  )}
                  {productRevision?.attachments?.map((attachment, index) => {
                    if (attachment.tag === AttachmentTags.SIGNATORY_SIGNED) return null;
                    return (
                      <div key={`attachment-${index}`} className="flex flex-row justify-start mt-2 sm:mt-5">
                        <a className="flex gap-1 sm:gap-2 items-center max-w-full" href={`${assetUrl}/${attachment.filepath}`} target="_blank" rel="noreferrer">
                          <FaRegFilePdf size={16} className="sm:w-5 sm:h-5 flex-shrink-0" color="red" />
                          <Typography size="xs" className="sm:text-sm truncate overflow-hidden text-ellipsis whitespace-nowrap max-w-full">
                            {attachment.label}
                          </Typography>
                        </a>
                      </div>
                    );
                  })}
                </div>
                <Typography className="!text-black text-sm sm:text-md text-left font-semibold">Signed Documents</Typography>
                <div className="flex flex-col mb-4 sm:mb-10 overflow-x-hidden overflow-y-auto max-h-40 sm:max-h-none">
                  {(productRevision?.attachments?.length ?? 0) === 0 && (
                    <div className="flex justify-center">
                      <Typography className="text-zinc-400 text-xs sm:text-sm">No Attachments</Typography>
                    </div>
                  )}
                  {productRevision?.attachments?.map((attachment, index) => {
                    if (attachment.tag !== AttachmentTags.SIGNATORY_SIGNED) return null;
                    return (
                      <div key={`attachment-${index}`} className="flex flex-row justify-start mt-2 sm:mt-5">
                        <a className="flex gap-1 sm:gap-2 items-center max-w-full" href={`${assetUrl}/${attachment.filepath}`} target="_blank" rel="noreferrer">
                          <FaRegFilePdf size={16} className="sm:w-5 sm:h-5 flex-shrink-0" color="red" />
                          <Typography size="xs" className="sm:text-sm truncate overflow-hidden text-ellipsis whitespace-nowrap max-w-full">
                            {attachment.label}
                          </Typography>
                        </a>
                      </div>
                    );
                  })}
                </div>
                <div className="flex flex-row justify-center items-center mb-2 sm:mb-0">
                  <FaUsers size={16} className="sm:w-5 sm:h-5 mr-1 sm:mr-2" />
                  <Typography size="md" className="sm:text-lg font-semibold">
                    SIGNEES
                  </Typography>
                </div>
                <div className="flex flex-1 flex-col pr-0 sm:pr-2 w-full">
                  <Tabs
                    className="mt-2 sm:mt-4 overflow-auto"
                    contentClass="p-2 sm:p-6 border-0 bg-sky-50"
                    headerClass="text-xs sm:text-sm rounded-t-lg h-8 sm:h-10"
                    activeTabClassName="bg-primary !text-white text-xs sm:text-sm"
                    headers={["Ongoing Approvals", "Approval History"]}
                    contents={[<Signatories2 signatories={productRevision?.signatories} toggleRemarksModal={toggleRemarksModal} />, <ApprovalHistory revisionId={productRevision?.id ?? ""} />]}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {processModal && (
        <Modal isOpen={processModal} onClose={toggleProcess} showCloseButton={!processing} modalContainerClassName="!max-w-6xl">
          {processing && (
            <div className="flex flex-1 flex-col justify-center items-center">
              <Loader />
              <Typography>Processing...</Typography>
            </div>
          )}
          {!processing && (
            <div className="flex flex-1 flex-col justify-center items-center">
              <embed src={pdfFile} className="h-[800px]" width="100%" />
            </div>
          )}
        </Modal>
      )}
      {statusModal && (
        <Modal title="Change Product Status" titleClass="text-md md:text-lg" modalContainerClassName="!max-w-2xl" isOpen={statusModal} onClose={toggleModal}>
          <ChangeStatusForm handleSubmit={handleSubmitStatus} productId={product?.id} revisionId={productRevision?.id} isSubmitting={processing} />
        </Modal>
      )}
      {remarksModal && (
        <Modal title="Remarks" titleClass="text-md md:text-lg" modalContainerClassName="!max-w-2xl" isOpen={remarksModal} onClose={toggleRemarksModal}>
          <ApproverRemarksList />
        </Modal>
      )}
    </Fragment>
  );
};

export default ReviewRevision;
