import Tabs from "@components/common/Tabs";
import { ReactNode, useEffect } from "react";
import SeriesTrackingTab from "../components/tabs/status-tracking-tabs/series-tracking";
import { clearData } from "@helpers/storage";

const HeadCashierStatusTracking = () => {
  const headers: string[] = ["Series Tracking"];
  const contents: ReactNode[] = [<SeriesTrackingTab />];

  useEffect(() => {
    clearData("statusTrackingDetailsID");
    clearData("seriesDetailTableId");
  }, []);

  return (
    <div>
      <div className=" my-4 py-4 border-b border-zinc-300 w-full text-zinc-500">
        Dashboard / <span className="text-primary font-poppins-semibold ">Status Tracking</span>
      </div>
      <Tabs headers={headers} contents={contents} size="md" headerClass="w-52" fullWidthHeader={false} />
    </div>
  );
};

export default HeadCashierStatusTracking;
