import EmptySeries from "./empty-series";
import IssuedSeries from "./issued-series";
import CancelledSeries from "./cancelled-series";
import Button from "@components/common/Button";
import { useNavigate, useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { ROUTES } from "@constants/routes";
import { getData, hasKey } from "@helpers/storage";
import PrPagination from "@modules/gam/components/pr-pagination";
import { PadStatus } from "@enums/form-status";

const ViewSeriesDetails = () => {
  const navigate = useNavigate();

  // Get ID in URL
  const { id } = useParams();

  // State
  const [seriesDetailTableId, setSeriesDetailTableId] = useState<number | null>(null);

  // Global states
  const { userPadSeriesDetails } = useSelector((state: RootState) => state.formInventoryTransmittal);

  // Actions
  const { getUserPadSeriesDetails } = useTransmittalFormActions();

  useEffect(() => {
    if (id) {
      getUserPadSeriesDetails({ id: parseInt(id) });
    }
  }, [id]);

  const handleBack = () => {
    if (parseInt(userPadSeriesDetails?.data?.seriesNo) !== parseInt(userPadSeriesDetails?.data?.seriesFrom)) {
      navigate(ROUTES.CHIEFCASHIER.viewSeriesDetails.parse((userPadSeriesDetails?.data.id - 1).toString()));
    }
  };

  const handleForward = () => {
    if (parseInt(userPadSeriesDetails?.data?.seriesNo) !== parseInt(userPadSeriesDetails?.data?.seriesTo)) {
      navigate(ROUTES.CHIEFCASHIER.viewSeriesDetails.parse(userPadSeriesDetails?.data.id + 1).toString());
    }
  };

  const redirect = () => {
    return seriesDetailTableId !== null ? navigate(ROUTES.CHIEFCASHIER.seriesDetails.parse(seriesDetailTableId.toString())) : navigate(ROUTES.CHIEFCASHIER.statusTracking.key);
  };

  useEffect(() => {
    if (hasKey("seriesDetailTableId")) {
      setSeriesDetailTableId(getData("seriesDetailTableId"));
    }
  }, []);

  return (
    <div>
      <div className="flex justify-start">
        <Button variant="secondary" onClick={redirect}>
          Back
        </Button>
      </div>
      {/* Content */}
      <div className="">
        {/* <EmptySeries /> */}
        {/* <IssuedSeries /> */}

        {/* Render this if status is USED */}
        {userPadSeriesDetails?.data && userPadSeriesDetails?.data?.status === PadStatus.USED && <IssuedSeries data={userPadSeriesDetails.data} />}

        {/* Render this if status is CANCELLED */}
        {userPadSeriesDetails?.data && userPadSeriesDetails?.data?.status === PadStatus.CANCELLED && <CancelledSeries data={userPadSeriesDetails.data} />}

        {/* Render this if status is UNUSED */}
        {userPadSeriesDetails?.data && userPadSeriesDetails?.data?.status === PadStatus.UNUSED && <EmptySeries />}

        {/* Pagination */}
        {/* Render only if the status is USED or CANCELLED */}
        {userPadSeriesDetails?.data && (userPadSeriesDetails?.data?.status === PadStatus.USED || userPadSeriesDetails?.data?.status === PadStatus.CANCELLED) && (
          <div className="mt-8">
            <PrPagination handleBack={handleBack} handleForward={handleForward} seriesNo={userPadSeriesDetails?.data?.seriesNo} seriesFrom={userPadSeriesDetails?.data?.padAssignment?.seriesFrom} />
          </div>
        )}
      </div>
    </div>
  );
};

export default ViewSeriesDetails;
