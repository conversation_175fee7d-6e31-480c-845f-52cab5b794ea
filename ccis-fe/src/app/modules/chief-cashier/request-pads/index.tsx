import { ReactNode } from "react";
import Tabs from "@components/common/Tabs";
import ForApprovalPadTab from "../components/tabs/request-pads-tabs/for-approval-pads";
import ApprovePadsTab from "../components/tabs/request-pads-tabs/appove-pads-tab";

const TreasuryRequestForms: React.FC = () => {
  const headers: string[] = ["For Approval", "Approved"];
  const contents: ReactNode[] = [<ForApprovalPadTab />, <ApprovePadsTab />];

  return (
    <div>
      <div className=" my-4 py-4 border-b border-zinc-300 w-full text-zinc-500">
        Dashboard / <span className="text-primary font-poppins-semibold ">Requests Pads</span>
      </div>
      <Tabs headers={headers} contents={contents} size="md" headerClass="w-52" fullWidthHeader={false} />
    </div>
  );
};

export default TreasuryRequestForms;
