import { TableColumn } from "react-data-table-component";
import Typography from "@components/common/Typography";
import ActionDropdown from "@components/common/ActionDropdown";
import { IActions } from "@interface/common.interface";
import { IFormTransmittalTrail, IPadAssignments } from "@interface/form-inventory.interface";
import { FormStatus, StatusTrackingRole } from "@enums/form-status";
import { formatWordDateDDMMYYY } from "@helpers/date";

type GetActionEventsFn = (row: IPadAssignments) => IActions<any>[];

type GetColumnsParams = {
  getActionEvents?: GetActionEventsFn;
  getColumnDate: (trails: IFormTransmittalTrail[], role: string, status: string) => IFormTransmittalTrail | undefined;
};

export const getColumns = ({ getActionEvents, getColumnDate }: GetColumnsParams): TableColumn<IPadAssignments>[] => {
  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const columns: TableColumn<IPadAssignments>[] = [
    {
      name: "No.",
      cell: (row) => row.id,
      width: "80px",
      ...commonSetting,
    },
    {
      name: "Transmittal No.",
      cell: (row) => row.id,
      ...commonSetting,
    },
    {
      name: "Division",
      cell: (row) => row.form?.division.divisionName,
      ...commonSetting,
    },
    {
      name: "Type",
      cell: (row) => row.form?.formType.formTypeCode,
      ...commonSetting,
    },
    {
      name: "Area Released",
      cell: (row) => row.form?.area.areaName,
      ...commonSetting,
    },
    {
      name: "Series From",
      cell: (row) => row.seriesFrom,
      ...commonSetting,
    },
    {
      name: "Series To",
      cell: (row) => row.seriesTo,
      ...commonSetting,
    },
    {
      name: "Cashier Outgoing",
      center: true,
      width: "350px",
      cell: (row) => {
        let receivedTrail: IFormTransmittalTrail | undefined;
        let releasedTrail: IFormTransmittalTrail | undefined;
        if (row.formTransmittal?.formTransmittalTrails) {
          receivedTrail = getColumnDate(row.formTransmittal?.formTransmittalTrails, StatusTrackingRole.CASHIEROUTGOING, FormStatus.RECEIVED);
          releasedTrail = getColumnDate(row.formTransmittal?.formTransmittalTrails, StatusTrackingRole.CASHIEROUTGOING, FormStatus.RELEASED);
        }
        return (
          <div className="flex gap-2 px-2 py-4">
            <div className="flex flex-col gap-1">
              <span className="text-[#a3a3a3]">Received:</span>
              <span>{row.formTransmittal?.formTransmittalTrails && receivedTrail !== undefined ? formatWordDateDDMMYYY(receivedTrail.receivedAt ?? "", true) : "-----"}</span>
            </div>
            <div className="divider divider-horizontal" />
            <div className="flex flex-col gap-1">
              <span className="text-[#a3a3a3]">Released:</span>
              <span>{row.formTransmittal?.formTransmittalTrails && releasedTrail !== undefined ? formatWordDateDDMMYYY(releasedTrail.receivedAt ?? "", true) : "-----"}</span>
            </div>
          </div>
        );
      },
      ...commonSetting,
    },
    {
      name: "Admin Outgoing",
      center: true,
      width: "350px",
      cell: (row) => {
        let receivedTrail: IFormTransmittalTrail | undefined;
        let releasedTrail: IFormTransmittalTrail | undefined;
        if (row.formTransmittal?.formTransmittalTrails) {
          receivedTrail = getColumnDate(row.formTransmittal?.formTransmittalTrails, StatusTrackingRole.ADMINOUTGOING, FormStatus.RECEIVED);
          releasedTrail = getColumnDate(row.formTransmittal?.formTransmittalTrails, StatusTrackingRole.ADMINOUTGOING, FormStatus.RELEASED);
        }
        return (
          <div className="flex gap-2 px-2 py-4">
            <div className="flex flex-col gap-1">
              <span className="text-[#a3a3a3]">Received:</span>
              <span>{row.formTransmittal?.formTransmittalTrails && receivedTrail !== undefined ? formatWordDateDDMMYYY(receivedTrail.receivedAt ?? "", true) : "-----"}</span>
            </div>
            <div className="divider divider-horizontal" />
            <div className="flex flex-col gap-1">
              <span className="text-[#a3a3a3]">Released:</span>
              <span>{row.formTransmittal?.formTransmittalTrails && releasedTrail !== undefined ? formatWordDateDDMMYYY(releasedTrail.receivedAt ?? "", true) : "-----"}</span>
            </div>
          </div>
        );
      },
      ...commonSetting,
    },
    {
      name: "Clifsa Admin",
      center: true,
      width: "350px",
      cell: (row) => {
        let receivedTrail: IFormTransmittalTrail | undefined;
        let releasedTrail: IFormTransmittalTrail | undefined;
        if (row.formTransmittal?.formTransmittalTrails) {
          receivedTrail = getColumnDate(row.formTransmittal?.formTransmittalTrails, StatusTrackingRole.CLIFSAADMIN, FormStatus.RECEIVED);
          releasedTrail = getColumnDate(row.formTransmittal?.formTransmittalTrails, StatusTrackingRole.CLIFSAADMIN, FormStatus.RELEASED);
        }
        return (
          <div className="flex gap-2 px-2 py-4">
            <div className="flex flex-col gap-1">
              <span className="text-[#a3a3a3]">Received:</span>
              <span>{row.formTransmittal?.formTransmittalTrails && receivedTrail !== undefined ? formatWordDateDDMMYYY(receivedTrail.receivedAt ?? "", true) : "-----"}</span>
            </div>
            <div className="divider divider-horizontal" />
            <div className="flex flex-col gap-1">
              <span className="text-[#a3a3a3]">Released:</span>
              <span>{row.formTransmittal?.formTransmittalTrails && releasedTrail !== undefined ? formatWordDateDDMMYYY(releasedTrail.receivedAt ?? "", true) : "-----"}</span>
            </div>
          </div>
        );
      },
      ...commonSetting,
    },
    {
      name: "GAM",
      center: true,
      width: "350px",
      cell: (row) => {
        let receivedTrail: IFormTransmittalTrail | undefined;
        let returenedTrail: IFormTransmittalTrail | undefined;
        if (row.formTransmittal?.formTransmittalTrails) {
          receivedTrail = getColumnDate(row.formTransmittal?.formTransmittalTrails, StatusTrackingRole.GAM, FormStatus.RECEIVED);
          returenedTrail = getColumnDate(row.formTransmittal?.formTransmittalTrails, StatusTrackingRole.GAM, FormStatus.RETURNED);
        }
        return (
          <div className="flex gap-2 px-2 py-4">
            <div className="flex flex-col gap-1">
              <span className="text-[#a3a3a3]">Received:</span>
              <span>{row.formTransmittal?.formTransmittalTrails && receivedTrail !== undefined ? formatWordDateDDMMYYY(receivedTrail.receivedAt ?? "", true) : "-----"}</span>
            </div>
            <div className="divider divider-horizontal" />
            <div className="flex flex-col gap-1">
              <span className="text-[#a3a3a3]">Returned:</span>
              <span>{row.formTransmittal?.formTransmittalTrails && returenedTrail !== undefined ? formatWordDateDDMMYYY(returenedTrail.receivedAt ?? "", true) : "-----"}</span>
            </div>
          </div>
        );
      },
      ...commonSetting,
    },
    {
      name: "Clifsa Admin",
      center: true,
      width: "350px",
      cell: (row) => {
        let receivedTrail: IFormTransmittalTrail | undefined;
        let returenedTrail: IFormTransmittalTrail | undefined;
        if (row.formTransmittal?.formTransmittalTrails) {
          receivedTrail = getColumnDate(row.formTransmittal?.formTransmittalTrails, StatusTrackingRole.CLIFSAADMIN, FormStatus.RECEIVED);
          returenedTrail = getColumnDate(row.formTransmittal?.formTransmittalTrails, StatusTrackingRole.CLIFSAADMIN, FormStatus.RETURNED);
        }
        return (
          <div className="flex gap-2 px-2 py-4">
            <div className="flex flex-col gap-1">
              <span className="text-[#a3a3a3]">Received:</span>
              <span>{row.formTransmittal?.formTransmittalTrails && receivedTrail !== undefined ? formatWordDateDDMMYYY(receivedTrail.receivedAt ?? "", true) : "-----"}</span>
            </div>
            <div className="divider divider-horizontal" />
            <div className="flex flex-col gap-1">
              <span className="text-[#a3a3a3]">Returned:</span>
              <span>{row.formTransmittal?.formTransmittalTrails && returenedTrail !== undefined ? formatWordDateDDMMYYY(returenedTrail.receivedAt ?? "", true) : "-----"}</span>
            </div>
          </div>
        );
      },
      ...commonSetting,
    },
    {
      name: "Admin Incoming",
      center: true,
      width: "350px",
      cell: (row) => {
        let receivedTrail: IFormTransmittalTrail | undefined;
        let returenedTrail: IFormTransmittalTrail | undefined;
        if (row.formTransmittal?.formTransmittalTrails) {
          receivedTrail = getColumnDate(row.formTransmittal?.formTransmittalTrails, StatusTrackingRole.ADMININCOMING, FormStatus.RECEIVED);
          returenedTrail = getColumnDate(row.formTransmittal?.formTransmittalTrails, StatusTrackingRole.ADMININCOMING, FormStatus.RETURNED);
        }
        return (
          <div className="flex gap-2 px-2 py-4">
            <div className="flex flex-col gap-1">
              <span className="text-[#a3a3a3]">Received:</span>
              <span>{row.formTransmittal?.formTransmittalTrails && receivedTrail !== undefined ? formatWordDateDDMMYYY(receivedTrail.receivedAt ?? "", true) : "-----"}</span>
            </div>
            <div className="divider divider-horizontal" />
            <div className="flex flex-col gap-1">
              <span className="text-[#a3a3a3]">Returned:</span>
              <span>{row.formTransmittal?.formTransmittalTrails && returenedTrail !== undefined ? formatWordDateDDMMYYY(returenedTrail.receivedAt ?? "", true) : "-----"}</span>
            </div>
          </div>
        );
      },
      ...commonSetting,
    },
    {
      name: "Cashier Incoming",
      center: true,
      width: "350px",
      cell: (row) => {
        let receivedTrail: IFormTransmittalTrail | undefined;
        let returenedTrail: IFormTransmittalTrail | undefined;
        if (row.formTransmittal?.formTransmittalTrails) {
          receivedTrail = getColumnDate(row.formTransmittal?.formTransmittalTrails, StatusTrackingRole.CASHIERINCOMING, FormStatus.RECEIVED);
          returenedTrail = getColumnDate(row.formTransmittal?.formTransmittalTrails, StatusTrackingRole.CASHIERINCOMING, FormStatus.RETURNED);
        }
        return (
          <div className="flex gap-2 px-2 py-4">
            <div className="flex flex-col gap-1">
              <span className="text-[#a3a3a3]">Received:</span>
              <span>{row.formTransmittal?.formTransmittalTrails && receivedTrail !== undefined ? formatWordDateDDMMYYY(receivedTrail.receivedAt ?? "", true) : "-----"}</span>
            </div>
            <div className="divider divider-horizontal" />
            <div className="flex flex-col gap-1">
              <span className="text-[#a3a3a3]">Returned:</span>
              <span>{row.formTransmittal?.formTransmittalTrails && returenedTrail !== undefined ? formatWordDateDDMMYYY(returenedTrail.receivedAt ?? "", true) : "-----"}</span>
            </div>
          </div>
        );
      },
      ...commonSetting,
    },
    {
      name: "Chief Cashier",
      center: true,
      width: "350px",
      cell: (row) => {
        let trail: IFormTransmittalTrail | undefined;
        if (row.formTransmittal?.formTransmittalTrails) {
          trail = getColumnDate(row.formTransmittal?.formTransmittalTrails, StatusTrackingRole.CHIEFCASHIER, FormStatus.RECEIVED);
          console.log(row.id);
          console.log(trail);
        }
        return (
          <div className="flex gap-4 px-2 py-4">
            <div className="flex flex-col gap-1">
              <span className="text-[#a3a3a3]">Received:</span>
              <span>{row.formTransmittal?.formTransmittalTrails && trail !== undefined ? formatWordDateDDMMYYY(trail.receivedAt ?? "", true) : "-----"}</span>
            </div>
          </div>
        );
      },
      ...commonSetting,
    },
  ];

  if (getActionEvents) {
    columns.push({
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />,
    });
  }

  return columns;
};
