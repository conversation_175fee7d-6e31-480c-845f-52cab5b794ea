import { TableColumn } from "react-data-table-component";
import Typography from "@components/common/Typography";
import ActionDropdown from "@components/common/ActionDropdown";
import { IActions } from "@interface/common.interface";
import { IPadAssignments } from "@interface/form-inventory.interface";
import ProgressBar from "@modules/gam/components/progress-bar";
import { FormStatus, PadStatus } from "@enums/form-status";
import { getTextStatusColor } from "@helpers/text";

type GetActionEventsFn = (row: IPadAssignments) => IActions<any>[];

type GetColumnsParams = {
  getActionEvents?: GetActionEventsFn;
};

export const getColumns = ({ getActionEvents }: GetColumnsParams): TableColumn<IPadAssignments>[] => {
  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const columns: TableColumn<IPadAssignments>[] = [
    {
      name: "Pad Number",
      cell: (row) => row.id,
      width: "80px",
      ...commonSetting,
    },
    {
      name: "Transmittal No.",
      cell: (row) => row?.formTransmittal?.transmittalNumber,
      ...commonSetting,
    },
    {
      name: "Series From",
      cell: (row) => row?.seriesFrom,
      ...commonSetting,
    },
    {
      name: "Series To",
      cell: (row) => row?.seriesTo,
      ...commonSetting,
    },
    {
      name: "Status",
      cell: (row) => {
        return (
          <Typography size="xs" className={getTextStatusColor(row?.status ? row?.status : FormStatus.RETURNED)}>
            {row?.status
              ? row?.status
                  .replace(/_/g, " ")
                  .toLowerCase()
                  .replace(/\b\w/g, (char) => char.toUpperCase())
              : "N/A"}
          </Typography>
        );
      },
      ...commonSetting,
    },
    {
      name: "PR Issued",
      cell: (row) => {
        let counts = { used: 0, unused: 0 };
        if (row?.padSeriesDetails) {
          counts = row?.padSeriesDetails.reduce(
            (pad, padDetails) => {
              if (padDetails.status === PadStatus.USED || padDetails.status === PadStatus.CANCELLED) {
                pad.used++;
              } else if (padDetails.status === PadStatus.UNUSED) {
                pad.unused++;
              }
              return pad;
            },
            { used: 0, unused: 0 }
          );
        }
        return <ProgressBar value={counts.used} max={row?.padSeriesDetails?.length || 50} />;
      },
      ...commonSetting,
    },
  ];

  if (getActionEvents) {
    columns.push({
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />,
    });
  }

  return columns;
};
