import { TableColumn } from "react-data-table-component";
import Typography from "@components/common/Typography";
import ActionDropdown from "@components/common/ActionDropdown";
import { IActions } from "@interface/common.interface";
import { IIncomingReceivedForm } from "@interface/form-inventory.interface";
import ProgressBar from "@modules/gam/components/progress-bar";

type GetActionEventsFn = (row: IIncomingReceivedForm) => IActions<any>[];

type GetColumnsParams = {
  getActionEvents?: GetActionEventsFn;
  padAssignmentsStatus: (row: IIncomingReceivedForm) => { active: number; returned: number };
};

export const getColumns = ({ getActionEvents, padAssignmentsStatus }: GetColumnsParams): TableColumn<IIncomingReceivedForm>[] => {
  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const columns: TableColumn<IIncomingReceivedForm>[] = [
    {
      name: "No.",
      cell: (row) => row.id,
      width: "80px",
      ...commonSetting,
    },
    {
      name: "Division",
      cell: (row) => row?.division?.divisionName,
      ...commonSetting,
    },
    {
      name: "Type",
      cell: (row) => row?.formType?.formTypeCode,
      ...commonSetting,
    },
    {
      name: "Area",
      cell: (row) => row?.area?.areaName,
      ...commonSetting,
    },
    {
      name: "Status Released",
      cell: (row) => {
        const counts = padAssignmentsStatus(row);
        return <ProgressBar value={counts.active} max={row?.noPads || 0} />;
      },
      ...commonSetting,
    },
    {
      name: "Status Returned",
      cell: (row) => {
        const counts = padAssignmentsStatus(row);

        return <ProgressBar value={counts.returned} max={row?.noPads || 0} />;
      },
      ...commonSetting,
    },
  ];

  if (getActionEvents) {
    columns.push({
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />,
    });
  }

  return columns;
};
