import { TableColumn } from "react-data-table-component";
import Typography from "@components/common/Typography";
import ActionDropdown from "@components/common/ActionDropdown";
import { IActions } from "@interface/common.interface";
import { IGamPadRequest } from "@interface/gam-request-pads";
import { formatWordDateDDMMYYY } from "@helpers/date";
import { capitalizeFirstLetterWords, getTextStatusColor } from "@helpers/text";

type GetActionEventsFn = (row: IGamPadRequest) => IActions<any>[];

type GetColumnsParams = {
  getActionEvents?: GetActionEventsFn;
};

export type TPadData = { padId: number; usedPads: number };

export const getColumns = ({ getActionEvents }: GetColumnsParams): TableColumn<IGamPadRequest>[] => {
  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const columns: TableColumn<IGamPadRequest>[] = [
    {
      name: "No.",
      cell: (row) => row.id,
      width: "80px",
      ...commonSetting,
    },
    {
      name: "Request Date",
      cell: (row) => (row?.createdAt ? formatWordDateDDMMYYY(row?.createdAt, true) : ""),
      ...commonSetting,
    },
    {
      name: "Requested By",
      cell: (row) => (row?.createdBy ? `${row?.createdBy.firstname} ${row?.createdBy.lastname}` : "N/A"),
      ...commonSetting,
    },
    {
      name: "Series From",
      width: "120px",
      cell: (row) => {
        const seriesFrom = row?.seriesFrom;
        return seriesFrom ? seriesFrom : "N/A";
      },
      ...commonSetting,
    },
    {
      name: "Series To",
      width: "120px",
      cell: (row) => {
        const seriesTo = row?.seriesTo;
        return seriesTo ? seriesTo : "N/A";
      },
      ...commonSetting,
    },
    {
      name: "No. of Pads",
      width: "150px",
      cell: (row) => row.numberOfPads,
      ...commonSetting,
    },
    {
      name: "Status",
      cell: (row) => (
        <Typography size="xs" className={getTextStatusColor(row.status ?? "")}>
          {capitalizeFirstLetterWords(row.status ?? "", "_")}
        </Typography>
      ),
    },
  ];

  if (getActionEvents) {
    columns.push({
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />,
    });
  }

  return columns;
};
