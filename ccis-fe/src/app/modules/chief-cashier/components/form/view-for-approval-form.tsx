import Button from "@components/common/Button";
import Typography from "@components/common/Typography";
import Loader from "@components/Loader";
import React, { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";

import { ROUTES } from "@constants/routes";

import TextField from "@components/form/TextField";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { usePadRequestActions } from "@state/reducer/gam-pad-request";
import { formatWordDateDDMMYYY } from "@helpers/date";
import { confirmSaveOrEdit } from "@helpers/prompt";
import { RequestPadStatus } from "@enums/request-pads";

const ViewForApprovalForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  const { gamPadRequestDetails, remainingPads, putGamPadRequest: putGAMPadRequest } = useSelector((state: RootState) => state.gamPadRequest);

  const { getPadRequestDetails, getRemainingPads, putGamPadRequest } = usePadRequestActions();

  const handleApprove = async () => {
    const isConfirmed = await confirmSaveOrEdit("Confirmation", "Do you confirm the request?");

    if (isConfirmed) {
      putGamPadRequest({
        id: gamPadRequestDetails.data.id,
        divisionId: gamPadRequestDetails.data.divisionId,
        formTypeId: gamPadRequestDetails.data.formTypeId,
        areaId: gamPadRequestDetails.data.areaId,
        numberOfPads: gamPadRequestDetails.data.numberOfPads,
        seriesFrom: gamPadRequestDetails.data.seriesFrom,
        seriesTo: gamPadRequestDetails.data.seriesTo,
        releasedTo: gamPadRequestDetails.data.releasedTo,
        status: RequestPadStatus.APPROVED,
      });
    }
  };

  const getButtonClassNames = (status: string, loading: boolean) => {
    const isForApproval = status === RequestPadStatus.APPROVED;

    return [
      isForApproval ? "bg-sky-700 cursor-not-allowed" : "bg-sky-500 hover:bg-sky-700 cursor-pointer",
      "w-80",
      "font-medium",
      "rounded-lg",
      "text-sm",
      "px-5",
      "py-2.5",
      "text-center",
      "mb-2",
      loading ? "cursor-progress bg-sky-700" : "",
    ].join(" ");
  };

  const disableBtn = () => {
    let isDisable = false;

    if (gamPadRequestDetails.data !== undefined && gamPadRequestDetails?.data.status === RequestPadStatus.APPROVED) {
      isDisable = true;
    }

    if (putGAMPadRequest.loading) {
      isDisable = true;
    }

    return isDisable;
  };

  useEffect(() => {
    if (id !== undefined) {
      getPadRequestDetails({ id: parseInt(id) });
    }
  }, [putGAMPadRequest.success]);

  useEffect(() => {
    if (gamPadRequestDetails?.data !== undefined) {
      getRemainingPads({ id: parseInt(gamPadRequestDetails.data.createdBy.id) });
    }
  }, [gamPadRequestDetails]);

  return gamPadRequestDetails.loading ? (
    <div className="flex justify-center items-center h-screen">
      <Loader />
    </div>
  ) : (
    <div>
      <Button classNames="btn bg-slate-600 btn-sm" onClick={() => navigate(ROUTES.CHIEFCASHIER.requestPads.key)}>
        Back
      </Button>
      <div className="mx-6">
        <Typography className="mt-6 text-primary font-poppins-semibold">REQUEST NEW PAD(s)</Typography>

        <div className="mt-8 gap-4 flex justify-center">
          <div className="w-full flex flex-col gap-10">
            <div className="flex w-full flex-col">
              <div className="divider divider-start font-semibold">REQUEST DETAILS</div>
            </div>
            {/* First row */}
            <div className="grid grid-cols-3 gap-4">
              <div className="w-40">
                <span className="text-sm text-grey-500">No. of Remaining Pads</span>
                <TextField
                  size="sm"
                  className="border-0 border-b border-grey-500 rounded-none focus-within:outline-none"
                  placeholder="No. of Remaining Pads"
                  readOnly
                  value={remainingPads.data !== undefined ? remainingPads.data.remaining_pads : 0}
                />
              </div>
              <div className="w-40">
                <span className="text-sm text-grey-500">No. of Requested Pads</span>
                <TextField
                  size="sm"
                  className="border-0 border-b border-grey-500 rounded-none focus-within:outline-none"
                  placeholder="No. of Requested Pads"
                  readOnly
                  value={gamPadRequestDetails.data !== undefined ? gamPadRequestDetails.data.numberOfPads : 0}
                />
              </div>
            </div>

            {/* Second row */}
            <div className="grid grid-cols-3 gap-4">
              <div className="w-40">
                <span className="text-sm text-grey-500">Request No.</span>
                <TextField
                  size="sm"
                  className="border-0 border-b border-grey-500 rounded-none focus-within:outline-none"
                  placeholder="Request No."
                  readOnly
                  value={gamPadRequestDetails.data !== undefined ? gamPadRequestDetails.data.id : 0}
                />
              </div>
              <div className="w-40">
                <span className="text-sm text-grey-500">Series From</span>
                <TextField
                  size="sm"
                  className="border-0 border-b border-grey-500 rounded-none focus-within:outline-none"
                  placeholder="Series From"
                  readOnly
                  value={gamPadRequestDetails.data !== undefined ? gamPadRequestDetails.data.seriesFrom : 0}
                />
              </div>
              <div className="w-40">
                <span className="text-sm text-grey-500">Series To</span>
                <TextField
                  size="sm"
                  className="border-0 border-b border-grey-500 rounded-none focus-within:outline-none"
                  placeholder="Series To"
                  readOnly
                  value={gamPadRequestDetails.data !== undefined ? gamPadRequestDetails.data.seriesTo : 0}
                />
              </div>
            </div>

            {/* Third row */}
            <div className="grid grid-cols-3 gap-4">
              <div className="w-40">
                <span className="text-sm text-grey-500">Division</span>
                <TextField
                  size="sm"
                  className="border-0 border-b border-grey-500 rounded-none focus-within:outline-none"
                  placeholder="Division"
                  readOnly
                  value={gamPadRequestDetails.data !== undefined ? gamPadRequestDetails.data.division.divisionCode : 0}
                />
              </div>
              <div className="w-40">
                <span className="text-sm text-grey-500">Type</span>
                <TextField
                  size="sm"
                  className="border-0 border-b border-grey-500 rounded-none focus-within:outline-none"
                  placeholder="Type"
                  readOnly
                  value={gamPadRequestDetails.data !== undefined ? gamPadRequestDetails.data.formType.formTypeCode : 0}
                />
              </div>
              <div className="w-40">
                <span className="text-sm text-grey-500">Area Released</span>
                <TextField
                  size="sm"
                  className="border-0 border-b border-grey-500 rounded-none focus-within:outline-none"
                  placeholder="Area Released"
                  readOnly
                  value={gamPadRequestDetails.data !== undefined ? gamPadRequestDetails.data.area.areaName : ""}
                />
              </div>
            </div>
          </div>
        </div>
        <div className="mt-8 gap-4 flex justify-center">
          <div className="w-full flex flex-col gap-10">
            <div className="flex w-full flex-col">
              <div className="divider divider-start font-semibold">ASSIGNEE DETAILS</div>
            </div>

            {/* First row */}
            <div className="grid grid-cols-3 gap-4">
              <div className="w-40">
                <span className="text-sm text-grey-500">Requested By</span>
                <TextField
                  size="sm"
                  className="border-0 border-b border-grey-500 rounded-none focus-within:outline-none"
                  placeholder="Requested By"
                  readOnly
                  value={gamPadRequestDetails.data !== undefined ? `${gamPadRequestDetails.data.createdBy.firstname} ${gamPadRequestDetails.data.createdBy.lastname}` : ""}
                />
              </div>
              <div className="w-40">
                <span className="text-sm text-grey-500">Requested To</span>
                <TextField
                  size="sm"
                  className="border-0 border-b border-grey-500 rounded-none focus-within:outline-none"
                  placeholder="Requested To"
                  readOnly
                  value={gamPadRequestDetails.data !== undefined ? `${gamPadRequestDetails.data.releasedUser.firstname} ${gamPadRequestDetails.data.releasedUser.lastname}` : ""}
                />
              </div>
              <div className="w-40">
                <span className="text-sm text-grey-500">Requested Date</span>
                <TextField
                  size="sm"
                  className="border-0 border-b border-grey-500 rounded-none focus-within:outline-none"
                  placeholder="Requested Date"
                  readOnly
                  value={gamPadRequestDetails.data !== undefined ? formatWordDateDDMMYYY(gamPadRequestDetails.data.createdAt, true) : ""}
                />
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-center gap-2 mt-14">
          <Button
            classNames="bg-[#33333399] w-80 font-medium rounded-lg px-5 py-2.5 text-center mb-2 hover:"
            isSubmitting={putGAMPadRequest.loading}
            onClick={() => navigate(ROUTES.CHIEFCASHIER.requestPads.key)}>
            {disableBtn() ? "Back To List" : "Cancel"}
          </Button>
          {!disableBtn() && (
            <Button classNames={getButtonClassNames(gamPadRequestDetails?.data?.status, putGAMPadRequest.loading)} isSubmitting={disableBtn()} onClick={handleApprove}>
              Approve
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ViewForApprovalForm;
