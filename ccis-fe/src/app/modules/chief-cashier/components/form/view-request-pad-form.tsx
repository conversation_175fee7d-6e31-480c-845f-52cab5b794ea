import Button from "@components/common/Button";
import Typography from "@components/common/Typography";
import { ROUTES } from "@constants/routes";
import { formatWordDateDDMMYYY } from "@helpers/date";
import { confirmSaveOrEdit } from "@helpers/prompt";
import { IPadAssignments } from "@interface/form-inventory.interface";
import { RootState } from "@state/reducer";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { usePadRequestActions } from "@state/reducer/gam-pad-request";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";

const ViewRequestPadForm = () => {
  const navigate = useNavigate();

  // ID in url
  const { id } = useParams<{ id: string }>();

  // Global State
  const { gamPadRequestDetails, remainingPads } = useSelector((state: RootState) => state.gamPadRequest);
  const { postTransmittalForm: transmittalForm } = useSelector((state: RootState) => state.formInventoryTransmittal);

  // Actions
  const { getPadRequestDetails, getRemainingPads } = usePadRequestActions();
  const { postTransmittalForm, resetTransmittalForm } = useTransmittalFormActions();

  const lastIndex = () => {
    return gamPadRequestDetails?.data?.padAssignments.length - 1;
  };

  const handleProceedAssignment = async () => {
    const isConfirmed = await confirmSaveOrEdit("Confirmation", "Do you want to proceed with the assignment?");
    if (isConfirmed) {
      const padAssignmentPayload = gamPadRequestDetails?.data?.padAssignments.map(({ createdAt, updatedAt, ...rest }: IPadAssignments) => rest);
      const payload = {
        releasedArea: gamPadRequestDetails?.data?.area?.id,
        releasedTo: gamPadRequestDetails?.data?.releasedUser.id,
        remarks: "",
        padAssignment: padAssignmentPayload,
      };

      postTransmittalForm(payload);
    }
  };

  useEffect(() => {
    if (id !== undefined) {
      getPadRequestDetails({ id: parseInt(id) });
    }
  }, []);

  useEffect(() => {
    if (gamPadRequestDetails?.data !== undefined) {
      getRemainingPads({ id: parseInt(gamPadRequestDetails.data.createdBy.id) });
    }
  }, [gamPadRequestDetails]);

  useEffect(() => {
    if (transmittalForm?.success) {
      resetTransmittalForm();
      navigate(ROUTES.CHIEFCASHIER.seriesAssignment.key);
    }
  }, [transmittalForm?.success]);

  return (
    <div>
      <Button classNames="btn bg-slate-600 btn-sm" onClick={() => navigate(ROUTES.CHIEFCASHIER.requestPads.key)}>
        Back
      </Button>

      <div className="mx-6">
        <Typography size="xl" className="text-primary my-16 flex flex-row font-poppins-semibold">
          REQUESTED PAD(s) DETAILS
        </Typography>
        <div>
          <div className="">
            <div className="flex w-full flex-col">
              <div className="divider divider-start font-semibold">REQUEST DETAILS</div>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="flex flex-col gap-2 mt-10">
                <span className="text-sm text-grey-500">No. of Remaining Pads</span>
                <Typography size="md" className="">
                  {remainingPads?.data?.remaining_pads ? remainingPads?.data?.remaining_pads : 0}
                </Typography>
              </div>

              <div className="flex flex-col gap-2 mt-10">
                <span className="text-sm text-grey-500">No. of Requested Pads</span>
                <Typography size="md" className="">
                  {gamPadRequestDetails?.data?.padAssignments ? gamPadRequestDetails?.data?.padAssignments.length : 0}
                </Typography>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="flex flex-col gap-2 mt-10">
                <span className="text-sm text-grey-500">Request No.</span>
                <Typography size="md" className="">
                  {gamPadRequestDetails?.data?.id ? gamPadRequestDetails?.data?.id : 0}
                </Typography>
              </div>

              <div className="flex flex-col gap-2 mt-10">
                <span className="text-sm text-grey-500">Series From</span>
                <Typography size="md" className="">
                  {gamPadRequestDetails?.data?.padAssignments ? gamPadRequestDetails?.data?.padAssignments[0].seriesFrom : 0}
                </Typography>
              </div>

              <div className="flex flex-col gap-2 mt-10">
                <span className="text-sm text-grey-500">Series To</span>
                <Typography size="md" className="">
                  {gamPadRequestDetails?.data?.padAssignments ? gamPadRequestDetails?.data?.padAssignments[lastIndex()].seriesTo : 0}
                </Typography>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="flex flex-col gap-2 mt-10">
                <span className="text-sm text-grey-500">Division</span>
                <Typography size="md" className="">
                  {gamPadRequestDetails?.data?.division ? gamPadRequestDetails?.data?.division?.divisionName : ""}
                </Typography>
              </div>

              <div className="flex flex-col gap-2 mt-10">
                <span className="text-sm text-grey-500">Type</span>
                <Typography size="md" className="">
                  {gamPadRequestDetails?.data?.formType ? gamPadRequestDetails?.data?.formType?.formTypeCode : ""}
                </Typography>
              </div>

              <div className="flex flex-col gap-2 mt-10">
                <span className="text-sm text-grey-500">Area Released</span>
                <Typography size="md" className="">
                  {gamPadRequestDetails?.data?.area ? gamPadRequestDetails?.data?.area?.areaName : ""}
                </Typography>
              </div>
            </div>
          </div>

          <div className="mt-10">
            <div className="flex w-full flex-col">
              <div className="divider divider-start font-semibold">ASSIGNEE DETAILS</div>
            </div>

            <div className="grid grid-cols-4 gap-4">
              <div className="flex flex-col gap-2 mt-10">
                <span className="text-sm text-grey-500">Requested By</span>
                <Typography size="md" className="">
                  {gamPadRequestDetails?.data?.createdBy ? `${gamPadRequestDetails?.data?.createdBy?.firstname} ${gamPadRequestDetails?.data?.createdBy?.lastname}` : ""}
                </Typography>
              </div>

              <div className="flex flex-col gap-2 mt-10">
                <span className="text-sm text-grey-500">Requested To</span>
                <Typography size="md" className="">
                  {gamPadRequestDetails?.data?.releasedUser ? `${gamPadRequestDetails?.data?.releasedUser?.firstname} ${gamPadRequestDetails?.data?.releasedUser?.lastname}` : ""}
                </Typography>
              </div>

              <div className="flex flex-col gap-2 mt-10">
                <span className="text-sm text-grey-500">Date Requested</span>
                <Typography size="md" className="">
                  {gamPadRequestDetails?.data?.createdAt ? formatWordDateDDMMYYY(gamPadRequestDetails?.data?.createdAt, true) : ""}
                </Typography>
              </div>

              {/* <div className="flex flex-col gap-2 mt-10">
                <span className="text-sm text-grey-500">Approve By</span>
                <Typography size="md" className="">
                  Lelyn Asay
                </Typography>
              </div> */}

              <div className="flex flex-col gap-2 mt-10">
                <span className="text-sm text-grey-500">Date Approved</span>
                <Typography size="md" className="">
                  {gamPadRequestDetails?.data?.updatedAt ? formatWordDateDDMMYYY(gamPadRequestDetails?.data?.updatedAt, true) : ""}
                </Typography>
              </div>
            </div>

            <div className="mt-20 w-full flex justify-center">
              <Button variant="primary" onClick={handleProceedAssignment}>
                Proceed to Assignment
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewRequestPadForm;
