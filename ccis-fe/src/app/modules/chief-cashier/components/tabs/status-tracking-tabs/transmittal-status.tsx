import { createDate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, createSelect<PERSON><PERSON>e<PERSON>and<PERSON> } from "@helpers/handlers";
import { useDebouncedSearch } from "@hooks/useDebouncedSearch";
import { useSelectOptions } from "@hooks/useSelectOptions";
import { RootState } from "@state/reducer";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { useState } from "react";
import { useSelector } from "react-redux";
import { getColumns } from "../../table/column/transmittal-tracking-column";
import { useFetchWithParams } from "@hooks/useFetchWithParams";
import TableFilter from "@modules/gam/request-pads/TableFilter";
import { useIncomingReceivedFormActions } from "@state/reducer/form-inventory-incoming-received-form";
import Table from "@components/common/Table";
import { IFormTransmittalTrail } from "@interface/form-inventory.interface";
import { IUserRoles } from "@interface/user.interface";

const TransmittalStatusTab = () => {
  // States
  const [resetCounter, setResetCounter] = useState(0);
  const [divisionFilter, setDivisionFilter] = useState<number>(0);
  const [type, setType] = useState<number>(0);
  const [area, setArea] = useState<number>(0);
  const [dateFrom, setDateFrom] = useState<string>("");
  const [dateTo, setDateTo] = useState<string>("");
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Global state
  const { transmittalStatus } = useSelector((state: RootState) => state.formInventoryIncomingReceivedForms);

  // for Select options
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const types = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const areas = useSelector((state: RootState) => state.utilitiesAreas.areas);

  // Actions
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const { getTransmittalStatus } = useIncomingReceivedFormActions();

  // Custom hooks
  const { value: searchText, handleChange: handleSearch, setValue: setSearchText } = useDebouncedSearch();

  const divisionOptions = useSelectOptions({
    data: divisions,
    firstOptionText: "Select Division",
    valueKey: "id",
    textKey: "divisionName",
  });

  const typeOptions = useSelectOptions({
    data: types,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: "formTypeName",
  });

  const areaOptions = useSelectOptions({
    data: areas,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: "areaName",
  });

  // Select handlers
  const handleDivisionChange = createSelectChangeHandler(setDivisionFilter);
  const handleTypeChange = createSelectChangeHandler(setType);
  const handleAreaChange = createSelectChangeHandler(setArea);

  // Date handlers
  const handleDateFromChange = createDateChangeHandler(setDateFrom);
  const handleDateToChange = createDateChangeHandler(setDateTo);

  useFetchWithParams(
    [getDivisions, getAreas, getFormTypes],
    {
      filter: "",
    },
    [],
    false
  );

  useFetchWithParams(
    getTransmittalStatus,
    {
      page,
      pageSize,
      filter: searchText,
      divisionFilter,
    },
    [searchText, divisionFilter, type, page, pageSize, dateFrom, dateTo]
  );

  const getColumnDate = (trails: IFormTransmittalTrail[], userRole: string, status: string): IFormTransmittalTrail | undefined => {
    const trail = trails.find((trail) => {
      return trail.status === status && trail.createdBy?.roles?.some((role: IUserRoles) => role.name === userRole);
    });

    return trail;
  };

  const columns = getColumns({ getColumnDate });

  const handleClearAll = () => {
    setSearchText("");
    setDivisionFilter(0);
    setType(0);
    setDateFrom("");
    setDateTo("");
    setResetCounter((prev) => prev + 1);
  };

  return (
    <div className="p-4">
      <div className="mt-8">
        <div className="flex flex-row items-center justify-between">
          <TableFilter
            handleClearAll={handleClearAll}
            searchText={searchText}
            handleSearch={handleSearch}
            resetCounter={resetCounter}
            type={type}
            handleDivisionChange={handleDivisionChange}
            handleTypeChange={handleTypeChange}
            typeOptions={typeOptions}
            divisionFilter={divisionFilter}
            divisionOptions={divisionOptions}
            areaFilter={area}
            areaOptions={areaOptions}
            handleAreaChange={handleAreaChange}
            dateFrom={dateFrom}
            handleDateFromChange={handleDateFromChange}
            dateTo={dateTo}
            handleDateToChange={handleDateToChange}
          />
        </div>
        <div className="w-screen">
          <Table
            className="!min-h-[100%] h-[500px] border-[1px] border-zinc-300 mt-8 overflow-y-auto"
            columns={columns}
            data={transmittalStatus?.tableData?.data?.data || []}
            searchable={false}
            multiSelect={false}
            paginationTotalRows={transmittalStatus?.tableData?.data?.data.length || [].length}
            paginationServer={true}
            // loading={loading}
            onChangeRowsPerPage={(newPageSize, newPage) => {
              setPageSize(newPageSize);
              setPage(newPage);
            }}
            onPaginate={(newPage) => setPage(newPage)}
          />
        </div>
      </div>
    </div>
  );
};

export default TransmittalStatusTab;
