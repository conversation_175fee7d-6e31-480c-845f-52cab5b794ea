import { createDate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, createSelect<PERSON>hangeHand<PERSON> } from "@helpers/handlers";
import { useDebouncedSearch } from "@hooks/useDebouncedSearch";
import { useFetchWithParams } from "@hooks/useFetchWithParams";
import { useSelectOptions } from "@hooks/useSelectOptions";
import { IActions } from "@interface/common.interface";
import { IIncomingReceivedForm } from "@interface/form-inventory.interface";
import { RootState } from "@state/reducer";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { getColumns } from "../../table/column/series-tracking-column";
import TableFilter from "../../table/filter/TableFilter";
import Table from "@components/common/Table";
import { GoVersions } from "react-icons/go";
import { useIncomingReceivedFormActions } from "@state/reducer/form-inventory-incoming-received-form";
import { FormStatus } from "@enums/form-status";
import { ROUTES } from "@constants/routes";

const SeriesTrackingTab = () => {
  const navigate = useNavigate();

  // States
  const [resetCounter, setResetCounter] = useState(0);
  const [divisionFilter, setDivisionFilter] = useState<number>(0);
  const [type, setType] = useState<number>(0);
  const [area, setArea] = useState<number>(0);
  const [dateFrom, setDateFrom] = useState<string>("");
  const [dateTo, setDateTo] = useState<string>("");
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Global State
  const { data: incomingFormData } = useSelector((state: RootState) => state.formInventoryIncomingReceivedForms.getIncomingReceivedForms);

  // for Select options
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const types = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const areas = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const user = useSelector((state: RootState) => state.auth.user.data);

  // Actions
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const { getIncomingReceivedForms } = useIncomingReceivedFormActions();

  // Custom hooks
  const { value: searchText, handleChange: handleSearch, setValue: setSearchText } = useDebouncedSearch();

  const divisionOptions = useSelectOptions({
    data: divisions,
    firstOptionText: "Select Division",
    valueKey: "id",
    textKey: "divisionName",
  });

  const typeOptions = useSelectOptions({
    data: types,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: "formTypeName",
  });

  const areaOptions = useSelectOptions({
    data: areas,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: "areaName",
  });

  // Select handlers
  const handleDivisionChange = createSelectChangeHandler(setDivisionFilter);
  const handleTypeChange = createSelectChangeHandler(setType);
  const handleAreaChange = createSelectChangeHandler(setArea);

  // Date handlers
  const handleDateFromChange = createDateChangeHandler(setDateFrom);
  const handleDateToChange = createDateChangeHandler(setDateTo);

  useFetchWithParams(
    [getDivisions, getFormTypes, getAreas],
    {
      filter: "",
    },
    [],
    false
  );
  useFetchWithParams(
    getIncomingReceivedForms,
    {
      page,
      pageSize,
      filter: searchText,
      statusFilter: FormStatus.VERIFIED,
    },
    [searchText, type, page, pageSize, dateFrom, dateTo],
    false
  );

  const padAssignmentsStatus = (row: IIncomingReceivedForm) => {
    let counts = { active: 0, returned: 0 };
    if (row?.padAssignments) {
      counts = row?.padAssignments.reduce(
        (pad, padAssignment) => {
          if (padAssignment.status === FormStatus.ACTIVE) {
            pad.active++;
          } else if (padAssignment.status === FormStatus.RETURNED) {
            if (padAssignment?.formTransmittal && padAssignment?.formTransmittal !== null) {
              if (
                padAssignment?.formTransmittal?.latestFormTransmittalTrail?.releasedTo?.id &&
                padAssignment.formTransmittal.latestFormTransmittalTrail.releasedTo?.id === user.id &&
                padAssignment.formTransmittal.latestFormTransmittalTrail.status === FormStatus.RECEIVED
              ) {
                pad.returned++;
              }
            }
          }
          return pad;
        },
        { active: 0, returned: 0 }
      );
    }

    return counts;
  };

  const getActionEvents = (row: IIncomingReceivedForm): IActions<any>[] => {
    const actions: IActions<any>[] = [
      {
        name: "View",
        event: async () => {
          navigate(ROUTES.CHIEFCASHIER.statusTrackingDetails.parse(row.id.toString()));
        },
        icon: GoVersions,
        color: "primary",
      },
    ];
    return actions;
  };

  const columns = getColumns({ getActionEvents, padAssignmentsStatus });

  const handleClearAll = () => {
    setSearchText("");
    setType(0);
    setDateFrom("");
    setDateTo("");
    setDivisionFilter(0);
    setResetCounter((prev) => prev + 1);
  };

  return (
    <div className="p-4">
      <div className="mt-8">
        <div className="flex flex-row items-center justify-between">
          <TableFilter
            handleClearAll={handleClearAll}
            searchText={searchText}
            handleSearch={handleSearch}
            resetCounter={resetCounter}
            type={type}
            handleDivisionChange={handleDivisionChange}
            handleTypeChange={handleTypeChange}
            typeOptions={typeOptions}
            divisionFilter={divisionFilter}
            divisionOptions={divisionOptions}
            areaFilter={area}
            areaOptions={areaOptions}
            handleAreaChange={handleAreaChange}
            dateFrom={dateFrom}
            handleDateFromChange={handleDateFromChange}
            dateTo={dateTo}
            handleDateToChange={handleDateToChange}
          />
        </div>
        <Table
          className="!min-h-[100%] h-[500px] border-[1px] border-zinc-300 mt-8"
          columns={columns}
          data={incomingFormData?.data || []}
          searchable={false}
          multiSelect={false}
          paginationTotalRows={incomingFormData?.data.length || [].length}
          paginationServer={true}
          // loading={loading}
          onChangeRowsPerPage={(newPageSize, newPage) => {
            setPageSize(newPageSize);
            setPage(newPage);
          }}
          onPaginate={(newPage) => setPage(newPage)}
        />
      </div>
    </div>
  );
};

export default SeriesTrackingTab;
