import { TableColumn } from "react-data-table-component";
import Typography from "@components/common/Typography";
import ActionDropdown from "@components/common/ActionDropdown";
import { IActions } from "@interface/common.interface";
import { getTextStatusColor } from "@helpers/text";
import { IFormTransmittal } from "@interface/form-inventory.interface";
import { FormStatus } from "@enums/form-status";
import { IDivision, IFormType } from "@interface/form-inventory-utilities";
import { IUserArea } from "@interface/utilities.interface";
import { findItem } from "@helpers/array";

type GetActionEventsFn = (row: IFormTransmittal) => IActions<any>[];

type GetColumnsParams = {
  getActionEvents?: GetActionEventsFn;
  divisions?: IDivision[];
  formTypes?: IFormType[];
  areas?: IUserArea[];
};

export const getColumns = ({ getActionEvents, divisions, formTypes, areas }: GetColumnsParams): TableColumn<IFormTransmittal>[] => {
  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const columns: TableColumn<IFormTransmittal>[] = [
    {
      name: "No.",
      cell: (row) => row.id,
      width: "80px",
      ...commonSetting,
    },
    {
      name: "Transmittal No.",
      cell: (row) => row.transmittalNumber,
      ...commonSetting,
    },
    {
      name: "ATP Number",
      cell: (row) => row.returnedPads?.[0]?.form?.atpNumber || "N/A",
      ...commonSetting,
    },
    {
      name: "Division",
      cell: (row) => {
        if (divisions) {
          const divisionsList = Array.from(
            new Set(
              row.returnedPads
                ?.map((assignment: { form?: { divisionId?: number } }) => {
                  const division = divisions.find((division) => division.id === assignment.form?.divisionId);
                  return division ? division.divisionName : null;
                })
                .filter(Boolean) // Remove null values
            )
          );
          return divisionsList.length > 0 ? divisionsList.join(", ") : "N/A";
        }

        return "N/A";
      },
      ...commonSetting,
    },
    {
      name: "Type",
      cell: (row) => {
        if (formTypes) {
          const uniqueFormTypes = Array.from(
            new Set(
              row.returnedPads
                ?.map((assignment: { form?: { formTypeId?: number } }) => {
                  const formType = formTypes.find((type) => type.id === assignment.form?.formTypeId);
                  return formType ? formType.formTypeCode : null;
                })
                .filter(Boolean) // Remove null values
            )
          );
          return uniqueFormTypes.length > 0 ? uniqueFormTypes.join(", ") : "N/A";
        }

        return "N/A";
      },
      ...commonSetting,
    },
    {
      name: "Released Area",
      cell: (row) => <div>{String(findItem(areas, "id", Number(row?.releasedAreaId), "areaName") || "N/A")}</div>,
      ...commonSetting,
    },
    {
      name: "No. of Pads",
      cell: (row) => row.returnedPads?.length || "N/A",
      width: "120px",
      ...commonSetting,
    },
    {
      name: "Status",
      cell: (_row) => {
        // Determine status based on receivedAt
        const status = _row?.oldestFormTransmittalTrail?.receivedAt ? FormStatus.RECEIVED : FormStatus.NOT_YET_RECEIVED;

        return (
          <Typography size="xs" className={getTextStatusColor(status)}>
            {status
              .replace(/_/g, " ")
              .toLowerCase()
              .replace(/\b\w/g, (char) => char.toUpperCase())}
          </Typography>
        );
      },
    },
  ];

  if (getActionEvents) {
    columns.push({
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />,
    });
  }

  return columns;
};
