import React, { ReactNode, useEffect } from "react";

import Tabs from "@components/common/Tabs";

import ForReceivingTab from "./components/ForReceivingTab";
import ReturnPadTab from "./return-pad";
import PRIssuanceTab from "./components/PRIssuanceTab";
import { clearData, hasKey } from "@helpers/storage";

const AdminSatelliteNewForms: React.FC = () => {
  const headers: string[] = ["For Receiving", "PR Issuance", "Return Pad"];
  const contents: ReactNode[] = [<ForReceivingTab />, <PRIssuanceTab />, <ReturnPadTab />];

  useEffect(() => {
    if (hasKey("currentPad")) {
      clearData("currentPad");
    }
  }, []);

  return (
    <div>
      <div className=" my-4 py-4 border-b border-zinc-300 w-full text-zinc-500">
        Dashboard / <span className="text-primary font-poppins-semibold ">New Forms</span>
      </div>
      <Tabs headers={headers} contents={contents} size="md" headerClass="w-52" fullWidthHeader={false} />
    </div>
  );
};

export default AdminSatelliteNewForms;
