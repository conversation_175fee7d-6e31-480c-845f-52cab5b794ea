import Typography from "@components/common/Typography";
import { formatWordDateDDMMYYY } from "@helpers/date";
import { IPadSeriesDetails } from "@interface/form-inventory.interface";
import AttachmentItem from "../../AttachmentItem";

const ViewCancelledPR = ({ data }: { data: IPadSeriesDetails }) => {
  console.log(data);
  return (
    <div className="mt-10">
      <div className="border-2 border-primary rounded py-8 px-6 w-full">
        <div className="flex gap-1">
          <Typography size="2xl" className="font-poppins-medium">
            Cancelled PR #
          </Typography>
          <Typography size="2xl" className="text-red-500 font-poppins-medium">
            {data?.seriesNo}
          </Typography>
        </div>

        {/* Cancel Details */}
        <div className="mt-10">
          {/* PR Date */}
          <div className="grid grid-cols-3 gap-4 w-1/3">
            <div className="col-span-1">
              <div className="flex items-center justify-between">
                <div className="">
                  <Typography size="lg" className="font-poppins-medium">
                    PR
                  </Typography>
                </div>
                <div className="">:</div>
              </div>
            </div>
            <div className="col-span-2">{data?.padAssignment?.formTransmittal?.createdAt ? formatWordDateDDMMYYY(data.padAssignment?.formTransmittal?.createdAt, true) : ""}</div>
          </div>

          {/* Cancel Date */}
          <div className="grid grid-cols-3 gap-4 w-1/3 mt-4">
            <div className="col-span-1">
              <div className="flex items-center justify-between">
                <div className="">Cancel Date</div>
                <div className="">:</div>
              </div>
            </div>
            <div className="col-span-2">{data?.cancelledAt ? formatWordDateDDMMYYY(data.cancelledAt, true) : ""}</div>
          </div>

          {/* Remarks */}
          <div className="grid grid-cols-3 gap-4 w-1/3 mt-4">
            <div className="col-span-1">
              <div className="flex items-center justify-between">
                <div className="">Remarks</div>
                <div className="">:</div>
              </div>
            </div>
            <div className="col-span-2">{data.remarks}</div>
          </div>

          {/* Attachments */}
          <div className="grid grid-cols-3 gap-4 w-1/3 mt-4">
            <div className="col-span-1">
              <div className="flex items-center justify-between">
                <div className="">Attachments</div>
                <div className="">:</div>
              </div>
            </div>
            <div className="col-span-2">
              {data?.attachments &&
                data.attachments.map((file) => (
                  <div key={file.label} className="flex items-center gap-2">
                    <AttachmentItem filename={file.label} mimeType={file.mimeType} size={file.size} filepath={file.filepath} />
                  </div>
                ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewCancelledPR;
