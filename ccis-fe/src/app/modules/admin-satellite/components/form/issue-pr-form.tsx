import Button from "@components/common/Button";
import FileDropzone from "@components/common/FileDropzone";
import Typography from "@components/common/Typography";
import TextField from "@components/form/TextField";
import { ROUTES } from "@constants/routes";
import { useEffect, useState } from "react";
import { FaCloudArrowUp } from "react-icons/fa6";
import { useNavigate, useParams } from "react-router-dom";
import CancelPRModal from "../modal/cancel-pr-modal";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { useUserManagementActions } from "@state/reducer/users-management";
import { useDebouncedSearch } from "@hooks/useDebouncedSearch";
import { useFetchWithParams } from "@hooks/useFetchWithParams";
import { PadStatus } from "@enums/form-status";
import { FormikProps, useFormik } from "formik";
import { useProductActions } from "@state/reducer/products";
import { useCooperativesManagementActions } from "@state/reducer/cooperatives";
import Select from "@components/form/Select";
import { useSelectOptions } from "@hooks/useSelectOptions";
import { ModeOfpayment } from "@enums/payment";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { formatWordDateDDMMYYY } from "@helpers/date";
import AttachmentItem from "../AttachmentItem";
import { MdDeleteForever } from "react-icons/md";
import { TCancelReceipt, TIssueReceipt } from "@state/types/form-inventory-transmittal";
import { CancelPadValidation, IssueSeriesValidation } from "@services/form-inventory-transmittal/form-inventory-transmittal.schema";
import { usePaymentMethodActions } from "@state/reducer/form-inventory-utilities-payment-methods";
import { getData, hasKey } from "@helpers/storage";
import { useBankAccountActions } from "@state/reducer/form-inventory-utilities-bank-accounts";
import { Combobox } from "@components/common-v2/Combobox";
import { IPaymentMethod } from "@interface/form-inventory-utilities";
import { formatNumberWithCommas, parseFormattedNumber } from "@helpers/format-number";
import { confirmSaveOrEdit } from "@helpers/prompt";
import { cleanPayload } from "@helpers/objects";
import { IPaymentDetails } from "@interface/form-inventory.interface";
import Loader from "@components/Loader";

const IssuePRForm = () => {
  const maxSize = 5242880;

  const navigate = useNavigate();

  // Get ID in URL
  const { id } = useParams();

  // States
  const [files, setFiles] = useState<Array<File>>([]);
  const [isAllowedSize, setIsAllowedSize] = useState<boolean>(true);
  const [imagePreview, setImagePreview] = useState<string | ArrayBuffer | null>(null);
  const [prTableId, setPrTableId] = useState<number | null>(null);
  const [selectedBankAccountId, setSelectedBankAccountId] = useState<number>(0);

  // Modal States
  const [isFormOpen, setIsFormOpen] = useState(false);

  // Global States
  const users = useSelector((state: RootState) => state.usersManagement.users);
  const { getProducts: products } = useSelector((state: RootState) => state.products);
  const { cooperatives } = useSelector((state: RootState) => state.cooperatives);
  const { userPadSeriesDetails, putCancelReceipt: cancelReceipt, putIssueReceipt: issueReceipt } = useSelector((state: RootState) => state.formInventoryTransmittal);
  const { bankAccounts } = useSelector((state: RootState) => state.formInventoryUtilitiesBankAccounts);
  const { paymentMethods } = useSelector((state: RootState) => state.formInventoryUtilitiesPaymentMethods);
  const user = useSelector((state: RootState) => state.auth.user.data);

  // Actions
  const { getUsers } = useUserManagementActions();
  const { getProducts } = useProductActions();
  const { getCooperatives } = useCooperativesManagementActions();
  const { getUserPadSeriesDetails, putCancelReceipt, putIssueReceipt, resetIssueForm } = useTransmittalFormActions();
  const { getPaymentMethods } = usePaymentMethodActions();
  const { getBankAccountsAvailable } = useBankAccountActions();

  const formik: FormikProps<TIssueReceipt> = useFormik<TIssueReceipt>({
    initialValues: {
      id: userPadSeriesDetails?.data?.id,
      padAssignmentId: userPadSeriesDetails?.data?.padAssignment.id,
      seriesNo: userPadSeriesDetails?.data?.seriesNo,
      issuedBy: user.id,
      remitTo: 0, // required
      productId: 0, // required
      cooperativeId: 0, // required
      releasedAt: "", // required
      status: PadStatus.USED,
      attachments: [], // required
      paymentDetail: {
        paymentMethodId: 0, // required
        amount: 0,
        dateDeposit: "",
        accountNumber: "",
        bankAccountId: 0,
        chequeNumber: "",
        coopName: "",
        remarks: "",
      },
    },
    validationSchema: IssueSeriesValidation(paymentMethods),
    onSubmit: async (values) => {
      const isConfirmed = await confirmSaveOrEdit("Confirmation", "Do you want the issue this PR?");
      if (isConfirmed) {
        // Remove empty string and 0 values in the payment details payload
        const cleanPaymentDetails = cleanPayload(values.paymentDetail);

        // merge the clean payment details payload
        const payload = {
          ...values,
          paymentDetail: cleanPaymentDetails as IPaymentDetails,
        };

        putIssueReceipt(payload);
      }
    },
  });

  const cancelledFormik: FormikProps<TCancelReceipt> = useFormik<TCancelReceipt>({
    initialValues: {
      id: userPadSeriesDetails?.data?.id,
      remarks: "",
      status: PadStatus.CANCELLED,
      cancelledAt: new Date().toISOString(),
      attachments: [],
    },
    validationSchema: CancelPadValidation,
    onSubmit: (values) => {
      putCancelReceipt(values);
    },
  });

  // custom hooks
  const { value: searchUser, handleChange: handleSearchUser } = useDebouncedSearch();
  const { value: searchProduct, handleChange: handleSearchProduct } = useDebouncedSearch();
  const { value: searchCoop, handleChange: handleSearchCoop } = useDebouncedSearch();

  const paymentOptions = useSelectOptions({
    data: [...paymentMethods].reverse(),
    firstOptionText: "Select Payment Method",
    valueKey: "id",
    textKey: "paymentMethodName",
  });

  const bankOptions = useSelectOptions({
    data: bankAccounts,
    firstOptionText: "Select Bank",
    valueKey: "id",
    formatText: (item) => `${item.bank?.bankName ?? "N/A"} - ${item.division?.divisionName ?? "N/A"}`,
  });

  // Fetch users for search
  useFetchWithParams(
    getUsers,
    {
      nameFilter: searchUser,
      page: 1,
      pageSize: 10,
    },
    [searchUser],
    false
  );

  // Fetch products for search
  useFetchWithParams(
    getProducts,
    {
      filter: searchProduct,
      page: 1,
      pageSize: 10,
    },
    [searchProduct]
  );

  // Fetch Cooperatives for search
  useFetchWithParams(
    getCooperatives,
    {
      filter: searchCoop,
    },
    [searchCoop],
    false
  );

  // Fetch Payment Method
  useFetchWithParams(
    getPaymentMethods,
    {
      filter: "",
    },
    [],
    false
  );

  // Fetch Payment Method
  useFetchWithParams(
    getBankAccountsAvailable,
    {
      areaFilter: userPadSeriesDetails?.data ? userPadSeriesDetails?.data.padAssignment.form.area.id : "",
      divisionFilter: userPadSeriesDetails?.data ? userPadSeriesDetails?.data.padAssignment.form.division.id : "",
    },
    [userPadSeriesDetails?.data]
  );

  const handleToggleFormModal = () => {
    setIsFormOpen((prev) => !prev);
  };

  const checkPaymentMethod = (fieldMethods: string[]) => {
    const selectedMethod = paymentMethods.find((method: IPaymentMethod) => method.id === formik.values.paymentDetail.paymentMethodId);

    // Check if it has selected payment method
    if (!selectedMethod) {
      return false;
    }

    return fieldMethods.includes(selectedMethod.paymentMethodCode);
  };

  const handleFile = (acceptedFiles: Array<File>) => {
    setFiles(acceptedFiles);

    // check if modal is open
    const fileAttachment = isFormOpen ? cancelledFormik : formik;

    fileAttachment.setFieldValue("attachments", [{ file: acceptedFiles[0] }]);

    if (["image/jpeg", "image/png", "image/jpg"].includes(acceptedFiles[0].type)) {
      const file = new FileReader();
      file.onload = () => {
        setImagePreview(file.result);
      };

      file.readAsDataURL(acceptedFiles[0]);

      if (acceptedFiles[0].size > maxSize) {
        setIsAllowedSize(false);
      } else {
        setIsAllowedSize(true);
      }
    }
  };

  const handleClear = () => {
    setFiles([]);
    setImagePreview(null);
    formik.setFieldValue("attachments", []);
    cancelledFormik.setFieldValue("attachments", []);
  };

  const dateNow = () => {
    const now = new Date();
    return formatWordDateDDMMYYY(now.toISOString(), true);
  };

  const clearPaymentDetailsForm = () => {
    formik.setFieldValue("paymentDetail.amount", 0);
    formik.setFieldValue("paymentDetail.dateDeposit", "");
    formik.setFieldValue("paymentDetail.accountNumber", "");
    formik.setFieldValue("paymentDetail.bankAccountId", 0);
    formik.setFieldValue("paymentDetail.chequeNumber", "");
    formik.setFieldValue("paymentDetail.remarks", "");
  };

  const redirect = () => {
    return prTableId !== null ? navigate(ROUTES.ADMINSATELLITE.viewPrTable.parse(prTableId.toString())) : navigate(ROUTES.ADMINSATELLITE.adminSatelliteAdminNewForm.key);
  };

  useEffect(() => {
    if (id) {
      getUserPadSeriesDetails({ id: parseInt(id) });
    }
  }, []);

  useEffect(() => {
    if (hasKey("prTableId")) {
      setPrTableId(getData("prTableId"));
    }
  }, []);

  useEffect(() => {
    if (userPadSeriesDetails?.data !== undefined) {
      cancelledFormik.setFieldValue("id", userPadSeriesDetails?.data?.id);
      formik.setFieldValue("id", userPadSeriesDetails?.data?.id);
      formik.setFieldValue("padAssignmentId", userPadSeriesDetails?.data?.padAssignment.id);
      formik.setFieldValue("seriesNo", userPadSeriesDetails?.data?.seriesNo);
    }
  }, [userPadSeriesDetails?.data]);

  useEffect(() => {
    handleClear();
    cancelledFormik.resetForm();
    cancelledFormik.setFieldValue("id", userPadSeriesDetails?.data?.id);
  }, [isFormOpen]);

  useEffect(() => {
    if (cancelReceipt?.success || (issueReceipt?.success && prTableId !== null)) {
      redirect();
      resetIssueForm();
    }
  }, [cancelReceipt?.success, issueReceipt?.success]);

  return cancelReceipt?.loading || issueReceipt?.loading ? (
    <div className="w-full h-screen flex items-center justify-center">
      <Loader />
    </div>
  ) : (
    <div className="px-4">
      <div className="flex justify-start">
        <Button variant="secondary" onClick={redirect}>
          Back
        </Button>
      </div>

      {/* Cancel Receipt Section */}
      <div className=" flex flex-col gap-2 mt-10">
        <div className="flex items-center gap-4">
          <Typography size="lg" className="font-semibold">
            Cancel Receipt?
          </Typography>
          <Button variant="primary" outline classNames="w-24" onClick={handleToggleFormModal}>
            Yes
          </Button>
        </div>
        <Typography size="sm" className="text-custom-gray">
          By clicking 'yes', you need to provide a brief remark explaining the reason for the cancellation
        </Typography>
      </div>

      {/* Cancel Modal */}
      <CancelPRModal
        isFormOpen={isFormOpen}
        handleToggleFormModal={handleToggleFormModal}
        prDate={userPadSeriesDetails?.data ? userPadSeriesDetails?.data.padAssignment.formTransmittal.createdAt : ""}
        files={files}
        handleFile={handleFile}
        handleClear={handleClear}
        isAllowedSize={isAllowedSize}
        imagePreview={imagePreview}
        formik={cancelledFormik}
      />

      {/* PR number */}
      <div className="flex items-center gap-2 mt-10">
        <Typography size="md">PR:</Typography>
        <Typography size="3xl" className="text-red-600">
          {userPadSeriesDetails?.data?.id}
        </Typography>
      </div>

      {/* PR Content */}
      <div className="w-full border-t-2 border-dashed border-gray-400 my-4" />
      <div className="flex flex-col gap-10">
        <form onSubmit={formik.handleSubmit}>
          {/* Assignee Details Section */}
          <div className="">
            <Typography className="mt-6 divider divider-start font-semibold">Assignee Details</Typography>
            <div className="grid grid-cols-3 gap-4">
              <div className=" flex flex-col gap-2">
                <span className="text-sm text-grey-500">Issued By</span>
                <TextField name="issuedBy" size="sm" placeholder={"Issued By"} value={`${user.firstname} ${user.lastname}`} onChange={() => {}} disabled />
              </div>

              <div className=" flex flex-col gap-2">
                <span className="text-sm text-grey-500">Remit To</span>
                <Combobox
                  suggestionOptions={users ? users : []}
                  optionLabel={(u) => `${u.firstname} ${u.lastname}`}
                  optionValue={(u) => u.id}
                  placeholder="Select User"
                  onInputChange={handleSearchUser}
                  setData={(u) => {
                    formik.setFieldValue("remitTo", u.id);
                  }}
                  onClear={() => {
                    formik.setFieldValue("remitTo", 0);
                  }}
                />
                {formik.touched.remitTo && formik.errors.remitTo && <span className="text-red-500">{formik.errors.remitTo}</span>}
              </div>

              <div className=" flex flex-col gap-2">
                <span className="text-sm text-grey-500">Date Released</span>
                <TextField name="dateReleased" size="sm" placeholder={"Date Released"} value={dateNow()} onChange={() => {}} disabled />
              </div>
            </div>
          </div>

          {/* PR Details Section*/}
          <div className="">
            <Typography className="mt-6 divider divider-start font-semibold">PR Details</Typography>
            <div className="grid grid-cols-3 gap-4">
              <div className=" flex flex-col gap-2">
                <span className="text-sm text-grey-500">Division</span>
                <TextField
                  name="division"
                  size="sm"
                  placeholder={"Division"}
                  value={userPadSeriesDetails?.data ? userPadSeriesDetails?.data.padAssignment.form.division.divisionName : ""}
                  onChange={() => {}}
                  disabled
                />
              </div>

              <div className=" flex flex-col gap-2">
                <span className="text-sm text-grey-500">Type</span>
                <TextField
                  name="formType"
                  size="sm"
                  placeholder={"Form Type"}
                  value={userPadSeriesDetails?.data ? userPadSeriesDetails?.data.padAssignment.form.formType.formTypeName : ""}
                  onChange={() => {}}
                  disabled
                />
              </div>

              <div className=" flex flex-col gap-2">
                <span className="text-sm text-grey-500">Area Released</span>
                <TextField
                  name="area"
                  size="sm"
                  placeholder={"Area Released"}
                  value={userPadSeriesDetails?.data ? userPadSeriesDetails?.data.padAssignment.form.area.areaName : ""}
                  onChange={() => {}}
                  disabled
                />
              </div>

              <div className=" flex flex-col gap-2">
                <span className="text-sm text-grey-500">Transmittal No.</span>
                <TextField
                  name="transmittalNumber"
                  size="sm"
                  placeholder={"Transmittal No."}
                  value={userPadSeriesDetails?.data ? userPadSeriesDetails?.data.padAssignment.formTransmittal.transmittalNumber : ""}
                  onChange={() => {}}
                  disabled
                />
              </div>

              <div className=" flex flex-col gap-2">
                <span className="text-sm text-grey-500">ATP No.</span>
                <TextField
                  name="issuedBy"
                  size="sm"
                  placeholder={"ATP No."}
                  value={userPadSeriesDetails?.data ? userPadSeriesDetails?.data.padAssignment.form.atpNumber : ""}
                  onChange={() => {}}
                  disabled
                />
              </div>
            </div>
          </div>

          {/* Coop Details Section */}
          <div className="">
            <Typography className="mt-6 divider divider-start font-semibold">Coop Details</Typography>
            <div className="grid grid-cols-3 gap-4">
              <div className="flex flex-col gap-2">
                <span className="text-sm text-grey-500">Coop No.</span>
                <TextField name="cooperativeId" size="sm" placeholder={"Coop No."} value={formik.values.cooperativeId} disabled />
              </div>

              <div className="flex flex-col gap-2">
                <span className="text-sm text-grey-500">Coop Name</span>
                <Combobox
                  suggestionOptions={cooperatives ? cooperatives : []}
                  optionLabel={(c) => `${c.coopName}`}
                  optionValue={(c) => c.id}
                  placeholder="Select Coop"
                  onInputChange={handleSearchCoop}
                  setData={(c) => {
                    formik.setFieldValue("cooperativeId", c.id);
                    formik.setFieldValue("paymentDetail.coopName", c.coopName);
                  }}
                  onClear={() => {
                    formik.setFieldValue("cooperativeId", 0);
                    formik.setFieldValue("paymentDetail.coopName", "");
                  }}
                />

                {formik.touched.cooperativeId && formik.errors.cooperativeId && <span className="text-red-500">{formik.errors.cooperativeId}</span>}
              </div>

              <div className="flex flex-col gap-2">
                <span className="text-sm text-grey-500">Product Name</span>
                <Combobox
                  suggestionOptions={products?.data !== undefined ? products?.data.data : []}
                  optionLabel={(p) => `${p.name}`}
                  optionValue={(p) => p.id}
                  placeholder="Select Product"
                  onInputChange={handleSearchProduct}
                  setData={(p) => {
                    formik.setFieldValue("productId", p.id);
                  }}
                  onClear={() => {
                    formik.setFieldValue("productId", 0);
                  }}
                />
                {formik.touched.productId && formik.errors.productId && <span className="text-red-500">{formik.errors.productId}</span>}
              </div>

              <div className="flex flex-col gap-2">
                <span className="text-sm text-grey-500">PR Date</span>
                <TextField
                  name="prDate"
                  size="sm"
                  placeholder={"PR Date"}
                  value={userPadSeriesDetails?.data ? formatWordDateDDMMYYY(userPadSeriesDetails?.data.padAssignment.formTransmittal.createdAt, true) : ""}
                  onChange={() => {}}
                  disabled
                />
              </div>

              <div className="flex flex-col gap-2">
                <span className="text-sm text-grey-500">Date Created</span>
                <TextField name="releasedAt" type="date" size="sm" placeholder={"Date Created"} value={formik.values.releasedAt} onChange={formik.handleChange} />
                {formik.touched.releasedAt && formik.errors.releasedAt && <span className="text-red-500">{formik.errors.releasedAt}</span>}
              </div>
            </div>
          </div>

          {/* Payment Details Section */}
          <div className="">
            <Typography className="mt-6 divider divider-start font-semibold">Payment Details</Typography>
            <div className="grid grid-cols-3 gap-6">
              <div className=" flex flex-col gap-2">
                <span className="text-sm text-grey-500">Mode of Payment</span>
                <Select
                  size="sm"
                  name="paymentDetail.paymentMethodId"
                  options={paymentOptions}
                  value={formik.values.paymentDetail.paymentMethodId}
                  onChange={(e) => {
                    formik.setFieldValue("paymentDetail.paymentMethodId", parseInt(e.target.value));
                    clearPaymentDetailsForm();
                  }}
                />
                {formik.touched.paymentDetail?.paymentMethodId && formik.errors.paymentDetail?.paymentMethodId && <span className="text-red-500">{formik.errors.paymentDetail.paymentMethodId}</span>}
              </div>

              {checkPaymentMethod([ModeOfpayment.CASH, ModeOfpayment.CHEQUE, ModeOfpayment.DEPOSIT_SLIP, ModeOfpayment.COOP_SAVINGS]) && (
                <div className="flex flex-col gap-2">
                  <span className="text-sm text-grey-500">Amount</span>
                  <TextField
                    name="paymentDetail.amount"
                    type="text"
                    size="sm"
                    placeholder={"Amount"}
                    value={formatNumberWithCommas(formik.values.paymentDetail.amount || 0)}
                    onChange={(e) => {
                      // user input
                      const raw = e.target.value;

                      // parsed formatted value
                      const parsed = parseFormattedNumber(raw);

                      // set value to formik
                      formik.setFieldValue("paymentDetail.amount", parsed);
                    }}
                  />
                  {formik.touched.paymentDetail?.amount && formik.errors.paymentDetail?.amount && <span className="text-red-500">{formik.errors.paymentDetail.amount}</span>}
                </div>
              )}

              {checkPaymentMethod([ModeOfpayment.CASH, ModeOfpayment.CHEQUE, ModeOfpayment.DEPOSIT_SLIP, ModeOfpayment.COOP_SAVINGS]) && (
                <>
                  <div className="flex flex-col gap-2">
                    <span className="text-sm text-grey-500">Date Deposited</span>
                    <TextField name="paymentDetail.dateDeposit" type="date" size="sm" placeholder={"Date Deposited"} value={formik.values.paymentDetail.dateDeposit} onChange={formik.handleChange} />
                    {formik.touched.paymentDetail?.dateDeposit && formik.errors.paymentDetail?.dateDeposit && <span className="text-red-500">{formik.errors.paymentDetail.dateDeposit}</span>}
                  </div>
                </>
              )}

              {checkPaymentMethod([ModeOfpayment.CASH, ModeOfpayment.CHEQUE, ModeOfpayment.DEPOSIT_SLIP]) && (
                <div className="flex flex-col gap-2">
                  <span className="text-sm text-grey-500">Bank</span>
                  <Select
                    size="sm"
                    name=""
                    options={bankOptions}
                    value={selectedBankAccountId}
                    onChange={(e) => {
                      setSelectedBankAccountId(parseInt(e.target.value));

                      const selectedAccount = bankAccounts.find((account) => account.id === parseInt(e.target.value));

                      formik.setFieldValue("paymentDetail.bankAccountId", parseInt(e.target.value));
                      formik.setFieldValue("paymentDetail.accountNumber", selectedAccount?.bankAccountNumber);
                    }}
                  />
                  {formik.touched.paymentDetail?.bankAccountId && formik.errors.paymentDetail?.bankAccountId && <span className="text-red-500">{formik.errors.paymentDetail.bankAccountId}</span>}
                </div>
              )}

              {checkPaymentMethod([ModeOfpayment.CASH, ModeOfpayment.CHEQUE, ModeOfpayment.DEPOSIT_SLIP, ModeOfpayment.COOP_SAVINGS]) && (
                <div className="flex flex-col gap-2">
                  <span className="text-sm text-grey-500">Account Number</span>
                  <TextField
                    name="paymentDetail.accountNumber"
                    size="sm"
                    placeholder={"Account Number"}
                    value={formik.values.paymentDetail.accountNumber}
                    onChange={formik.handleChange}
                    disabled={!checkPaymentMethod([ModeOfpayment.COOP_SAVINGS])}
                  />
                  {formik.touched.paymentDetail?.accountNumber && formik.errors.paymentDetail?.accountNumber && <span className="text-red-500">{formik.errors.paymentDetail.accountNumber}</span>}
                </div>
              )}

              {checkPaymentMethod([ModeOfpayment.COOP_SAVINGS]) && (
                <div className="flex flex-col gap-2">
                  <span className="text-sm text-grey-500">Coop Name</span>
                  <TextField name="paymentDetail.coopName" size="sm" value={formik.values.paymentDetail.coopName} onChange={formik.handleChange} disabled />
                  {formik.touched.paymentDetail?.coopName && formik.errors.paymentDetail?.coopName && <span className="text-red-500">{formik.errors.paymentDetail.coopName}</span>}
                </div>
              )}

              {checkPaymentMethod([ModeOfpayment.CASH, ModeOfpayment.DEPOSIT_SLIP, ModeOfpayment.COOP_SAVINGS]) && (
                <div className="flex flex-col gap-2">
                  <span className="text-sm text-grey-500">Remarks</span>
                  <TextField name="paymentDetail.remarks" size="sm" placeholder={"Remarks"} value={formik.values.paymentDetail.remarks} onChange={formik.handleChange} />
                  {formik.touched.paymentDetail?.remarks && formik.errors.paymentDetail?.remarks && <span className="text-red-500">{formik.errors.paymentDetail.remarks}</span>}
                </div>
              )}

              {checkPaymentMethod([ModeOfpayment.CHEQUE]) && (
                <div className="flex flex-col gap-2">
                  <span className="text-sm text-grey-500">Cheque Number</span>
                  <TextField name="paymentDetail.chequeNumber" size="sm" placeholder={"Cheque Number"} value={formik.values.paymentDetail.chequeNumber} onChange={formik.handleChange} />
                  {formik.touched.paymentDetail?.chequeNumber && formik.errors.paymentDetail?.chequeNumber && <span className="text-red-500">{formik.errors.paymentDetail.chequeNumber}</span>}
                </div>
              )}
            </div>
          </div>

          <div className="mt-6">
            <div className="flex flex-col gap-2">
              <span className="text-sm text-grey-500">Please scan or upload deposit slip:</span>

              {files.length !== 0 && files.length < 2 ? (
                imagePreview ? (
                  <div className="flex items-center gap-2">
                    <img src={imagePreview as string} alt="Image Preview" width={200} />
                    <Button variant="danger" classNames="rounded-full" onClick={handleClear}>
                      <MdDeleteForever />
                    </Button>
                  </div>
                ) : (
                  <div className="">
                    {files.map((file) => (
                      <div key={file.name} className="flex items-center gap-2">
                        <AttachmentItem filename={file.name} mimeType={file.type} size={file.size} isForViewing={true} />
                        <Button variant="danger" classNames="rounded-full" onClick={handleClear}>
                          <MdDeleteForever />
                        </Button>
                      </div>
                    ))}
                  </div>
                )
              ) : (
                <>
                  <div className="bg-sky-100 border border-dashed rounded cursor-pointer">
                    <FileDropzone height={150} setFiles={handleFile}>
                      <div className="flex flex-1 flex-col items-center">
                        <FaCloudArrowUp size={30} className="mb-4" color="sky-500" />
                        <Typography size="xs">Drop files here or Click to upload</Typography>
                        <Typography className="text-slate-400">Supported formats: JPEG, PNG, PDF</Typography>
                      </div>
                    </FileDropzone>
                  </div>
                  {formik.touched.attachments && formik.errors.attachments && (
                    <span className="text-red-500">
                      {typeof formik.errors.attachments === "string"
                        ? formik.errors.attachments
                        : Array.isArray(formik.errors.attachments)
                          ? formik.errors.attachments.map((error, index) => {
                              if (typeof error === "object" && error !== null && "file" in error) {
                                return <div key={index}>{error.file}</div>;
                              }
                              return null;
                            })
                          : null}
                    </span>
                  )}
                </>
              )}

              {files.length >= 2 && (
                <Typography size="sm" className="text-center text-danger">
                  Minimum of 1 image or file only
                </Typography>
              )}

              {!isAllowedSize && (
                <Typography size="sm" className="text-center text-danger">
                  Image size exceeds 20MB. Please select 20MB below file.
                </Typography>
              )}
            </div>
          </div>

          <div className="flex items-center justify-center mt-6">
            <div className="w-1/4">
              <Button variant="primary" classNames="w-full" type="submit">
                Continue
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default IssuePRForm;
