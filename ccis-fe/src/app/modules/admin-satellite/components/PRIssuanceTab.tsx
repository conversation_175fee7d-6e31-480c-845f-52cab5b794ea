import React, { useEffect, useState } from "react";
import Table from "@components/common/Table";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { IActions } from "@interface/common.interface";
import { GoVersions } from "react-icons/go";
import { useNavigate } from "react-router-dom";
import { ROUTES } from "@constants/routes";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { IPadAssignments } from "@interface/form-inventory.interface";
import { useDebouncedSearch } from "@hooks/useDebouncedSearch";
import { useSelectOptions } from "@hooks/useSelectOptions";
import { createSelectChangeHandler } from "@helpers/handlers";
import { useFetchWithParams } from "@hooks/useFetchWithParams";
import TableFilter from "./table/filter/TableFilter";
import { getColumns } from "./table/column/pr-issuance-column";
import { clearData, hasKey, saveData } from "@helpers/storage";

const PRIssuanceTab: React.FC = () => {
  const navigate = useNavigate();

  // States
  const [resetCounter, setResetCounter] = useState(0);
  const [divisionFilter, setDivisionFilter] = useState<number>(0);
  const [type, setType] = useState<number>(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Global State
  // for Select options
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const types = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const { userPadAssignment } = useSelector((state: RootState) => state.formInventoryTransmittal);

  // Actions
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getUserPadAssignment } = useTransmittalFormActions();

  // Custom Hook
  const { value: searchText, handleChange: handleSearch, setValue: setSearchText } = useDebouncedSearch();

  const divisionOptions = useSelectOptions({
    data: divisions,
    firstOptionText: "Select Division",
    valueKey: "id",
    textKey: "divisionName",
  });

  const typeOptions = useSelectOptions({
    data: types,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: "formTypeName",
  });

  useFetchWithParams([getDivisions, getFormTypes], { filter: "" }, [], false);

  useFetchWithParams(
    getUserPadAssignment,
    {
      page,
      pageSize,
      filter: searchText,
      divisionFilter,
      formtypeFilter: type,
    },
    [searchText, divisionFilter, type, page, pageSize],
    false
  );

  // Select handlers
  const handleDivisionChange = createSelectChangeHandler(setDivisionFilter);
  const handleTypeChange = createSelectChangeHandler(setType);

  const handleClearAll = () => {
    setSearchText("");
    setDivisionFilter(0);
    setType(0);
    setResetCounter((prev) => prev + 1);
  };

  const getActionEvents = (row: IPadAssignments): IActions<any>[] => {
    const actions: IActions<any>[] = [
      {
        name: "View",
        event: async () => {
          await saveData("prTableId", row.id);
          navigate(ROUTES.ADMINSATELLITE.viewPrTable.parse(row.id.toString()));
        },
        icon: GoVersions,
        color: "primary",
      },
    ];
    return actions;
  };

  const columns = getColumns({ getActionEvents });

  useEffect(() => {
    if (hasKey("prTableId")) {
      clearData("prTableId");
    }
  }, []);

  return (
    <div className="p-4">
      <div className="mt-8">
        <div className="flex flex-row justify-between">
          <TableFilter
            divisionFilter={divisionFilter}
            divisionOptions={divisionOptions}
            handleClearAll={handleClearAll}
            handleDivisionChange={handleDivisionChange}
            searchText={searchText}
            handleSearch={handleSearch}
            handleTypeChange={handleTypeChange}
            resetCounter={resetCounter}
            type={type}
            typeOptions={typeOptions}
          />
        </div>
        <Table
          className="!min-h-[100%] h-[500px] border-[1px] border-zinc-300 mt-8"
          columns={columns}
          loading={userPadAssignment.tableData.loading}
          data={userPadAssignment.tableData.data || []}
          paginationServer={true}
          paginationTotalRows={userPadAssignment.tableData.data?.meta?.total || [].length}
          onChangeRowsPerPage={(newPageSize, newPage) => {
            setPageSize(newPageSize);
            setPage(newPage);
          }}
          onPaginate={(newPage) => setPage(newPage)}
        />
      </div>
    </div>
  );
};

export default PRIssuanceTab;
