import Modal from "@components/common/Modal";
import Typography from "@components/common/Typography";
import TextField from "@components/form/TextField";
import { formatWordDateDDMMYYY } from "@helpers/date";
import { IPadAssignments } from "@interface/form-inventory.interface";
import { useRef, useState } from "react";
import { IoChevronBackOutline, IoChevronForwardOutline } from "react-icons/io5";
import AttachmentItem from "../AttachmentItem";
import { IAttachment } from "@interface/products.interface";
import { FormStatus } from "@enums/form-status";

type ViewPadDetailsModalProps = {
  isViewOpen: boolean;
  isReturned?: boolean;
  handleToggleViewModal: () => void;
  padAssignment?: IPadAssignments;
  returnedPads?: IPadAssignments;
};

const ViewPadDetailsModal = ({ isViewOpen, isReturned, handleToggleViewModal, padAssignment, returnedPads }: ViewPadDetailsModalProps) => {
  const [seriesNumber, setSeriesNumber] = useState(1);
  const [isEditing, setIsEditing] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const activePadData = isReturned ? returnedPads : padAssignment;

  // Get the current pad series details
  const currentSeries = activePadData?.padSeriesDetails ? activePadData.padSeriesDetails[seriesNumber - 1] : null;
  // Check if the current series is cancelled
  const isCancelled = currentSeries?.status === FormStatus.CANCELLED;

  const handleClick = () => {
    setIsEditing(true);
    // Delay to ensure state updates before focus
    setTimeout(() => {
      inputRef.current?.focus();
    }, 0);
  };

  const handleBlur = (e: React.FormEvent<HTMLDivElement>) => {
    const target = e.currentTarget as HTMLDivElement;
    if (typeof target.innerText !== "number") {
      if (
        isNaN(parseInt(target.innerText)) ||
        parseInt(target.innerText) < 1 ||
        parseInt(target.innerText) > 50 ||
        (activePadData?.padSeriesDetails && activePadData?.padSeriesDetails.length < seriesNumber)
      ) {
        // Revert to a default or last valid number
        setSeriesNumber(1);
      }
    } else {
      setSeriesNumber(1);
    }

    setIsEditing(false);
  };

  const handleChange = (e: React.FormEvent<HTMLDivElement>) => {
    const target = e.currentTarget as HTMLDivElement;

    if (/^\d+$/.test(target.innerText)) {
      const series = parseInt(target.innerText, 10);

      // Check range
      if (series > 1 || series < 50) {
        setSeriesNumber(series);
      }
    }
  };

  const handleBack = () => {
    if (seriesNumber !== 1) {
      setSeriesNumber((prev) => prev - 1);
    }
  };

  const handleForward = () => {
    if (activePadData?.padSeriesDetails && activePadData?.padSeriesDetails.length > seriesNumber && seriesNumber !== 50) {
      setSeriesNumber((prev) => prev + 1);
    }
  };

  const dateNow = () => {
    const now = new Date();
    return formatWordDateDDMMYYY(now.toISOString(), true);
  };

  return (
    <Modal title="View Pad Details" modalContainerClassName="max-w-6xl" titleClass="text-primary text-lg uppercase" isOpen={isViewOpen} onClose={handleToggleViewModal}>
      <div className=" w-full">
        <div className="w-full flex flex-col justify-end">
          {/* PR Number */}
          <div className="flex items-center justify-end">
            <span className="mr-1">PR NO: </span>
            <Typography size="3xl" className="text-red-500 font-poppins-semibold">
              {currentSeries?.seriesNo || ""}
            </Typography>
          </div>

          {/* Show cancelled view if the series is cancelled */}
          {isCancelled ? (
            <div className="mt-10 px-10">
              <div>
                <div className="flex gap-1">
                  <Typography size="2xl" className="font-poppins-medium">
                    Cancelled PR
                  </Typography>
                </div>

                {/* Cancel Details */}
                <div className="mt-10">
                  {/* PR Date */}
                  <div className="grid grid-cols-3 gap-4 w-1/3">
                    <div className="col-span-1">
                      <div className="flex items-center justify-between">
                        <div className="">
                          <Typography size="lg" className="font-poppins-medium">
                            PR
                          </Typography>
                        </div>
                        <div className="">:</div>
                      </div>
                    </div>
                    <div className="col-span-2">{activePadData?.formTransmittal?.createdAt ? formatWordDateDDMMYYY(activePadData.formTransmittal.createdAt, true) : ""}</div>
                  </div>

                  {/* Cancel Date */}
                  <div className="grid grid-cols-3 gap-4 w-1/3 mt-4">
                    <div className="col-span-1">
                      <div className="flex items-center justify-between">
                        <div className="">Cancel Date</div>
                        <div className="">:</div>
                      </div>
                    </div>
                    <div className="col-span-2">{currentSeries?.cancelledAt ? formatWordDateDDMMYYY(currentSeries.cancelledAt, true) : ""}</div>
                  </div>

                  {/* Remarks */}
                  <div className="grid grid-cols-3 gap-4 w-1/3 mt-4">
                    <div className="col-span-1">
                      <div className="flex items-center justify-between">
                        <div className="">Remarks</div>
                        <div className="">:</div>
                      </div>
                    </div>
                    <div className="col-span-2">{currentSeries?.remarks}</div>
                  </div>

                  {/* Attachments */}
                  <div className="mt-8 gap-4 flex justify-center">
                    <div className="w-full flex flex-col gap-10">
                      <div className="flex w-full flex-col">
                        <div className="divider divider-start font-semibold">ATTACHMENTS</div>
                      </div>
                      {/* First row */}
                      <div className="grid grid-cols-4 gap-4">
                        {currentSeries?.attachments &&
                          (currentSeries.attachments as IAttachment[]).map((attachment: IAttachment, index: number) => {
                            return (
                              <AttachmentItem
                                key={index}
                                filename={attachment?.filename ? attachment.filename : ""}
                                filepath={attachment?.filepath ? attachment.filepath : ""}
                                mimeType={attachment?.mimeType ? attachment.mimeType : ""}
                                size={attachment?.size ? attachment.size : 0}
                              />
                            );
                          })}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className=" px-10">
              {/* Normal view content (your existing code) */}
              {/* Assignee Details */}
              <div className="mt-8 gap-4 flex justify-center">
                <div className="w-full flex flex-col gap-10">
                  <div className="flex w-full flex-col">
                    <div className="divider divider-start font-semibold">RETURN DETAILS</div>
                  </div>
                  {/* First row */}
                  <div className="grid grid-cols-4 gap-4">
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">Issue By</span>
                      <Typography size="sm" className=" font-poppins-semibold">
                        {activePadData?.createdBy?.firstname} {activePadData?.createdBy?.lastname}
                      </Typography>
                    </div>
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">Remit To</span>
                      <Typography size="sm" className=" font-poppins-semibold">
                        Sarah Ybanez
                      </Typography>
                    </div>
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">Release To</span>
                      <Typography size="sm" className=" font-poppins-semibold">
                        {dateNow()}
                      </Typography>
                    </div>
                  </div>
                </div>
              </div>

              {/* PR Details */}
              <div className="mt-8 gap-4 flex justify-center">
                <div className="w-full flex flex-col gap-10">
                  <div className="flex w-full flex-col">
                    <div className="divider divider-start font-semibold">PR DETAILS</div>
                  </div>
                  {/* First row */}
                  <div className="grid grid-cols-4 gap-4">
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">Division</span>
                      <Typography size="sm" className=" font-poppins-semibold">
                        {activePadData?.form?.division?.divisionName}
                      </Typography>
                    </div>
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">Type</span>
                      PR
                    </div>
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">Area Released</span>
                      <Typography size="sm" className=" font-poppins-semibold">
                        {/* {activePadData?.form?.area.areaName || ""} */}
                      </Typography>
                    </div>
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">Transmittal No.</span>
                      <Typography size="sm" className=" font-poppins-semibold">
                        {activePadData?.formTransmittal?.transmittalNumber}
                      </Typography>
                    </div>
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">ATP No.</span>
                      <Typography size="sm" className=" font-poppins-semibold">
                        {activePadData?.form?.atpNumber}
                      </Typography>
                    </div>
                  </div>
                </div>
              </div>

              {/* PR Details */}
              <div className="mt-8 gap-4 flex justify-center">
                <div className="w-full flex flex-col gap-10">
                  <div className="flex w-full flex-col">
                    <div className="divider divider-start font-semibold">COOP DETAILS</div>
                  </div>
                  {/* First row */}
                  <div className="grid grid-cols-4 gap-4">
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">Coop No.</span>
                      <Typography size="sm" className=" font-poppins-semibold">
                        {currentSeries?.cooperative?.coopCode || ""}
                      </Typography>
                    </div>
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">Coop Name</span>
                      <Typography size="sm" className=" font-poppins-semibold">
                        {currentSeries?.cooperative?.coopName || ""}
                      </Typography>
                    </div>
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">Coop Branch</span>
                      <Typography size="sm" className=" font-poppins-semibold">
                        Main Branch
                      </Typography>
                    </div>
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">Product Name</span>
                      <Typography size="sm" className=" font-poppins-semibold">
                        {currentSeries?.product?.productCode || ""}
                      </Typography>
                    </div>
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">PR Date</span>
                      <Typography size="sm" className=" font-poppins-semibold">
                        {currentSeries?.createdAt || ""}
                      </Typography>
                    </div>
                    <div className="flex flex-col gap-2">
                      <span className="text-sm text-grey-500">Date Create</span>
                      <Typography size="sm" className=" font-poppins-semibold">
                        {currentSeries?.createdAt || ""}
                      </Typography>
                    </div>
                  </div>
                </div>
              </div>

              {/* Attachment Details */}
              <div className="mt-8 gap-4 flex justify-center">
                <div className="w-full flex flex-col gap-10">
                  <div className="flex w-full flex-col">
                    <div className="divider divider-start font-semibold">ATTACHMENTS</div>
                  </div>
                  {/* First row */}
                  <div className="grid grid-cols-4 gap-4">
                    {currentSeries?.attachments &&
                      (currentSeries.attachments as IAttachment[]).map((attachment: IAttachment, index: number) => {
                        return (
                          <AttachmentItem
                            key={index}
                            filename={attachment?.filename ? attachment.filename : ""}
                            filepath={attachment?.filepath ? attachment.filepath : ""}
                            mimeType={attachment?.mimeType ? attachment.mimeType : ""}
                            size={attachment?.size ? attachment.size : 0}
                          />
                        );
                      })}
                  </div>
                </div>
              </div>

              {/* Official Receipt Details */}
              <div className="mt-8 gap-4 flex justify-center">
                <div className="w-full flex flex-col gap-10">
                  <div className="flex w-full flex-col">
                    <div className="font-semibold">OFFICIAL RECEIPT(s) DETAILS</div>
                  </div>
                  {/* First row */}
                  <div className="grid grid-cols-4 gap-4">
                    <div className="flex flex-col gap-2 border border-[#01081C26] rounded py-5 px-4 col-span-4 shadow-lg">
                      <div className=" flex flex-col gap-2 w-1/3">
                        <span className="text-sm text-grey-500">Issued Official Receipt(s)</span>
                        <TextField size="sm" disabled placeholder="123451323" />
                        <div>
                          <span className="text-[#60a5fa] text-sm underline cursor-pointer">View Details</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Pagination */}
          <div className="w-full mt-8 flex justify-end">
            <div className="flex">
              <div onClick={handleBack} className="border border-[#a3a3a3] rounded-l-lg py-2 px-2 flex items-center justify-center cursor-pointer">
                <IoChevronBackOutline />
              </div>
              <div
                className="tooltip border-t border-b border-[#a3a3a3] py-2 px-4 flex items-center justify-center cursor-pointer"
                data-tip="Go to specific PR(1–50)"
                contentEditable={isEditing}
                suppressContentEditableWarning={true}
                onClick={handleClick}
                onBlur={handleBlur}
                onInput={handleChange}>
                {seriesNumber}
              </div>
              <div onClick={handleForward} className="border rounded-r-lg py-2 px-2 border-[#a3a3a3] flex items-center justify-center cursor-pointer">
                <IoChevronForwardOutline />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default ViewPadDetailsModal;
