import Modal from "@components/common/Modal";
import TableFilter, { TableFilterProps } from "../table/filter/TableFilter";
import Table from "@components/common/Table";
import { IPadAssignments } from "@interface/form-inventory.interface";
import { TableColumn } from "react-data-table-component";
import Button from "@components/common/Button";

type TableProps = {
  data: IPadAssignments[];
  columns: TableColumn<IPadAssignments>[];
  loading: boolean;
  totalCount: number;
  onChangeRowsPerPage: (newPerPage: number, page: number) => void;
  onPaginate: (page: number) => void;
};

type CompletedModalProps = TableFilterProps &
  TableProps & {
    isFormOpen: boolean;
    handleToggleFormModal: () => void;
    handleAddCompletedPads: () => void;
  };

const CompletedPadsModal = ({
  isFormOpen,
  handleToggleFormModal,
  handleAddCompletedPads,
  searchText,
  handleSearch,
  handleClearAll,
  resetCounter,
  divisionOptions,
  divisionFilter,
  typeOptions,
  handleDivisionChange,
  type,
  handleTypeChange,
  areaOptions,
  areaFilter,
  handleAreaChange,
  dateFrom,
  handleDateFromChange,
  dateTo,
  handleDateToChange,
  data,
  columns,
  loading,
  totalCount,
  onChangeRowsPerPage,
  onPaginate,
}: CompletedModalProps) => {
  return (
    <Modal title="Add New" modalContainerClassName="max-w-4xl" titleClass="text-primary text-lg uppercase" isOpen={isFormOpen} onClose={handleToggleFormModal}>
      <div className="flex flex-row justify-end">
        <TableFilter
          divisionFilter={divisionFilter}
          divisionOptions={divisionOptions}
          handleClearAll={handleClearAll}
          handleDivisionChange={handleDivisionChange}
          searchText={searchText}
          handleSearch={handleSearch}
          handleTypeChange={handleTypeChange}
          areaFilter={areaFilter}
          areaOptions={areaOptions}
          handleAreaChange={handleAreaChange}
          resetCounter={resetCounter}
          type={type}
          typeOptions={typeOptions}
          dateFrom={dateFrom}
          handleDateFromChange={handleDateFromChange}
          dateTo={dateTo}
          handleDateToChange={handleDateToChange}
        />
      </div>
      <Table
        className="!min-h-[100%] h-[500px] border-[1px] border-zinc-300 mt-2"
        columns={columns}
        data={data}
        searchable={false}
        multiSelect={false}
        selectable={false}
        paginationTotalRows={totalCount}
        paginationServer={true}
        loading={loading}
        onChangeRowsPerPage={onChangeRowsPerPage}
        onPaginate={onPaginate}
      />
      <div className="flex items-center justify-center w-full">
        <div className="w-[550px] flex gap-2">
          <Button variant="secondary" classNames="w-full" onClick={handleToggleFormModal}>
            Cancel
          </Button>
          <Button variant="primary" classNames="w-full bg-[#4385F5] hover:bg-[#699df7]" onClick={handleAddCompletedPads}>
            Add
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default CompletedPadsModal;
