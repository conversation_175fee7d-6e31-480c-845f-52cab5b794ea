import { TableColumn } from "react-data-table-component";
import Table from "@components/common/Table";
import { IFormTransmittal } from "@interface/form-inventory.interface";

interface TransmittalTableProps {
  data: IFormTransmittal[];
  columns: TableColumn<IFormTransmittal>[];
  loading: boolean;
  totalCount: number;
  onChangeRowsPerPage: (newPerPage: number, page: number) => void;
  onPaginate: (page: number) => void;
}

const TransmittalTable = ({ data, columns, loading, totalCount, onChangeRowsPerPage, onPaginate }: TransmittalTableProps) => {
  return (
    <div>
      <Table
        className="!min-h-[100%] h-[500px] border-[1px] border-zinc-300 mt-8"
        columns={columns}
        data={data}
        searchable={false}
        multiSelect={false}
        paginationTotalRows={totalCount}
        paginationServer={true}
        loading={loading}
        onChangeRowsPerPage={onChangeRowsPerPage}
        onPaginate={onPaginate}
      />
    </div>
  );
};

export default TransmittalTable;
