import { FC } from "react";
import { RouteItem } from "../core/interface/routes.interface";
import { Route, Routes } from "react-router-dom";
import { renderRoutesWithLayout } from "@helpers/permissions";
import { adminRoutes } from "../core/services/routes/admin-routes";
import { authRoutes } from "../core/services/routes/auth-routes";
import AuthLayout from "../core/layouts/AuthLayout";
import SidebarLayout from "../core/layouts/SidebarLayout";
import { userRoutes } from "@services/routes/user-routes";
import UsersLayout from "@layouts/UsersLayout";
import { uatUserRoutes } from "@services/routes/uat-users-routes";
import UATGuard from "@layouts/UatGuard";
import UatSidebarLayout from "@layouts/UatSidebarLayout";
import { uatAdminRoutes } from "@services/routes/uat-admin-routes";
import NotFound from "./shared/status/404";
import { marketingRoutes } from "@services/routes/marketing-routes";
import { actuaryRoutes } from "@services/routes/actuary-routes";
import { cashierRoutes } from "@services/routes/cashier-routes";
import { salesRoutes } from "@services/routes/sales-routes";
import { treasuryRoutes } from "@services/routes/treasury-routes";
import { salesExecutiveAssistantRoutes } from "@services/routes/sales-executive-assistant-routes";
import { IncomingCashierRoutes } from "@services/routes/incoming-cashier";
import { outgoingCashierRoutes } from "@services/routes/outgoing-cashier";
import { outgoingAdminRoutes } from "@services/routes/outgoing-admin";
import { chiefCashierRoutes } from "@services/routes/chief-cashier";
import { clifsaAdminRoutes } from "@services/routes/clifsa-admin";
import { areaAdminRoutes } from "@services/routes/area-admin-route";
import AreaAdminLayout from "@layouts/AreaAdminLayout";
import ClifsaAdminLayout from "@layouts/ClifsaAdminLayout";
import { gamRoutes } from "@services/routes/gam";
import { adminSatelliteRoutes } from "@services/routes/admin-satellite";
import { incomingOutgoingCashierRoutes } from "@services/routes/incoming-outgoing-cashier";
import { complianceRoutes } from "@services/routes/compliance-routes";
import ComplianceLayout from "@layouts/compliance/ComplianceLayout";
import { underwritingRoutes } from "@services/routes/underwriting-routes";
import { UnderwritingLayout } from "@layouts/UnderwritingLayouts/UnderwritingLayout";
import { claimsRoutes } from "@services/routes/claims-routes";
import { ClaimsLayout } from "@layouts/ClaimLayouts/ClaimsLayout";
import { incomingAdminRoutes } from "@services/routes/incoming-admin";
import { actuaryManagerRoutes } from "@services/routes/actuary-manager-routes";
import { actuaryAnalyst1Routes } from "@services/routes/actuary-analyst1-routes";
import { actuaryAssistant1Routes } from "@services/routes/actuary-assistant1-routes";
import { avplifenonlifeRoutes } from "@services/routes/actuary-avp-life-nonlife-routes";
import { rndRoutes } from "@services/routes/research-development-officer-routes";
import { claimsManagerRoutes } from "@services/routes/claims-manager-routes";
import { vicePresidentSalesRoutes } from "@services/routes/vice-president-sales";
import { vicePresidentOperationRoutes } from "@services/routes/vice-president-operations";
import DashBoardLayout from "@layouts/DashboardLayout";
import { presidentCeoRoutes } from "@services/routes/president-ceo";
import { areaSalesManagerRoutes } from "@services/routes/area-sales-manager-routes";
import { infrastructureOfficerRoutes } from "@services/routes/infrastructure-officer-routes";
import { accountingRoutes } from "@services/routes/accounting-routes";
import { propertyCustodianRoutes } from "@services/routes/property-custodian-routes";
import { regionalSalesManagerRoutes } from "@services/routes/regional-sales-routes";
import { vpAgencyDistributionChannelManagementRoutes } from "@services/routes/vp-agency-regional-distribution-routes";
import { dataProcessingAssistantRoutes } from "@services/routes/data-processing-assistant-routes";
import { dataProcessingAssistant1Routes } from "@services/routes/data-processing-assistant1-routes";
import { maintenanceRoutes } from "@services/routes/maintenance-routes";
import { frontEndProgrammerRoutes } from "@services/routes/frontend-programmer-routes";
import { administrativeAssistantRoutes } from "@services/routes/administrative-assistant-routes";
import { managerAgencyDistributionChannelRoutes } from "@services/routes/manager-agency-distribution-channel-routes";
import { avpAdminCorplanRoutes } from "@services/routes/avp-admin-corplan-routes";
import { bookkeeperRoutes } from "@services/routes/bookkeeper-routes";
import { adminSpecialistRoutes } from "@services/routes/admin-specialist-routes";
import { claimsAssistantRoutes } from "@services/routes/claim-assistant-routes";
import { uiUxDesignerRoutes } from "@services/routes/uiux-designer-routes";
import { lifeCashierRoutes } from "@services/routes/life-cashier-routes";
import { policyIssuanceAssistantRoutes } from "@services/routes/policy-issuance-assistant-routes";
import { backEndProgrammerRoutes } from "@services/routes/backend-programmer-routes";
import { nlPropertyClaimsAssistant1Routes } from "@services/routes/nl-property-claims-assistant1-routes";
import { filingClerkRoutes } from "@services/routes/filing-clerk-routes";
import { bookkeeperNonlifeRoutes } from "@services/routes/bookkeeper-nonlife-routes";
import { underwritingStaffRoutes } from "@services/routes/underwriting-staff-routes";
import { dataEncoderRoutes } from "@services/routes/data-encoder-routes";
import { salesDevelopmentOfficerNonlifeRoutes } from "@services/routes/sales-development-officer-nonlife-routes";
import { businessDevelopmentManagerRoutes } from "@services/routes/business-development-manager-routes";
import { memberRelationsAssistant2LifeRoutes } from "@services/routes/member-relations-assistant2-life-routes";
import { assistantCashier1Routes } from "@services/routes/assistant-cashier1-routes";
import { treasuryOfficerRoutes } from "@services/routes/treasury-officer-routes";
import { vpFinanceInvestmentTreasuryComplianceRoutes } from "@services/routes/vp-finance-investment-treasury-compliance-routes";
import { claimsAssistant1LifeRoutes } from "@services/routes/claims-assistant1-life-routes";
import { adminAssistantVpFinanceRoutes } from "@services/routes/admin-assistant-vp-finance-routes";
import { businessAnalystRoutes } from "@services/routes/business-analyst-routes";
import { dataProcessingManagerRoutes } from "@services/routes/data-processing-manager-routes";
import { oicOperationsManagerNonLifeRoutes } from "@services/routes/oic-operations-manager-nonlife-routes";
import { oicSalesDevelopmentSpecialistVisayasRoutes } from "@services/routes/oic-sales-development-specialist-visayas-routes";
import { claimsProcessorRoutes } from "@services/routes/claims-processor-routes";
import { actuaryAnalystRoutes } from "@services/routes/actuary-analyst-routes";
import { technicalSupportRoutes } from "@services/routes/technical-support-routes";
import { hrAssistantRoutes } from "@services/routes/hr-assistant-routes";
import { internalAuditorRoutes } from "@services/routes/internal-auditor-routes";
import { corporatePlanningAssistant1Routes } from "@services/routes/corporate-planning-assistant1-routes";
import { subsidiaryProjectAccountantRoutes } from "@services/routes/subsidiary-project-accountant-routes";
import { claimsAssistant2Routes } from "@services/routes/claims-assistant2-routes";
import { disbursementAssistantRoutes } from "@services/routes/disbursement-assistant-routes";
import { complianceManagerLifeRoutes } from "@services/routes/compliance-manager-life-routes";
import { claimsAdminAsstRoutes } from "@services/routes/claims-admin-assistant-routes";
import { dataWarehousingManagerRoutes } from "@services/routes/data-warehousing-manager-routes";
import { salesDevelopmentAnalystNcrCentralLuzonRoutes } from "@services/routes/sales-development-analyst-ncrCentralLuzon-routes";
import { underwritingAssistant1Routes } from "@services/routes/underwriting-assistant1-routes";
import { oicBookkeeperTianoRoutes } from "@services/routes/oic-bookkeeper-tiano-routes";
import { adminAssistant1VpAdminCorplanRoutes } from "@services/routes/admin-assistant1-vp-admin-corplan-routes";
import { reinsuranceSpecialistRoutes } from "@services/routes/reinsurance-specialist-routes";
import { communicationsAssistantRoutes } from "@services/routes/communications-assistant-routes";
import { bankReconciliationAssistant1LifeRoutes } from "@services/routes/bank-reconciliation-assistant1-life-routes";
import { adminAsstOutgoingRoutes } from "@services/routes/admin-asst-outgoing-routes";
import { oicAsmRoutes } from "@services/routes/oic-asm-routes";
import { accountingAssistantRoutes } from "@services/routes/accounting-assistant-routes";
import { technicalWriterRoutes } from "@services/routes/technical-writer-routes";
import { underwritingAssistantRoutes } from "@services/routes/underwriting-assistant-routes";
import { salesDevelopmentAnalystSouthLuzonRoutes } from "@services/routes/sales-development-analyst-southluzon-routes";
import { driverRoutes } from "@services/routes/driver-routes";
import { accountantRoutes } from "@services/routes/accountant-routes";
import { vpSalesMarketingRoutes } from "@services/routes/vp-sales-marketing-routes";
import { socialMediaAssistantRoutes } from "@services/routes/social-media-assistant-routes";
import { administrativeAssistantOperationsLifeRoutes } from "@services/routes/administrative-assistant-operations-life-routes";
import { vpAdminCorplanCeoPrincipalCcpRoutes } from "@services/routes/vp-admin-corplan-ceo-principal-ccp-routes";
import { fireMarshallRoutes } from "@services/routes/fire-marshall-routes";
import { messengerUtilityStaffRoutes } from "@services/routes/messenger-utility-staff-routes";
import { bordereauxRoutes } from "@services/routes/borderaux-routes";
import { bankReconciliationAssistant1Routes } from "@services/routes/bank-reconciliation-assistant1-routes";
import { nafecoopBusinessDevtAssistantRoutes } from "@services/routes/nafecoop-business-devt-assistant-routes";
import { qualityDataProcessorRetrievalTechnicalSupportRoutes } from "@services/routes/quality-data-processor-retrieval-technical-support-routes";
import { accountingAssistant1Routes } from "@services/routes/accounting-assistant1-routes";
import { qaDocumentationAnalyst1Routes } from "@services/routes/qa-documentation-analyst1-routes";
import { actuaryAnalyst2Routes } from "@services/routes/actuary-analyst2-routes";
import { avpInvestmentTreasuryRoutes } from "@services/routes/avp-investment-treasury-routes";
import { collectionAnalystRoutes } from "@services/routes/collection-analyst-routes";
import { digitalMediaAssistantRelieverRoutes } from "@services/routes/digital-media-assistant-reliever-routes";
import { investmentAssistantRoutes } from "@services/routes/investment-assistant-routes";
import { learningProgramCoordinatorRoutes } from "@services/routes/learning-program-coordinator-routes";
import { qualityAssuranceDocumentationAssistant1Routes } from "@services/routes/quality-assurance-documentation-assistant1-routes";
import { businessDevelopmentJuniorManagerRoutes } from "@services/routes/business-development-junior-manager-routes";
import { avpNlSalesLuzonRoutes } from "@services/routes/avp-nl-sales-luzon-routes";
import { executiveManagerRoutes } from "@services/routes/executive-manager-routes";
import { underwriterRoutes } from "@services/routes/underwriter-routes";
import { adminAsstCashierRoutes } from "@services/routes/admin-asst-cashier-routes";
import { administrativeAssistantProcurementRoutes } from "@services/routes/administrative-assistant-procurement-routes";
import { policyIssuanceAsstRoutes } from "@services/routes/policy-issuance-asst-routes";
import { oicUnderwritingOfficerRoutes } from "@services/routes/oic-underwriting-officer-routes";
import { systemProgrammerRoutes } from "@services/routes/system-programmer-routes";
import { treasuryAssistant1Routes } from "@services/routes/treasury-assistant1-routes";
import { bookkeeperNlRoutes } from "@services/routes/bookkeeper-nl-routes";
import { networkAdministratorRoutes } from "@services/routes/network-administrator-routes";
import { claimsAssistant2MicroinsuranceRoutes } from "@services/routes/claims-assistant2-microinsurance-routes";
import { cashieringAssistantRoutes } from "@services/routes/cashiering-assistant-routes";
import { avpSalesLifeRoutes } from "@services/routes/avp-sales-life-routes";
import { claimsExaminerRoutes } from "@services/routes/claims-examiner-routes";
import { marketingAssistantRoutes } from "@services/routes/marketing-assistant-routes";
import { oicCompensationBenefitsAnalyst1Routes } from "@services/routes/oic-compensation-benefits-analyst1-routes";
import { operationsManagerRoutes } from "@services/routes/operations-manager-routes";
import { insurtechSeniorManagerRoutes } from "@services/routes/insurtech-senior-manager-routes";
import { memberRelationsAssistantRoutes } from "@services/routes/member-relations-assistant-routes";
import { avpNonlifeSalesVisminRoutes } from "@services/routes/avp-nonlife-sales-vismin-routes";
import { claimsManagerLifeRoutes } from "@services/routes/claims-manager-life-routes";
import { salesDevelopmentAnalystSouthMindanaoRoutes } from "@services/routes/sales-development-analyst-southmindanao-routes";
import { nlReinsurance2Routes } from "@services/routes/nl-reinsurance2-routes";
import { systemDevelopmentSystemAdministrationManagerRoutes } from "@services/routes/system-development-system-administration-manager-routes";
import { legalCounselInHouseRoutes } from "@services/routes/legal-counsel-inhouse-routes";
import { avpLifeNonlifeRoutes } from "@services/routes/avp-life-nonlife-routes";
import { juniorProgrammer1Routes } from "@services/routes/junior-programmer1-routes";
import { policyIssuanceNlRoutes } from "@services/routes/policy-issuance-nl-routes";
import { policyIssuanceClerkRoutes } from "@services/routes/policy-issuance-clerk-routes";
import { complianceManagerNonlifeRoutes } from "@services/routes/compliance-manager-nonlife-routes";
import { infrastructureDataCenterManagerRoutes } from "@services/routes/infrastructure-data-center-manager";
import { digitalMediaAssistantRoutes } from "@services/routes/digital-media-assistant-routes";
import { programManagerRoutes } from "@services/routes/program-manager-routes";
import { dataWarehousingAssistantRoutes } from "@services/routes/data-warehousing-assistant-routes";
import { dataQualityManagementSpecialistRoutes } from "@services/routes/data-quality-management-specialist-routes";
import { claimsSpecialistRoutes } from "@services/routes/claim-specialist-routes";
import { nonLifeClaimsClerkRoutes } from "@services/routes/nonlife-claims-clerk-routes";
import { hrManagerRoutes } from "@services/routes/hr-manager-routes";
import { bookkeeper1Routes } from "@services/routes/bookkeeper1-routes";
import { actuarialAssistantRoutes } from "@services/routes/actuarial-assistant-routes";
import { avpFinanceRoutes } from "@services/routes/avp-finance-routes";
import { actuaryAssistantRelieverRoutes } from "@services/routes/actuary-assistant-reliever-routes";
import { customerServiceAcctRetentionSpecialistRoutes } from "@services/routes/customer-service-acct-retention-specialist-routes";
import { reinsuranceAsstRoutes } from "@services/routes/reinsurance-asst-routes";
import { assistantDivisionManagerNlRoutes } from "@services/routes/assistant-division-manager-nl-routes";
import { claimsEvaluatorProcessorRoutes } from "@services/routes/claims-evaluator-processor-routes";
import { memberRelationsManagerRoutes } from "@services/routes/member-relations-manager-routes";
import { underwritingFilingClerkClimbsCaresStaffRoutes } from "@services/routes/underwriting-filing-clerk-climbs-cares-staff-routes";
import { administrativeAssistant1OopRoutes } from "@services/routes/administrative-assistant1-oop-routes";
import { occupationalHealthNurseRoutes } from "@services/routes/occupational-health-nurse-routes";
import { paralegalRoutes } from "@services/routes/paralegal-routes";
import { adminEnvironmentalAssistant1Routes } from "@services/routes/admin-environmental-assistant1-routes";
import { chiefInternalAuditorRoutes } from "@services/routes/chief-internal-auditor-routes";
import { reinsuranceUnderwritingManagerRoutes } from "@services/routes/reinsurance-underwriting-manager-routes";
import { projectResearchMelOfficerRoutes } from "@services/routes/project-research-mel-officer-routes";
import { billingCollectionsRoutes } from "@services/routes/billing-collections-routes";
import { cacRoutes } from "@services/routes/cac-routes";
import { usersFeedbackRoutes } from "@services/routes/users-feedback-routes";
import { useAuthorizedRoutes } from "@hooks/useAuthorizedRoutes";
import { claimsAssistantNlRoutes } from "@services/routes/claims-assistant-nl-routes";
import { bankReconSpecialistRoutes } from "@services/routes/bank-recon-specialist-routes";
import { chiefOfStaffRoutes } from "@services/routes/chief-of-staff-routes";
import { underwritingAssistantNlRoutes } from "@services/routes/underwriting-assistant-nl-routes";
import { dataProcessorRoutes } from "@services/routes/data-processor-routes";
import { adminOfficerRoutes } from "@services/routes/admin-officer-routes";
import { claimsAnalystRoutes } from "@services/routes/claims-analyst-routes";
import { salesDevelopmentSpecialistRoutes } from "@services/routes/sales-development-specialist-routes";
import { cashierNlRoutes } from "@services/routes/cashier-nl-routes";
import { financeRoutes } from "@services/routes/finance-routes";
import UnderMaintenance from "../modules/shared/maintenance";

export type ChildRoutesProps = {
  Layout: any;
  routes?: RouteItem[];
};

const childRoutes: FC<ChildRoutesProps> = ({ Layout, routes }): any => {
  return routes?.map(({ component: Component, guard, path, roles, props, permissions, children }, key) => {
    const Guard = guard;
    const childRoutes = children?.map(({ path, component: Component, props }) => {
      return <Route path={path} element={<Component {...props} />} />;
    });
    return (
      <Route
        key={key}
        path={path}
        element={
          <Layout>
            {guard ? (
              <Guard roles={roles} permissions={permissions}>
                <Component {...props} />
              </Guard>
            ) : (
              <Component {...props} />
            )}
          </Layout>
        }
      >
        {...childRoutes ?? []}
      </Route>
    );
  });
};

const AppRoutes = () => {
  const authorizedCashierRoutes = useAuthorizedRoutes({ rawRoutes: cashierRoutes });
  
  const authorizedActuaryRoutes = useAuthorizedRoutes({ rawRoutes: actuaryRoutes });
  const authorizedActuaryManagerRoutes = useAuthorizedRoutes({ rawRoutes: actuaryManagerRoutes });

  return (
    <Routes>
      {childRoutes({ routes: adminRoutes, Layout: SidebarLayout })}
      {childRoutes({ routes: userRoutes, Layout: UsersLayout })}
      {childRoutes({ routes: authRoutes, Layout: AuthLayout })}
      {childRoutes({ routes: usersFeedbackRoutes, Layout: AuthLayout })}
      {/* UAT Admin  */}
      {childRoutes({ routes: uatAdminRoutes, Layout: UatSidebarLayout })}
      {/* Compliance */}
      {childRoutes({ routes: complianceRoutes, Layout: ComplianceLayout })}
      {childRoutes({ routes: underwritingRoutes, Layout: UnderwritingLayout })}
      {childRoutes({ routes: claimsRoutes, Layout: ClaimsLayout })}

      {/* UAT user route */}
      {/* {uatUserRoutes.map((route) => (
        <Route
          key={route.id}
          path={route.path}
          element={
            <UATGuard>
              <route.component />
            </UATGuard>
          }
        />
      ))} */}

      {childRoutes({
        routes: clifsaAdminRoutes,
        Layout: ClifsaAdminLayout,
      })}

      {childRoutes({
        routes: areaAdminRoutes,
        Layout: AreaAdminLayout,
      })}

      {renderRoutesWithLayout(salesExecutiveAssistantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(salesRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(authorizedCashierRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(outgoingCashierRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(IncomingCashierRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(chiefCashierRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(incomingOutgoingCashierRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(uatUserRoutes, UATGuard)}
      {renderRoutesWithLayout(gamRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(adminSatelliteRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(marketingRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(authorizedActuaryRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(treasuryRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(outgoingAdminRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(incomingAdminRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(authorizedActuaryManagerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(actuaryAnalyst1Routes, DashBoardLayout)}
      {renderRoutesWithLayout(actuaryAssistant1Routes, DashBoardLayout)}
      {renderRoutesWithLayout(avplifenonlifeRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(claimsManagerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(rndRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(vicePresidentSalesRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(vicePresidentOperationRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(presidentCeoRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(areaSalesManagerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(infrastructureOfficerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(accountingRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(cacRoutes, DashBoardLayout)}
      {/*  */}
      {renderRoutesWithLayout(propertyCustodianRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(regionalSalesManagerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(vpAgencyDistributionChannelManagementRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(dataProcessingAssistantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(dataProcessingAssistant1Routes, DashBoardLayout)}
      {renderRoutesWithLayout(maintenanceRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(frontEndProgrammerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(administrativeAssistantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(managerAgencyDistributionChannelRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(avpAdminCorplanRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(bookkeeperRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(adminSpecialistRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(claimsAssistantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(uiUxDesignerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(lifeCashierRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(policyIssuanceAssistantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(backEndProgrammerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(nlPropertyClaimsAssistant1Routes, DashBoardLayout)}
      {renderRoutesWithLayout(filingClerkRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(bookkeeperNonlifeRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(underwritingStaffRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(dataEncoderRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(salesDevelopmentOfficerNonlifeRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(businessDevelopmentManagerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(memberRelationsAssistant2LifeRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(assistantCashier1Routes, DashBoardLayout)}
      {renderRoutesWithLayout(treasuryOfficerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(vpFinanceInvestmentTreasuryComplianceRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(claimsAssistant1LifeRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(adminAssistantVpFinanceRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(businessAnalystRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(dataProcessingManagerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(oicOperationsManagerNonLifeRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(oicSalesDevelopmentSpecialistVisayasRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(claimsProcessorRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(actuaryAnalystRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(technicalSupportRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(hrAssistantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(internalAuditorRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(corporatePlanningAssistant1Routes, DashBoardLayout)}
      {renderRoutesWithLayout(subsidiaryProjectAccountantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(claimsAssistant2Routes, DashBoardLayout)}
      {renderRoutesWithLayout(disbursementAssistantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(complianceManagerLifeRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(claimsAdminAsstRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(dataWarehousingManagerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(salesDevelopmentAnalystNcrCentralLuzonRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(underwritingAssistant1Routes, DashBoardLayout)}
      {renderRoutesWithLayout(oicBookkeeperTianoRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(adminAssistant1VpAdminCorplanRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(reinsuranceSpecialistRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(communicationsAssistantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(bankReconciliationAssistant1LifeRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(adminAsstOutgoingRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(oicAsmRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(accountingAssistantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(technicalWriterRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(underwritingAssistantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(salesDevelopmentAnalystSouthLuzonRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(driverRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(accountantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(vpSalesMarketingRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(socialMediaAssistantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(administrativeAssistantOperationsLifeRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(vpAdminCorplanCeoPrincipalCcpRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(fireMarshallRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(messengerUtilityStaffRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(bordereauxRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(bankReconciliationAssistant1Routes, DashBoardLayout)}
      {renderRoutesWithLayout(nafecoopBusinessDevtAssistantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(qualityDataProcessorRetrievalTechnicalSupportRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(accountingAssistant1Routes, DashBoardLayout)}
      {renderRoutesWithLayout(qaDocumentationAnalyst1Routes, DashBoardLayout)}
      {renderRoutesWithLayout(actuaryAnalyst2Routes, DashBoardLayout)}
      {renderRoutesWithLayout(avpInvestmentTreasuryRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(collectionAnalystRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(digitalMediaAssistantRelieverRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(investmentAssistantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(learningProgramCoordinatorRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(qualityAssuranceDocumentationAssistant1Routes, DashBoardLayout)}
      {renderRoutesWithLayout(businessDevelopmentJuniorManagerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(avpNlSalesLuzonRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(executiveManagerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(underwriterRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(adminAsstCashierRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(administrativeAssistantProcurementRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(policyIssuanceAsstRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(oicUnderwritingOfficerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(systemProgrammerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(treasuryAssistant1Routes, DashBoardLayout)}
      {renderRoutesWithLayout(bookkeeperNlRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(networkAdministratorRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(claimsAssistant2MicroinsuranceRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(cashieringAssistantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(avpSalesLifeRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(claimsExaminerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(marketingAssistantRoutes, DashBoardLayout)}
      {/*  */}
      {renderRoutesWithLayout(oicCompensationBenefitsAnalyst1Routes, DashBoardLayout)}
      {renderRoutesWithLayout(operationsManagerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(insurtechSeniorManagerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(memberRelationsAssistantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(avpNonlifeSalesVisminRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(claimsManagerLifeRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(salesDevelopmentAnalystSouthMindanaoRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(nlReinsurance2Routes, DashBoardLayout)}
      {renderRoutesWithLayout(systemDevelopmentSystemAdministrationManagerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(legalCounselInHouseRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(avpLifeNonlifeRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(juniorProgrammer1Routes, DashBoardLayout)}
      {renderRoutesWithLayout(policyIssuanceNlRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(policyIssuanceClerkRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(complianceManagerNonlifeRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(infrastructureDataCenterManagerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(digitalMediaAssistantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(programManagerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(dataWarehousingAssistantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(dataQualityManagementSpecialistRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(claimsSpecialistRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(nonLifeClaimsClerkRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(hrManagerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(bookkeeper1Routes, DashBoardLayout)}
      {renderRoutesWithLayout(actuarialAssistantRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(avpFinanceRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(actuaryAssistantRelieverRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(customerServiceAcctRetentionSpecialistRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(reinsuranceAsstRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(assistantDivisionManagerNlRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(claimsEvaluatorProcessorRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(memberRelationsManagerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(underwritingFilingClerkClimbsCaresStaffRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(administrativeAssistant1OopRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(occupationalHealthNurseRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(paralegalRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(adminEnvironmentalAssistant1Routes, DashBoardLayout)}
      {renderRoutesWithLayout(chiefInternalAuditorRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(reinsuranceUnderwritingManagerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(projectResearchMelOfficerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(billingCollectionsRoutes, DashBoardLayout)}

      {renderRoutesWithLayout(claimsAssistantNlRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(bankReconSpecialistRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(chiefOfStaffRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(underwritingAssistantNlRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(dataProcessorRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(adminOfficerRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(claimsAnalystRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(salesDevelopmentSpecialistRoutes, DashBoardLayout)}
      {renderRoutesWithLayout(cashierNlRoutes, DashBoardLayout)}

      
      {renderRoutesWithLayout(financeRoutes, DashBoardLayout)}

      <Route path="/maintenance" element={<UnderMaintenance />} />
      <Route path="/" element={<AuthLayout />} />
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

export default AppRoutes;
