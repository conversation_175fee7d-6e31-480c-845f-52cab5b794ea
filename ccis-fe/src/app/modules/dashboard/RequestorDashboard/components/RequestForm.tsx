import React, { useEffect, useState } from "react";
//GUI Elements
import Button from "@components/common/Button";
import Typography from "@components/common/Typography";
import Radio from "@components/form/Radio";
import Select1 from "@components/form/Combo-box1";
import TextField from "@components/form/TextField";
import { RiArrowGoBackLine } from "react-icons/ri";
import { useNavigate } from "react-router-dom";
import { IAttachment } from "@interface/products.interface";
import Attachments from "@modules/admin/products/components/Common/Attachments";
import { useFormik, Form, FormikProvider } from "formik";
import { TicketFormType } from "@enums/ticket-form-type";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import { formatSelectOptions } from "@helpers/array";
import { useDepartmentsManagementActions } from "@state/reducer/utilities-departments";
import { useRequestTypeActions } from "@state/reducer/departmental-ticketing-request-type";
import { REQUEST_TYPES_ID } from "./constants";
import httpClient from "@clients/httpClient";
import { toast } from "react-toastify";
import { showSuccess } from "@helpers/prompt";
import { AttachmentTags } from "@enums/attachment-tags";
import { useCooperativesManagementActions } from "@state/reducer/cooperatives";
import { getRolePath, getRolePathWithId } from "@helpers/navigatorHelper";
import * as Yup from "yup";
import Loader from "@components/Loader";
import { ISaveAttachmentsPayload } from "@interface/departmental-ticketing-interface";
import { postTicketCommentAttachmentService } from "@services/departmental-ticketing/departmental-ticketing.service";
import DatePicker from "@components/common/CustomDatePicker";
import VPSApprovalModal from "./modals/VPSApprovalModal";
import { SYSTEM_SETTINGS_IDS } from "@constants/global-constant-value";
import { SharedRoutes } from "@enums/shared-routes";
import { confirmVPsApproval } from "./prompts/DepartmentalTicketingPrompts";

const RequestForm: React.FC = () => {
  const navigate = useNavigate();
  const [openComboBox, setOpenComboBox] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [files, setFiles] = useState<File[]>([]);
  const [attachments, setAttachments] = useState<IAttachment[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [showOtherIssueInput, setShowOtherIssueInput] = useState(false);
  const { getDepartment } = useDepartmentsManagementActions();
  const { getRequestTypes } = useRequestTypeActions();
  const { getCooperatives } = useCooperativesManagementActions();
  const user = useSelector((state: RootState) => state.auth.user.data);
  const departments = useSelector((state: RootState) => state.utilitiesDepartments?.getDepartment?.data || []);
  const requestTypes = useSelector((state: RootState) => state.departmentalTicketingRequestType?.getRequestTypes?.data || []);
  const cooperatives = useSelector((state: RootState) => state.cooperatives.cooperatives);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [vpsApprovalModalOpen, setVpsApprovalModalOpen] = useState<boolean>(false);
  const [createdTicketId, setCreatedTicketId] = useState<number>(0);
  const vicePresidentIds = useSelector(
    (state: RootState) => state?.globalSettings?.getGlobalSettings?.data?.data?.find((setting: any) => setting.key === SYSTEM_SETTINGS_IDS.VICE_PRESIDENT_USER_ID)?.value
  );
  const presidentIds = useSelector((state: RootState) => state?.globalSettings?.getGlobalSettings?.data?.data?.find((setting: any) => setting.key === SYSTEM_SETTINGS_IDS.PRESIDENT_USER_ID)?.value);
  const managerIds = useSelector((state: RootState) => state?.globalSettings?.getGlobalSettings?.data?.data?.find((setting: any) => setting.key === SYSTEM_SETTINGS_IDS.MANAGER_USER_ID)?.value);

  const handleSelectedCoop = (coop: any) => {
    formik.setFieldValue("coopName", coop?.coopName);
    formik.setFieldValue("coopId", coop?.coopCode); // Use coopCode instead of id
    formik.setFieldValue("branch", coop?.city); // Auto-populate with city
    setSearchText(coop?.coopCode + " - " + coop?.coopName); // Show both code and name
    setOpenComboBox(false);
  };

  const handleSetFiles = (newFiles: File[]) => {
    setFiles(newFiles);
    const newAttachments: IAttachment[] = newFiles.map((file) => ({
      file,
      label: file.name,
      size: file.size,
    }));
    setAttachments([...attachments, ...newAttachments]);
  };

  const handleRemoveFile = (index: number) => {
    const updatedAttachments = attachments.filter((_, i) => i !== index);
    setAttachments(updatedAttachments);
  };

  const handleUploadFiles = async (_uploadingFiles: File[]) => {
    setIsUploading(true);
    // simulate upload
    setTimeout(() => {
      setIsUploading(false);
    }, 2000);
  };

  useEffect(() => {
    if (presidentIds === undefined || vicePresidentIds === undefined || managerIds === undefined) {
      navigate(getRolePath(SharedRoutes.REQUEST_DASHBOARD));
    }
  }, []);

  useEffect(() => {
    if (user) {
      formik.setFieldValue("fromDepartmentId", user.departmentId);
      formik.setFieldValue("fromDepartmentName", user.department?.departmentName || "");
      formik.setFieldValue("toDepartmentName", user.departmentName || "");
      // formik.setFieldValue("assignedToId", null);
      formik.setFieldValue("coopName", user.cooperativeName || "");
      formik.setFieldValue("coopId", user.cooperativeId || undefined);
      formik.setFieldValue("requestorName", user?.firstname + " " + user?.middlename + " " + user?.lastname || "");
      formik.setFieldValue("contactNumber", user?.contactNumber || "");
    }
  }, [user]);

  const validationSchema = Yup.object({
    toDepartmentId: Yup.string().required("Receiving Department is required"),
    requestTypeId: Yup.string().required("Request Type is required"),
    priorityLevel: Yup.string().required("Priority Level is required"),
    description: Yup.string().required("Request Description is required"),
    expectedCompletionDate: Yup.string().required("Expected Resolution Date is required"),

    // For Coop Support form
    coopId: Yup.number()
      .nullable()
      .when("requestTypeId", {
        is: REQUEST_TYPES_ID.coopSupport,
        then: (schema) => schema.required("Cooperative is required").min(1, "Please select a valid cooperative"),
        otherwise: (schema) => schema.notRequired().nullable(),
      }),
    branch: Yup.string().when("requestTypeId", {
      is: REQUEST_TYPES_ID.coopSupport,
      then: (schema) => schema.required("Branch is required"),
      otherwise: (schema) => schema.notRequired(),
    }),
    issueType: Yup.string().when("requestTypeId", {
      is: (val: string) => val === REQUEST_TYPES_ID.coopSupport || val === REQUEST_TYPES_ID.technicalSupport,
      then: (schema) => schema.required("Issue Category is required"),
      otherwise: (schema) => schema.notRequired(),
    }),
    isUrgent: Yup.string().when("requestTypeId", {
      is: (val: string) => val === REQUEST_TYPES_ID.coopSupport || val === REQUEST_TYPES_ID.technicalSupport,
      then: (schema) => schema.required("Please specify if issue is preventing work"),
      otherwise: (schema) => schema.notRequired(),
    }),
  });

  const formik = useFormik({
    initialValues: {
      id: null,
      cooperative: "",
      subject: "",
      description: "",
      contactNumber: "",
      requestorName: "",
      assignedToId: undefined,
      fromDepartmentId: undefined,
      toDepartmentId: undefined,
      fromDepartmentName: "",
      toDepartmentName: "",
      requestTypeId: "",
      selectedCoop: {},
      coopId: null,
      coopName: "",
      branch: "",
      issueType: "",
      isUrgent: "",
      device: "",
      operatingSystem: "",
      applicationName: "",
      expectedCompletionDate: "",
      formType: "",
      hasTopManApproval: 0,
      priorityLevel: "",
      attachments: [],
    },
    validationSchema,
    onSubmit: () => {
      handleCreateTicket();
    },
  });

  useEffect(() => {
    getDepartment({ filter: "" });
    getCooperatives({ payload: { filter: "" } });
  }, []);

  useEffect(() => {
    if (formik.values.selectedCoop) {
      formik.setFieldValue("selectedCoop", formik.values.selectedCoop);
      formik.setFieldValue("coopId", (formik.values.selectedCoop as any).id);
    }
  }, [formik.values.selectedCoop]);

  useEffect(() => {
    if (formik.values.requestTypeId === REQUEST_TYPES_ID.coopSupport) {
      formik.setFieldValue("formType", TicketFormType.COOP_SUPPORT);
    } else if (formik.values.requestTypeId === REQUEST_TYPES_ID.technicalSupport) {
      formik.setFieldValue("formType", TicketFormType.TECH_SUPPORT);
    } else {
      formik.setFieldValue("formType", TicketFormType.DEFAULT);
    }
    formik.setFieldValue("issueType", "");
    setShowOtherIssueInput(false);
  }, [formik.values.requestTypeId]);

  const handleRadio = (name: string, value: string) => {
    formik.setFieldValue(name, value);
  };

  useEffect(() => {
    if (formik.values.toDepartmentId) {
      getRequestTypes({ departmentId: formik.values.toDepartmentId ?? 0 });
    }
  }, [formik.values.toDepartmentId]);

  const showVPSModalIfManagerTicketCreator = async (ticketId: number) => {
    setCreatedTicketId(ticketId);
    const vpResult = await confirmVPsApproval();
    if (vpResult.isConfirmed) {
      setVpsApprovalModalOpen(true);
    } else {
      navigate(getRolePathWithId(SharedRoutes.REQUEST_FORM, ticketId.toString()));
    }
    // Store navigate and path for use in VPSApprovalModal
    // You could store these in state or pass them directly as props
  };

  const handleCreateTicket = async () => {
    setIsSubmitting(true);
    try {
      const formData = new FormData();
      Object.entries(formik.values).forEach(([key, value]) => {
        if (key !== "attachments") {
          // Skip null/undefined values entirely for optional fields
          if (value === null || value === undefined || value === "") {
            if (key === "coopId" && formik.values.requestTypeId !== REQUEST_TYPES_ID.coopSupport) {
              // Skip coopId entirely for non-coop requests
              return;
            }
            if (key === "assignedToId") {
              // Skip assignedToId if it's null/undefined
              return;
            }
            // For other fields, you might want to skip them too or handle differently
            // Add more conditions here as needed
            return; // Skip all null/undefined values
          }
          formData.append(key, value as string | Blob);
        }
      });

      const response = await httpClient.post("/ticket", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (response) {
        // Wait for attachments to be saved before proceeding
        if (formik.values.attachments?.length > 0) {
          await handleSaveAttachments(response.data);
        }
        setCreatedTicketId(response.data.id);
        showSuccess("Ticket request submitted!", undefined, undefined, false).then((result) => {
          if (result.isConfirmed) {
            if (managerIds.includes(user?.id) && !vicePresidentIds.includes(user?.id) && !presidentIds.includes(user?.id)) {
              // Pass navigate and path to the modal
              showVPSModalIfManagerTicketCreator(response.data.id);
            } else {
              navigate(getRolePathWithId(SharedRoutes.REQUEST_FORM, response.data.id));
            }
          }
        });
      }
    } catch (error) {
      toast.error("Failed to create ticket. Please try again.");
    } finally {
      setIsSubmitting(false); // Re-enable button
    }
  };

  const handleSaveAttachments = async (data: { id: string | number }) => {
    try {
      // Validate inputs
      if (!data?.id || !formik.values.attachments?.length) {
        throw new Error("Missing required data or attachments");
      }

      // Construct the payload according to ISaveAttachmentsPayload
      const payload: ISaveAttachmentsPayload = {
        attachableType: AttachmentTags.TICKET,
        attachableId: data.id,
        files: formik.values.attachments,
      };

      // Call the service
      await postTicketCommentAttachmentService(payload);

      // Optional: Add success feedback if needed
    } catch (error) {}
  };

  useEffect(() => {
    if (formik.values.toDepartmentId) {
      // Reset form-specific fields when department changes
      formik.setFieldValue("requestTypeId", "");
      formik.setFieldValue("formType", "");
      formik.setFieldValue("issueType", "");
      formik.setFieldValue("isUrgent", "");
      formik.setFieldValue("priorityLevel", "");
      formik.setFieldValue("description", "");
      formik.setFieldValue("expectedCompletionDate", "");
      formik.setFieldValue("coopId", null);
      formik.setFieldValue("coopName", "");
      formik.setFieldValue("branch", "");
      formik.setFieldValue("attachments", []);

      // Clear UI states
      setShowOtherIssueInput(false);
      setFiles([]);
      setAttachments([]);
      setSearchText("");

      // Fetch new request types for the selected department
      getRequestTypes({ departmentId: formik.values.toDepartmentId ?? 0 });
    }
  }, [formik.values.toDepartmentId]);

  if (isSubmitting) {
    return (
      <div className="flex flex-1 w-full items-center justify-center">
        <Loader />
      </div>
    );
  }

  return (
    <div>
      <Button
        classNames="text-white flex items-center justify-center border-primary border rounded-xl hover:bg-zinc-200 "
        onClick={() => {
          navigate(getRolePath(SharedRoutes.REQUEST_DASHBOARD));
        }}
      >
        <RiArrowGoBackLine className="text-primary  w-6 h-8" />
      </Button>

      <FormikProvider value={formik}>
        <Form className="p-4 md:p-6">
          <Typography className=" font-poppins-semibold mt-4 mb-2" variant="primary" size="2xl">
            Departmental Request Form
          </Typography>
          <p className=" text-slate-500">Use this form to request assistance, changes, or services from any department. Ensure all fields are complete.</p>

          <div className="flex flex-col md:flex-row gap-4 mt-8">
            <div className="w-full lg:w-1/3">
              <Typography size="xl" className="font-poppins-semibold mt-4 mb-2">
                Requestor Information
              </Typography>
            </div>
            <div className="w-full lg:mt-10">
              <div className="grid grid-cols-1 md:grid-cols-3 items-start md:items-center mb-2 gap-2 md:gap-0">
                <div className="font-medium md:font-normal">Requesting Department</div>
                <div className="md:col-span-2">
                  <TextField disabled value={formik.values.fromDepartmentName} className="border border-slate-300" />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 items-start md:items-center mb-2 gap-2 md:gap-0">
                <div className="font-medium md:font-normal">Requesting Person</div>
                <div className="md:col-span-2">
                  <TextField disabled value={formik.values.requestorName} className="border border-slate-300" />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 items-start md:items-center mb-2 gap-2 md:gap-0">
                <div className="font-medium md:font-normal">Contact Number</div>
                <div className="md:col-span-2">
                  <TextField disabled value={formik.values.contactNumber || ""} className="border border-slate-300" />
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-1/3">
              <Typography size="xl" className="font-poppins-semibold mt-4 mb-2">
                Request Details
              </Typography>
            </div>
            <div className="w-full mt-10">
              <div className="grid grid-cols-3 items-center mb-2">
                <div>Receiving Department</div>
                <div className="col-span-2">
                  <Select1
                    name="toDepartmentId"
                    options={formatSelectOptions(
                      departments.filter((dept: any) => dept.id !== user.departmentId),
                      "departmentName"
                    )}
                    value={formik.values.toDepartmentId}
                    onChange={formik.handleChange}
                    placeholder="Select Receiving Department"
                    error={formik.touched.toDepartmentId && !!formik.errors.toDepartmentId}
                    errorText={formik.errors.toDepartmentId}
                  />
                </div>
              </div>
              <div className="grid grid-cols-3 items-center mb-2">
                <div>Request Type</div>
                <div className="col-span-2">
                  <Select1
                    name="requestTypeId"
                    options={formatSelectOptions(requestTypes, "name")}
                    value={formik.values.requestTypeId}
                    onChange={formik.handleChange}
                    placeholder="Select Request Type"
                    error={formik.touched.requestTypeId && !!formik.errors.requestTypeId}
                    errorText={formik.errors.requestTypeId}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* DEFAULT FORM */}

          {formik.values.requestTypeId !== REQUEST_TYPES_ID.coopSupport && formik.values.requestTypeId !== REQUEST_TYPES_ID.technicalSupport && formik.values.requestTypeId !== "" && (
            <div className="rounded-md w-full flex flex-col lg:flex-row gap-4 h-max py-4">
              <div className="w-full lg:w-1/4"></div>
              <div className="w-full lg:w-3/4">
                <div className="flex flex-col md:flex-row gap-2 md:gap-0">
                  <div className="w-full md:w-1/3 flex items-start md:items-center font-medium md:font-normal">Priority Level</div>
                  <div className="w-full md:w-2/3">
                    <div className="grid grid-cols-2 md:flex gap-2 md:gap-6">
                      {["LOW", "MEDIUM", "HIGH", "CRITICAL"].map((level) => (
                        <Radio key={level} name="priority" value={level} label={level} checked={formik.values.priorityLevel === level} onChange={() => handleRadio("priorityLevel", level)} />
                      ))}
                    </div>
                    {formik.touched.priorityLevel && formik.errors.priorityLevel && <div className="text-red-500 text-sm my-1">{formik.errors.priorityLevel}</div>}
                  </div>
                </div>
                <div className="mt-4 flex flex-col md:flex-row gap-2 md:gap-0">
                  <div className="w-full md:w-1/3 font-medium md:font-normal">Request Description</div>
                  <div className="w-full md:w-2/3 md:mt-2">
                    <textarea
                      name="description"
                      className="border border-slate-300 w-full rounded p-2"
                      placeholder="Enter request description"
                      rows={4}
                      value={formik.values.description}
                      onChange={formik.handleChange}
                    />
                    {formik.touched.description && formik.errors.description && <div className="text-red-500 text-sm my-1">{formik.errors.description}</div>}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 items-start mb-4 gap-2 md:gap-0">
                  <div className="pt-2 font-medium md:font-normal">Attachments</div>
                  <div className={`md:col-span-2 rounded py-3 ${formik.values.attachments.length > 0 ? "" : "border border-slate-300 "}`}>
                    <Attachments
                      files={files}
                      setFiles={(newFiles) => {
                        const maxFiles = 5;
                        const currentAttachments = formik.values.attachments;
                        const availableSlots = maxFiles - currentAttachments.length;

                        // Only take the number of files that fit within the limit
                        const filesToAdd = newFiles.slice(0, availableSlots);

                        if (filesToAdd.length > 0) {
                          handleSetFiles(filesToAdd);
                          formik.setFieldValue("attachments", [...currentAttachments, ...filesToAdd]);
                        }

                        // Optional: Show a warning if some files were rejected due to limit
                        if (newFiles.length > availableSlots) {
                          toast.error(`Only ${filesToAdd.length} files were added. Maximum ${maxFiles} files allowed.`);
                          // You could also show a toast notification here
                        }
                      }}
                      attachments={attachments}
                      removeFile={(index) => {
                        handleRemoveFile(index);
                        const updatedAttachments = formik.values.attachments.filter((_, i) => i !== index);
                        formik.setFieldValue("attachments", updatedAttachments);
                      }}
                      uploadFiles={handleUploadFiles}
                      isUploading={isUploading}
                      maxFiles={5}
                      fileType={["pdf", "png", "jpeg"]}
                      editable
                      autoUpload={true}
                      label="Click or drag files here to upload"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 items-start md:items-center mb-2 gap-2 md:gap-0">
                  <div className="font-medium md:font-normal">Expected Resolution Date</div>
                  <div className="md:col-span-2">
                    <DatePicker
                      label=""
                      value={formik.values.expectedCompletionDate ? new Date(formik.values.expectedCompletionDate) : null}
                      onChange={(date) => formik.setFieldValue("expectedCompletionDate", date ? date.toISOString().slice(0, 10) : "")}
                    />
                    {formik.touched.expectedCompletionDate && formik.errors.expectedCompletionDate && <div className="text-red-500 text-sm my-1">{formik.errors.expectedCompletionDate}</div>}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* COOP SUPPORT */}
          {formik.values.requestTypeId === REQUEST_TYPES_ID.coopSupport && (
            <div className=" rounded-md ">
              <div className=" w-full gap-4 flex h-max ">
                <div className="md:w-1/4"></div>
                <div className="w-3/4">
                  <div className="flex flex-col">
                    <div className=" w-full flex items-center mb-2">
                      <div className="w-1/3 flex items-center ">Cooperative</div>
                      <div className="w-2/3 relative">
                        <div>
                          <TextField
                            className="border-zinc-300"
                            name="cooperative"
                            error={formik.touched.cooperative && !!formik.errors.cooperative}
                            errorText="Cooperative is required"
                            value={searchText}
                            onChange={(e) => {
                              setSearchText(e.target.value);
                              // Clear selected coop when typing
                              if (e.target.value === "") {
                                formik.setFieldValue("coopName", "");
                                formik.setFieldValue("coopId", "");
                                formik.setFieldValue("branch", "");
                              }
                            }}
                            placeholder="Search Coop"
                            onClick={() => setOpenComboBox(true)}
                          />
                          {formik.touched.coopId && formik.errors.coopId && <div className="text-red-500 text-sm mt-1">{formik.errors.coopId}</div>}
                        </div>

                        {openComboBox && (
                          <div className="absolute top-14 rounded w-full h-80 overflow-y-auto border border-zinc-300 bg-zinc-50 z-10">
                            {cooperatives
                              .filter((coop: any) => coop?.coopCode?.toLowerCase().includes(searchText.toLowerCase()) || coop?.coopName?.toLowerCase().includes(searchText.toLowerCase()))
                              .map((coop: any) => (
                                <div key={coop?.id} className="h-16 w-full p-3 hover:bg-zinc-200 cursor-pointer border-b" onClick={() => handleSelectedCoop(coop)}>
                                  <div className="font-medium">{coop?.coopCode}</div>
                                  <div className="text-sm text-gray-600 truncate">{coop?.coopName}</div>
                                </div>
                              ))}
                            {cooperatives.filter((coop: any) => coop?.coopCode?.toLowerCase().includes(searchText.toLowerCase()) || coop?.coopName?.toLowerCase().includes(searchText.toLowerCase()))
                              .length === 0 &&
                              searchText && <div className="h-12 w-full p-4 text-gray-500 text-center">No cooperatives found</div>}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="grid grid-cols-3 items-center mb-2">
                      <div>Coop Code</div>
                      <div className="col-span-2">
                        <TextField
                          name="coopCode"
                          value={formik.values.coopId || ""}
                          onChange={(e) => {
                            const enteredCode = e.target.value;
                            formik.setFieldValue("coopId", enteredCode);

                            // Find matching cooperative by code
                            const matchingCoop = cooperatives.find((coop: any) => coop?.coopCode === enteredCode);

                            if (matchingCoop) {
                              formik.setFieldValue("coopName", matchingCoop.coopName);
                              formik.setFieldValue("branch", matchingCoop.city);
                              setSearchText(matchingCoop.coopCode + " - " + matchingCoop.coopName);
                            } else {
                              formik.setFieldValue("coopName", "");
                              formik.setFieldValue("branch", "");
                              setSearchText(enteredCode);
                            }
                          }}
                          placeholder="Enter Coop Code"
                        />
                        {formik.touched.coopId && formik.errors.coopId && <div className="text-red-500 text-sm mt-1">{formik.errors.coopId}</div>}
                      </div>
                    </div>
                    <div className="grid grid-cols-3 items-center mb-2">
                      <div>Branch</div>
                      <div className="col-span-2">
                        <TextField name="branch" placeholder="Autofill after select a coop" value={formik.values.branch} onChange={formik.handleChange} />
                      </div>
                    </div>
                    <div className="flex flex-col md:flex-row gap-2 md:gap-0">
                      <div className="w-full md:w-1/3 flex items-start md:items-center font-medium md:font-normal">Priority Level</div>
                      <div className="w-full md:w-2/3">
                        <div className="grid grid-cols-2 md:flex gap-2 md:gap-6">
                          {["LOW", "MEDIUM", "HIGH", "CRITICAL"].map((level) => (
                            <Radio key={level} name="priority" value={level} label={level} checked={formik.values.priorityLevel === level} onChange={() => handleRadio("priorityLevel", level)} />
                          ))}
                        </div>
                        {formik.touched.priorityLevel && formik.errors.priorityLevel && <div className="text-red-500 text-sm my-1">{formik.errors.priorityLevel}</div>}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="w-full lg:w-1/4 font-poppins-semibold mt-4 mb-2 text-xl">ISSUE DETAILS</div>
              <div className="w-full flex flex-col lg:flex-row gap-4 h-max py-4 px-2">
                <div className="w-full lg:w-1/4 font-poppins-semibold mt-4 mb-2 text-xl lg:block hidden"></div>
                <div className="w-full lg:w-3/4">
                  <div className="grid grid-cols-1 md:grid-cols-3 items-start mb-2 gap-2 md:gap-0">
                    <div className="pt-2 text-start font-medium md:font-normal">Issue Category</div>
                    <div className="md:col-span-2">
                      <div className="flex flex-col gap-2">
                        {[
                          "Hardware (e.g., computer, printer, scanner, etc.)",
                          "Software (system errors, application crashes, etc.)",
                          "Network/Internet Connectivity",
                          "Email and Communication Tools",
                          "Access/Login Issues",
                        ].map((option, index) => (
                          <Radio
                            key={index}
                            name="issueType"
                            value={option}
                            size="xs"
                            className="border border-slate-400"
                            checked={formik.values.issueType === option}
                            label={option}
                            onChange={() => {
                              handleRadio("issueType", option);
                              setShowOtherIssueInput(false);
                            }}
                          />
                        ))}

                        <Radio
                          name="issueType"
                          value="Other"
                          size="xs"
                          className="border border-slate-400"
                          checked={showOtherIssueInput}
                          label="Other"
                          onChange={() => {
                            setShowOtherIssueInput((prev) => !prev);
                            if (!showOtherIssueInput) {
                              formik.setFieldValue("issueType", "");
                            }
                          }}
                        />

                        {showOtherIssueInput && (
                          <textarea
                            className="border border-slate-300 w-full rounded p-2 mt-2 text-xs"
                            placeholder="Please specify if 'Other'"
                            rows={2}
                            value={formik.values.issueType}
                            onChange={(e) => formik.setFieldValue("issueType", e.target.value)}
                          />
                        )}
                        {formik.touched.issueType && formik.errors.issueType && <div className="text-red-500 text-sm my-1">{formik.errors.issueType}</div>}
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 flex flex-col md:flex-row gap-2 md:gap-0">
                    <div className="w-full md:w-1/3 font-medium md:font-normal">Request Description</div>
                    <div className="w-full md:w-2/3 md:mt-2">
                      <textarea
                        name="description"
                        className="border border-slate-300 w-full rounded p-2"
                        placeholder="Enter request description"
                        rows={4}
                        value={formik.values.description}
                        onChange={(e) => formik.setFieldValue("description", e.target.value)}
                      />
                      {formik.touched.description && formik.errors.description && <div className="text-red-500 text-sm my-1">{formik.errors.description}</div>}
                    </div>
                  </div>

                  <div className="grid grid-cols-3 items-start mb-4">
                    <div className="pt-2">Attachments</div>
                    <div className={`col-span-2 rounded py-3 ${formik.values.attachments.length > 0 ? "" : "border border-slate-300 "}`}>
                      <Attachments
                        files={files}
                        setFiles={(newFiles) => {
                          const maxFiles = 5;
                          const currentAttachments = formik.values.attachments;
                          const availableSlots = maxFiles - currentAttachments.length;

                          // Only take the number of files that fit within the limit
                          const filesToAdd = newFiles.slice(0, availableSlots);

                          if (filesToAdd.length > 0) {
                            handleSetFiles(filesToAdd);
                            formik.setFieldValue("attachments", [...currentAttachments, ...filesToAdd]);
                          }

                          // Optional: Show a warning if some files were rejected due to limit
                          if (newFiles.length > availableSlots) {
                            toast.error(`Only ${filesToAdd.length} files were added. Maximum ${maxFiles} files allowed.`);
                            // You could also show a toast notification here
                          }
                        }}
                        attachments={attachments}
                        removeFile={(index) => {
                          handleRemoveFile(index);
                          const updatedAttachments = formik.values.attachments.filter((_, i) => i !== index);
                          formik.setFieldValue("attachments", updatedAttachments);
                        }}
                        uploadFiles={handleUploadFiles}
                        isUploading={isUploading}
                        maxFiles={5}
                        fileType={["pdf", "png", "jpeg"]}
                        editable
                        autoUpload={true}
                        label="Click or drag files here to upload"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-3 items-center mb-2">
                    <div>Expected Resolution Date</div>
                    <div className="col-span-2">
                      <DatePicker
                        label=""
                        value={formik.values.expectedCompletionDate ? new Date(formik.values.expectedCompletionDate) : null}
                        onChange={(date) => formik.setFieldValue("expectedCompletionDate", date ? date.toISOString().slice(0, 10) : "")}
                      />
                      {formik.touched.expectedCompletionDate && formik.errors.expectedCompletionDate && <div className="text-red-500 text-sm my-1">{formik.errors.expectedCompletionDate}</div>}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 items-start md:items-center mb-2 gap-2 md:gap-0">
                    <div className="font-medium md:font-normal">Is the Issue preventing work?</div>
                    <div className="md:col-span-2">
                      <div className="flex flex-col sm:flex-row gap-2 sm:gap-6 mt-2 md:mt-16">
                        <Radio
                          key="Yes"
                          name="isUrgent"
                          value={formik.values.isUrgent.toString()}
                          label="Yes (Urgent)"
                          checked={formik.values.isUrgent === "Yes"}
                          onChange={() => handleRadio("isUrgent", "Yes")}
                        />
                        <Radio
                          key="No"
                          name="isUrgent"
                          value={formik.values.isUrgent}
                          label="No (Can work with limitation)"
                          checked={formik.values.isUrgent === "No"}
                          onChange={() => handleRadio("isUrgent", "No")}
                        />
                      </div>
                      {formik.touched.isUrgent && formik.errors.isUrgent && <div className="text-red-500 text-sm my-1">{formik.errors.isUrgent}</div>}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          {/* TECHNICAL SUPPORT */}
          {formik.values.requestTypeId === REQUEST_TYPES_ID.technicalSupport && (
            <div className="rounded-md w-full gap-4 flex flex-col md:flex-row h-max ">
              <div className="md:w-1/4 font-poppins-semibold mt-4 mb-2">ISSUE DETAILS</div>
              <div className=" w-3/4 gap-4 flex h-max">
                <div className="w-full">
                  <div className="flex flex-col md:flex-row gap-2 md:gap-0">
                    <div className="w-full md:w-1/3 flex items-start md:items-center font-medium md:font-normal">Priority Level</div>
                    <div className="w-full md:w-2/3">
                      <div className="grid grid-cols-2 md:flex gap-2 md:gap-6">
                        {["LOW", "MEDIUM", "HIGH", "CRITICAL"].map((level) => (
                          <Radio key={level} name="priority" value={level} label={level} checked={formik.values.priorityLevel === level} onChange={() => handleRadio("priorityLevel", level)} />
                        ))}
                      </div>
                      {formik.touched.priorityLevel && formik.errors.priorityLevel && <div className="text-red-500 text-sm my-1">{formik.errors.priorityLevel}</div>}
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 items-start mb-2 gap-2 md:gap-0">
                    <div className="pt-2 text-start font-medium md:font-normal">Issue Category</div>
                    <div className="md:col-span-2">
                      <div className="flex flex-col gap-2">
                        {[
                          "Hardware (e.g., computer, printer, scanner, etc.)",
                          "Software (system errors, application crashes, etc.)",
                          "Network/Internet Connectivity",
                          "Email and Communication Tools",
                          "Access/Login Issues",
                        ].map((option, index) => (
                          <Radio
                            key={index}
                            name="issueType"
                            value={option}
                            size="xs"
                            className="border border-slate-400"
                            checked={formik.values.issueType === option}
                            label={option}
                            onChange={() => {
                              handleRadio("issueType", option);
                              setShowOtherIssueInput(false);
                            }}
                          />
                        ))}

                        <Radio
                          name="issueType"
                          value="Other"
                          size="xs"
                          className="border border-slate-400"
                          checked={showOtherIssueInput}
                          label="Other"
                          onChange={() => {
                            setShowOtherIssueInput((prev) => !prev);
                            if (!showOtherIssueInput) {
                              formik.setFieldValue("issueType", "");
                            }
                          }}
                        />

                        {showOtherIssueInput && (
                          <textarea
                            className="border border-slate-300 w-full rounded p-2 mt-2 text-xs"
                            placeholder="Please specify if 'Other'"
                            rows={2}
                            value={formik.values.issueType}
                            onChange={(e) => formik.setFieldValue("issueType", e.target.value)}
                          />
                        )}
                        {formik.touched.issueType && formik.errors.issueType && <div className="text-red-500 text-sm my-1">{formik.errors.issueType}</div>}
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 flex">
                    <div className="w-1/3">Request Description</div>
                    <div className=" w-2/3 mt-2">
                      <textarea
                        name="description"
                        className="border border-slate-300 w-full rounded p-2"
                        placeholder="Enter request description"
                        rows={4}
                        value={formik.values.description}
                        onChange={formik.handleChange}
                      />
                      {formik.touched.description && formik.errors.description && <div className="text-red-500 text-sm my-1">{formik.errors.description}</div>}
                    </div>
                  </div>

                  <div className="grid grid-cols-3 items-start mb-4">
                    <div className="pt-2">Attachments</div>
                    <div className={`col-span-2 rounded py-3 ${formik.values.attachments.length > 0 ? "" : "border border-slate-300 "}`}>
                      <Attachments
                        files={files}
                        setFiles={(newFiles) => {
                          const maxFiles = 5;
                          const currentAttachments = formik.values.attachments;
                          const availableSlots = maxFiles - currentAttachments.length;

                          // Only take the number of files that fit within the limit
                          const filesToAdd = newFiles.slice(0, availableSlots);

                          if (filesToAdd.length > 0) {
                            handleSetFiles(filesToAdd);
                            formik.setFieldValue("attachments", [...currentAttachments, ...filesToAdd]);
                          }

                          // Optional: Show a warning if some files were rejected due to limit
                          if (newFiles.length > availableSlots) {
                            toast.error(`Only ${filesToAdd.length} files were added. Maximum ${maxFiles} files allowed.`);
                            // You could also show a toast notification here
                          }
                        }}
                        attachments={attachments}
                        removeFile={(index) => {
                          handleRemoveFile(index);
                          const updatedAttachments = formik.values.attachments.filter((_, i) => i !== index);
                          formik.setFieldValue("attachments", updatedAttachments);
                        }}
                        uploadFiles={handleUploadFiles}
                        isUploading={isUploading}
                        maxFiles={5}
                        fileType={["pdf", "png", "jpeg"]}
                        editable
                        autoUpload={true}
                        label="Click or drag files here to upload"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-3 items-center mb-2">
                    <div>Expected Resolution Date</div>
                    <div className="col-span-2">
                      <DatePicker
                        label=""
                        value={formik.values.expectedCompletionDate ? new Date(formik.values.expectedCompletionDate) : null}
                        onChange={(date) => formik.setFieldValue("expectedCompletionDate", date ? date.toISOString().slice(0, 10) : "")}
                      />
                      {formik.touched.expectedCompletionDate && formik.errors.expectedCompletionDate && <div className="text-red-500 text-sm my-1">{formik.errors.expectedCompletionDate}</div>}
                    </div>
                  </div>
                  <div className="grid grid-cols-3 items-center mb-2">
                    <div className="">Is the Issue preventing work?</div>
                    <div className="col-span-2">
                      <div className="flex gap-6 mt-16">
                        <Radio
                          key="Yes"
                          name="isUrgent"
                          value={formik.values.isUrgent.toString()}
                          label="Yes (Urgent)"
                          checked={formik.values.isUrgent === "Yes"}
                          onChange={() => handleRadio("isUrgent", "Yes")}
                        />
                        <Radio
                          key="No"
                          name="isUrgent"
                          value={formik.values.isUrgent}
                          label="No (Can work with limitation)"
                          checked={formik.values.isUrgent === "No"}
                          onChange={() => handleRadio("isUrgent", "No")}
                        />
                      </div>
                      {formik.touched.isUrgent && formik.errors.isUrgent && <div className="text-red-500 text-sm my-1">{formik.errors.isUrgent}</div>}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex flex-col sm:flex-row justify-end mt-4">
            <Button classNames="bg-primary text-white px-4 py-2 rounded sm:w-60" type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Submitting..." : "Submit Request"}
            </Button>
          </div>
        </Form>
      </FormikProvider>
      <VPSApprovalModal
        ticketId={createdTicketId}
        isOpen={vpsApprovalModalOpen}
        onClose={() => setVpsApprovalModalOpen(false)}
        navigate={navigate} // Pass navigate function
        path={createdTicketId ? getRolePathWithId(SharedRoutes.REQUEST_FORM, createdTicketId.toString()) : ""} // Pass path
      />
    </div>
  );
};

export default RequestForm;
