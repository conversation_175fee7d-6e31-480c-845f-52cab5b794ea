import httpClient from "@clients/httpClient";
import Button from "@components/common/Button";
import CustomTextField from "@components/common/CustomTextFieldWithSearch";
import Modal from "@components/common/Modal";
import { RequestStatus } from "@enums/ticket-status";
import { useTicketActions } from "@state/reducer/departmental-ticketing";
import { Form, Formik } from "formik";
import React, { useEffect } from "react";
import { NavigateFunction } from "react-router-dom"; // Import NavigateFunction type
import { toast } from "react-toastify";
import * as Yup from "yup";

interface VPSApprovalModalProps {
  isOpen: boolean;
  onClose: () => void;
  ticketId: number;
  handleFetchTicketByID?: () => void;
  navigate?: NavigateFunction; // Add navigate prop
  path?: string; // Add path prop
  shouldShowForApprovalReceivingDepartmentButton?: boolean;
  remarks?: string;
}

function VPSApprovalModal({
  isOpen,
  onClose,
  ticketId,
  handleFetchTicketByID,
  navigate,
  path,
  shouldShowForApprovalReceivingDepartmentButton,
  remarks
}: VPSApprovalModalProps) {
  const { postUpdateTicketApproval } = useTicketActions();
  const [approvers, setApprovers] = React.useState<any[]>([]);

  const fetchApprovers = async () => {
    try {
      const response = await httpClient.get("/signatories-templates");
      setApprovers(response.data);
    } catch (error) {
      console.error("Error fetching approvers:", error);
      toast.error("Failed to fetch approvers. Please try again.");
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchApprovers();
    }
  }, [isOpen]);

  const approverOptions = approvers
    .map((approver) => ({
      value: approver.id,
      text: `${approver.templateName.replace("Signatories", "").trim()} - ${
        approver.description.replace("This is a default description", "").trim() || "Default Approver"
      }`,
    }))
    .filter((option) => option.text);

  type FormValues = {
    department: string;
    approver: string;
  };
  
    const initialValues: FormValues = {
      department: "",
      approver: "",
    };

  const handleSubmit = async (values: FormValues) => {
    let approverId: string | number = values.approver;

    if (typeof approverId === "string") {
      const match = approverId.match(/^\d+/);
      if (match) {
        approverId = match[0];
      }
    }

    const selectedApprover = approvers.find((approver) => approver.id.toString() === approverId.toString());

    if (!selectedApprover) {
      toast.error("Please select a valid approver.");
      return;
    }

    const payload = {
      status: RequestStatus.Approved,
      signatoryIdTemplateId: selectedApprover.id,
    };

    const shouldShowForApprovalReceivingDepartmentButtonPayload = {
      status: RequestStatus.ForApproval,
      vpSignatoryTemplateId: selectedApprover.id,
      remarks
    };
    

    try {
      if (shouldShowForApprovalReceivingDepartmentButton) {
        await postUpdateTicketApproval({
          ticketId,
          payload: shouldShowForApprovalReceivingDepartmentButtonPayload,
        });
      } else {
        await postUpdateTicketApproval({
          ticketId,
          payload,
        });
      }
    
      if (handleFetchTicketByID) {
        await handleFetchTicketByID();
      }
      onClose();
      // Navigate after successful submission
      if (navigate && path) {
        navigate(path);
      }
    } catch (error: any) {
      toast.error("Error updating ticket:", error);
    }
  };

  // Modified onClose to include navigation
  const handleClose = () => {
    onClose();
    if (navigate && path) {
      navigate(path);
    }
  };

  const validationSchema = Yup.object({
    approver: Yup.string().required("Please select an approver"),
  });

  const getCleanApproverId = (value: string): string => {
    if (!value) return "";
    const match = value.toString().match(/^\d+/);
    return match ? match[0] : value.toString();
  };

  return (
    <Modal
      title="Approver"
      modalContainerClassName="max-w-lg"
      titleClass="text-gray-900 text-xl font-semibold"
      isOpen={isOpen}
      onClose={handleClose} // Use modified handleClose
    >
      <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={handleSubmit}>
        {({ values, setFieldValue }) => (
          <Form className="w-full">
            <p className="text-gray-600 text-sm">
              Please select the designated signatory to validate this request.
            </p>

            <div className="divider my-7"></div>

            <div className="mb-1">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 mb-4">Selected Approvers</label>
                <p className="text-center text-gray italic min-h-[24px]">
                  {values.approver
                    ? (() => {
                        const cleanId = getCleanApproverId(values.approver);
                        const selectedApprover = approvers.find(
                          (approver) => approver.id.toString() === cleanId
                        );
                        return selectedApprover?.templateName || "NO SELECTED APPROVER";
                      })()
                    : "NO SELECTED APPROVER"}
                </p>
              </div>
            </div>

            <div className="divider my-7"></div>
            <div className="mb-1">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 mb-4">Select Approver</label>
                <CustomTextField
                  name="approver"
                  suggestionOptions={approverOptions}
                  getOptionLabel={(item) => item.text}
                  getOptionValue={(item) => item.value}
                  placeholder="--- Approver ---"
                  required
                  value={values.approver}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    setFieldValue("approver", e.target.value);
                  }}
                />
              </div>
            </div>

            <div className="flex gap-3 mt-8">
              <button
                type="button"
                className="bg-default hover:bg-default-dark border border-default text-secondary-dark text-xs w-full py-3 px-4 rounded-md transition-colors duration-200 font-medium"
                onClick={handleClose} // Use modified handleClose
              >
                Cancel
              </button>
              <Button type="submit" classNames="bg-primary hover:bg-primary-dark text-white text-xs w-full">
                Set
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
}

export default VPSApprovalModal;