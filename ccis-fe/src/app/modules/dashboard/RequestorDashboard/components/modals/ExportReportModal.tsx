import React from "react";
import { Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";
import Modal from "@components/common/Modal";
import Button from "@components/common/Button";
import Select from "@components/form/Select";
import TextField from "@components/form/TextField";
import { toast } from "react-toastify";

// Define the export report payload interface
import { IExportReportPayload } from "@interface/departmental-ticketing-interface";

// Define the form values interface
interface IExportReportFormValues {
  departmentId: number | string;
  status: string;
  CreatedDateFrom: string;
  CreatedDateTo: string;
  fileType: string;
}

// Validation schema for the export form
const ExportReportSchema = Yup.object().shape({
  departmentId: Yup.string().required("Please select a department"),
  status: Yup.string().required("Please select a status"),
  CreatedDateFrom: Yup.string().required("Please select start date"),
  CreatedDateTo: Yup.string()
    .required("Please select end date")
    .test(
      "date-range",
      "End date must be after start date",
      function (value) {
        const { CreatedDateFrom } = this.parent;
        if (!CreatedDateFrom || !value) return true;
        return new Date(value) >= new Date(CreatedDateFrom);
      }
    ),
  fileType: Yup.string().required("Please select file type"),
});

type ExportReportModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onExport: (payload: IExportReportPayload) => void;
  departments: { id: number; name: string }[];
  loading?: boolean;
};

const ExportReportModal: React.FC<ExportReportModalProps> = ({
  isOpen,
  onClose,
  onExport,
  departments,
  loading = false,
}) => {
  // Initial form values
  const initialValues: IExportReportFormValues = {
    departmentId: "",
    status: "",
    CreatedDateFrom: "",
    CreatedDateTo: "",
    fileType: "PDF",
  };

  // Formik setup
  const formik = useFormik({
    initialValues,
    validationSchema: ExportReportSchema,
    onSubmit: (values, { resetForm }) => {
      try {
        onExport(values);
        resetForm();
        onClose();
      } catch (error) {
        toast.error(`Error exporting report: ${(error as any).message}`);
      }
    },
  });

  // Handle modal close and reset form
  const handleClose = () => {
    formik.resetForm();
    onClose();
  };

  // Department options for select
  const departmentOptions = [
    { value: "", text: "— Select Department —" },
    ...departments.map((dept) => ({
      value: dept.id.toString(),
      text: dept.name,
    })),
  ];

  // Status options
  const statusOptions = [
    { value: "", text: "— Select Status —" },
    { value: "all", text: "All" },
    { value: "resolved", text: "Resolved" },
    { value: "unresolved", text: "Unresolved" },
    { value: "pending", text: "Pending" },
    { value: "in_progress", text: "In Progress" },
  ];

  // File type options
  const fileTypeOptions = [
    { value: "PDF", text: "PDF" },
    { value: "CSV", text: "CSV" },
    { value: "EXCEL", text: "Excel" },
  ];

  return (
    <Modal
      title="Generate Reports"
      titleClass="text-lg font-semibold text-gray-900"
      modalContainerClassName="max-w-md z-[70]"
      className="z-[70]"
      isOpen={isOpen}
      onClose={handleClose}
    >
      <FormikProvider value={formik} key={isOpen ? 'modal-open' : 'modal-closed'}>
        <Form className="space-y-4">
          <div className="space-y-4">
            {/* Department Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Department <span className="text-red-500">*</span>
              </label>
              <Select
                name="departmentId"
                options={departmentOptions}
                value={formik.values.departmentId}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={!!(formik.touched.departmentId && formik.errors.departmentId)}
                errorText={formik.touched.departmentId ? formik.errors.departmentId : ""}
                placeholder="Select Department"
                required
              />
            </div>

            {/* Status Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status <span className="text-red-500">*</span>
              </label>
              <Select
                name="status"
                options={statusOptions}
                value={formik.values.status}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={!!(formik.touched.status && formik.errors.status)}
                errorText={formik.touched.status ? formik.errors.status : ""}
                placeholder="Select Status"
                required
              />
            </div>

            {/* Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Period <span className="text-red-500">*</span>
              </label>
              <div className="grid grid-cols-2 gap-2">
                <TextField
                  type="date"
                  name="CreatedDateFrom"
                  placeholder="From"
                  value={formik.values.CreatedDateFrom}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={!!(formik.touched.CreatedDateFrom && formik.errors.CreatedDateFrom)}
                  errorText={formik.touched.CreatedDateFrom ? formik.errors.CreatedDateFrom : ""}
                  required
                />
                <TextField
                  type="date"
                  name="CreatedDateTo"
                  placeholder="To"
                  value={formik.values.CreatedDateTo}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={!!(formik.touched.CreatedDateTo && formik.errors.CreatedDateTo)}
                  errorText={formik.touched.CreatedDateTo ? formik.errors.CreatedDateTo : ""}
                  required
                />
              </div>
            </div>

            {/* File Type Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                File Type <span className="text-red-500">*</span>
              </label>
              <Select
                name="fileType"
                options={fileTypeOptions}
                value={formik.values.fileType}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={!!(formik.touched.fileType && formik.errors.fileType)}
                errorText={formik.touched.fileType ? formik.errors.fileType : ""}
                placeholder="Select File Type"
                required
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="secondary"
              onClick={handleClose}
              classNames="px-4 py-2"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={formik.isSubmitting || !formik.isValid || loading}
              classNames="px-4 py-2"
            >
              {loading || formik.isSubmitting ? "Exporting..." : "Export"}
            </Button>
          </div>
        </Form>
      </FormikProvider>
    </Modal>
  );
};

export default ExportReportModal;

