import Modal from "@components/common/Modal";
import type React from "react";
import { useState, useEffect, useRef } from "react";
import { useSelector } from "react-redux";
import Select from "@components/form/Select";
import Button from "@components/common/Button";
import { Statuses } from "@constants/global-constant-value";
import { useTicketActions } from "@state/reducer/departmental-ticketing";
import { postClosureStatusService } from "@services/departmental-ticketing/departmental-ticketing.service";
import { RootState } from "@state/store";
import { showProcessingToast, showSuccessToast } from "../prompts/DepartmentalTicketingPrompts";
import Swal from "sweetalert2";

type CloseTicketModalProps = {
  title: string;
  isOpen: boolean;
  ticketId: string;
  onClose: () => void;
  handleFetchTicketByID: () => void;
};

const CloseTicketModal: React.FC<CloseTicketModalProps> = ({ isOpen, ticketId, onClose, handleFetchTicketByID, title }) => {
  const [status, setStatus] = useState<string>("");
  const [remarks, setRemarks] = useState<string>("");
  const [error, setError] = useState<string>("");
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const { putUpdateTicketStatus } = useTicketActions();

  // Add these for automatic refresh logic
  const putUpdateTicketStatusState = useSelector((state: RootState) => state?.departmentalTicketing?.putUpdateTicketStatus);
  const prevPutUpdateSuccessRef = useRef(false);
  const [isCloseTicketAction, setIsCloseTicketAction] = useState(false);

  // Add this useEffect for automatic refresh
  // Add this useEffect for automatic refresh and toast notifications
  useEffect(() => {
    // Show loading toast when action starts
    if (isCloseTicketAction && putUpdateTicketStatusState?.loading) {
      showProcessingToast("Processing request...");
    }

    // Handle success
    if (isCloseTicketAction && putUpdateTicketStatusState?.success && !prevPutUpdateSuccessRef.current) {
      Swal.close();
      showSuccessToast("Ticket status updated successfully.");
      handleFetchTicketByID();
      setIsCloseTicketAction(false); // Reset the flag
      setIsProcessing(false); // Reset processing state
    }

    // Reset processing state on error
    if (isCloseTicketAction && putUpdateTicketStatusState?.error) {
      setIsProcessing(false);
      setIsCloseTicketAction(false);
    }

    prevPutUpdateSuccessRef.current = putUpdateTicketStatusState?.success || false;
  }, [putUpdateTicketStatusState?.success, putUpdateTicketStatusState?.error, putUpdateTicketStatusState?.loading, isCloseTicketAction]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!status) {
      setError("Please select a status.");
      return;
    }
    if (status === Statuses.UNRESOLVED && !remarks.trim()) {
      setError("Remarks are required for Unresolved status.");
      return;
    }

    try {
      setIsProcessing(true);

      // Convert ticketId to integer
      const parsedTicketId = parseInt(ticketId, 10);
      if (isNaN(parsedTicketId)) {
        setError("Invalid ticket ID.");
        setIsProcessing(false);
        return;
      }

      // Create payload for the saga
      const payload = {
        id: parsedTicketId,
        status,
        ...(status === Statuses.UNRESOLVED && { remarks }),
      };

      // Handle different logic based on title
      if (title === "Close Ticket") {
        // For Close Ticket: only use postClosureStatusService
        showProcessingToast("Processing request...");
        await postClosureStatusService(
          parseInt(ticketId), // Pass ticketId as the first argument
          {
            // Pass payload as the second argument
            status,
            ...(status === Statuses.UNRESOLVED && { remarks }),
          }
        );
        // Manual refresh for postClosureStatusService since it doesn't use Redux
        Swal.close(); // Close the processing toast
        showSuccessToast("Ticket status updated successfully.");
        handleFetchTicketByID();
        setIsProcessing(false);
      } else if (title === "Set Status") {
        // For Set Status: use putUpdateTicketStatus with automatic refresh
        setIsCloseTicketAction(true); // Set flag before the action
        putUpdateTicketStatus(payload);
        // Don't call handleFetchTicketByID() here - let useEffect handle it
      }

      // Reset form and close modal
      setError("");
      setStatus("");
      setRemarks("");
      onClose();
    } catch (err) {
      Swal.close(); // Close any open processing toast
      setError("Failed to update ticket status. Please try again.");
      setIsProcessing(false);
      setIsCloseTicketAction(false);
    }
  };

  const handleCancel = () => {
    setStatus("");
    setRemarks("");
    setError("");
    onClose();
  };

  const statusOptions = [
    { text: "Resolved", value: Statuses.RESOLVED },
    { text: "Unresolved", value: Statuses.UNRESOLVED },
  ];

  return (
    <Modal title={title} modalContainerClassName="max-w-lg" titleClass="text-gray-900 text-xl font-semibold" isOpen={isOpen} onClose={onClose}>
      <form className="w-full" onSubmit={handleSubmit}>
        {title === "Close Ticket" ? (
          <p className="text-gray-600 text-sm mb-14">Please select the appropriate status before closing the ticket.</p>
        ) : title === "Set Status" ? (
          <p className="text-gray-600 text-sm mb-14">Please select the new status and provide any necessary remarks.</p>
        ) : null}

        <div className="mb-1">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 mb-4">Status</label>
            <Select key={isOpen ? "open" : "closed"} options={statusOptions} value={status} onChange={(e) => setStatus(e.target.value)} placeholder="--- Select ---" required />{" "}
          </div>
        </div>

        {status === Statuses.UNRESOLVED && (
          <div className="flex-none w-full mb-14 mt-4">
            <label className="block mb-2 text-sm font-medium">Remarks</label>
            <textarea className="w-full p-2 border rounded" placeholder="Enter remarks" value={remarks} onChange={(e) => setRemarks(e.target.value)} required rows={4} />
          </div>
        )}

        {error && <p className="text-red-500 text-sm mt-2">{error}</p>}

        <div className="flex gap-3 mt-8">
          <Button onClick={handleCancel} type="button" variant="custom">
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={!status || (status === Statuses.UNRESOLVED && !remarks.trim()) || isProcessing}
            classNames={`${isProcessing ? "bg-gray-400" : "bg-primary hover:bg-primary-dark"} text-white text-xs w-full mt-4`}
          >
            {isProcessing ? "Processing..." : "Set"}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default CloseTicketModal;
