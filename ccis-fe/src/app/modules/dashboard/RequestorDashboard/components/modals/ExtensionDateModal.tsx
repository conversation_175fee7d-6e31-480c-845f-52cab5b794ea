import Button from "@components/common/Button";
import DatePicker from "@components/common/CustomDatePicker";
import { useTicketActions } from "@state/reducer/departmental-ticketing";
import type React from "react";
import { useState } from "react";
import { useParams } from "react-router-dom";
import { toast } from "react-toastify";

interface ExtensionDateModalProps {
  isOpen: boolean;
  onClose: () => void;
  handleFetchTicketByID: () => void;
  expectedCompletionDate: string| Date | null;
}

const ExtensionDateModal: React.FC<ExtensionDateModalProps> = ({ isOpen, onClose, handleFetchTicketByID, expectedCompletionDate }) => {
  const [extensionDate, setExtensionDate] = useState<Date | null>(null);
  const [remarks, setRemarks] = useState<string>("");
  const { id } = useParams<{ id: string }>();
  const { putTicketExtendDate } = useTicketActions();

  const formatDate = (date: Date): string => {
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  };

  const handleSubmit = async () => {
    if (!extensionDate) {
      toast.error("Please select an extension date.");
      return;
    }

    if (!remarks) {
      toast.error("Please enter remarks.");
      return;
    }

    if (!id) {
      toast.error("Ticket ID is missing.");
      return;
    }

    try {
      await putTicketExtendDate({
        params: {
          ticketId: id, // Convert string to number since TITicketExtendDatePayload expects ticketId as number
          extendedDate: formatDate(extensionDate),
          extensionRemarks: remarks,
        },
      });
      await handleFetchTicketByID();
      setExtensionDate(null);
      setRemarks("");
      onClose();
    } catch (error) {}
  };

  const handleCancel = () => {
    setExtensionDate(null);
    setRemarks("");
    onClose();
  };


  if (!isOpen) return null;
  

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Set Extension Date</h2>
            <p className="text-sm text-gray-600">Please specify the date and provide a reason for the extension.</p>
          </div>

          {/* Expected Date to Complete */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Expected Date to Complete</h3>
            <div className="bg-gray-100 border border-gray-200 rounded-md px-3 py-2">
              <span className="text-gray-700">
                {expectedCompletionDate
                  ? formatDate(
                      typeof expectedCompletionDate === "string"
                        ? new Date(expectedCompletionDate)
                        : expectedCompletionDate
                    )
                  : null}
              </span>
            </div>
          </div>

          {/* Extension Date to Complete */}
          <div className="mb-6">
            <DatePicker label="Extension Date to Complete" value={extensionDate} onChange={setExtensionDate} />
          </div>

          {/* Remarks */}
          <div className="mb-8">
            <label className="block text-sm font-medium text-gray-700 mb-2">Remarks</label>
            <textarea
              className="w-full border border-gray-300 rounded-md px-3 py-2 h-32 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter remarks"
              value={remarks}
              onChange={(e) => setRemarks(e.target.value)}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button onClick={handleCancel} variant="custom">
              Cancel
            </Button>
            <Button disabled={!extensionDate || !remarks} onClick={handleSubmit} variant="primary" classNames="w-full mt-4">
              Set
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExtensionDateModal;
