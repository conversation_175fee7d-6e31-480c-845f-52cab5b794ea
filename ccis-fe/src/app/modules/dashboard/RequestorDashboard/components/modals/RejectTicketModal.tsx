import Modal from "@components/common/Modal";
import { RejectModalTitle } from "@enums/ticket-status";
import { FC, useState } from "react";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  onReject: (remarks: string) => void;
  title?: string;
  placeholder?: string;
  ticketId?: number;
};

const RejectTicketModal: FC<Props> = ({ isOpen, onClose, onReject, title, placeholder }) => {
  const [remarks, setRemarks] = useState("");

  const handleReject = () => {
    onReject(remarks);
    setRemarks(""); // Clear the textarea after rejection
    onClose();
  };

  const handleCancel = () => {
    setRemarks(""); // Clear the textarea on cancel
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleCancel} showCloseButton={false} showHeader={false} modalContainerClassName="max-w-md" className="backdrop-blur-sm">
      {/* Custom Header with Icon */}
      <div className="pt-6">
        <div className="flex flex-col items-center text-center mb-6 ">
          {title === "Reject Request" && (
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4 ">
              <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
          )}
          <h2 className="text-xl font-semibold text-gray-800 mb-2">{title}</h2>
        </div>

        {/* Remarks Section */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">Remarks</label>
          <textarea
            value={remarks}
            onChange={(e) => setRemarks(e.target.value)}
            placeholder={placeholder}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 resize-none"
            rows={4}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex gap-6 justify-center">
          <button
            type="button"
            onClick={handleCancel}
            className="px-6 py-2 bg-slate-600 text-white rounded-md hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-gray-300 transition-colors duration-200"
          >
            Cancel
          </button>

          {title === RejectModalTitle.RejectRequest ? (
            <button
              type="button"
              onClick={handleReject}
              className="px-6 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-300 transition-colors duration-200"
            >
              Reject
            </button>
          ) : (
            title === RejectModalTitle.Reconsider && (
              <button
                type="button"
                onClick={handleReject}
                className="px-6 py-2 bg-sky-600 hover:bg-sky-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-red-300 transition-colors duration-200"
              >
                Reconsider
              </button>
            )
          )}
        </div>
      </div>
    </Modal>
  );
};

export default RejectTicketModal;
