import Swal, { SweetAlertIcon } from "sweetalert2";

export const confirmApproval = async (title?: string, message?: string, onApprove?: () => void, icon?: SweetAlertIcon, confirmButtonText?: string) => {
  return Swal.fire({
    title: title ?? "Confirmation",
    text: message ?? "Are you sure you want to approve this request?",
    icon: icon ?? "info",
    iconColor: "#5B9BD5",
    showConfirmButton: true,
    confirmButtonText: confirmButtonText ?? "Approve",
    confirmButtonColor: "#5B9BD5",
    showCancelButton: true,
    cancelButtonText: "Cancel",
    cancelButtonColor: "#808080",
    reverseButtons: true,
    preConfirm: onApprove,
    customClass: {
      confirmButton: "swal2-confirm-approval",
    },
    didOpen: () => {
      // Add custom styling for the highlighted border effect
      const style = document.createElement("style");

      document.head.appendChild(style);
    },
  });
};

export const showApprovalSuccess = async (title?: string, message?: string, icon?: SweetAlertIcon, onClose?: () => void) => {
  const result = await Swal.fire({
    title: title ?? "Approved",
    text: message ?? "The request has been successfully approved",
    icon: icon ?? "success",
    showConfirmButton: true,
    confirmButtonText: "Done",
    confirmButtonColor: "#22C55E",
    showCancelButton: false,
    customClass: {
      confirmButton: "swal2-success-approval",
    },
  });

  // Trigger the onClose callback if provided
  if (onClose) onClose();
  return result;
};

export const confirmVPsApproval = async (title?: string, message?: string, onYes?: () => void, onNo?: () => void) => {
  const result = await Swal.fire({
    title: title ?? "VPs Approval",
    text: message ?? "Select whether this request requires VPs approval",
    icon: "info",
    showConfirmButton: true,
    confirmButtonText: "Yes",
    confirmButtonColor: "#5B9BD5",
    showCancelButton: true,
    cancelButtonText: "No",
    cancelButtonColor: "#808080",
    reverseButtons: true,
    allowOutsideClick: false,
    customClass: {
      confirmButton: "swal2-vp-approval-yes",
    },
  });

  // Handle the callbacks based on user selection
  if (result.isConfirmed && onYes) {
    onYes();
  } else if (result.dismiss === Swal.DismissReason.cancel && onNo) {
    onNo();
  }

  return {
    requiresVPApproval: result.isConfirmed,
    isConfirmed: result.isConfirmed,
    isDismissed: result.dismiss === Swal.DismissReason.cancel,
  };
};

export const showRejectRequest = async (onReject?: (remarks: string) => void) => {
  const { value: remarks, isConfirmed } = await Swal.fire({
    title: "Reject Request",
    html: `
      <div class="flex flex-col items-start w-full">
        <label for="remarks-textarea" class="text-sm font-medium text-gray-700 mb-2">Remarks</label>
        <textarea
          id="remarks-textarea"
          class="swal2-textarea w-full p-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          placeholder="Enter remarks"
          rows="5"
        ></textarea>
      </div>
    `,
    iconHtml:
      '<div class="swal2-icon swal2-error swal2-animate-error-icon" style="display: flex;"><span class="swal2-x-mark"><span class="swal2-x-mark-line-left"></span><span class="swal2-x-mark-line-right"></span></span></div>',
    showCancelButton: true,
    confirmButtonText: "Reject",
    cancelButtonText: "Cancel",
    confirmButtonColor: "#FF5151",
    cancelButtonColor: "#d1d1d1",
    reverseButtons: true,
    preConfirm: () => {
      const textarea = Swal.getPopup()?.querySelector<HTMLTextAreaElement>("#remarks-textarea");
      return textarea?.value;
    },
    customClass: {
      icon: "swal2-no-border-icon", // Custom class to remove default icon border if any
    },
    didOpen: () => {
      // Ensure the textarea is focused when the modal opens
      const textarea = Swal.getPopup()?.querySelector<HTMLTextAreaElement>("#remarks-textarea");
      if (textarea) {
        textarea.focus();
      }
    },
  });

  if (isConfirmed && remarks !== undefined && onReject) {
    onReject(remarks);
  }

  return { isConfirmed, remarks };
};

export const showTicketReceived = async (onProceed?: () => void) => {
  return Swal.fire({
    title: "Ticket Received",
    html: "This Ticket has been successfully marked as <strong>In Progress</strong>. You may now begin working on the request.",
    icon: "success",
    showConfirmButton: true,
    confirmButtonText: "Proceed",
    confirmButtonColor: "#5B9BD5",
    showCancelButton: false,
    allowOutsideClick: false,
    allowEscapeKey: false,
    preConfirm: onProceed,
    customClass: {
      popup: "swal2-ticket-received",
      confirmButton: "swal2-proceed-button",
    },
  });
};

export const showApprovalRequest = async (onSubmit?: (remarks: string) => void) => {
  const { value: remarks, isConfirmed } = await Swal.fire({
    title: "Ticket Approval Request",
    showCancelButton: true,
    confirmButtonText: "Submit",
    cancelButtonText: "Cancel",
    confirmButtonColor: "#2563eb",
    cancelButtonColor: "#d1d1d1",
    reverseButtons: true,
    preConfirm: () => {
      const textarea = Swal.getPopup()?.querySelector<HTMLTextAreaElement>("#remarks-textarea");
      return textarea?.value;
    },
    didOpen: () => {
      // Ensure the textarea is focused when the modal opens
      const textarea = Swal.getPopup()?.querySelector<HTMLTextAreaElement>("#remarks-textarea");
      if (textarea) {
        textarea.focus();
      }
    },
  });

  if (isConfirmed && remarks !== undefined && onSubmit) {
    onSubmit(remarks);
  }

  return { isConfirmed, remarks };
};

export const showProcessingToast = (message = "Processing your request...") => {
  Swal.fire({
    title: 'Processing',
    html: message,
    allowOutsideClick: false,
    allowEscapeKey: false,
    showConfirmButton: false,
    didOpen: () => {
      Swal.showLoading();
    },
  });
};

// Show success modal with SweetAlert2
export const showSuccessToast = (message = "Request completed successfully!") => {
  Swal.fire({
    title: 'Success',
    html: message,
    icon: 'success',
    timer: 3000,
    timerProgressBar: true,
    showConfirmButton: true,
    confirmButtonText: 'OK',
    confirmButtonColor: '#3085d6',
  });
};
