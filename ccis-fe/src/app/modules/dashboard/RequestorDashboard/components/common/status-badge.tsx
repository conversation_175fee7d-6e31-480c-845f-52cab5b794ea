import { RequestStatus } from "@enums/ticket-status";

interface StatusBadgeProps {
  status: RequestStatus;
  className?: string;
}

// Define the configuration type
interface StatusConfig {
  label: string;
  textColor: string;
}

// Create a statusConfig object with RequestStatus keys only
const statusConfig: Partial<Record<RequestStatus, StatusConfig>> = {
  [RequestStatus.Approved]: {
    label: "Approved",
    textColor: "text-success",
  },
  [RequestStatus.Pending]: {
    label: "Pending",
    textColor: "text-warning",
  },
  [RequestStatus.Rejected]: {
    label: "Rejected",
    textColor: "text-danger",
  },
  [RequestStatus.ForApproval]: {
    label: "For Approval",
    textColor: "text-info",
  },
  [RequestStatus.Assigned]: {
    label: "Assigned",
    textColor: "text-saffron-mango-500",
  },
  [RequestStatus.Overdue]: {
    label: "Overdue",
    textColor: "text-danger-dark",
  },
  [RequestStatus.Resolved]: {
    label: "Resolved",
    textColor: "text-success",
  },
  [RequestStatus.Unresolved]: {
    label: "Unresolved",
    textColor: "text-danger",
  },
  [RequestStatus.InProgress]: {
    label: "In Progress",
    textColor: "text-info",
  },
  [RequestStatus.Reconsidered]: {
    label: "Reconsidered",
    textColor: "text-purple-500", // Choose an appropriate color
  },
  [RequestStatus.Low]: {
    label: "Low",
    textColor: "text-[#464646]",
  },
  [RequestStatus.Medium]: {
    label: "Medium",
    textColor: "text-[#E3C000]",
  },
  [RequestStatus.High]: {
    label: "High",
    textColor: "text-[#FFA500]",
  },
  [RequestStatus.Critical]: {
    label: "Critical",
    textColor: "text-[#FF0000]",
  },
};

export function StatusBadge({ status, className = "" }: StatusBadgeProps) {
  const config = statusConfig[status] || {
    label: status,
    textColor: "text-black",
  };

  return <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-poppins-medium ${config.textColor} ${className}`}>{config.label}</span>;
}
