
import React, { useEffect } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import Button from "@components/common/Button";

//For the chart
import { Pie, PieChart, ResponsiveContainer, Tooltip as RechartsTooltip, Cell, PieLabelRenderProps } from 'recharts';
import { Chart as ChartJS, CategoryScale, ArcElement, LinearScale, BarElement, Title, Tooltip, Legend } from "chart.js";

//Images and Icons are stored here.
import { FiChevronRight } from "react-icons/fi";
import dashboard from "@assets/dashboard/male_user.gif";
import ColumnChart from "../IncomingOutgoingCashierDashboard.tsx/charts/columnChart";
import { MdFilterList } from "react-icons/md";
import { AiOutlineDownload } from "react-icons/ai";

// Register the components you plan to use
ChartJS.register(CategoryScale, LinearScale, BarElement, ArcElement, Title, Tooltip, Legend);

//For the Sales Dashboard
import { useSalesDashboardActions } from "@state/reducer/dashboard-sales";

const SalesDashboard: React.FC = () => {
  //For getting the user data
  const user = useSelector((state: RootState) => state.auth?.user?.data);
  const { postSalesDashboard } = useSalesDashboardActions();
  const salesDashboard = useSelector((state: RootState) => state.salesDashboard.salesDashboard.data);
  const salesDashboardLoading = useSelector((state: RootState) => state.salesDashboard.postSalesDashboard.loading);
  const salesDashboardError = useSelector((state: RootState) => state.salesDashboard.postSalesDashboard.error);
  //The data that will be retrieved
  const proposalLineBar = salesDashboard?.proposalLineBar;
  const productsSold = salesDashboard?.productSoldDonut;
  const proposalTable = salesDashboard?.proposalTable;
  const overallDonut = salesDashboard?.overallDonut;

  //We move the data in the object to an actual array
  const productSoldArray = Object.entries(productsSold ?? {}).map(([product, totalSold]) => ({ product, totalSold }));
  const proposalLineBarArray = Object.entries(proposalLineBar ?? {}).map(([label, data]) => ({ label, data }));

  //To get the data from the API
  useEffect(() => {
    postSalesDashboard({});
  }, []);
  

  //Dummy data 
  const data = {
    labels: proposalLineBarArray?.map(item => item.label),
    datasets: [
      {
        label: "Registered Cooperatives 2024",
        data: proposalLineBarArray?.map(item => item.data),
        backgroundColor: "#1213eb",
        borderRadius: 3,
        barThickness: 30, // Sets the exact width of each bar in pixels
        maxBarThickness: 40, // Sets the maximum width of the bar
        categoryPercentage: 0.8, // Controls the width of the category relative to the entire bar chart
        barPercentage: 0.9, // Controls the width of the bars relative to the category width
      },
    ],
  };

  const columnCategories = data.labels;
  const columnSeries = data.datasets.map(ds => ({ name: ds.label, data: ds.data }));
  const columnColors = data.datasets.map(ds =>
    Array.isArray(ds.backgroundColor) ? (ds.backgroundColor[0] as string) : (ds.backgroundColor as string || "#1213eb")
  );

  //For the pie chart
  
  
  const data2 = [
    { name: 'Life Products', value: productSoldArray[0]?.totalSold, color: '#042882' },
    //{ name: 'Non-Life Products', value: productsSold?.[1]?.totalSold, color: '#FFD000' },
    // { name: 'Micro Life Products', value: productsSold?.[2]?.totalSold, color: '#28A845' }, 
    // { name: 'Micro Non-Life Products', value: productsSold?.[3]?.totalSold, color: '#6A31FF' }, 
  ];
  const data3 = [
    { name: 'Pending', value: overallDonut?.pendingTotal, color: '#FFD000' },
    { name: 'Approved', value: overallDonut?.total-overallDonut?.pendingTotal, color: '#28A845' },
  ];

  const renderLabel = (palette: any[]) => (props: PieLabelRenderProps) => {
    const { cx, cy, midAngle, outerRadius, index } = props as any;
    const r = outerRadius + 45;
    const x = cx + r * Math.cos(-midAngle * (Math.PI / 180));
    const y = cy + r * Math.sin(-midAngle * (Math.PI / 180));
    const isRight = x > cx;
    const color = palette[index]?.color ?? '#666';

    const lines = String(palette[index]?.name || "").split(" ");
    const firstDy = -8, lineH = 12;                 // same spacings you used
    const yLast = y + firstDy + (lines.length - 1) * lineH;
    const ySep  = yLast + 6;                        // separator line y
    const yNum  = ySep + 12;                        // number y
    const sepLen = 50;                              // separator line length

    return (
      <g>
        <text x={x} y={y} fill="#000" textAnchor={isRight ? "start" : "end"} style={{ fontSize: "11px", fontWeight: "bold" }}>
          {lines.map((line: string, i: number) => (
            <tspan key={i} x={x} dy={i === 0 ? firstDy : lineH}>{line}</tspan>
          ))}
        </text>
        <line
          x1={isRight ? x : x - sepLen}
          x2={isRight ? x + sepLen : x}
          y1={ySep}
          y2={ySep}
          stroke={color}
          strokeWidth={2}
        />
        <text x={x} y={yNum} fill={color} textAnchor={isRight ? "start" : "end"} style={{ fontSize: "11px", fontWeight: "bold" }}>
          {String(palette[index]?.value || "")}
        </text>
      </g>
    );
  };

  return (
    <div className="p-5 gap-5">
      <div className="w-full flex items-start justify-between px-5">
        <div className="flex-1 flex flex-col items-start">
          <span className="text-primary xl:text-4xl text-xl font-semibold">Welcome back, {user?.firstname}!</span>
          <span className="xl:text-sm text-xs text-gray-700">Have a nice day at work.</span>
        </div>
        <div className="relative self-start ml-8 w-[20rem] h-[300px] mb-[-50px] overflow-hidden">
          <img src={dashboard} className="absolute inset-0 w-full h-full object-cover transform scale-[1.15] translate-y-[-12%] translate-x-[5%]" />
        </div>
      </div>
      <br />
      <div className="w-full flex py-2 text-2xl font-semibold translate-y-[-25%]">
        Overview
      </div>
      {/* PARENT SA MGA CHARTS */}
      <div className="w-full h-1/2">
        <div className="w-full h-full flex gap-4">
          {" "}
          <div className="w-2/3 h-full rounded-lg border border-zinc-100 shadow-md p-5"> 
            <div className="w-full flex justify-between items-center uppercase font-semibold text-zinc-500 p-5">
              Registered Cooperatives
            </div>
             <ColumnChart 
                scrollbar={true} 
                categories={columnCategories} 
                series={columnSeries} 
                colors={columnColors} 
                useGradient 
                title=" " 
                percentage={true}
                hideToolbar={true}
              />
          </div>
          <div className="w-1/3 h-[480px] rounded-lg border border-zinc-100 shadow-md p-5">
            <div className="w-full flex justify-center items-center uppercase font-semibold text-zinc-500 text-xl">
              PRODUCTS SOLD
            </div>
            <ResponsiveContainer width="100%" height="70%">
              <PieChart>
                <Pie
                    data={data2}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    label={renderLabel(data2)}
                    outerRadius={70}
                    fill="#8884d8"
                    dataKey="value"
                  >
                  {data2.map((entry, i) => <Cell key={i} fill={entry.color} />)}
                </Pie>
                <RechartsTooltip />
              </PieChart>
            </ResponsiveContainer>
            {/* Insert Legend here: */}
            <div className="w-full flex justify-center items-center uppercase font-semibold text-zinc-500 text-xs mt-5">
              <div className="w-1/2 flex flex-col items-start">
                <div className="flex items-center gap-2 p-2">
                  <div className="w-4 h-4 bg-blue rounded-full shrink-0"></div>
                  <div>Life Products</div>
                </div>
                <div className="flex items-center gap-2 p-2">
                  <div className="w-4 h-4 bg-yellow-500 rounded-full shrink-0"></div>
                  <div>Non-Life Products</div>
                </div>
              </div>
              <div className="w-1/2 flex flex-col items-start">
                <div className="flex items-center gap-2 p-2">
                  <div className="w-4 h-4 bg-green-500 rounded-full shrink-0"></div>
                  <div>Micro Life Products</div>
                </div>
                <div className="flex items-center gap-2 p-2">
                  <div className="w-4 h-4 bg-purple-500 rounded-full shrink-0"></div>
                  <div>Micro Non-Life Products</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Redesign the table to fit to the Jira task more */}
      <div className="h-1/4 w-full flex flex-col border border-zinc-100 shadow-md mt-5 p-5 bg-white rounded-lg">
        <div className="mb-4 flex justify-between items-center border-b border-zinc-300 pb-5">
          <div className="text-xl font-semibold text-primary">PROPOSALS</div>
          {/* Change this to the buttons */}
          <div className="flex gap-5">
            <button className="btn btn-md btn-zinc-400 text-gray-700 shadow-md">
              Filter
              <MdFilterList className="pl-2" size={30} />
            </button>
            <button className="btn btn-md btn-zinc-400 text-gray-700 shadow-md">
              Export
              <AiOutlineDownload className="pl-2" size={30} />
            </button>
          </div>
        </div>
        {/* We'll split this into two parts, the main part would be the table while the other part would be the chart */}
        <div className=" w-full flex overflow-auto gap-5">
          <div className="w-2/3 h-[380px] flex flex-col items-baseline gap-2 overflow-y-auto ">
            {/* This is the header */}
            <div className="w-full flex px-6 py-2 text-sm items-baseline text-zinc-500 border-b border-zinc-300 pb-5 gap-5 sticky top-0 bg-white">
              <div className="w-1/4 flex">AREA</div>
              <div className="w-1/4 flex">COOPERATIVE</div>
              <div className="w-1/4 flex">TOTAL CREATED</div>
              <div className="w-1/4 flex">PENDING PROPOSAL</div>
            </div>
            {/* Map the data from the API */}
            {proposalTable?.map((item) => (
              <div className="w-full flex px-6 py-5 border-b border-zinc-300 text-sm gap-5">
                <div className="w-1/4 flex">{item.province + ", " + item.city}</div>
                <div className="w-1/4 flex">{item.coop_name}</div>
                <div className="w-1/4 flex">{item.total}</div>
                <div className="w-1/4 flex">{item.pendingTotal}</div>
              </div>
            ))}

          </div>
          {/* For the charts of the approvals */}
          <div className="w-1/3 h-[480px] relative border-l border-zinc-300 p-5 mt-[-80px]">
            <ResponsiveContainer width="100%" height="70%" className="mt-[60px] shrink-0">
              <PieChart>
                <Pie
                    data={data3}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    label={renderLabel(data3)}
                    outerRadius={70}
                    fill="#8884d8"
                    dataKey="value"
                  >
                  {data3.map((_, index) => (
                    <Cell key={`cell-${index}`} fill={data3[index].color} />
                  ))}
                </Pie>
                <RechartsTooltip />
              </PieChart>
            </ResponsiveContainer>
            {/* Insert Legend here: */}
            <div className="w-full flex justify-center items-center uppercase font-semibold text-zinc-500 text-xs mb-[-50px]">
              <div className="flex items-center gap-2 p-2">
                <div className="w-4 h-4 bg-green-500 rounded-full shrink-0"></div>
                <div>Approved</div>
              </div>
              <div className="flex items-center gap-2 p-2">
                <div className="w-4 h-4 bg-red-500 rounded-full shrink-0"></div>
                <div>Rejected</div>
              </div>
              <div className="flex items-center gap-2 p-2">
                <div className="w-4 h-4 bg-yellow-500 rounded-full shrink-0"></div>
                <div>Pending</div>
              </div>
            </div>

          </div>

        </div>
        
      </div>
    </div>
  );
};

export default SalesDashboard;
