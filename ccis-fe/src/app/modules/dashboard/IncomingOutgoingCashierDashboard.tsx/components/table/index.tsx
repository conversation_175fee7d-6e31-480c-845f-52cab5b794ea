import { useEffect, useState } from "react";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { FormStatus, RoleType } from "@enums/form-status";
import { IDefaultParams } from "@interface/common.interface";
import { IFormTransmittal } from "@interface/form-inventory.interface";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import { TableColumn } from "react-data-table-component";
import Typography from "@components/common/Typography";
import { getTextStatusColor } from "@helpers/text";
import { NavLink } from "react-router-dom";
import Table from "@components/common/Table";
export default function ClifsaDashboardTable() {

  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  // Add these selectors after existing useSelector calls
  const { data: FORMS } = useSelector((state: RootState) => state.formInventoryTransmittal.getTransmittalForms);
  const { getTransmittalFormsTrail } = useTransmittalFormActions();
  const loading = useSelector((state: RootState) => state.formInventoryTransmittal.getTransmittalForms.loading);

  // const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const formTypes = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const filterDivision = "";
  const filterFormType = "";
  const filterArea = "";

 

  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const sortedTransmittal = FORMS?.data?.slice().sort((a: IFormTransmittal, b: IFormTransmittal) => {
    return Number(b.id) - Number(a.id);
  });

  // Update columns to match ForReceivingTab structure
  const columns: TableColumn<any>[] = [
  

    {
        name: "Date",
        cell: (row) => {
          const date = new Date(row.createdAt);
          return date.toLocaleDateString("en-US", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
          });
        },
        width: "120px",
        ...commonSetting,
    },
    {
      name: "Type",
      cell: (row) => {
        const uniqueFormTypes = Array.from(
          new Set(
            row.padAssignments
              ?.map((assignment: { form?: { formTypeId?: number } }) => {
                const formType = formTypes.find((type) => type.id === assignment.form?.formTypeId);
                return formType ? formType.formTypeCode : null;
              })
              .filter(Boolean)
          )
        );
        return uniqueFormTypes.length > 0 ? uniqueFormTypes.join(", ") : "N/A";
      },
      ...commonSetting,
    },
  
    {
      name: "Status",
      cell: (_row) => (
        <Typography size="xs" className={getTextStatusColor(_row.status)}>
          {_row.status}
        </Typography>
      ),
    },
    {
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row) => (
        <NavLink className="btn btn-sm btn-primary" to={`/sales-executive-assistant/product-proposals-notarization/partnership-agreement/${row.id}`}>
          View Details
        </NavLink>
      ),
    },
  ];

  // Add pagination handlers
  const handleRowsChange = (newPerPage: number, pagination: number) => {
    setPageSize(newPerPage);
    setPage(pagination);
  };

  const handlePaginate = (page: number) => {
    setPage(page);
  };

  // Add fetch function
  const fetchForms = () => {
    const payload = {
      // filter: searchText,
      page: page,
      pageSize: pageSize,
      // divisionFilter: divisionFilter,
      role: RoleType.CHIEFCASHIER,
      statusFilter: FormStatus.PENDING,
    } as IDefaultParams;
    getTransmittalFormsTrail(payload);
  };

  // Add useEffects before return statement
  useEffect(() => {
    const timer = setTimeout(() => fetchForms(), 500);
    return () => clearTimeout(timer);
  }, [
   
    page, pageSize]);

  useEffect(() => {
    getDivisions({ filter: filterDivision });
    getFormTypes({ filter: filterFormType });
    getAreas({ filter: filterArea });
    fetchForms();
  }, []);
  return (
    <div className="w-full mt-10">
      <div className="mb-4 lg:text-lg text-sm">
        <div>Continue where you left off ...</div>
      </div>
      <Table
        className="!min-h-[100%] border-[1px] border-zinc-300"
        columns={columns}
        data={sortedTransmittal}
        paginationServer={true}
        paginationTotalRows={FORMS?.meta?.total}
        loading={loading}
        onChangeRowsPerPage={handleRowsChange}
        onPaginate={handlePaginate}
        searchable={false}
        multiSelect={false}
      />
    </div>
  );
}
