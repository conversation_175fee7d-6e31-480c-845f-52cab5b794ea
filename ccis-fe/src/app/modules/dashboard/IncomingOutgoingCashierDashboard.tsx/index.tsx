import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import dashboard from "@assets/dashboard_img.png";
import AreaChart from "./charts/areaChart";
import DonutChart from "./charts/donutChart";
import ColumnChart from "./charts/columnChart";
import { useFormInventoryDashboardsActions } from "@state/reducer/form-inventory-dashboards";
import ClifsaDashboardTable from "./components/table";
import { normalizeRoleName } from "@helpers/roleChecker";
import { UserRoles } from "@interface/routes.interface";
import Loader from "@components/Loader";

type SelectedCategory =
  | "Sales Invoice"
  | "Official Receipt"
  | "Provisional Receipt";
type SelectedPeriod = "Weekly" | "Monthly" | "Quarterly" | "Annually";
type ChartType = "area" | "line";

const IncomingOutgoingCashierDashboard: React.FC = () => {
  const user = useSelector((state: RootState) => state.auth?.user?.data);

    // Normalize the user's role name for comparison
  const isAreaAdmin = user.roles[0].name === UserRoles.areaAdmin; // "incoming-outgoing-cashier"
  const isClifsaAdmin = normalizeRoleName(user.roles[0].name) === UserRoles.clifsaAdmin; // "clifsa-admin"
  const isTreasury = normalizeRoleName(user.roles[0].name) === UserRoles.treasury; // "treasury-officer"
  const isGam = normalizeRoleName(user.roles[0].name) === UserRoles.gam; // "gam"
  const isChiefCashier = normalizeRoleName(user.roles[0].name) === UserRoles.chiefCashier; // "chief-cashier"
  const isAdminOutgoing = normalizeRoleName(user.roles[0].name) === UserRoles.outgoingAdmin; // "outgoing-admin"
  const isAdminIncoming = normalizeRoleName(user.roles[0].name) === UserRoles.incomingAdmin; // "outgoing-admin"
  const isIncomingOutgoingCashier = user.roles[0].name === UserRoles.ioc; // "incoming-outgoing-cashier"
  const isAdminSatellite = normalizeRoleName(user.roles[0].name) === UserRoles.adminSatellite; // "admin-satellite"


  // AreaChart state and data
  const [chartType] = useState<ChartType>("area");
  const [chartHeight] = useState<number | string>(300);
  const [enableDataLabels] = useState(false);
  const [selectedCategory, setSelectedCategory] =
    useState<SelectedCategory>("Sales Invoice");
  const [selectedPeriod, setSelectedPeriod] =
    useState<SelectedPeriod>("Weekly");
  const [intervalCount] = useState<number>(3);

  const { getSpecificClifsaDashboard, getAdminClifsaAdmin} = useFormInventoryDashboardsActions();
    
  // Get the dynamic data from Redux store
  const specificRoleDashboard = useSelector((state: RootState) => state.fromInventoryDashboards?.getSpecificClifsaDashboard?.data);
  const adminRoleDashboard = useSelector((state: RootState) => state.fromInventoryDashboards?.getAdminClifsaAdmin?.data);
  const specificRoleDashboardLoadingState = useSelector((state: RootState) => state.fromInventoryDashboards?.getSpecificClifsaDashboard?.loading);
  const adminRoleDashboardLoadingState = useSelector((state: RootState) => state.fromInventoryDashboards?.getAdminClifsaAdmin?.loading);

  useEffect(() => {
    if (isGam || isClifsaAdmin || isAreaAdmin || isAdminOutgoing || isAdminIncoming || isIncomingOutgoingCashier || isAdminSatellite) {
      getSpecificClifsaDashboard({
        metricType: "form",
        params: { interval: intervalCount }
      });
    }
    if ( isTreasury || isChiefCashier) {
        getAdminClifsaAdmin({
        params: { interval: intervalCount }
      })
    }
  }, [])

  // Function to get data with fallback to static data

  const getChartData = () => {
    // Return data based on user role
    if (( isTreasury || isChiefCashier) && adminRoleDashboard) {
      return adminRoleDashboard;
    } 
    if (( isGam || isClifsaAdmin || isAdminOutgoing || isAdminIncoming || isIncomingOutgoingCashier || isAreaAdmin || isAdminSatellite ) && specificRoleDashboard) {
      return specificRoleDashboard;
    }
    // Fallback to static test data if no role-specific data is available
    return null;
  };

  const currentData = getChartData();

  // AreaChart data - now dynamic
  const areaChartData: {
    [key in "Sales Invoice" | "Official Receipt" | "Provisional Receipt"]: {
      [key in "Weekly" | "Monthly" | "Quarterly" | "Annually"]: {
        released: number[];
        returned: number[];
      };
    };
  } = {
    "Sales Invoice": {
      Weekly: {
        released: currentData?.["Sales Invoice"]?.Weekly?.released || [],
        returned: currentData?.["Sales Invoice"]?.Weekly?.returned || [],
      },
      Monthly: {
        released: currentData?.["Sales Invoice"]?.Monthly?.released || [],
        returned: currentData?.["Sales Invoice"]?.Monthly?.returned || [],
      },
      Quarterly: {
        released: currentData?.["Sales Invoice"]?.Quarterly?.released || [],
        returned: currentData?.["Sales Invoice"]?.Quarterly?.returned || [],
      },
      Annually: {
        released: currentData?.["Sales Invoice"]?.Annually?.released || [],
        returned: currentData?.["Sales Invoice"]?.Annually?.returned || [],
      },
    },
    "Official Receipt": {
      Weekly: {
        released: currentData?.["Official Receipt"]?.Weekly?.released || [],
        returned: currentData?.["Official Receipt"]?.Weekly?.returned || [],
      },
      Monthly: {
        released: currentData?.["Official Receipt"]?.Monthly?.released || [],
        returned: currentData?.["Official Receipt"]?.Monthly?.returned || [],
      },
      Quarterly: {
        released: currentData?.["Official Receipt"]?.Quarterly?.released || [],
        returned: currentData?.["Official Receipt"]?.Quarterly?.returned || [],
      },
      Annually: {
        released: currentData?.["Official Receipt"]?.Annually?.released || [],
        returned: currentData?.["Official Receipt"]?.Annually?.returned || [],
      },
    },
    "Provisional Receipt": {
      Weekly: {
        released: currentData?.["Provisional Receipt"]?.Weekly?.released || [],
        returned: currentData?.["Provisional Receipt"]?.Weekly?.returned || [],
      },
      Monthly: {
        released: currentData?.["Provisional Receipt"]?.Monthly?.released || [],
        returned: currentData?.["Provisional Receipt"]?.Monthly?.returned || [],
      },
      Quarterly: {
        released: currentData?.["Provisional Receipt"]?.Quarterly?.released || [],
        returned: currentData?.["Provisional Receipt"]?.Quarterly?.returned || [],
      },
      Annually: {
        released: currentData?.["Provisional Receipt"]?.Annually?.released || [],
        returned: currentData?.["Provisional Receipt"]?.Annually?.returned || [],
      },
    },
  };

  // ColumnChart data - now dynamic
  const columnChartData = {
    categories: currentData?.chartDonutData?.labels,
    series: [
      {
        name: "Released",
        data: currentData?.chartData?.dataX || [],
      },
      {
        name: "Returned",
        data: currentData?.chartData?.dataY || [],
      },
    ],
    colors: ["#042882", "#E3C000"], // Released: Blue | Returned: Yellow
  };

  // DonutChart data - now dynamic
  const donutChartData = {
    series: currentData?.chartDonutData?.series || [],
    labels: currentData?.chartDonutData?.labels || [],
    colors: currentData?.chartDonutData?.colors || [],
  };

  // Calculate total for DonutChart
  const donutTotal = donutChartData.series.reduce((acc: any, val:any) => acc + val, 0);

  // Generate AreaChart series based on selected category and period
  const areaChartSeries = [
    {
      name: "Released",
      data: areaChartData[selectedCategory][selectedPeriod].released,
    },
    {
      name: "Returned",
      data: areaChartData[selectedCategory][selectedPeriod].returned,
    },
  ];

  const getXaxisLabels = () => {
    switch (selectedPeriod) {
      case "Weekly":
        return currentData?.xAxisLabels?.Weekly || [];
      case "Monthly":
        return currentData?.xAxisLabels?.Monthly || [];
      case "Quarterly":
        return currentData?.xAxisLabels?.Quarterly || [];
      case "Annually":
        return currentData?.xAxisLabels?.Annually || [];
      default:
        return [];
    }
  };

  const xaxis = {
    categories: getXaxisLabels(),
    type: "category" as const,
  };

  if (specificRoleDashboardLoadingState || adminRoleDashboardLoadingState) {
  return (
      <div className="flex flex-1 w-full items-center justify-center">
        <Loader />
      </div>
    );
  }


  return (
    <div className="h-full w-full ">
      <div className="h-1/3 w-full flex items-center justify-center">
        <div className="w-2/3 h-full flex flex-col items-start justify-center p-4 ">
          <span className="text-primary xl:text-4xl text-xl font-semibold mb-2">
            Welcome back, {user?.firstname}
          </span>
          <span className="xl:text-sm text-xs text-zinc-700">
            Have a nice day at work.
          </span>
        </div>
        <div className="w-1/3 h-full flex items-center justify-center ">
          <img src={dashboard} className="" />
        </div>
      </div>
      <br />
      <div className="flex flex-col lg:flex-row justify-between gap-4">
        <div className="flex justify-start w-full lg:w-2/3">
          <div className="w-full">
            <div className="w-full py-7 pb-0">
              <div className="xl:text-xl text-md">Overview</div>
            </div>
            <div>
              <AreaChart
                chartType={chartType}
                chartHeight={chartHeight}
                enableDataLabels={enableDataLabels}
                selectedCategory={selectedCategory}
                setSelectedCategory={setSelectedCategory}
                selectedPeriod={selectedPeriod}
                setSelectedPeriod={setSelectedPeriod}
                chartSeries={areaChartSeries}
                xaxis={xaxis}
                isClifsaAdmin={isClifsaAdmin}
                isGam={isGam}
              />
            </div>
             <ClifsaDashboardTable/>
          </div>
        </div>
        {/* Second container  */}
        <div className="w-full lg:w-1/3 min-w-[300px]">
          <div className="mt-6">
            <DonutChart
              series={donutChartData.series}
              labels={donutChartData.labels}
              colors={donutChartData.colors}
              total={donutTotal}
            />
          </div>
          <div className="mt-6">
            <ColumnChart
              categories={columnChartData.categories}
              series={columnChartData.series}
              colors={columnChartData.colors}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default IncomingOutgoingCashierDashboard;