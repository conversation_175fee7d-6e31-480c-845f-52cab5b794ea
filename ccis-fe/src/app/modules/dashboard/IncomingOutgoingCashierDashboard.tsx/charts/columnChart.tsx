import React from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';

interface ColumnChartProps {
  categories: string[];
  series: Array<{
    name: string;
    data: number[];
  }>;
  colors: string[];
  scrollbar?: boolean;
  slantText?: boolean; // Optional prop to control text slant
  useGradient?: boolean; // Optional prop to control gradient
  title?: string; // Optional prop for dynamic title
  customTooltip?: any; // Add this prop
  percentage?: boolean;
  hideToolbar?: boolean;
}

const ColumnChart: React.FC<ColumnChartProps> = ({ 
  categories, 
  series, 
  colors, 
  slantText = false, 
  scrollbar = false, 
  useGradient = false, 
  title = 'Document Status', 
  customTooltip, 
  percentage = false,
  hideToolbar = false,
}) => {
  const chartOptions: ApexOptions = {
    chart: {
      type: 'bar',
      height: 350,
      width: scrollbar ? categories.length * 80 : '100%', // Dynamic width based on categories
      toolbar: {
        show: !hideToolbar,
      },
    },
    plotOptions: {
      bar: {
        borderRadius: 4,
        horizontal: false,
        dataLabels: {
          position: 'top',
        },
      },
    },
    dataLabels: {
      enabled: false,
    },
    xaxis: {
      categories: categories,
      labels: {
        //Minor bugfix to avoid text from hiding if slantText is off.
        rotate: slantText ? -45 : 0,
        hideOverlappingLabels: false,
        trim: false,
      },
    },
    yaxis: {
      labels: {
        formatter: function (val: number) {
          return percentage ? val.toString() + "%" : val.toString();
        }
      }
    },
    title: {
      text: title, 
      align: 'center',
      style: {
        fontSize: '20px',
        fontWeight: 'bold',
      },
    },
    colors: colors,
    fill: useGradient
      ? {
          type: 'gradient',
          gradient: {
            shade: 'dark',
            type: 'vertical', // Gradient direction
            shadeIntensity: 0.8,
            gradientToColors: colors, // Gradient end colors
            inverseColors: true,
            opacityFrom: 1.2,
            opacityTo: 0.8,
            stops: [0, 100],
          },
        }
      : {
          type: 'solid', // Use solid fill if gradient is not enabled
        },
    tooltip: customTooltip || {
      enabled: true,
      y: {
        formatter: function (val: number) {
          return val + "%";
        }
      }
    },
  };

  return (
    <div className={`bg-white`}>
      <style>{`
        .apexcharts-tooltip {
          transform: translateY(25px) !important; // Minor change since I can't find the apex charts tooltip
        }
      `}</style>
      <Chart options={chartOptions} series={series} type="bar" height={360} />
    </div>
  );
};

export default ColumnChart;
