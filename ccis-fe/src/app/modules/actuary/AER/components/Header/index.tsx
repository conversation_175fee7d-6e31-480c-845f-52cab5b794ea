import React, { useEffect, useState } from "react";
import TextField from "@components/form/TextField";
import Select1 from "@components/form/Combo-box";
import { FormikProvider, useFormik, Form } from "formik";
import { useCooperativesManagementActions } from "@state/reducer/cooperatives";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { useLocation } from "react-router-dom";
import { useSelectOptions } from "@hooks/useSelectOptions";

interface ReusableFormProps {
  initialValues: any;
  onSubmit: (values: any, actions: any) => void;
  updateStatus: (values: any) => void;
  onClickUploadMasterlist?: () => void;
}

const Header: React.FC<ReusableFormProps> = ({ initialValues, onSubmit, updateStatus }) => {
  const location = useLocation();
  const { getCooperatives } = useCooperativesManagementActions();
  const cooperatives = useSelector((state: RootState) => state?.cooperatives?.cooperatives);
  const [selectedCoop, setSelectedCoop] = useState<any>({});
  const contestability = useSelector((state: RootState) => state?.contestability?.contestabilities ?? []);

  const [openComboBox, setOpenComboBox] = useState(false);
  const [searchText, setSearchText] = useState("");

  const contestabilityOptions = useSelectOptions({
    data: contestability,
    firstOptionText: "Select contestability",
    valueKey: "id",
    textKey: "label",
  });

  const formik = useFormik({
    initialValues,
    onSubmit,
  });
  useEffect(() => {
    if (selectedCoop) {
      formik.setFieldValue("selectedCoop", selectedCoop);
      formik.setFieldValue("cooperativeId", selectedCoop.id);
    }
  }, [selectedCoop]);

  useEffect(() => {
    updateStatus(formik.values);
  }, [formik.values]);

  const handleSelectedCoop = (coop?: any) => {
    setSelectedCoop(coop);
    setSearchText(coop?.coopName);
    setOpenComboBox(false);
  };

  useEffect(() => {
    getCooperatives({ payload: { filter: "" } });
  }, []);

  useEffect(() => {
    getCooperatives({ payload: { filter: searchText } });
  }, [searchText]);

  useEffect(() => {
    if (location?.state?.selectedQuotation) {
      formik.setFieldValue("selectedCoop", location.state.selectedQuotation.quotation.cooperative);
      formik.setFieldValue("cooperative", location.state.selectedQuotation.quotation.cooperative.coopName);
      setSearchText(location.state.selectedQuotation.quotation.cooperative.coopName);
      //formik.setFieldValue("branch", location.state.selectedQuotation.quotation.branch);
      formik.setFieldValue("previousProvider", location.state.selectedQuotation.quotation.previousProvider);
      formik.setFieldValue("contestability", location.state.selectedQuotation.quotation.contestability);
      formik.setFieldValue("totalNoOfMembers", location.state.selectedQuotation.quotation.totalNumberOfMembers);
      formik.setFieldValue("premiumBudget", location.state.selectedQuotation.quotation.gyrtQuotations.premiumBudget);
      formik.setFieldValue("averageAge", location.state.selectedQuotation.quotation.gyrtQuotations.averageAge);
      formik.setFieldValue("averageClaims", location.state.selectedQuotation.quotation.gyrtQuotations.averageClaims);
      formik.setFieldValue("udd", location.state.selectedQuotation.quotation.gyrtQuotations.udd);
      formik.setFieldValue("lossRatio", location.state.selectedQuotation.quotation.gyrtQuotations.lossRatio);
    }
  }, []);

  return (
    <FormikProvider value={formik}>
      <Form className="p-4">
        <div className="text-4xl font-poppins-semibold text-zinc-700">GYRT WORKSHEET</div>

        <div className="w-full flex flex-col gap-4 pt-10">
          <div className="w-full flex">
            <div className="w-1/6 flex items-center ">Cooperative</div>
            <div className="w-5/6 relative">
              <div>
                <TextField
                  disabled
                  className="border-zinc-300"
                  name="cooperative"
                  error={formik.touched.cooperative && !!formik.errors.cooperative}
                  errorText="Cooperative is required"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  placeholder="Search Coop"
                  onClick={() => setOpenComboBox(true)}
                />
              </div>

              {openComboBox && (
                <div className="absolute top-14 rounded w-full h-80 overflow-y-auto  border border-zinc-300 bg-zinc-50">
                  {cooperatives.map((coop: any) => (
                    <div key={coop?.id} className="h-12 w-full p-4 hover:bg-zinc-200 cursor-pointer" onClick={() => handleSelectedCoop(coop)}>
                      {coop?.coopName}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="w-full flex gap-4">
            {/* <div className="w-1/2 flex ">
              <div className="w-1/3 flex  items-center ">Branch</div>
              <div className="w-2/3">
                <TextField
                  placeholder="Enter Branch"
                  error={formik.touched.branch && !!formik.errors.branch}
                  errorText="Branch is required"
                  name="branch"
                  value={formik.values.branch}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  className="border-zinc-300"
                />
              </div>
            </div> */}
            <div className="w-full flex">
              <div className="w-1/6 flex items-center text-start pr-4 ">Previous Provider</div>
              <div className="w-5/6">
                <TextField
                  disabled
                  placeholder="Enter Previous Provider"
                  name="previousProvider"
                  value={formik.values.previousProvider}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  className="border-zinc-300"
                />
              </div>
            </div>
          </div>

          <div className="w-full flex">
            <div className="w-1/6 flex items-center ">Contestability</div>
            <div className="w-2/6">
              <Select1
                disabled
                name="contestability"
                value={formik.values.contestability ?? ""}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                options={contestabilityOptions}
                error={formik.touched.contestability && !!formik.errors.contestability}
              />
            </div>

            {/* <div className="w-3/6 flex items-center justify-end">
              <label
                htmlFor="file-upload"
                className="flex gap-2 bg-info hover:bg-sky-400 felx justify-center items-center text-white p-3 px-5 cursor-pointer rounded-md"
                onClick={onClickUploadMasterlist}
              >
                <IoCloudUploadOutline size={20} />
                Upload Masterlist
              </label>
            </div> */}
          </div>
        </div>

        <div className="w-full mt-6 flex gap-8">
          <div className="w-1/4 flex flex-col gap-2">
            <div>
              Premium Budget <span className="text-xs text-zinc-500">( Optional )</span>
            </div>
            <div>
              <TextField disabled type="number" value={formik.values.premiumBudget} onBlur={formik.handleBlur} onChange={formik.handleChange} className="border-zinc-300" />
            </div>
          </div>

          <div className="w-1/4 flex flex-col gap-2">
            <div>Total Number of Members</div>
            <div>
              <TextField disabled type="number" name="totalNoOfMembers" value={formik.values.totalNoOfMembers} onBlur={formik.handleBlur} onChange={formik.handleChange} className="border-zinc-300" />
            </div>
          </div>

          <div className="w-1/4 flex flex-col gap-2">
            <div>Average Age </div>
            <div>
              <TextField disabled type="number" name="averageAge" value={formik.values.averageAge} onBlur={formik.handleBlur} onChange={formik.handleChange} className="border-zinc-300" />
            </div>
          </div>

          <div className="w-1/4 flex flex-col gap-2">
            <div>Average Claims</div>
            <div>
              <TextField disabled type="number" name="averageClaims" value={formik.values.averageClaims} onBlur={formik.handleBlur} onChange={formik.handleChange} className="border-zinc-300" />
            </div>
          </div>
        </div>

        {/* <div className="w-full flex  gap-8  border-b border-zinc-300 py-6">
          <div className="w-1/3">
            <div>Projected No. of Death</div>
            <div>
              <TextField type="number" name="udd" value={formik.values.udd} onBlur={formik.handleBlur} onChange={formik.handleChange} className="w-full border-zinc-300" />
            </div>
          </div>
          <div className="w-1/4">
            <div>Loss Ratio</div>
            <div>
              <TextField type="number" name="lossRatio" value={formik.values.lossRatio} onBlur={formik.handleBlur} onChange={formik.handleChange} className="w-full border-zinc-300" />
            </div>
          </div>
        </div> */}
      </Form>
    </FormikProvider>
  );
};

export default Header;
