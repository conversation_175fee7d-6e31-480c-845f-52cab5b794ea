import { ChangeEvent, FC, Fragment, useEffect, useState, useRef } from "react";
import { useSelector } from "react-redux";
import { TableColumn } from "react-data-table-component";
import { RootState } from "@state/store";
// import { CiEdit } from "react-icons/ci";
// import { IActions } from "@interface/common.interface";
import Table from "@components/common/Table";
// import ActionDropdown from "@components/common/ActionDropdown";
import { useDebouncedCallback } from "use-debounce";
import { formatDate } from "@helpers/date";
import Typography from "@components/common/Typography";
import { FaSearch } from "react-icons/fa";
import TextField from "@components/form/TextField";
import { capitalizeFirstLetterWords, getTextStatusColor } from "@helpers/text";
import { useNavigate } from "react-router-dom";
import { ROUTES } from "@constants/routes";
// import { ProposalStatus } from "@enums/proposal-status";
// import { toast } from "react-toastify";
// import { GoVersions } from "react-icons/go";
import Modal from "@components/common/Modal";
import Button from "@components/common/Button";
import { LuSearch } from "react-icons/lu";
import { IGuidelineContent, IGuidelineContentTable } from "@interface/guidelines.interface";
import { useProductActions } from "@state/reducer/products";
import { IProduct, IProductRevisions } from "@interface/products.interface";
import Loader from "@components/Loader";
import { useFipAERActions } from "@state/reducer/actuary-fip-aer";
import { Status } from "@constants/global-constant-value";

const QoutationRequestTable: FC = () => {
  const navigate = useNavigate();
  const [searchText, setSearchText] = useState<string | undefined>("");
  const [page, setPage] = useState<number>(1);
  const [rows, setRows] = useState<number>(10);
  const [loading, setLoading] = useState<boolean>(false);
  const totalRows = useSelector((state: RootState) => state.fipAER.totalRows);

  const { getProducts } = useProductActions();
  const products = useSelector((state: RootState) => state.products.getProducts?.data?.data);
  const [chosenProduct, setChosenProduct] = useState<IProduct | null>(null);
  const [chosenLatestProductRevision, setChosenLatestProductRevision] = useState<IProductRevisions | null>(null);

  const FIP = useSelector((state: RootState) => state.fipAER.getFipAERS?.data);
  const { getFipAERS } = useFipAERActions();

  useEffect(() => {
    if (FIP) {
      setLoading(false);
    }
  }, [FIP]);

  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const [productModal, setProductModal] = useState<boolean>(false);

  const handleChooseProductModal = () => {
    setProductModal((prev) => !prev);
  };

  const productGuidelines = chosenLatestProductRevision?.productGuidelines ?? [];

  const commissionStructure = chosenLatestProductRevision?.commission ?? null;

  const standard =
    commissionStructure?.commissionDetails?.filter((row: any) => {
      return row.commissionAgeType?.name?.toLocaleLowerCase() !== "standard";
    }) ?? [];
  const sectionRefs = useRef<(HTMLDivElement | null)[]>([]);

  // const getActionEvents = (productProposal: IProductProposal): IActions<IProductProposal>[] => {
  //   const actions: IActions<IProductProposal>[] = [
  //     ...(productProposal.status === ProposalStatus.active
  //       ? [
  //           {
  //             name: "View",
  //             event: (data: IProductProposal) => {
  //               navigate(ROUTES.ADMIN.viewProductProposal.parse(data.id), { state: { proposal: data } });
  //             },
  //             icon: GoVersions,
  //             color: "primary",
  //           },
  //         ]
  //       : []),
  //     ...(productProposal.status === ProposalStatus.draft
  //       ? [
  //           {
  //             name: "Edit",
  //             event: (data: IProductProposal) => {
  //               navigate(ROUTES.ADMIN.editProductProposal.parse(data.id), { state: { proposal: data } });
  //             },
  //             icon: CiEdit,
  //             color: "primary",
  //           },
  //         ]
  //       : []),
  //   ];

  //   return actions;
  // };

  const columns: TableColumn<any>[] = [
    {
      name: "Product Name",
      cell: (row) => row?.product?.name ?? "Not Set",
      ...commonSetting,
    },
    {
      name: "Cooperative",
      cell: (row) => row?.quotation?.cooperative?.coopName ?? "Not Set",
      ...commonSetting,
    },

    {
      name: "Creation Date",
      cell: (row) => formatDate(row?.createdAt, "d MMMM yyyy"),
      ...commonSetting,
    },
    {
      name: "Status",
      cell: (row) => (
        <Typography size="xs" className={`${getTextStatusColor(row.status)}`}>
          {capitalizeFirstLetterWords(row.status, "_")}
        </Typography>
      ),
    },

    // {
    //   name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
    //   cell: (_row, _rowIndex) => {
    //     return (
    //       <div className="flex flex-1 flex-row justify-center items-center gap-x-2">
    //         {/* {deleting && <FaSpinner className="animate-spin" />}
    //         {!deleting && <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />} */}
    //       </div>
    //     );
    //   },
    // },
  ];

  const handleSearch = useDebouncedCallback((event: ChangeEvent<HTMLInputElement>) => {
    // Extract the current input value from the event
    const value = event.target.value;
    setSearchText(value);
  }, 500);

  const handlePaginate = (pagination: number) => {
    setPage(pagination);
  };

  const handleRowsChange = (rowsPerPage: number, pagination: number) => {
    setRows(rowsPerPage);
    setPage(pagination);
  };

  // const prod = [{ name: "Coop Life Savings Plan" }, { name: "Coop Life Protection Plan" }, { name: "Group Yearly Renewable Term" }, { name: "Family Insurance Plan" }];

  useEffect(() => {
    getProducts({
      params: {
        page: 1,
        pageSize: 10,
        statusFilter: Status.approved,
        parentFilter: "",
      },
    });
  }, []);

  useEffect(() => {
    getProducts({
      params: {
        page: 1,
        pageSize: 10,
        statusFilter: Status.approved,
        parentFilter: "",
        filter: searchText,
      },
    });
  }, [searchText]);

  const handleChose = (product?: IProduct) => {
    if (product) {
      setChosenProduct(product);
      const latestApprovedRevision = product.productRevisions
        ?.filter((revision: IProductRevisions) => revision.approvalStatus === Status.approved)
        .sort((a: IProductRevisions, b: IProductRevisions) => (b as any).id - (a as any).id)[0];
      if (latestApprovedRevision) {
        setChosenLatestProductRevision(latestApprovedRevision);
      }
    }
  };

  useEffect(() => {
    getFipAERS({ status: `${Status.approved + "," + Status.for_signatory}`, condition: "orWhere", page: page, rows: rows });
    setLoading(true);
  }, [page, rows]);

  return (
    <Fragment>
      <Modal isOpen={productModal} onClose={handleChooseProductModal} modalContainerClassName="max-w-6xl" title="Select Product for Qoutation" titleClass="text-xl font-poppins-semibold">
        <div className="h-[40rem] bg-zinc-100 rounded-md flex justify-between gap-4 w-full p-4">
          <div className="w-1/3 ">
            <TextField placeholder="Search Product" onChange={handleSearch} rightIcon={<LuSearch className="text-accent " />} variant="primary" />

            <div className="flex flex-col gap-2 mt-2 h-[90%] overflow-auto ">
              {" "}
              {products?.length !== 0 &&
                products?.map((product: any) => (
                  <div
                    onClick={() => {
                      handleChose(product);
                    }}
                    className={`p-4 text-sm  rounded-md text-start cursor-pointer ${chosenProduct?.id === product?.id ? "bg-primary text-white" : " bg-white hover:bg-sky-50 "}`}
                    key={product?.id}
                  >
                    {product?.name}
                  </div>
                ))}
              {products?.length === 0 && <div className="mt-4 text-center w-full h-full flex items-center justify-center">No Results Found</div>}
              {!products && (
                <div className="w-full h-full flex items-center justify-center">
                  <Loader />
                </div>
              )}
            </div>
          </div>
          <div className="w-2/3 bg-white rounded-xl h-full overflow-auto ">
            {!chosenLatestProductRevision && <div className="flex flex-col items-center justify-center h-full text-sm ">Choose a product to display its guidelines. </div>}
            {chosenLatestProductRevision && (
              <div className="xl:p-8 p-4">
                <div className="flex flex-col">
                  {/* FIRST PRODUCT GUIDELINE*/}
                  <div className="flex flex-col xl:text-base text-xs ">
                    {productGuidelines?.map((value: any, gIndex: any) => {
                      return (
                        <div key={`guideline-${gIndex}`} className="scroll-mt-16 flex flex-1 flex-col mb-2 xl:mb-10" ref={(el) => (sectionRefs.current[gIndex] = el)}>
                          <Typography className="xl:text-xl text-sm font-poppins-semibold text-primary">{value.label}</Typography>
                          {value.productGuideline.map((pgValue: any, pgIndex: any) => {
                            let listValue;
                            let tableValue;
                            if (pgValue.type === "list") {
                              listValue = pgValue.value as IGuidelineContent[];
                            }

                            if (pgValue.type === "table") {
                              tableValue = pgValue.value as IGuidelineContentTable;
                            }

                            return (
                              <div key={`pg-${pgIndex}`} className="p-2">
                                {pgValue.type === "textfield" && (
                                  <Fragment>
                                    <Typography className="ml-4 xl:mt-4  mt-0 text-justify xl:text-base text-xs">{pgValue.value as string}</Typography>
                                  </Fragment>
                                )}
                                {pgValue.type === "list" && (
                                  <Fragment>
                                    <Typography className="ml-4 xl:mt-4  mt-0 xl:text-justify text-start xl:text-base text-xs">{pgValue.label}</Typography>
                                    <ul className="list-disc xl:ml-12 ml-6">
                                      {listValue &&
                                        listValue.map((listValue, listIndex) => {
                                          return (
                                            <li key={`listItem-${listIndex}`} className="xl:mt-4 mt-0">
                                              <Typography className="text-justify xl:text-base text-xs">{listValue.value as string}</Typography>
                                            </li>
                                          );
                                        })}
                                    </ul>
                                  </Fragment>
                                )}
                                {pgValue.type === "texteditor" && (
                                  <Fragment>
                                    <div
                                      className="ml-4 mt-2 xl:text-base text-xs "
                                      dangerouslySetInnerHTML={{
                                        __html: (pgValue as any).value ?? "",
                                      }}
                                    ></div>
                                  </Fragment>
                                )}
                                {pgValue.type === "table" && (
                                  <Fragment>
                                    <div className="flex flex-1 mt-2 mx-6 overflow-x-scroll xl:text-base text-xs">
                                      <table className="table border-[1px]">
                                        <thead className="table-header-group">
                                          <tr>
                                            {tableValue?.columns?.map((cValue, cIndex) => {
                                              return (
                                                <td key={`col-${cIndex}`} className="table-cell border-[1px] xl:text-base text-xs">
                                                  <Typography className="font-semibold text-xs">{cValue.value as string}</Typography>
                                                </td>
                                              );
                                            })}
                                          </tr>
                                        </thead>
                                        <tbody>
                                          {tableValue?.rows?.map((rValue, rIndex) => {
                                            return (
                                              <tr key={`row-${rIndex}`}>
                                                {rValue.map((cell, cellIndex) => {
                                                  return (
                                                    <td className="border-[1px] xl:text-base text-xs" key={`cell-${cellIndex}`}>
                                                      <Typography size="xs">{cell.value as string}</Typography>
                                                    </td>
                                                  );
                                                })}
                                              </tr>
                                            );
                                          })}
                                        </tbody>
                                      </table>
                                    </div>
                                  </Fragment>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      );
                    })}
                  </div>
                  {/* LAST PRODUCT GUIDELINE*/}

                  {commissionStructure && (
                    <Fragment>
                      <Typography className="font-poppins-semibold text-primary text-xl">Commission Structure</Typography>
                      <Fragment>
                        <Typography className="ml-4 mt-4 xl:text-base text-xs">
                          {parseFloat(commissionStructure.maximumDisposableRate ?? "").toFixed(2)}% Maximum Disposable Commission - Standard Rate
                        </Typography>

                        <div className="flex-flex-1 mt-6 mx-6 overflow-x-scroll">
                          <table className="table overflow-scroll">
                            <thead>
                              <tr>
                                <td className="table-cell border-1 text-center text-xs">Type</td>
                                <td className="table-cell border-1 text-center text-xs">Age Type</td>
                                {standard.length > 0 && (
                                  <Fragment>
                                    <td className="table-cell border-1 text-center text-xs">Age From</td>
                                    <td className="table-cell border-1 text-center text-xs">Age To</td>
                                  </Fragment>
                                )}
                                <td className="table-cell border-1 text-center text-xs">Rate</td>
                              </tr>
                            </thead>
                            <tbody>
                              {commissionStructure.commissionDetails?.map((rowValue: any, rowIndex: any) => {
                                return (
                                  <tr key={`commissionDetailsRow-${rowIndex}`}>
                                    <td className="table-cell border-1 text-xs text-center  font-poppins-semibold">{rowValue?.commissionType?.commissionName}</td>
                                    <td className="table-cell border-1 text-xs text-center">{rowValue?.commissionAgeType?.name}</td>
                                    {standard.length > 0 && (
                                      <Fragment>
                                        <td className="table-cell border-1 text-center text-xs">{rowValue.ageFrom}</td>
                                        <td className="table-cell border-1 text-center text-xs">{rowValue.ageTo}</td>
                                      </Fragment>
                                    )}
                                    <td className="table-cell border-1 text-center text-xs">{rowValue.rate ? parseFloat(rowValue.rate.toString()).toFixed(0) : ""}%</td>
                                  </tr>
                                );
                              })}
                            </tbody>
                          </table>
                        </div>
                      </Fragment>
                    </Fragment>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="flex w-full justify-end mt-2">
          {" "}
          <Button classNames="btn w-32 text-sm py-3 rounded-md" variant="primary" isSubmitting={!chosenLatestProductRevision} onClick={() => navigate(ROUTES.ACTUARY.createAerFIP.key)}>
            PROCEED
          </Button>
        </div>
      </Modal>{" "}
      <div className="p-4">
        <Typography className=" font-poppins-semibold text-xl mt-6 text-zinc-700">List of AER</Typography>
        <div className="flex flex-row justify-between  mt-4">
          <div className="flex flex-1 flex-row justify-start items-center gap-x-2">
            <div className="w-3/4">
              <TextField rightIcon={<FaSearch className="text-accent" />} placeholder="Search" size="xs" className="input-sm py-6 border-zinc-300" variant="primary" onChange={handleSearch} />
            </div>
          </div>
          {/* COMMENTED MAYBE GAMITON BALIK */}
          {/* <div className="w-full flex flex-1 items-center">
            <div className="flex flex-1 flex-row justify-end items-center gap-x-2">
              <Button
                classNames="btn btn-sm btn-success text-white min-w-44"
                variant="success"
                // onClick={() => navigate(ROUTES.ACTUARY.createAer.key)}
                onClick={() => handleChooseProductModal()}
                // onClick={() => navigate(ROUTES.ACTUARY.createAerFIP.key)}
              >
                Create New AER
                <FaPlus />
              </Button>
            </div>
          </div> */}
        </div>
        <div className="flex mt-4">
          <Table
            className="h-[500px]"
            columns={columns}
            data={FIP?.data || []}
            loading={loading}
            searchable={false}
            multiSelect={false}
            paginationTotalRows={totalRows}
            paginationServer={true}
            onPaginate={handlePaginate}
            onChangeRowsPerPage={handleRowsChange}
          />
        </div>
      </div>
    </Fragment>
  );
};

export default QoutationRequestTable;
