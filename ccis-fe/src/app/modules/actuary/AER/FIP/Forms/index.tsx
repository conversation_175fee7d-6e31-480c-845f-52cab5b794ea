import React, { useEffect, useState } from "react";
import { FormikProvider, Form, useFormik } from "formik";
import { IoChevronBack } from "react-icons/io5";
import Button from "@components/common/Button";
import { useNavigate } from "react-router-dom";
import { ROUTES } from "@constants/routes";
import Table from "../../components/Table";
// import TableWithHeaderButton from "../../components/TableWithHeaderButton";
import { BiMinusCircle } from "react-icons/bi";
import Radio from "@components/form/Radio";
import Modal from "@components/common/Modal";
import TextField from "@components/form/TextField";
import { RATE_PROJECT_COMMISSION_ALLOCATION } from "@constants/product-proposal";
import { data8, principalColumn, coInsuredColumn, coInsuredColumn2 } from "./data";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { formatSelectOptions } from "@helpers/array";
import { useProductBenefitsManagementActions } from "@state/reducer/utilities-product-benefits";
import { useProductCommissionManagementActions } from "@state/reducer/utilities-product-commission";
import { useLocation } from "react-router-dom";
import httpClient from "@clients/httpClient";
import * as Yup from "yup";
import { showSuccess } from "@helpers/prompt";
import { removeProperties } from "@helpers/objects";
import Select1 from "@components/form/Combo-box";
import { useContestabilityActions } from "@state/reducer/contestability";
// import { ISelectOptions } from "@interface/common.interface";
// import { IContestability } from "@interface/contestability.interface";
import CheckBox from "@components/form/CheckBox";
import { useCooperativesManagementActions } from "@state/reducer/cooperatives";
// import Wysiwyg from "@components/common/Wysiwyg";
import { IPostFIPAER } from "@interface/actuary-fip-aer.interface";
import { toast } from "react-toastify";
import { useCommissionAgeTypeSManagementActions } from "@state/reducer/utilities-commission-age-types";
// import ConditionsList from "@modules/actuary/AER/FIP/templates/condition";
import Wysiwyg from "@components/common/Wysiwyg";
// import { typeCoInsured } from "@constants/global-constant-value";
import UploadMasterListModal from "@modules/actuary/modals/UploadMasterListModal";
import BasisModal from "@modules/actuary/modals/BasisModal";
import DemographicModal from "@modules/actuary/modals/DemographicModa";
import { ProductCode } from "@enums/product-code";
import { useModalController } from "@modules/sales/controller";
import { useProductActions } from "@state/reducer/products";
import { ProductName } from "@enums/product-code";
import FormattedNumberInput from "@components/common/InputFormattedNumber";
import { postFIPAERService, calculateFIPAERService } from "@services/actuary-fip-aer/actuary-fip-aer.service";
import { useSelectOptions } from "@hooks/useSelectOptions";
import { eInsuredType } from "@constants/global-constant-value";
import { AERStatus } from "@enums/aer-status";

type TRemarks = {
  AllIn: boolean;
  WaivedContestability: boolean;
  MembersAged65to69: boolean;
  Remarks: string;
};

const Type = {
  PRIMARY: "PRIMARY",
  SECONDARY: "SECONDARY",
} as const;

const CreateAERFIP: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [principalBenefitsModal, setPrincipalBenefitsModal] = useState<boolean>(false);
  const [coInsuredBenefitsModal, setCoInsuredBenefitsModal] = useState<boolean>(false);
  // const [selectedDetail, setSelectedDetail] = useState<string>(EXPERIENCE_DATA_ANALYSIS.BASED_ON_MASTERLIST.key);
  const [commissionLoadingModal, setCommissionLoadingModal] = useState<boolean>(false);
  const [ratingModal, setRatingModal] = useState<boolean>(false);
  const { getProductBenefits } = useProductBenefitsManagementActions();
  const { getProductCommission } = useProductCommissionManagementActions();
  const benefits = useSelector((state: RootState) => state.utilitiesProductBenefits.productBenefits);
  const commissionTypes = useSelector((state: RootState) => state.utilitiesProductCommission.productCommission);
  const [_remarks, _setRemarks] = useState<TRemarks>({
    AllIn: false,
    WaivedContestability: false,
    MembersAged65to69: false,
    Remarks: "",
  });

  const { getCommissionAgeType } = useCommissionAgeTypeSManagementActions();
  const { getContestability } = useContestabilityActions();
  const { getProducts } = useProductActions();

  const mainProducts = useSelector((state: RootState) => state.products?.getMainProducts?.data?.data || []);
  const subProducts = useSelector((state: RootState) => state.products?.getSubProducts?.data?.data || []);
  const products = useSelector((state: RootState) => state.products?.getProducts?.data?.data || []);

  const ageTypes = useSelector((state: RootState) => state.utilitiesCommissionsAgeTypes?.commissionAgeTypes);
  const contestability = useSelector((state: RootState) => state.contestability?.contestabilities);
  const [condition, setCondition] = useState<string>("");
  const relationshipOptions = [
    { id: "", relationshipName: "Select Relationship" },
    {
      id: "Parent",
      relationshipName: "Parent",
    },
    {
      id: "Spouse",
      relationshipName: "Spouse",
    },
    {
      id: "Children",
      relationshipName: "Children",
    },
    {
      id: "Sibling",
      relationshipName: "Sibling",
    },
    {
      id: "Other",
      relationshipName: "Other",
    },
  ];

  const [searchText, setSearchText] = useState<string>("");
  const [openComboBox, setOpenComboBox] = useState<boolean>(false);
  const [selectedCoop, setSelectedCoop] = useState<any>({});
  const [_selectedCoopName, setSelectedCoopName] = useState<string>("");

  const [optionCount, setOptionCount] = useState<number>(1);
  const [options, setOptions] = useState<any[]>([]);
  // const [optionFipBenefits, setOptionFipBenefits] = useState<any[]>([]);
  const [optionAerOptions, setOptionAerOptions] = useState<string>("");
  const [selectedOption, setSelectedOption] = useState<number>(0); // highlight selected option

  const [relationship, setRelationship] = useState<string>(Type.PRIMARY);
  const { getCooperatives } = useCooperativesManagementActions();
  const cooperatives = useSelector((state: RootState) => state.cooperatives.cooperatives);

  //OPTIONS PARTS
  const [fipCoInsuredDependents, setFipCoInsuredDependents] = useState<any[]>([]); //option for CO-INSURED
  const [fipCoInsuredDependentsBenefits, setFipCoInsuredDependentsBenefits] = useState<any[]>([]); //option for CO-INSURED BENEFITS
  const [fipPrincipalMemberRequestData, setFipPrincipalMemberRequestData] = useState<any[]>([]); //option for PRINCIPAL
  const [fipPrincipalMemberBenefitRequestData, setFipPrincipalMemberBenefitRequestData] = useState<any[]>([]); //option for PRINCIPAL BENEFITS

  const [viewOption, setViewOption] = useState<number>(0); // view option for principal and co-insured benefits
  const [_fileName, setFileName] = useState<string>("");
  const [demographicData, setDemographicData] = useState<boolean>(false);
  const [hasDemographics, setHasDemographics] = useState<boolean>(false);

  const [fipRating, setFipRating] = useState<any[]>([]);
  const [fipCommissionLoading, setFipCommissionLoading] = useState<any[]>([]);
  const [fipProjection, setFipProjection] = useState<any[]>([]);

  // Modal controllers
  const uploadMasterListModalController = useModalController();
  const demographicModalController = useModalController();

  const handleRadio = (value: string) => {
    formik.setFieldValue("rateProjectionCommissionAllocation", value);
  };

  // const handleSelectDetail = (detail: string | null) => {
  //   setSelectedDetail(detail ?? "");
  // };

  useEffect(() => {
    getProductBenefits({ filter: "" });
    getProductCommission({ filter: "" });
    getCommissionAgeType({ filter: "" });
    getProducts({ params: { filter: "", statusFilter: "APPROVED", page: 1, pageSize: 9999 } });
  }, []);

  useEffect(() => {
    getCooperatives({ payload: { filter: searchText } });
  }, [searchText]);

  const formik = useFormik({
    initialValues: {
      selectedCoop: {},
      // branch: "",
      cooperative: "",
      cooperativeId: 0,
      previousProvider: "",
      contestability: "",
      contestabilityLabel: "",
      premiumBudget: 0,
      totalNoOfMembers: 0,
      averageAge: 0,
      averageClaims: 0,
      premiumBasis: "",
      benefits: [],
      principalTotalNetPremium: 0,
      principalTotalGrossPremium: 0,
      coInsuredTotalNetPremium: 0,
      coInsuredTotalGrossPremium: 0,
      experienceAndDataAnalysis: "",
      rateProjectionCommissionAllocation: "",
      lossRatio: "",
      basisForCalculation: "",
      projection: {
        totalPremiumNetRate: 0,
        totalPremiumGrossRate: 0,
        numberOfClaims: 0,
        amountOfClaims: 0,
        claimsRatio: 0,
      },
      rating: [],
      commissionLoading: [],
      tempPrincipalBenefit: [],
      principalBenefit: [],
      tempCoInsuredBenefit: [],
      coInsuredBenefit: [],
      rateBasedFrom: "",
      udd: 0,
      oldCommissionLoading: [],
      oldRating: [],
      fipAges: [],
    },
    validationSchema: Yup.object({
      contestability: Yup.number().required("Contestability is required"),

      cooperative: Yup.string().required("Cooperative is required"),
      totalNoOfMembers: Yup.number().required("Total number of members is required"),
      averageAge: Yup.number().required("Average age is required"),
      averageClaims: Yup.number().required("Average claims is required"),
      options: Yup.array().min(1, "At least one option is required"),
    }),
    onSubmit: () => {},
  });

  const handlePrincipalBenefitsModal = () => {
    if (formikBenefitsPrincipal.values.contestability === "") {
      toast.error("Contestability is required");
      return;
    }
    setPrincipalBenefitsModal((prev) => !prev);
  };

  const handleAddPrincipalBenefits = () => {
    formik.setFieldValue("principalBenefit", formik.values.tempPrincipalBenefit);
    handlePrincipalBenefitsModal();
  };

  const formikBenefitsPrincipal = useFormik({
    initialValues: {
      benefitId: "",
      benefitName: "",
      ageTypeId: "",
      ageType: "",
      exitAge: "",
      minimumAge: "",
      maximumAge: "",
      coverage: "",
      memberType: "",
      expectedClaims: 11,
      premiumDueToClimbsFromDataInput: 0,
      contestability: "",
      premiumBudget: 500,
      totalNoOfMembers: 989,
      averageAge: 50,
      totalNetPremium: "",
      totalGrossPremium: "",
    },
    validationSchema: Yup.object({
      benefitId: Yup.string().required("Benefit is required").notOneOf([""], "Benefit is required"),
      coverage: Yup.number().required("Coverage is required").min(1, "Coverage must be greater than 0"),
    }),
    onSubmit: (values) => {
      const selectedBenefit = benefits.find((benefit: any) => benefit.id.toString() === values.benefitId);
      if (selectedBenefit) {
        values.benefitName = selectedBenefit.benefitName;
        values.benefitId = selectedBenefit.id.toString();
      }
      const selectedAgeType = ageTypes.find((type: any) => type.id === parseInt(values.ageTypeId));

      const sanitizedData = {
        premiumBasis: formik.values.premiumBasis,
        ageType: selectedAgeType?.name,
        ageTypeId: values.ageTypeId,
        minimumAge: values.minimumAge,
        maximumAge: values.maximumAge,
        exitAge: values.exitAge,
        benefitId: values.benefitId,
        benefitName: values.benefitName,
        coverage: values.coverage,
        option: optionCount,
      };
      formik.setFieldValue("tempPrincipalBenefit", [...formik.values.tempPrincipalBenefit, sanitizedData]);
      formik.setFieldValue("ageType", " ");
      formik.setFieldValue("minimumAge", 0);
      formik.setFieldValue("maximumAge", 0);
      formik.setFieldValue("exitAge", 0);
      formik.setFieldValue("coverage", 0);
      formik.setFieldValue("benefitId", "");
    },
  });

  useEffect(() => {
    if (formikBenefitsPrincipal.values.maximumAge) {
      formikBenefitsPrincipal.setFieldValue("exitAge", formikBenefitsPrincipal.values.maximumAge + 1);
    }
  }, [formikBenefitsPrincipal.values.maximumAge]);

  const handleCoInsuredBenefitsModal = () => {
    setCoInsuredBenefitsModal((prev) => !prev);
  };

  const handleAddCoInsuredBenefits = () => {
    formik.setFieldValue("coInsuredBenefit", formik.values.tempCoInsuredBenefit);
    handleCoInsuredBenefitsModal();
  };

  const formikBenefitsCoInsured = useFormik({
    initialValues: {
      benefitId: "",
      benefitName: "",
      ageType: "",
      exitAge: 0,
      minimumAge: 0,
      maximumAge: 0,
      coverage: 0,
      maxNoOfInsured: 0,
      relationship: "",
      memberType: "",
      expectedClaims: 0,
      premiumDueToClimbsFromDataInput: 0,
      contestability: "",
      premiumBudget: 0,
      totalNoOfMembers: 0,
      averageAge: 0,
      totalNetPremium: "",
      totalGrossPremium: "",
      totalNoOfDependents: 0,
      dividedEqually: false,
      type: "",
    },
    validationSchema: Yup.object({
      benefitId: Yup.string().required("Benefit is required").notOneOf([""], "Benefit is required"),
      coverage: Yup.number().required("Coverage is required").min(1, "Coverage must be greater than 0"),
    }),
    onSubmit: (values) => {
      values.type = relationship;
      const selectedBenefit = benefits.find((benefit: any) => benefit.id.toString() === values.benefitId);
      if (selectedBenefit) {
        values.benefitName = selectedBenefit.benefitName;
        values.benefitId = selectedBenefit.id.toString();
      }

      const sanitizedData = {
        premiumBasis: formik.values.premiumBasis,
        relationship: values.relationship,
        minimumAge: values.minimumAge,
        maximumAge: values.maximumAge,
        exitAge: values.exitAge,
        maximumNumberOfInsured: values.maxNoOfInsured,
        benefitId: values.benefitId,
        benefitName: values.benefitName,
        coverage: values.coverage,
        dividedEqually: values.dividedEqually,
        type: values.type,
        option: optionCount,
      };

      formik.setFieldValue("tempCoInsuredBenefit", [...formik.values.tempCoInsuredBenefit, sanitizedData]);

      formikBenefitsCoInsured.setFieldValue("relationship", "");
      formikBenefitsCoInsured.setFieldValue("minimumAge", 0);
      formikBenefitsCoInsured.setFieldValue("maximumAge", 0);
      formikBenefitsCoInsured.setFieldValue("exitAge", 0);
      formikBenefitsCoInsured.setFieldValue("maxNoOfInsured", 0);
      formikBenefitsCoInsured.setFieldValue("coverage", 0);
      formikBenefitsCoInsured.setFieldValue("benefitId", "");
      formikBenefitsCoInsured.setFieldValue("dividedEqually", false);
      // handleCoInsuredBenefitsModal();
    },
  });

  const handleCommissionLoadingModal = () => {
    setCommissionLoadingModal((prev) => !prev);
  };
  const handleRatingModal = () => {
    setRatingModal((prev) => !prev);
  };

  const formikRating = useFormik({
    initialValues: {
      rate: 0,
      rateName: "",
      rateId: "",
    },
    onSubmit: (values, { resetForm }) => {
      const selectedCommission = commissionTypes.find((item: any) => item.id.toString() === values.rateId);
      if (selectedCommission) {
        values.rateName = selectedCommission.commissionName;
        values.rateId = selectedCommission.id.toString();
      }

      const updatedRating = [...formik.values.rating, values];
      formik.setFieldValue("rating", updatedRating);
      handleRatingModal();
      resetForm();
    },
  });

  const formikCommissionLoading = useFormik({
    initialValues: {
      commissionTypeId: "",
      commissionTypeName: "",
      rate: 0,
    },
    onSubmit: (values, { resetForm }) => {
      const selectedCommission = commissionTypes.find((item: any) => item.id.toString() === values.commissionTypeId);
      if (selectedCommission) {
        values.commissionTypeName = selectedCommission.commissionName;
        values.commissionTypeId = selectedCommission.id.toString();
      }

      const updatedCommissionLoading = [...formik.values.commissionLoading, values];
      formik.setFieldValue("commissionLoading", updatedCommissionLoading);
      handleCommissionLoadingModal();
      resetForm();
    },
  });

  useEffect(() => {
    if (location?.state?.selectedQuotation) {
      formik.resetForm();
      setSelectedCoop(location.state.selectedQuotation.quotation.cooperative);
      formik.setFieldValue("selectedCoop", location.state.selectedQuotation.quotation.cooperative);
      formik.setFieldValue("cooperative", location.state.selectedQuotation.quotation.cooperative.coopName);
      setSearchText(location.state.selectedQuotation.quotation.cooperative.coopName);
      // formik.setFieldValue("branch", location.state.selectedQuotation.quotation.branch);
      formik.setFieldValue("previousProvider", location.state.selectedQuotation.quotation.previousProvider);
      formikBenefitsPrincipal.setFieldValue("contestability", location.state.selectedQuotation.quotation.contestability);
      formik.setFieldValue("totalNoOfMembers", location.state.selectedQuotation.quotation.totalNumberOfMembers);
      formik.setFieldValue("premiumBasis", location.state.selectedQuotation?.quotation?.fipPrincipalMember?.[0]?.premiumBasis);
      formik.setFieldValue("fipAges", location.state.selectedQuotation.quotation?.fipAges);
      setCondition(location.state.selectedQuotation.quotation.quotationCondition.condition);
      basis.setFieldValue("fileName", location.state.selectedQuotation.quotation.fileName ?? "");

      formik.setFieldValue("condition", location.state.selectedQuotation.quotation.quotationCondition.condition);

      setOptions(JSON.parse(location.state.selectedQuotation.options));

      const principal = location?.state?.selectedQuotation?.quotation.fipPrincipalMember.map((item: any) => removeProperties(item, ["createdAt", "updatedAt"]));
      const principalBenefit = location?.state?.selectedQuotation?.quotation.fipPrincipalMemberBenefit.map((item: any) => removeProperties(item, ["createdAt", "updatedAt"]));
      const coInsured = location?.state?.selectedQuotation?.quotation.fipCoInsuredDependent.map((item: any) => removeProperties(item, ["createdAt", "updatedAt"]));
      const coInsuredBenefit = location?.state?.selectedQuotation?.quotation.fipCoInsuredDependentBenefit.map((item: any) => removeProperties(item, ["createdAt", "updatedAt"]));

      setFipPrincipalMemberRequestData(principal);
      setFipPrincipalMemberBenefitRequestData(principalBenefit);
      setFipCoInsuredDependents(coInsured);
      setFipCoInsuredDependentsBenefits(coInsuredBenefit);
      //options
      const highestOption = Math.max(...JSON.parse(location.state.selectedQuotation.options));
      setOptionCount(highestOption + 1);
      setOptionAerOptions(location.state.selectedQuotation.options);
    }
    getContestability({ filter: ProductCode.FIP });
    localStorage.setItem("productCode", ProductCode.FIP);
  }, []);

  useEffect(() => {
    setSelectedOption(options[0]);
  }, [options]);

  const handleCalculate = async () => {
    const commissionLoadingWithPercentage = formik.values.commissionLoading.map((item: any) => ({
      ...item,
      rate: item.rate,
    }));

    try {
      const response = await calculateFIPAERService({
        averageAge: formikBenefitsPrincipal.values.averageAge,
        averageClaims: 11,
        groupSize: formikBenefitsPrincipal.values.totalNoOfMembers,
        premiumBasis: formik.values.premiumBasis,
        fipPrincipalMemberBenefit: formik.values.principalBenefit,
        fipCoInsuredDependentBenefit: formik.values.coInsuredBenefit,
        quotationCommissionDistribution: commissionLoadingWithPercentage,
        lossRatio: formik.values.lossRatio,
        basisForCalculation: formik.values.basisForCalculation,

        contestabilityId: formik.values.contestability,
        ageType: location.state.selectedQuotation.quotation?.gyrtAge?.[0]?.ageType,
        rating: formik.values.rating,
        // udd: formik.values.udd,
      });
      if (response) {
        formik.setFieldValue("principalTotalNetPremium", response.data.principalPremium.toFixed(2));
        formik.setFieldValue("principalTotalGrossPremium", response.data.principalGrossPremium.toFixed(2));
        formik.setFieldValue("coInsuredTotalNetPremium", response.data.coInsuredDependentPremium.toFixed(2));
        formik.setFieldValue("coInsuredTotalGrossPremium", response.data.coInsuredDependentGrossPremium.toFixed(2));
        toast.success("Premiums calculated successfully!");
      }
    } catch (error: any) {
      toast.error(error.response.data.message);
    }
  };

  const hasDuplicateBenefit = (
    benefits: Array<{ benefitId: number; coverage: number; minimumAge: number; maximumAge: number; exitAge: number }>,
    optionGyrtBenefits: Array<{ benefitId: number; coverage: number; minimumAge: number; maximumAge: number; exitAge: number; option: string }>
  ): boolean => {
    return benefits.some((benefit) =>
      optionGyrtBenefits.some(
        (gyrt) =>
          gyrt.benefitId === benefit.benefitId &&
          gyrt.coverage === benefit.coverage &&
          gyrt.minimumAge === benefit.minimumAge &&
          gyrt.maximumAge === benefit.maximumAge &&
          gyrt.exitAge === benefit.exitAge
      )
    );
  };

  const hasDuplicateBenefit2 = (
    benefits: Array<{ benefitId: number; coverage: number; minimumAge: number; maximumAge: number; relationship: string }>,
    optionGyrtBenefits: Array<{ benefitId: number; coverage: number; minimumAge: number; maximumAge: number; relationship: string; option: string }>
  ): boolean => {
    return benefits.some((benefit) =>
      optionGyrtBenefits.some(
        (gyrt) =>
          gyrt.benefitId === benefit.benefitId &&
          gyrt.coverage === benefit.coverage &&
          gyrt.minimumAge === benefit.minimumAge &&
          gyrt.maximumAge === benefit.maximumAge &&
          gyrt.relationship === benefit.relationship
      )
    );
  };

  const handleSaveOption = () => {
    // Store the current option's data before resetting the forms
    // Gather all principal benefits and co-insured benefits for this option

    const result = hasDuplicateBenefit(formik.values.principalBenefit, fipPrincipalMemberBenefitRequestData);
    const result2 = hasDuplicateBenefit2(formik.values.coInsuredBenefit, fipCoInsuredDependentsBenefits);

    if (result) {
      toast.error("Duplicate benefit found on Principal Benefits!");
      return;
    }
    if (result2) {
      toast.error("Duplicate benefit found on Co-Insured Benefits!");
      return;
    }

    const principal = {
      premiumBasis: formik.values.premiumBasis,
      averageAge: formikBenefitsPrincipal.values.averageAge,
      totalNumberOfMembers: formikBenefitsPrincipal.values.totalNoOfMembers,
      grossPremium: formik.values.principalTotalGrossPremium,
      netPremium: formik.values.principalTotalNetPremium,
      contestabilityId: formikBenefitsPrincipal.values.contestability,
      premiumBudget: formikBenefitsPrincipal.values.premiumBudget,
      option: optionCount,
    };

    // Save all principal benefits for this option
    const principalBenefits = formik.values.principalBenefit.map((benefit: any) => ({
      ...benefit,
      option: optionCount,
      premiumBasis: formik.values.premiumBasis,
    }));

    const coInsured = {
      premiumBasis: formik.values.premiumBasis,
      maximumTotalNumberOfDependents: formikBenefitsCoInsured.values.maxNoOfInsured,
      grossPremium: formik.values.coInsuredTotalGrossPremium,
      netPremium: formik.values.coInsuredTotalNetPremium,
      option: optionCount,
    };

    // Save all co-insured benefits for this option
    const coInsuredBenefits = formik.values.coInsuredBenefit.map((benefit: any) => ({
      ...benefit,
      option: optionCount,
      premiumBasis: formik.values.premiumBasis,
    }));

    const commissionLoading = formik.values.commissionLoading.map((item: any) => ({
      ...item,
      option: optionCount,
    }));

    const rating = formik.values.rating.map((item: any) => ({
      ...item,
      option: optionCount,
    }));

    const projection = {
      ...formik.values.projection,
      option: optionCount,
    };

    setFipPrincipalMemberRequestData((prev) => [...prev, principal]);
    setFipPrincipalMemberBenefitRequestData((prev) => [...prev, ...principalBenefits]);
    setFipCoInsuredDependents((prev) => [...prev, coInsured]);
    setFipCoInsuredDependentsBenefits((prev) => [...prev, ...coInsuredBenefits]);

    setFipRating((prev) => [...prev, ...rating]);
    setFipCommissionLoading((prev) => [...prev, ...commissionLoading]);
    setFipProjection((prev) => [...prev, projection]);

    setOptions((prev) => [...prev, optionCount]);
    setOptionCount((prev) => prev + 1);

    formik.setFieldValue("benefits", []);
    formik.setFieldValue("totalNetPremium", "");
    formik.setFieldValue("totalGrossPremium", "");

    setOptionAerOptions(JSON.stringify(Array.from({ length: optionCount - 1 }, (_, i) => i + 1)));
    // Reset all form fields except premiumBasis
    const premiumBasis = formik.values.premiumBasis;
    formik.resetForm({
      values: {
        ...formik.initialValues,
        premiumBasis,
      },
    });
    toast.success("Option saved successfully.");
  };

  //on observation if dili mo work ang newly implemented functions, maybe used later
  // useEffect(() => {
  //   setOptionAerOptions(JSON.stringify(Array.from({ length: optionCount - 1 }, (_, i) => i + 1)));
  // }, [optionCount]);

  const handleCreateAER = async () => {
    if (!condition) {
      toast.error("Please input condition.");
      return;
    }
    const toastId = toast.loading("Submitting Actuary Evaluation Report...", { closeOnClick: true });
    // TO BE USED LATER
    // const principalMinimumAge = Math.min(...formik.values.principalBenefit.map((item: any) => item.minimumAge));
    // const principalMaximumAge = Math.max(...formik.values.principalBenefit.map((item: any) => item.maximumAge));
    // const principalExitAge = Math.max(...formik.values.principalBenefit.map((item: any) => item.exitAge));
    // const primaryMinimumAge = Math.min(...formik.values.coInsuredBenefit.filter((item: any) => item.type === typeCoInsured.primary).map((item: any) => item.minimumAge));
    // const primaryMaximumAge = Math.max(...formik.values.coInsuredBenefit.filter((item: any) => item.type === typeCoInsured.primary).map((item: any) => item.maximumAge));
    // const primaryExitAge = Math.max(...formik.values.coInsuredBenefit.filter((item: any) => item.type === typeCoInsured.primary).map((item: any) => item.exitAge));
    // const secondaryMinimumAge = Math.min(...formik.values.coInsuredBenefit.filter((item: any) => item.type === typeCoInsured.secondary).map((item: any) => item.minimumAge));
    // const secondaryMaximumAge = Math.max(...formik.values.coInsuredBenefit.filter((item: any) => item.type === typeCoInsured.secondary).map((item: any) => item.maximumAge));
    // const secondaryExitAge = Math.max(...formik.values.coInsuredBenefit.filter((item: any) => item.type === typeCoInsured.secondary).map((item: any) => item.exitAge));
    // const primaryRelationship = formik.values.coInsuredBenefit
    //   .filter((item: any) => item.type === typeCoInsured.primary)
    //   .map((item: any) => item.relationship)
    //   .join("/");

    // const secondaryRelationship = formik.values.coInsuredBenefit
    //   .filter((item: any) => item.type === typeCoInsured.secondary)
    //   .map((item: any) => item.relationship)
    //   .join("/");

    const payload: IPostFIPAER = {
      status: AERStatus.draft,
      quotations: {
        coopId: selectedCoop?.id ?? 0,
        previousProvider: formik.values.previousProvider,
        // branch: formik.values.branch,
        contestability: parseInt(formikBenefitsPrincipal.values.contestability),
        fileName: "",
        parentId: localStorage.getItem("parentId") ?? 0,
      },
      quotationCondition: {
        // TO BE USED LATER
        // condition: ConditionsList({
        //   groupSize: formikBenefitsPrincipal.values.totalNoOfMembers,
        //   principalMinimumAge: principalMinimumAge,
        //   principalMaximumAge: principalMaximumAge,
        //   principalMaxExitAge: principalExitAge,
        //   primaryMinimumAge: primaryMinimumAge,
        //   primaryMaximumAge: primaryMaximumAge,
        //   primaryMaxExitAge: primaryExitAge,
        //   secondaryMinimumAge: secondaryMinimumAge,
        //   secondaryMaximumAge: secondaryMaximumAge,
        //   secondaryMaxExitAge: secondaryExitAge,
        //   contestability: formik.values.contestabilityLabel,
        //   averageAge: formikBenefitsPrincipal.values.averageAge,
        //   primaryRelationship: primaryRelationship,
        //   secondaryRelationship: secondaryRelationship,
        // }),
        condition: condition,
      },
      options: {
        fipCoInsuredDependents: fipCoInsuredDependents,
        fipCoInsuredDependentsBenefits: fipCoInsuredDependentsBenefits,
        fipPrincipalMemberRequestData: fipPrincipalMemberRequestData,
        fipPrincipalMemberBenefitRequestData: fipPrincipalMemberBenefitRequestData,
        aerOptions: optionAerOptions,
      },
      projection: fipProjection,
      commissionDistributions: fipCommissionLoading,
      rating: fipRating,
    };

    try {
      const response = await postFIPAERService(payload);
      if (response) {
        toast.dismiss(toastId);
        showSuccess("Actuary Evaluation Report Created Successfully").then(() => navigate(ROUTES.ACTUARY.signatoryFIP.key, { state: { data: response.data, payload: payload } }));
      }
    } catch (error) {
      toast.error("Failed to create Actuary Evaluation Report. Please try again.");
      toast.dismiss(toastId);
    }
  };

  const loadOptions = () => {
    if (location?.state?.selectedQuotation) {
      formik.setFieldValue("principalTotalGrossPremium", location.state.selectedQuotation.quotation.fipPrincipalMember?.find((premium: any) => premium.option === selectedOption)?.grossPremium);
      formik.setFieldValue("principalTotalNetPremium", location.state.selectedQuotation.quotation.fipPrincipalMember?.find((premium: any) => premium.option === selectedOption)?.netPremium);
      formik.setFieldValue("coInsuredTotalGrossPremium", location.state.selectedQuotation.quotation.fipCoInsuredDependent?.find((premium: any) => premium.option === selectedOption)?.grossPremium);
      formik.setFieldValue("coInsuredTotalNetPremium", location.state.selectedQuotation.quotation.fipCoInsuredDependent?.find((premium: any) => premium.option === selectedOption)?.netPremium);

      const benefitsData = location.state.selectedQuotation?.quotation?.fipPrincipalMemberBenefit.filter((benefit: any) => benefit.option === selectedOption);
      const sanitizedData = benefitsData.map((item: any) => removeProperties(item, ["createdAt", "updatedAt"]));
      const benefitsData2 = location.state.selectedQuotation?.quotation?.fipCoInsuredDependentBenefit.filter((benefit: any) => benefit.option === selectedOption);
      const sanitizedData2 = benefitsData2.map((item: any) => removeProperties(item, ["createdAt", "updatedAt"]));

      //principal
      formikBenefitsPrincipal.setFieldValue("premiumBudget", location.state.selectedQuotation?.quotation?.fipPrincipalMember.find((benefit: any) => benefit.option === selectedOption)?.premiumBudget);
      formikBenefitsPrincipal.setFieldValue("averageAge", location.state.selectedQuotation?.quotation?.fipPrincipalMember.find((benefit: any) => benefit.option === selectedOption)?.averageAge);
      formikBenefitsPrincipal.setFieldValue(
        "totalNoOfMembers",
        location.state.selectedQuotation?.quotation?.fipPrincipalMember.find((benefit: any) => benefit.option === selectedOption)?.totalNumberOfMembers
      );
      formikBenefitsPrincipal.setFieldValue(
        "contestability",
        location.state.selectedQuotation?.quotation?.fipPrincipalMember.find((benefit: any) => benefit.option === selectedOption)?.contestabilityId
      );

      // co-insured
      formikBenefitsCoInsured.setFieldValue(
        "maxNoOfInsured",
        location.state.selectedQuotation?.quotation?.fipCoInsuredDependent.find((benefit: any) => benefit.option === selectedOption)?.maximumTotalNumberOfDependents
      );
      formikBenefitsCoInsured.setFieldValue(
        "premiumBudget",
        location.state.selectedQuotation?.quotation?.fipCoInsuredDependent.find((benefit: any) => benefit.option === selectedOption)?.premiumBudget
      );
      formikBenefitsCoInsured.setFieldValue("averageAge", location.state.selectedQuotation?.quotation?.fipCoInsuredDependent.find((benefit: any) => benefit.option === selectedOption)?.averageAge);
      formikBenefitsCoInsured.setFieldValue(
        "totalNoOfMembers",
        location.state.selectedQuotation?.quotation?.fipCoInsuredDependent.find((benefit: any) => benefit.option === selectedOption)?.totalNumberOfMembers
      );
      formikBenefitsCoInsured.setFieldValue(
        "contestabilityId",
        location.state.selectedQuotation?.quotation?.fipCoInsuredDependent.find((benefit: any) => benefit.option === selectedOption)?.contestabilityId
      );

      if (sanitizedData && sanitizedData.length > 0) {
        formik.setFieldValue("principalBenefit", sanitizedData);
        formik.setFieldValue("tempPrincipalBenefit", sanitizedData);
      } else {
        formik.setFieldValue("principalBenefit", []);
      }

      if (sanitizedData2 && sanitizedData2.length > 0) {
        formik.setFieldValue("coInsuredBenefit", sanitizedData2);
        formik.setFieldValue("tempCoInsuredBenefit", sanitizedData2);
      } else {
        formik.setFieldValue("coInsuredBenefit", []);
      }

      // Map commissionLoadingData by option
      const commissionLoadingData = location?.state?.selectedQuotation?.quotation?.quotationCommissionDistribution;

      if (commissionLoadingData) {
        // Only set commission loading where option matches the current viewOption
        const filteredCommissionLoading = commissionLoadingData
          .map((item: any) => removeProperties(item, ["createdAt", "updatedAt"]))
          .filter((item: any) => Number(item.option) === viewOption || item.option === null);

        formik.setFieldValue("commissionLoading", filteredCommissionLoading);
        formik.setFieldValue("oldCommissionLoading", filteredCommissionLoading);
      }

      // Map commissionLoadingData by option
      const benefitRatingData = location?.state?.selectedQuotation?.quotation?.rating;

      if (benefitRatingData) {
        // Only set commission loading where option matches the current viewOption
        const filteredBenefitRating = benefitRatingData.map((item: any) => removeProperties(item, ["createdAt", "updatedAt"])).filter((item: any) => Number(item.option) === viewOption);

        formik.setFieldValue("rating", filteredBenefitRating);
        formik.setFieldValue("oldRating", filteredBenefitRating);
      }

      // Map commissionLoadingData by option
      const projectionData = location?.state?.selectedQuotation?.quotation?.projection;

      if (projectionData) {
        // Only set commission loading where option matches the current viewOption
        const filteredProjection = projectionData.map((item: any) => removeProperties(item, ["createdAt", "updatedAt"])).filter((item: any) => Number(item.option) === viewOption);

        if (filteredProjection.length > 0) {
          // Convert all string numbers to numbers for each attribute
          const projection = Object.fromEntries(
            Object.entries(filteredProjection[0]).map(([key, value]) => [key, typeof value === "string" && !isNaN(Number(value)) && value.trim() !== "" ? Number(value) : value])
          );
          formik.setFieldValue("projection", projection);
        }
      }
    }
  };

  const handleSelectedCoop = (coop: any) => {
    formik.setFieldValue("selectedCoop", coop);
    formik.setFieldValue("cooperative", coop.coopName);
    setSearchText(coop.coopName);
    setOpenComboBox(false);
    setSelectedCoop(coop);
    setSelectedCoopName(coop.coopName);
  };

  const contestabilitySelectOptions = useSelectOptions({
    data: contestability,
    firstOptionText: "Select Contestability",
    valueKey: "id",
    textKey: "label",
  });

  const benefitsSelectOptions = useSelectOptions({
    data: benefits,
    firstOptionText: "Select Benefit",
    valueKey: "id",
    textKey: "benefitName",
  });

  const ageTypesSelectOptions = useSelectOptions({
    data: ageTypes,
    firstOptionText: "Select Age Type",
    valueKey: "id",
    textKey: "name",
  });

  const commissionTypesSelectOptions = useSelectOptions({
    data: commissionTypes,
    firstOptionText: "Select Commission Type",
    valueKey: "id",
    textKey: "commissionName",
  });

  // const handleWysiwygs = (data: string) => {
  //   formik.setFieldValue("condition", data);
  // };

  const handleClickedRowBenefits = () => {
    formik.setFieldValue(
      "tempPrincipalBenefit",
      formik.values.tempPrincipalBenefit.filter((_, index) => index)
    );
    formik.setFieldValue(
      "principalBenefit",
      formik.values.principalBenefit.filter((_, index) => index)
    );
  };

  const handleClickedRowCoInsured = () => {
    formik.setFieldValue(
      "tempCoInsuredBenefit",
      formik.values.tempCoInsuredBenefit.filter((_, index) => index)
    );
    formik.setFieldValue(
      "coInsuredBenefit",
      formik.values.coInsuredBenefit.filter((_, index) => index)
    );
  };

  const handleSetFileName = (fileName: string) => {
    setFileName(fileName);
  };

  const handleDeleteCommissionLoading = (index: number) => {
    const updatedCommissionLoading = formik.values.commissionLoading.filter((_, i) => i !== index);
    formik.setFieldValue("commissionLoading", updatedCommissionLoading);
  };

  const handleDeleteRating = (index: number) => {
    const updatedRating = formik.values.rating.filter((_, i) => i !== index);
    formik.setFieldValue("rating", updatedRating);
  };

  const handleDeleteOption = (option: number) => {
    const aerOptions = JSON.parse(optionAerOptions);
    const updatedOptions = aerOptions.filter((opt: any) => opt !== option);
    setOptionAerOptions(JSON.stringify(updatedOptions));

    setFipPrincipalMemberRequestData(fipPrincipalMemberRequestData.filter((item) => item.option !== option));
    setFipPrincipalMemberBenefitRequestData(fipPrincipalMemberBenefitRequestData.filter((item) => item.option !== option));
    setFipCoInsuredDependents(fipCoInsuredDependents.filter((item) => item.option !== option));
    setFipCoInsuredDependentsBenefits(fipCoInsuredDependentsBenefits.filter((item) => item.option !== option));

    setFipRating(fipRating.filter((item) => item.option !== option));
    setFipRating(fipCommissionLoading.filter((item) => item.option !== option));
    setFipRating(fipProjection.filter((item) => item.option !== option));
    // Update optionAerOptions to reflect the remaining options after deletion
    setOptionAerOptions(JSON.stringify(updatedOptions));
    setOptions(updatedOptions);

    toast.success("Option deleted successfully.");
    setViewOption(0);
  };

  const handleShowDemographic = () => {
    if (hasDemographics) {
      if (demographicModalController.isOpen) {
        handleSelectBasisModal();
      } else {
        demographicModalController.openFn();
      }
    } else {
      handleSelectBasisModal();
    }
  };

  useEffect(() => {
    basis.setFieldValue("cooperativeId", (formik.values.selectedCoop as any).id ?? 0);
  }, [formik.values.selectedCoop]);

  const basis = useFormik({
    initialValues: {
      productId: "",
      // subProductId: "",
      baseFrom: "",
      periodType: "",
      periodFrom: "01-01-2023",
      periodTo: "31-12-2024",
      ageBasis: "",
      age: [
        {
          ageFrom: 18,
          ageTo: 49,
        },
        {
          ageFrom: 50,
          ageTo: 65,
        },
        {
          ageFrom: 66,
          ageTo: 69,
        },
        {
          ageFrom: 70,
          ageTo: 999,
        },
      ],
      productType: ProductCode.GYRT,
      cooperativeId: formik.values.cooperativeId ?? 0,
      // fileName: "2025-07-07_0_kdVbmvvC.csv",
      fileName: "",
    },
    onSubmit: () => {
      handleSelectBasisModal();
    },
  });

  const [selectBasisModal, setSelectBasisModal] = useState<boolean>(false);
  const handleSelectBasisModal = () => {
    setSelectBasisModal((prev) => !prev);
  };

  const handleUpdateBasis = (data: any) => {
    basis.setValues(data);
  };

  const handleGenerate = async () => {
    if (basis.values.cooperativeId === 0) {
      toast.error("Please select a cooperative first.");
      return;
    }

    const ranges = basis.values.age
      .filter((item) => item.ageFrom !== undefined && item.ageTo !== undefined)
      .map((item, idx) => ({ idx, minimum: item.ageFrom, maximum: item.ageTo }))
      .sort((a, b) => a.minimum - b.minimum);

    // Check for minimum greater than maximum
    const invalidRange = ranges.find((range) => range.minimum > range.maximum);
    if (invalidRange) {
      toast.error(`In age bracket #${invalidRange.idx + 1}, minimum (${invalidRange.minimum}) should not be greater than maximum (${invalidRange.maximum}).`);

      return;
    }

    // Check for overlapping/duplicate ranges
    let duplicateRange: { minimum: number; maximum: number } | null = null;
    for (let i = 1; i < ranges.length; i++) {
      if (ranges[i].minimum <= ranges[i - 1].maximum) {
        duplicateRange = ranges[i];
        break;
      }
    }

    if (duplicateRange) {
      toast.error(`Age bracket ${duplicateRange.minimum}–${duplicateRange.maximum} has already been entered or overlaps with another. Duplicate or overlapping age ranges are not allowed.`);
    }
    const formattedDate = {
      periodFrom: basis.values.periodFrom.split("-").reverse().join("-"),
      periodTo: basis.values.periodTo.split("-").reverse().join("-"),
    };
    const { age, periodFrom, periodTo, ...rest } = basis.values;
    const data = {
      ...rest,
      ...formattedDate,
    };

    try {
      const response = await httpClient.post("/quotations/actuaryDemographicData", data);
      if (response) {
        showSuccess("Demographic data generated successfully.").then(() => {
          // handleSelectBasisModal();
          demographicModalController.openFn();
          setHasDemographics(true);
          setDemographicData(response.data);
        });
      }
    } catch (error) {
      toast.error("An error occurred while generating the basis. Please try again.");
    }
  };

  return (
    <>
      <UploadMasterListModal
        controller={uploadMasterListModalController}
        onUpload={(files: any[], _: string) => {
          setHasDemographics(files.length > 0);
          return Promise.resolve();
        }}
        data={formik.values}
        setFileName={handleSetFileName}
      />
      {/* {selectModal && <SelectModal isOpen={selectModal} onClose={handleSelectModal} onSelect={(selectedValue) => setSelectedBasis(selectedValue)} />} */}
      {selectBasisModal && (
        <BasisModal
          isOpen={selectBasisModal}
          onClose={handleSelectBasisModal}
          data={basis.values}
          onUpdate={handleUpdateBasis}
          onGenerate={handleGenerate}
          mainProducts={mainProducts}
          subProducts={subProducts}
          products={products}
        />
      )}
      {demographicData && (
        <DemographicModal controller={demographicModalController} basisValues={basis.values} hasDemographics={hasDemographics} demographicData={demographicData} productName={ProductName.FIP} />
      )}

      <Button onClick={() => navigate(ROUTES.ACTUARY.FIP.key)} classNames="bg-white border-0 flex items-center justify-center" outline variant="primary">
        <IoChevronBack />
        Back
      </Button>
      <FormikProvider value={formik}>
        <Form className="p-4">
          <div className="text-4xl font-poppins-semibold text-zinc-700">FIP WORKSHEET</div>

          <div className="w-full flex flex-col gap-4 pt-10">
            <div className="w-full flex">
              <div className="w-1/5 flex items-center ">Cooperative</div>
              <div className="w-4/5 relative">
                <div>
                  <TextField
                    className="border-zinc-300"
                    name="cooperative"
                    disabled
                    error={formik.touched.cooperative && !!formik.errors.cooperative}
                    errorText="Cooperative is required"
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    placeholder="Search Coop"
                    onClick={() => setOpenComboBox(true)}
                    onFocus={() => setOpenComboBox(true)}
                  />
                </div>

                {openComboBox && (
                  <div onMouseLeave={() => setOpenComboBox(false)} className="absolute top-14 rounded w-full h-80 overflow-y-auto  border border-zinc-300 bg-zinc-50">
                    {cooperatives.map((coop: any) => (
                      <div key={coop?.id} className="h-12 w-full p-4 hover:bg-zinc-200 cursor-pointer" onClick={() => handleSelectedCoop(coop)}>
                        {coop?.coopName}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
            {/* <div className="w-full flex ">
              <div className="w-1/5 flex  items-center ">Branch</div>
              <div className="w-4/5">
                <TextField
                  placeholder="Enter Branch"
                  error={formik.touched.branch && !!formik.errors.branch}
                  errorText="Branch is required"
                  name="branch"
                  value={formik.values.branch}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  className="border-zinc-300"
                />
              </div>
            </div> */}
            <div className="w-full flex">
              {" "}
              <div className=" w-1/5 flex items-center  pr-4 ">Previous Provider</div>
              <div className="w-4/5">
                <TextField
                  disabled
                  placeholder="Enter Previous Provider"
                  name="previousProvider"
                  value={formik.values.previousProvider}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  className="border-zinc-300"
                />
              </div>
            </div>

            {/* <div className="w-full flex">
              {" "}
              <div className=" w-1/5 flex items-center  pr-4 ">Loss Ratio</div>
              <div className="w-4/5">
                <TextField
                  type="number"
                  placeholder="Enter Loss Ratio"
                  name="lossRatio"
                  value={formik.values.lossRatio}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  className="border-zinc-300"
                />
              </div>
            </div> */}
            {/* <div className="w-full flex">
              <div className="w-1/5 flex items-center  pr-4 ">Uniform Distribution of Death ( UDD )</div>
              <div className="w-4/5">
                <TextField type="number" placeholder="Enter UDD" name="udd" value={formik.values.udd} onChange={formik.handleChange} onBlur={formik.handleBlur} className="border-zinc-300" />
              </div>
            </div> */}
            <div className="w-full flex">
              <div className="w-1/5 flex items-center ">Premium Basis</div>
              <div className="w-2/6">
                <Select1
                  disabled
                  placeholder="Select Premium Basis"
                  name="premiumBasis"
                  value={formik.values.premiumBasis ?? 0}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  options={data8}
                  error={formik.touched.premiumBasis && !!formik.errors.premiumBasis}
                />
              </div>
            </div>
          </div>
        </Form>
      </FormikProvider>
      <Modal isOpen={ratingModal} onClose={handleRatingModal} modalContainerClassName="max-w-xl" title="Add Rating" titleClass="text-3xl font-poppins-semibold">
        <FormikProvider value={formikRating}>
          <Form className="flex flex-col gap-2">
            <div className="flex flex-col gap-2">
              <div>Rate Name</div>
              <Select1
                required
                options={commissionTypesSelectOptions}
                name="rateId"
                value={formikRating.values.rateId}
                onChange={formikRating.handleChange}
                onBlur={formikRating.handleBlur}
                placeholder="Select Rate"
              />
            </div>

            <div className="flex flex-col gap-2">
              <div>Rate</div>
              <TextField required type="number" name="value" value={formikRating.values.rate ?? ""} onChange={formikRating.handleChange} onBlur={formikRating.handleBlur} placeholder="Enter Rate" />
            </div>

            <div className="flex justify-end">
              {" "}
              <Button type="submit" classNames="w-24 text-sm py-3 rounded-md" variant="primary">
                Add
              </Button>
            </div>
          </Form>
        </FormikProvider>
      </Modal>
      <Modal isOpen={commissionLoadingModal} onClose={handleCommissionLoadingModal} modalContainerClassName="max-w-xl" title="Add Commission Loading" titleClass="text-3xl font-poppins-semibold">
        <FormikProvider value={formikCommissionLoading}>
          <Form className="flex flex-col gap-2">
            <div className="flex flex-col gap-2">
              <div>Name</div>
              <Select1
                required
                options={commissionTypesSelectOptions}
                name="commissionTypeId"
                value={formikCommissionLoading.values.commissionTypeId}
                onChange={formikCommissionLoading.handleChange}
                onBlur={formikCommissionLoading.handleBlur}
                placeholder="Select Commission Loading"
              />
            </div>

            <div className="flex flex-col gap-2">
              <div>Rate</div>
              <TextField
                required
                name="rate"
                type="number"
                value={formikCommissionLoading.values.rate ?? 0}
                onChange={formikCommissionLoading.handleChange}
                onBlur={formikCommissionLoading.handleBlur}
                placeholder="Enter Value"
              />
            </div>

            <div className="flex justify-end">
              {" "}
              <Button type="submit" classNames="w-24 text-sm py-3 rounded-md" variant="primary">
                Add
              </Button>
            </div>
          </Form>
        </FormikProvider>
      </Modal>
      <Modal
        isOpen={principalBenefitsModal}
        onClose={handlePrincipalBenefitsModal}
        modalContainerClassName={"max-w-7xl "}
        titleClass="text-primary text-xl tracking-wider font-poppins-semibold pl-4"
        title="PRINCIPAL BENEFITS"
      >
        <FormikProvider value={formikBenefitsPrincipal}>
          <Form className="border-t border-zinc-300 pt-2">
            <div className="flex w-full gap-4  pt-2">
              <div className="w-1/3 relative bg-zinc-50 p-4 pt-0 ">
                <div className="w-full py-3  font-semibold text-lg">SPECIFICATION FORM</div>
                <div className="flex justify-center items-center gap-2 mb-4">
                  <div className="w-1/2">Age Type</div>
                  <Select1
                    required
                    options={ageTypesSelectOptions}
                    name="ageTypeId"
                    placeholder="Select Age Type"
                    value={formikBenefitsPrincipal.values.ageTypeId}
                    onChange={formikBenefitsPrincipal.handleChange}
                    onBlur={formikBenefitsPrincipal.handleBlur}
                    className="border-zinc-300 w-1/2"
                  />
                </div>
                {formikBenefitsPrincipal.values.ageTypeId === "2" && (
                  <>
                    <div className="flex justify-center items-center gap-2 mb-4">
                      <div className="w-1/2">Min. Age</div>
                      <TextField
                        required
                        name="minimumAge"
                        type="number"
                        value={formikBenefitsPrincipal.values.minimumAge}
                        onChange={formikBenefitsPrincipal.handleChange}
                        onBlur={formikBenefitsPrincipal.handleBlur}
                        className="border-zinc-300 w-1/2"
                        placeholder="Enter Minimum Age"
                      />
                    </div>

                    <div className="flex justify-center items-center gap-2 mb-4">
                      <div className="w-1/2">Max. Age</div>
                      <TextField
                        required
                        name="maximumAge"
                        type="number"
                        value={formikBenefitsPrincipal.values.maximumAge}
                        onChange={formikBenefitsPrincipal.handleChange}
                        onBlur={formikBenefitsPrincipal.handleBlur}
                        className="border-zinc-300 w-1/2"
                        placeholder="Enter Maximum"
                      />
                    </div>
                  </>
                )}
                <div className="flex justify-center items-center gap-2 mb-4">
                  <div className="w-1/2">Exit Age</div>
                  <TextField
                    required
                    name="exitAge"
                    disabled
                    type="number"
                    value={formikBenefitsPrincipal.values.exitAge}
                    onChange={formikBenefitsPrincipal.handleChange}
                    onBlur={formikBenefitsPrincipal.handleBlur}
                    className="border-zinc-300 w-1/2"
                    placeholder="Enter Exit Age"
                  />
                </div>
                <div className="flex justify-center items-center gap-2 mb-4">
                  <div className="w-1/2">Benefits</div>
                  <Select1
                    required
                    options={benefitsSelectOptions}
                    name="benefitId"
                    placeholder="Select Benefits"
                    value={formikBenefitsPrincipal.values.benefitId}
                    onChange={formikBenefitsPrincipal.handleChange}
                    onBlur={formikBenefitsPrincipal.handleBlur}
                    className="border-zinc-300 w-1/2"
                  />
                </div>

                <div className="flex justify-center items-center gap-2 mb-4">
                  <div className="w-1/3">Coverage</div>
                  <FormattedNumberInput
                    required
                    name="coverage"
                    value={formikBenefitsPrincipal.values.coverage}
                    onChange={(e) => formikBenefitsPrincipal.setFieldValue("coverage", e.target.value)}
                    className="border-zinc-300 w-3/4"
                    placeholder="Enter Coverage"
                  />
                </div>

                <div className="w-full gap-2 flex justify-end  absolute bottom-2 right-2 ">
                  <Button classNames="border-zinc-200 border text-zinc-500 rounded-lg px-8" onClick={() => formik.setFieldValue("tempPrincipalBenefit", [])}>
                    Clear All
                  </Button>
                  <Button classNames="bg-info rounded-xl px-8" type="submit">
                    Add
                  </Button>
                </div>
              </div>
              <div className="w-2/3 h-[45rem] p-4 pt-0 bg-zinc-50 ">
                <div className="w-full py-3  font-semibold text-lg">PREVIEW</div>

                <div>
                  <Table
                    columns={principalColumn}
                    onClickedRow={handleClickedRowBenefits}
                    tableMaxHeight="h-[35rem]"
                    data={formik.values.tempPrincipalBenefit}
                    headerClassNames={"bg-primary text-white text-xs"}
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              {" "}
              <Button onClick={handleAddPrincipalBenefits} classNames="w-24 text-sm py-3 rounded-md" variant="primary">
                Done
              </Button>
            </div>
          </Form>
        </FormikProvider>
      </Modal>
      <Modal
        isOpen={coInsuredBenefitsModal}
        onClose={handleCoInsuredBenefitsModal}
        modalContainerClassName={"max-w-7xl "}
        titleClass="text-primary text-xl tracking-wider font-poppins-semibold pl-4"
        title="COINSURED BENEFITS"
      >
        <FormikProvider value={formikBenefitsCoInsured}>
          <Form className="border-t border-zinc-300 pt-2">
            <div className="flex w-full gap-4  pt-2">
              <div className="w-1/3 relative bg-zinc-50 p-4 pt-0 ">
                <div className="w-full py-3  font-semibold text-lg">SPECIFICATION FORM</div>

                <div className="flex justify-center items-center gap-2 mb-4">
                  <div className="w-1/2">Relationship</div>
                  <Select1
                    required
                    placeholder="Enter Relationship"
                    name="relationship"
                    options={formatSelectOptions(relationshipOptions, "relationshipName")}
                    value={formikBenefitsCoInsured.values.relationship}
                    onChange={formikBenefitsCoInsured.handleChange}
                    onBlur={formikBenefitsCoInsured.handleBlur}
                    className="border-zinc-300 w-1/2"
                  />
                </div>
                <div className="flex justify-center items-center gap-2 mb-4">
                  <div className="w-1/2">Min. Age</div>
                  <TextField
                    required
                    name="minimumAge"
                    type="number"
                    value={formikBenefitsCoInsured.values.minimumAge}
                    onChange={formikBenefitsCoInsured.handleChange}
                    onBlur={formikBenefitsCoInsured.handleBlur}
                    className="border-zinc-300 w-1/2"
                    placeholder="Enter Minimum Age"
                  />
                </div>

                <div className="flex justify-center items-center gap-2 mb-4">
                  <div className="w-1/2">Max. Age</div>
                  <TextField
                    required
                    name="maximumAge"
                    type="number"
                    value={formikBenefitsCoInsured.values.maximumAge}
                    onChange={formikBenefitsCoInsured.handleChange}
                    onBlur={formikBenefitsCoInsured.handleBlur}
                    className="border-zinc-300 w-1/2"
                    placeholder="Enter Maximum"
                  />
                </div>

                {relationship === Type.SECONDARY && (
                  <>
                    <div className="flex justify-center items-center gap-2 mb-4">
                      <div className="w-1/2">Exit Age</div>
                      <TextField
                        required
                        name="exitAge"
                        type="number"
                        value={formikBenefitsCoInsured.values.exitAge}
                        onChange={formikBenefitsCoInsured.handleChange}
                        onBlur={formikBenefitsCoInsured.handleBlur}
                        className="border-zinc-300 w-1/2"
                        placeholder="Enter Exit Age"
                      />
                    </div>

                    <div className="flex justify-center items-center gap-2 mb-4">
                      <div className="w-1/2">Max No. of Insured</div>
                      <TextField
                        required
                        name="maxNoOfInsured"
                        type="number"
                        value={formikBenefitsCoInsured.values.maxNoOfInsured}
                        onChange={formikBenefitsCoInsured.handleChange}
                        onBlur={formikBenefitsCoInsured.handleBlur}
                        className="border-zinc-300 w-1/2"
                        placeholder="Enter Max no. of Insured"
                      />
                    </div>
                  </>
                )}
                <div className="flex justify-center items-center gap-2 mb-4">
                  <div className="w-1/2">Benefits</div>
                  <Select1
                    required
                    options={benefitsSelectOptions}
                    name="benefitId"
                    placeholder="Select Benefits"
                    value={formikBenefitsCoInsured.values.benefitId}
                    onChange={formikBenefitsCoInsured.handleChange}
                    onBlur={formikBenefitsCoInsured.handleBlur}
                    className="border-zinc-300 w-1/2"
                  />
                </div>

                <div className="flex justify-center items-center gap-2 mb-4">
                  <div className="w-1/2">Coverage</div>
                  <FormattedNumberInput
                    required
                    name="coverage"
                    value={formikBenefitsCoInsured.values.coverage}
                    onChange={(e) => formikBenefitsCoInsured.setFieldValue("coverage", e.target.value)}
                    className="border-zinc-300 w-1/2"
                    placeholder="Enter Coverage"
                  />
                </div>

                {relationship === Type.SECONDARY && (
                  <div className="flex justify-center items-center gap-2 mb-4">
                    <div className="w-1/2">Divided Equally</div>
                    <div className="w-1/2">
                      {" "}
                      <CheckBox name="dividedEqually" checked={formikBenefitsCoInsured.values.dividedEqually} onChange={formikBenefitsCoInsured.handleChange} className="border-zinc-300 w-full" />
                    </div>
                  </div>
                )}

                <div className="w-full gap-2 flex justify-end  absolute bottom-2 right-2 ">
                  <Button classNames="border-zinc-200 border text-zinc-500 rounded-lg px-8" onClick={() => formik.setFieldValue("tempCoInsuredBenefit", [])}>
                    Clear All
                  </Button>
                  <Button type="submit" classNames="bg-info rounded-xl px-8">
                    Add
                  </Button>
                </div>
              </div>
              <div className="w-2/3 h-[45rem] p-4 pt-0 bg-zinc-50 ">
                <div className="w-full py-3  font-semibold text-lg">PREVIEW</div>

                <div>
                  <Table
                    onClickedRow={handleClickedRowCoInsured}
                    columns={relationship === Type.PRIMARY ? coInsuredColumn : coInsuredColumn2}
                    tableMaxHeight="h-[35rem]"
                    data={formik.values.tempCoInsuredBenefit.filter((benefit: any) => benefit.type === relationship)}
                    headerClassNames={"bg-primary text-white text-xs"}
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              {" "}
              <Button onClick={handleAddCoInsuredBenefits} classNames="w-24 text-sm py-3 rounded-md" variant="primary">
                Done
              </Button>
            </div>
          </Form>
        </FormikProvider>
      </Modal>
      {formik.values.premiumBasis === "" && <div className="w-full pt-20 text-5xl text-center ">Select Premuim Basis to reveal</div>}
      {formik.values.premiumBasis !== "" && (
        <div className="px-6 mb-40 h-max">
          {/* PRINCIPAL */}
          <div className="flex w-full h-full mb-10 mt-4 gap-4 border-t pt-8 border-zinc-300">
            <div className="w-3/4  h-full border-r-8 pr-4 border-primary">
              <div className="w-full flex justify-between mb-2 ">
                <div className="flex justify-between">
                  <div className="text-3xl text-zinc-700 font-poppins-semibold ">PRINCIPAL</div>
                  <div className="px-2 flex items-center justify-center ml-4"> </div>
                </div>
                <div>
                  <Button classNames="text-xs" variant="primary" outline onClick={handlePrincipalBenefitsModal}>
                    {location?.state?.selectedQuotation ? "Edit" : "Add"}
                  </Button>
                </div>
              </div>{" "}
              <div className="w-full mt-6 flex  border-b border-zinc-300 pb-10">
                <div className="w-1/4 flex flex-col gap-2 px-3">
                  <div>Premium Budget</div>
                  <div>
                    <FormattedNumberInput
                      disabled={true}
                      type="number"
                      name="premiumBudget"
                      value={formikBenefitsPrincipal.values.premiumBudget}
                      onChange={(e) => formikBenefitsPrincipal.setFieldValue("premiumBudget", e.target.value)}
                      className="border-zinc-300"
                    />
                  </div>
                </div>
                <div className="w-1/4 flex flex-col gap-2 px-3">
                  <div>Average Age </div>
                  <div>
                    <FormattedNumberInput
                      disabled={true}
                      name="averageAge"
                      type="number"
                      value={formikBenefitsPrincipal.values.averageAge}
                      onBlur={formikBenefitsPrincipal.handleBlur}
                      onChange={formikBenefitsPrincipal.handleChange}
                      className="border-zinc-300"
                    />
                  </div>
                </div>

                <div className="w-1/4 flex flex-col gap-2 px-3">
                  <div>Total Number of Members</div>
                  <div>
                    <FormattedNumberInput
                      disabled={true}
                      name="totalNoOfMembers"
                      value={formikBenefitsPrincipal.values.totalNoOfMembers}
                      onChange={(e) => formikBenefitsPrincipal.setFieldValue("totalNoOfMembers", e.target.value)}
                      className="border-zinc-300"
                    />
                  </div>
                </div>

                <div className="w-1/4 flex flex-col gap-2 px-3">
                  <div>Contestability</div>
                  <Select1
                    placeholder="Select Contestability"
                    name="contestability"
                    disabled={true}
                    value={formikBenefitsPrincipal.values.contestability ?? 0}
                    onChange={formikBenefitsPrincipal.handleChange}
                    onBlur={formikBenefitsPrincipal.handleBlur}
                    options={contestabilitySelectOptions}
                    error={formikBenefitsPrincipal.touched.contestability && !!formikBenefitsPrincipal.errors.contestability}
                  />
                </div>
              </div>
              <Table
                columns={principalColumn}
                // onClickedRow={handleClickedRow}
                data={formik.values.principalBenefit}
                tableMaxHeight="max-h-96 min-h-[20rem]"
                headerClassNames={"bg-primary text-white"}
              />
            </div>

            <div className="w-1/4 h-full flex flex-col pt-10">
              {formik.values.premiumBasis && <div className="uppercase text-4xl font-poppins-semibold mb-4">{formik?.values?.premiumBasis} Premium</div>}
              <div className="h-full min-h-40">
                <p className="text-zinc-400 text-xl font-poppins-medium mb-2">Total Net Premium</p>
                <p className="text-primary text-7xl font-poppins-semibold">
                  {Number(formik.values.principalTotalNetPremium).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </p>
              </div>
              <div className="h-full min-h-40">
                <p className="text-zinc-400 text-xl font-poppins-medium mb-2">Total Gross Premium</p>

                <p className="text-primary  text-7xl font-poppins-semibold">
                  {Number(formik.values.principalTotalGrossPremium).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </p>
              </div>
            </div>
          </div>
          {/* CONINSURED */}
          <div className="flex w-full h-full mb-10 mt-4 gap-4 border-t pt-8 border-zinc-300">
            <div className="w-3/4  h-full">
              <div className="w-full flex justify-between mb-2 ">
                <div className="flex justify-between">
                  <div className="text-3xl text-zinc-700 font-poppins-semibold ">CO-INSURED DEPENDENT</div>
                  <div className="px-2 flex items-center justify-center ml-4"> </div>
                </div>
                <div>
                  <Button classNames="text-xs" variant="primary" outline onClick={handleCoInsuredBenefitsModal}>
                    {location?.state?.selectedQuotation ? "Edit" : "Add"}
                  </Button>
                </div>
              </div>{" "}
              <div className="w-full mt-6  border-b border-zinc-300 pb-3">
                {formik.values.premiumBasis === "PER INSURED RATING" && (
                  <div className="w-full mt-6 flex  pb-3">
                    <div className="w-1/4 flex flex-col gap-2 px-3">
                      <div>Premium Budget</div>
                      <div>
                        <FormattedNumberInput
                          value={formikBenefitsCoInsured.values.premiumBudget}
                          onChange={(e) => formikBenefitsCoInsured.setFieldValue("premiumBudget", e.target.value)}
                          className="border-zinc-300"
                        />
                      </div>
                    </div>
                    <div className="w-1/4 flex flex-col gap-2 px-3">
                      <div>Average Age </div>
                      <div>
                        <TextField
                          type="number"
                          name="averageAge"
                          value={formikBenefitsCoInsured.values.averageAge}
                          onBlur={formikBenefitsCoInsured.handleBlur}
                          onChange={formikBenefitsCoInsured.handleChange}
                          className="border-zinc-300"
                        />
                      </div>
                    </div>

                    <div className="w-1/4 flex flex-col gap-2 px-3">
                      <div>Total Number of Members</div>
                      <div>
                        <FormattedNumberInput
                          name="totalNoOfMembers"
                          value={formikBenefitsCoInsured.values.totalNoOfMembers}
                          onChange={(e) => formikBenefitsCoInsured.setFieldValue("totalNoOfMembers", e.target.value)}
                          className="border-zinc-300"
                        />
                      </div>
                    </div>

                    <div className="w-1/4 flex flex-col gap-2 px-3">
                      <div>Contestability</div>
                      <Select1
                        placeholder="Select Contestability"
                        name="contestability"
                        value={formikBenefitsCoInsured.values.contestability ?? 0}
                        onChange={formikBenefitsCoInsured.handleChange}
                        onBlur={formikBenefitsCoInsured.handleBlur}
                        options={contestabilitySelectOptions}
                        error={formikBenefitsCoInsured.touched.contestability && !!formikBenefitsCoInsured.errors.contestability}
                      />
                    </div>
                  </div>
                )}
                <div className="w-1/3 flex flex-col gap-2 px-3">
                  <div className="text-zinc-400">Max total no. of dependedents</div>
                  <div>
                    <TextField
                      type="number"
                      value={formikBenefitsCoInsured.values.totalNoOfDependents}
                      onBlur={formikBenefitsCoInsured.handleBlur}
                      onChange={formikBenefitsCoInsured.handleChange}
                      className="border-zinc-300"
                    />
                  </div>
                </div>
                <div className="flex mt-6">
                  <div className={`px-5 py-1 cursor-pointer rounded-l-sm ${relationship === Type.PRIMARY ? "bg-primary text-white" : " bg-zinc-300"}`} onClick={() => setRelationship(Type.PRIMARY)}>
                    Primary
                  </div>
                  <div
                    className={`px-5 py-1 cursor-pointer rounded-r-sm ${relationship === Type.SECONDARY ? "bg-primary text-white" : " bg-zinc-300"}`}
                    onClick={() => setRelationship(Type.SECONDARY)}
                  >
                    Secondary
                  </div>
                </div>
              </div>
              <Table
                columns={relationship === Type.PRIMARY ? coInsuredColumn : coInsuredColumn2}
                // ang data dapat naa ni type its either primary or secondary
                data={formik.values.coInsuredBenefit.filter((benefit: any) => benefit.type === relationship)}
                tableMaxHeight="max-h-96 min-h-[20rem]"
                headerClassNames={`bg-primary text-white ${relationship === Type.SECONDARY ? "text-xs" : ""}`}
              />
            </div>

            <div className="w-1/4 h-full flex flex-col pt-10">
              {formik.values.premiumBasis && <div className="uppercase text-4xl font-poppins-semibold mb-4">{formik?.values?.premiumBasis} Premium</div>}
              <div className="h-full min-h-40">
                <p className="text-zinc-400 text-xl font-poppins-medium mb-2">Total Net Premium</p>
                <p className="text-primary text-7xl font-poppins-semibold">
                  {Number(formik.values.coInsuredTotalNetPremium).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </p>
              </div>
              <div className="h-full min-h-40">
                <p className="text-zinc-400 text-xl font-poppins-medium mb-2">Total Gross Premium</p>

                <p className="text-primary  text-7xl font-poppins-semibold">
                  {Number(formik.values.coInsuredTotalGrossPremium).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </p>
              </div>
            </div>
          </div>

          {/* EXPERIENCE DATA AND ANALYSIS */}
          <div className="w-full p-4 border border-zinc-300 rounded-md items-center  h-max gap-2 flex">
            <div className="w-full flex items-center justify-between gap-2">
              <div className="text-sm text-primary flex items-center justify-center font-poppins-semibold">Demographic Data</div>

              <Button classNames="text-sm text-yellow-500 bg-yellow-100 rounded-md  " onClick={handleShowDemographic}>
                Show Demographic
              </Button>
            </div>
          </div>

          {/* OKAY RANI DIRI */}
          <div className="h-80  mt-8 ">
            <div className="text-2xl font-poppins-semibold">Rate Project & Commission Allocation</div>
            <div className="text-zinc-500 mt-1">Select the basis for calculation.</div>
            <div className="flex gap-4 text-xl mt-4">
              <Radio
                key="1"
                value={RATE_PROJECT_COMMISSION_ALLOCATION.MASTERLIST.key}
                label="Masterlist"
                name="RateProjectionCommissionAllocation"
                checked={formik.values.rateProjectionCommissionAllocation === RATE_PROJECT_COMMISSION_ALLOCATION.MASTERLIST.key}
                onChange={handleRadio}
                required
              />
              <Radio
                key={"2"}
                value={RATE_PROJECT_COMMISSION_ALLOCATION.COOP_EXISTING_PRODUCT.key}
                label={"Coop Existing Product"}
                name="RateProjectionCommissionAllocation"
                checked={formik.values.rateProjectionCommissionAllocation === RATE_PROJECT_COMMISSION_ALLOCATION.COOP_EXISTING_PRODUCT.key}
                onChange={handleRadio}
                required
              />
            </div>
            <div className="flex gap-4 my-6 ">
              <div className=" flex items-center ">Rate Based From</div>
              <div className=" w-80">
                <Select1
                  className=""
                  placeholder="Select Rate Based From"
                  name="rateBasedFrom"
                  value={formik.values.rateBasedFrom ?? ""}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  options={[{ text: "CLIMBS Mortality Rate 1", value: "CLIMBS Mortality Rate 1" }]}
                  error={formik.touched.rateBasedFrom && !!formik.errors.rateBasedFrom}
                />
              </div>
            </div>

            <div className="flex w-full gap-3">
              <div className="w-[25%] h-full border border-zinc-300 rounded-md">
                <div className="bg-primary py-4  text-white font-poppins-semibold flex">
                  <div className="w-1/3 pl-4">PROJECTION</div>
                </div>

                <div className="w-full flex items-center justify-center">
                  <div className="w-1/3 p-2 pl-4 text-sm">Net Premium</div>
                  <div className="w-2/3 p-2">
                    <FormattedNumberInput
                      disabled
                      name="projection.totalPremiumNetRate"
                      value={formik?.values?.projection?.totalPremiumNetRate}
                      onChange={(e) => formik.setFieldValue("projection.totalPremiumNetRate", e.target.value)}
                    />
                  </div>
                </div>

                <div className="w-full flex items-center justify-center">
                  <div className="w-1/3 p-2 pl-4 text-sm">Gross Premium</div>

                  <div className="w-2/3 p-2">
                    <FormattedNumberInput
                      disabled
                      name="projection.totalPremiumGrossRate"
                      value={formik?.values?.projection?.totalPremiumGrossRate}
                      onChange={(e) => formik.setFieldValue("projection.totalPremiumGrossRate", e.target.value)}
                    />
                  </div>
                </div>

                <div className="w-full flex items-center justify-center">
                  <div className="w-1/3 p-2 pl-4 text-sm">No. of Claims</div>
                  <div className="w-2/3 p-2">
                    <FormattedNumberInput
                      disabled
                      name="projection.numberOfClaims"
                      value={formik?.values?.projection?.numberOfClaims}
                      onChange={(e) => formik.setFieldValue("projection.numberOfClaims", e.target.value)}
                    />
                  </div>
                </div>

                <div className="w-full flex items-center justify-center">
                  <div className="w-1/3 p-2 pl-4 text-sm">Amount of Claims</div>
                  <div className="w-2/3 p-2">
                    <FormattedNumberInput
                      disabled
                      name="projection.amountOfClaims"
                      value={formik.values?.projection?.amountOfClaims}
                      onChange={(e) => formik.setFieldValue("projection.amountOfClaims", e.target.value)}
                    />
                  </div>
                </div>

                <div className="w-full flex items-center justify-center">
                  <div className="w-1/3 p-2 pl-4 text-sm">Claims Ratio</div>
                  <div className="w-2/3 p-2">
                    <FormattedNumberInput
                      disabled
                      name="projection.claimsRatio"
                      value={formik.values?.projection?.claimsRatio}
                      onChange={(e) => formik.setFieldValue("projection.claimsRatio", e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <div className="w-[37.5%] h-96 overflow-y-auto border border-zinc-300 rounded-md">
                <div className="bg-primary py-3 text-white font-poppins-semibold flex justify-between items-center px-4">
                  <span>BENEFIT RATING</span>
                  <Button classNames="text-xs bg-white text-primary hover:bg-primary hover:text-white border" outline onClick={handleRatingModal}>
                    Add
                  </Button>
                </div>
                <div className="max-h-80  ">
                  <table className="w-full border-collapse">
                    <tbody>
                      {formik.values.rating.map((row: any, rowIndex) => (
                        <tr className={`w-full  first-line:cursor-pointer even:bg-gray-100  `} key={rowIndex}>
                          <td className="p-2 w-2/5 pl-4">{row.benefitName ? row?.benefitName : row?.benefit?.benefitName}</td>
                          <td className=" w-1/5 text-center">
                            {(formik.values.oldRating?.[rowIndex] as any)?.rate ? (
                              <span className="py-2 px-4 rounded-lg w-full bg-zinc-200 text-zinc-400">{parseFloat((formik.values.oldRating?.[rowIndex] as any).rate).toFixed(3)}</span>
                            ) : (
                              ""
                            )}
                          </td>
                          <td className="p-2 w-1/5 text-black">
                            <TextField type="number" name={`rating[${rowIndex}].rate`} value={(formik.values.rating[rowIndex] as any).rate} onChange={formik.handleChange} onBlur={formik.handleBlur} />
                          </td>
                          <td onClick={() => handleDeleteRating(rowIndex)} className=" w-1/5 mt-2  ">
                            <span className="flex  items-center justify-center h-full ">
                              {" "}
                              <BiMinusCircle size={30} className="text-red-500 text-center cursor-pointer " />
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
              <div className="w-[37.5%] h-96  border border-zinc-300 rounded-md">
                <div className="bg-primary py-3 text-white font-poppins-semibold flex justify-between items-center px-4">
                  <span>COMMISSION LOADING</span>
                  <Button classNames="text-xs bg-white text-primary hover:bg-primary hover:text-white border" outline onClick={handleCommissionLoadingModal}>
                    Add
                  </Button>
                </div>
                <div className="max-h-72 overflow-y-auto ">
                  <table className="w-full border-collapse ">
                    <tbody>
                      {formik.values.commissionLoading.map((row: any, rowIndex) => (
                        <tr className={`w-full  first-line:cursor-pointer even:bg-gray-100`} key={rowIndex}>
                          <td className="p-2 w-2/5 pl-4">{row?.commissionTypeName ? row?.commissionTypeName : row?.commissionType?.commissionName}</td>
                          <td className=" w-1/5 text-center">
                            {(formik.values.oldCommissionLoading?.[rowIndex] as any)?.rate ? (
                              <span className="py-2 px-4 rounded-lg w-full bg-zinc-200 text-zinc-400">{parseFloat((formik.values.oldCommissionLoading?.[rowIndex] as any).rate).toFixed(3)}</span>
                            ) : (
                              ""
                            )}
                          </td>
                          <td className="p-2 text-black">
                            <TextField
                              type="number"
                              name={`commissionLoading[${rowIndex}].rate`}
                              value={(formik.values.commissionLoading[rowIndex] as any).rate}
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                              className="w-10"
                            />
                          </td>
                          <td onClick={() => handleDeleteCommissionLoading(rowIndex)} className=" w-1/5 mt-2  ">
                            <span className="flex  items-center justify-center h-full ">
                              {" "}
                              <BiMinusCircle size={30} className="text-red-500 text-center cursor-pointer " />
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <div className="p-4 mt-4">
              <div className="font-poppins-semibold text-xl">CONDITIONS</div>
              <div className="w-full h-max rounded-md mt-2">
                <Wysiwyg stateValue={condition} onChange={(content) => setCondition(content)} />
              </div>
            </div>

            <div className="mt-8 border-t-8  border-primary ">
              <p className="text-xl mt-4 font-poppins-semibold">Options</p>
              <p className="text-zinc-400">Current options added are display below.</p>
              {options && (
                <div className="w-full flex gap-2 mt-2">
                  {options.map((option: number) => (
                    <div
                      className={`text-sm font-poppins-medium p-2 px-4 cursor-pointer border ${viewOption === option ? "bg-primary text-white" : "border-primary text-primary"} rounded-md`}
                      key={option}
                      onClick={() => {
                        setViewOption(option);
                        setSelectedOption(option);
                      }}
                    >
                      Option {option}
                    </div>
                  ))}
                </div>
              )}
            </div>
            <div className="w-full mt-4">
              {viewOption != 0 && (
                <div className="w-full flex flex-col border-t pt-4 border-zinc-300 ">
                  <div className="flex justify-between mb-4">
                    <div className="text-2xl font-poppins-semibold">Option {viewOption}</div>
                    <div className="flex gap-2">
                      <Button classNames="text-xs" variant="primary" outline onClick={loadOptions}>
                        Load Above
                      </Button>
                      <Button classNames="text-xs text-red-500" variant="danger" outline onClick={() => handleDeleteOption(viewOption)}>
                        Remove
                      </Button>
                      <div className="flex items-center text-xs text-zinc-400 justify-center cursor-pointer px-1 " onClick={() => setViewOption(0)}>
                        Hide
                      </div>
                    </div>
                  </div>
                  {/* {optionQuotationPremiums
                    .filter((item: any) => item.option === viewOption)
                    .map((item: any, index: number) => (
                      <div key={index} className="flex gap-4 text-primary mb-6">
                        <div className=" w-1/2 flex flex-col">
                          <span className="text-xl  font-poppins-semibold"> Net Premium</span>
                          <span className="font-poppins-semibold text-7xl"> {parseFloat(item.netPremium).toLocaleString()}</span>
                        </div>
                        <div className=" w-1/2 flex flex-col">
                          <span className="text-xl  font-poppins-semibold"> Gross Premium</span>
                          <span className="font-poppins-semibold text-7xl"> {parseFloat(item.grossPremium).toLocaleString()}</span>
                        </div>
                      </div>
                    ))} */}

                  <div className="w-full h-max border border-zinc-300 rounded-xl">
                    <Table
                      columns={principalColumn}
                      data={fipPrincipalMemberBenefitRequestData
                        .filter((benefit: any) => benefit.option === viewOption)
                        .map((benefit: any) => {
                          const ageData = formik.values.fipAges?.find((age: any) => age.memberType === eInsuredType.principalMember && age.option === benefit.option);
                          return {
                            ...benefit,
                            ...(ageData || {}),
                          };
                        })}
                      tableMaxHeight="max-h-96 min-h-[20rem]"
                      headerClassNames={"bg-primary text-white"}
                    />
                  </div>
                  <div className="w-full h-max border border-zinc-300 rounded-xl">
                    <Table
                      columns={coInsuredColumn2}
                      data={fipCoInsuredDependentsBenefits.filter((item: any) => item.option === viewOption)}
                      tableMaxHeight="max-h-96 min-h-[20rem]"
                      headerClassNames={"bg-primary text-white"}
                    />
                  </div>
                </div>
              )}
            </div>
            {/* <div className="mt-20 h-max">
              <div className="my-3 text-xl font-poppins-semibold">COND ITIONS</div>
              <Wysiwyg stateValue={formik.values.condition} onChange={(content) => handleWysiwygs(content)} className={`h-max  ${formik.values.condition === "" ? "border-red-600 border-2 " : ""}`} />
            </div>
            {formik.values.condition === "" && <span className="italic text-red-600 font-regular text-sm">Condition is empty.</span>} */}

            <div className="flex w-full justify-end gap-2 pb-20 mt-10 ">
              <Button classNames="text-zinc-500 w-40" onClick={handleSaveOption} isSubmitting={!formik.values.principalTotalNetPremium}>
                Save Option
              </Button>
              <Button classNames="bg-green-500 text-white w-40" onClick={handleCalculate} isSubmitting={formik.values.principalBenefit.length === 0 || formik.values.coInsuredBenefit.length === 0}>
                Calculate
              </Button>
              <Button
                onClick={handleCreateAER}
                isSubmitting={options.length === 0 || condition === ""}
                classNames={` text-white w-60 ${options.length === 0 || condition === "" ? "bg-primary opacity-75" : " bg-primary"}`}
              >
                Continue to Signatory
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default CreateAERFIP;
