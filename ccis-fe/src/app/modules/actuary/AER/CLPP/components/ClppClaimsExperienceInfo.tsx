import { useEffect, useState } from "react";
import EditableTable from "@modules/sales/components/editable-table";
import colorMode from "@modules/sales/utility/color";

export type TClppClaimsExperienceInfo = {
  years: TClppClaimsExperienceInfoYears[];
  ages: TClppClaimsExperienceInfoAges[];
  isAgeVisible?: boolean;
};

type TClppClaimsExperienceInfoYears = {
  year: number;
  numberOfDeaths: number;
  totalClaimAmount: number;
};

type TClppClaimsExperienceInfoAges = {
  ageFrom: number;
  ageTo: number;
  numberOfDeaths: number;
  totalClaimAmount: number;
};

type TClppClaimsExperienceInfoProps = {
  value?: TClppClaimsExperienceInfo;
  onChange?: (data: TClppClaimsExperienceInfo) => void;
};

export const ClppClaimsExperienceInfo = ({ value, onChange }: TClppClaimsExperienceInfoProps) => {
  const [fieldState, setFieldState] = useState<TClppClaimsExperienceInfo>(
    value ?? {
      years: [
        {
          year: new Date().getFullYear(),
          numberOfDeaths: 0.0,
          totalClaimAmount: 0.0,
        },
        {
          year: new Date().getFullYear() - 1,
          numberOfDeaths: 0.0,
          totalClaimAmount: 0.0,
        },
        {
          year: new Date().getFullYear() - 2,
          numberOfDeaths: 0.0,
          totalClaimAmount: 0.0,
        },
      ],
      ages: [
        {
          ageFrom: 0,
          ageTo: 0,
          numberOfDeaths: 0,
          totalClaimAmount: 0,
        },
        {
          ageFrom: 0,
          ageTo: 0,
          numberOfDeaths: 0,
          totalClaimAmount: 0,
        },
        {
          ageFrom: 0,
          ageTo: 0,
          numberOfDeaths: 0,
          totalClaimAmount: 0,
        },
      ],
    }
  );

  const [isAgeVisible, setIsAgeVisible] = useState(false);

  useEffect(() => {
    if (value) {
      setFieldState(value);
      setIsAgeVisible(value.isAgeVisible ?? false);
    }
  }, [value]);

  useEffect(() => {
    if (value) {
      setFieldState(value);
      setIsAgeVisible(value.isAgeVisible ?? false);
    }
  }, [value]);

  return (
    <div
      className={colorMode({
        classLight: "border border-gray/10 bg-[#F6F6F680] p-6",
        classDark: "shadow-sm bg-black/10 p-6",
      })}
    >
      <h3
        className={colorMode({
          classLight: "text-lg text-primary font-[600] text-[16px] mb-3",
          classDark: "text-lg text-white/60 font-[600] text-[16px] mb-3",
        })}
      >
        CLAIMS EXPERIENCE
      </h3>
      <div>
        <div className="col-span-12 lg:col-span-6">
          <div className="flex gap-4 justify-end">
            {/* <div className="flex items-center gap-2 mb-4">
              <span className="text-sm">ADD AGE</span>
              <Switch checked={isAgeVisible} onChange={handleSwitchToggle} />
            </div> */}
            {/* <div>
              <Button classNames="block ms-auto" onClick={handleCreateClaimsExperienceYear}>
                <div className="flex flex-row items-center gap-2">
                  <PiPlusCircle className="inline text-primary" />
                  <span className="font-thin text-primary font-[300] text-sm">Add Year</span>
                </div>
              </Button>
            </div> */}
          </div>
          <span className="text-lg text-primary">Year</span>
          <EditableTable
            columns={[
              {
                key: "year",
                header: "YEAR",
                className: "text-[14px] font-[500] text-nowrap",
                number: true,
                disabled: true,
              },
              {
                key: "numberOfDeaths",
                header: "NUMBER OF DEATHS",
                className: "text-[14px] font-[500] text-nowrap",
                number: true,
                disabled: true,
              },
              {
                key: "totalClaimAmount",
                header: "TOTAL CLAIM AMOUNT",
                className: "text-[14px] font-[500] text-nowrap",
                number: true,
                formatInput: true,
                disabled: true,
              },
              // {
              //   key: "",
              //   header: "",
              //   align: "center",
              //   className: "text-[14px] font-[500] w-[50px]",
              //   render: (_: any, index: number) => {
              //     return (
              //       <Button
              //         classNames="!w-fit !h-fit !p-0"
              //         onClick={() => {
              //           const currentYears = [...fieldState.years];
              //           if (currentYears.length <= MIN_CLAIM_YEARS) return;
              //           currentYears.splice(index, 1);
              //           setFieldState((prev) => ({
              //             ...prev,
              //             years: currentYears,
              //           }));
              //         }}
              //       >
              //         <PiMinusCircle className="inline text-primary" />
              //       </Button>
              //     );
              //   },
              // },
            ]}
            rows={fieldState.years}
            onChange={(updatedRows) => {
              const data = {
                ...fieldState,
                years: updatedRows as TClppClaimsExperienceInfoYears[],
                isAgeVisible,
              };
              setFieldState(data);
              onChange?.(data);
            }}
          />
        </div>

        <div className="col-span-12 lg:col-span-6">
          {/* <Button classNames="block ms-auto" onClick={handleCreateClaimsExperienceAge}>
              <div className="flex flex-row items-center gap-2">
                <PiPlusCircle className="inline text-primary" />
                <span className="font-thin text-primary font-[300] text-sm">Add Age</span>
              </div>
            </Button> */}
          <span className="text-lg text-primary flex gap-2">
            Age <p className="text-xs mt-2">(Optional)</p>
          </span>
          <EditableTable
            columns={[
              {
                key: "ageFrom",
                header: "AGE FROM",
                className: "text-[14px] font-[500] text-nowrap",
                number: true,
                disabled: true,
              },
              {
                key: "ageTo",
                header: "AGE TO",
                className: "text-[14px] font-[500] text-nowrap",
                number: true,
                disabled: true,
              },
              {
                key: "numberOfDeaths",
                header: "NUMBER OF DEATHS",
                className: "text-[14px] font-[500] text-nowrap",
                number: true,
                disabled: true,
              },
              {
                key: "totalClaimAmount",
                header: "TOTAL CLAIM AMOUNT",
                className: "text-[14px] font-[500] text-nowrap",
                number: true,
                formatInput: true,
                disabled: true,
              },
              // {
              //   key: "",
              //   header: "",
              //   align: "center",
              //   className: "text-[14px] font-[500] w-[50px]",
              //   render: (_: any, index: number) => {
              //     return (
              //       <Button
              //         classNames="!w-fit !h-fit !p-0"
              //         onClick={() => {
              //           const currentAges = [...fieldState.ages];
              //           if (currentAges.length <= MIN_CLAIM_AGE) return;
              //           currentAges.splice(index, 1);
              //           setFieldState((prev) => ({
              //             ...prev,
              //             ages: currentAges,
              //           }));
              //         }}
              //       >
              //         <PiMinusCircle className="inline text-primary" />
              //       </Button>
              //     );
              //   },
              // },
            ]}
            rows={fieldState.ages}
            onChange={(updatedRows) => {
              const data = {
                ...fieldState,
                ages: updatedRows as TClppClaimsExperienceInfoAges[],
                isAgeVisible,
              };
              setFieldState(data);
              onChange?.(data);
            }}
          />
        </div>
      </div>
    </div>
  );
};
