// modules/actuary/CLPP/components/ClppOptionInfo/index.tsx
import { toast } from "react-toastify";
import Button from "@components/common/Button";
import WysiwygConditionEditor from "../../CLSP/components/wysiwyg/wysiwygConditions";
import SignatorySelect from "./SignatorySelect";

export type TClppOptionsInfoProps = {
  show: boolean;
  canSubmit?: boolean;
  submitErrors?: string[];

  quotationCondition: { condition: string };
  onSetShowConditionContainer: (visible: boolean) => void;
  showConditionContainer: boolean;
  onConditionChange: (content: string) => void;
  signatoryTemplateId?: number | null;
  onChangeSignatoryTemplateId?: (id: number | null) => void;
  onOpenReview?: () => void;
};

export default function ClppSavedOptionsPanel({
  show,
  quotationCondition,
  onConditionChange,
  onSetShowConditionContainer,
  showConditionContainer,
  canSubmit = false,
  submitErrors = [],
  signatoryTemplateId = null,
  onChangeSignatoryTemplateId,
  onOpenReview,
}: TClppOptionsInfoProps) {
  if (!show) return null;

  const canContinue = canSubmit && !!signatoryTemplateId;

  return (
    <div className="mt-6">
      <div className="flex justify-end">
        {!quotationCondition.condition && (
          <Button classNames="elevation-sm shadow-md rounded-[5px] !px-12 mt-8" variant="primary" onClick={() => onSetShowConditionContainer(true)}>
            <div className="flex flex-row items-center gap-2">Create Condition</div>
          </Button>
        )}
      </div>

      {(showConditionContainer || quotationCondition.condition) && (
        <div className="mb-3">
          <h2 className="text-[20px] font-poppins-medium font-[600] text-primary mt-4">CONDITIONS</h2>
          <WysiwygConditionEditor value={quotationCondition.condition} onChange={onConditionChange} className="border border-gray/20 rounded-md bg-white" />
        </div>
      )}

      {/* Signatory Template */}
      <div className="mb-6">
        <h2 className="text-[20px] font-poppins-medium font-[600] text-primary mt-4">SIGNATORY</h2>
        <SignatorySelect value={signatoryTemplateId ?? null} onChange={onChangeSignatoryTemplateId} />
      </div>

      {/* Review & Submit */}
      <div className="flex justify-end">
        <Button
          classNames={`rounded-[5px] !px-12 ${canContinue ? "!bg-primary hover:opacity-90" : "!bg-slate-400 cursor-not-allowed"}`}
          onClick={() => {
            if (!canSubmit) {
              toast.error(submitErrors[0] || "Please correct the form errors before proceeding.");
              return;
            }
            if (!signatoryTemplateId) {
              toast.error("Please select a signatory template.");
              return;
            }
            onOpenReview?.();
          }}
          disabled={!canContinue}
        >
          <span className="text-white text-[14px] font-[400] font-poppins-medium">Review & Submit</span>
        </Button>
      </div>
    </div>
  );
}
