// modules/actuary/components/SignatorySelect.tsx
import { useEffect, useMemo, useRef, useState } from "react";
import Select from "@components/form/Select";
import { FaUserCircle } from "react-icons/fa";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { useSignatoryTemplatesManagementActions } from "@state/reducer/utilities-signatory-template";
import { useSignatoryTypesManagementActions } from "@state/reducer/utilities-signatory-type";
import { useUserManagementActions } from "@state/reducer/users-management";

type Props = {
  value?: number | null;
  onChange?: (id: number | null) => void;
  disabled?: boolean;
};

export default function SignatorySelect({ value = null, onChange, disabled = false }: Props) {
  const { getSignatoryTemplate, getSignatoryTemplateID } = useSignatoryTemplatesManagementActions();
  const { getSignatoryType } = useSignatoryTypesManagementActions();
  const { getUsers } = useUserManagementActions();

  const signatoryTemplates = useSelector((s: RootState) => s.utilitiesSignatoryTemplate.signatoryTemplates);
  const selectedTemplate = useSelector((s: RootState) => s.utilitiesSignatoryTemplate.selectedSignatoryTemplate);
  const users = useSelector((s: RootState) => s.usersManagement.users);
  const signatoryTypes = useSelector((s: RootState) => s.utilitiesSignatoryType?.signatoryTypes);

  const [selectedId, setSelectedId] = useState<number | null>(value ?? null);
  const prevTplRef = useRef<number | null>(null);

  // initial fetches
  useEffect(() => {
    getSignatoryTemplate({ params: { filter: "" } });
    getUsers({ filter: "" });
    getSignatoryType({ filter: "" });
  }, []);

  // keep internal selected id in sync with prop updates
  useEffect(() => {
    setSelectedId(value ?? null);
  }, [value]);

  // fetch selected template’s details
  useEffect(() => {
    if (!selectedId || prevTplRef.current === selectedId) return;
    prevTplRef.current = selectedId;
    getSignatoryTemplateID({ id: selectedId });
  }, [selectedId]);

  // write to localStorage
  const persistToStorage = (id: number | null) => {
    const latestRaw = localStorage.getItem("quotationData");
    const latest = latestRaw ? JSON.parse(latestRaw) : {};
    const updated = {
      ...latest,
      quotations: {
        ...(latest.quotations || {}),
        signatoryTemplateId: id ?? undefined,
      },
    };
    localStorage.setItem("quotationData", JSON.stringify(updated));
  };

  const handleChange = (e: any) => {
    const id = Number(e.target.value) || null;
    setSelectedId(id);
    persistToStorage(id);
    onChange?.(id);
  };

  const templateOptions = [{ value: "", text: "Select Signatory Template" } as const].concat((signatoryTemplates || []).map((t: any) => ({ value: t?.id, text: t?.templateName })));

  const signatories = useMemo(() => {
    const templateUsers = selectedTemplate?.data?.signatoriesTemplateUsers || [];
    return templateUsers
      .map((sig: any) => {
        const user = users.find((u) => u.id === sig.userId);
        return user ? { ...user, sequence: sig.sequence, signatoryTypeId: sig.signatoryTypeId } : null;
      })
      .filter(Boolean)
      .sort((a: any, b: any) => a.sequence - b.sequence);
  }, [selectedTemplate, users]);

  const grouped = useMemo(() => {
    const g: Record<number, any[]> = {};
    for (const s of signatories) {
      if (!s) continue;
      if (!g[s.signatoryTypeId]) g[s.signatoryTypeId] = [];
      g[s.signatoryTypeId].push(s);
    }
    return g;
  }, [signatories]);

  return (
    <div className="mt-6">
      <div className="text-primary mt-2 mb-2">Select Signatory Template</div>
      <Select name="signatoryTemplateId" options={templateOptions} value={selectedId ? selectedId.toString() : ""} onChange={handleChange} disabled={disabled} required />

      {!!selectedId &&
        Object.entries(grouped).map(([typeId, group]) => {
          const type = signatoryTypes.find((t) => t.id === Number(typeId));
          return (
            <div key={typeId} className="mt-6">
              <h3 className="uppercase text-primary text-md font-semibold mb-2">{type?.signatoryTypeName || "Unnamed Type"}</h3>
              <div className="space-y-2">
                {group.map((sig: any) => (
                  <div key={sig.id} className="flex items-center w-full h-16 rounded-xl p-4 bg-gray-100">
                    <div className="px-4">
                      <FaUserCircle size={40} className="text-primary" />
                    </div>
                    <div className="flex flex-col w-[85%] items-start h-full justify-center">
                      <div className="text-lg font-bold flex gap-2 items-center text-primary h-full">
                        <span>{sig.firstname}</span>
                        <span>{sig.middlename}</span>
                        <span>{sig.lastname}</span>
                      </div>
                      <div className="text-xs text-gray-500">{sig.position?.positionName}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          );
        })}
    </div>
  );
}
