import React, { useEffect, useMemo, useRef, useState } from "react";
import Button from "@components/common/Button";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { IContestability } from "@interface/contestability.interface";
import { TClppBenefits } from "@state/types/actuary-clpp-aer";
import { BENEFITSCOLUMNS } from "../constants";
import { useCooperativesManagementActions } from "@state/reducer/cooperatives";

export type ClppReviewContentProps = {
  quotationData: any;
  selectedQuotationProduct?: any;
  signatoryTemplateId: number | null;
  status?: string | null;
  aerID?: number | string | null;
  onSubmitAer: () => void;
  onSaveDraft: () => void;
};

const ClppReviewContent: React.FC<ClppReviewContentProps> = ({ quotationData, aerID, onSubmitAer, onSaveDraft }) => {
  const { getCooperativeById } = useCooperativesManagementActions();
  const cooperativesData = useSelector((state: RootState) => state.cooperatives?.cooperatives);
  const getCooperativeByIdState = useSelector((state: RootState) => state.cooperatives?.getCooperativeById);
  const contestabilityData = useSelector((state: RootState) => state.contestability?.contestabilities);

  const coopId: number | undefined = useMemo(() => {
    return (
      Number(quotationData?.quotations?.coopId) || Number(quotationData?.quotation?.coopId) || Number(quotationData?.cooperativeId) || Number(quotationData?.quotation?.cooperative?.id) || undefined
    );
  }, [quotationData]);

  const [cooperativeName, setCooperativeName] = useState<string>("N/A");
  const fetchedCoopOnceRef = useRef(false);

  // First, try to find the cooperative in the existing list
  useEffect(() => {
    if (!coopId) return;
    const fromList = cooperativesData?.find((c) => Number(c.id) === Number(coopId));
    if (fromList?.coopName) {
      setCooperativeName(fromList.coopName);
    }
  }, [coopId, cooperativesData]);

  // If not found in list, fetch it individually
  useEffect(() => {
    if (!coopId) return;
    const inList = cooperativesData?.some((c) => Number(c.id) === Number(coopId));
    if (inList) return; // Already found in the list
    if (fetchedCoopOnceRef.current) return; // Already attempted to fetch

    fetchedCoopOnceRef.current = true;
    getCooperativeById({ id: coopId });
  }, [coopId, cooperativesData, getCooperativeById]);

  // Update cooperative name when getCooperativeById succeeds
  useEffect(() => {
    if (getCooperativeByIdState?.success && getCooperativeByIdState?.data?.coopName) {
      setCooperativeName(getCooperativeByIdState.data.coopName);
    }
  }, [getCooperativeByIdState?.success, getCooperativeByIdState?.data?.coopName]);

  const contestabilityLabel = useMemo(() => {
    return contestabilityData?.find((c: IContestability) => c.id === quotationData?.quotations?.contestability)?.label || "N/A";
  }, [contestabilityData, quotationData?.quotations?.contestability]);

  const benefits = useMemo<TClppBenefits[]>(() => {
    return quotationData?.quotation?.clppBenefits ?? quotationData?.options?.clppBenefits ?? quotationData?.quotations?.clppBenefits ?? [];
  }, [quotationData]);

  return (
    <section className="px-6 py-4">
      <h1 className="text-xl font-bold text-primary mb-4">ACTUARY EVALUATION REPORT</h1>

      {/* Summary */}
      <div>
        {aerID && (
          <div className="grid grid-cols-4 gap-4 text-sm mb-4">
            <div className="text-slate-400">AER No.</div>
            <div>{aerID}</div>
          </div>
        )}

        <div className="grid grid-cols-4 gap-4 text-sm mb-4">
          <div className="text-zinc-400">COOPERATIVE</div>
          <div className="w-full">{cooperativeName || "N/A"}</div>
        </div>

        <div className="grid grid-cols-4 gap-4 text-sm mb-4">
          <div className="text-slate-400">Previous Provider</div>
          <div>{quotationData?.quotations?.previousProvider || "N/A"}</div>
        </div>

        <div className="grid grid-cols-4 gap-4 text-sm mb-4">
          <div className="text-slate-400">Total Members</div>
          <div>{quotationData?.quotations?.totalNumberOfMembers || "N/A"}</div>
        </div>

        <div className="grid grid-cols-4 gap-4 text-sm mb-4">
          <div className="text-slate-400">Contestability</div>
          <div>{contestabilityLabel}</div>
        </div>
      </div>

      <hr className="my-10 border-gray/10" />

      {/* Benefits */}
      <div className="w-full border-b border-zinc-200 bg-zinc-100 rounded-md">
        <div className="p-4">
          <div className="text-xl font-poppins-semibold text-primary">BENEFITS</div>
        </div>

        <div className="overflow-x-auto p-4">
          <table className="table-auto bg-white w-full border-collapse border border-zinc-300">
            <thead className="bg-primary text-white">
              <tr>
                <th className="border border-zinc-300 px-4 py-2"></th>
                {benefits.map((_, index) => (
                  <th key={index} className="border border-zinc-300 px-4 py-2">{`ENTRY ${index + 1}`}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {BENEFITSCOLUMNS.map((column, rowIdx) => (
                <tr key={rowIdx}>
                  <td className="text-left border px-4 py-2 w-1/4">{column.label}</td>
                  {benefits.map((benefit: TClppBenefits, colIdx: number) => {
                    if (column.name === "ageGroup") {
                      return (
                        <td key={colIdx} className="text-center border px-4 py-2">
                          {`${benefit.ageFrom} - ${benefit.ageTo}`}
                        </td>
                      );
                    }
                    const value = benefit[column.name as keyof TClppBenefits];

                    // Logic to display "Graduated" if isGraduated is true
                    if (column.name === "rate" && benefit.isGraduated) {
                      return (
                        <td key={colIdx} className="text-center border px-4 py-2">
                          Graduated
                        </td>
                      );
                    }

                    return (
                      <td key={colIdx} className="text-center border px-4 py-2">
                        {typeof value === "object" && value !== null ? JSON.stringify(value) : (value ?? "")}
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Conditions */}
      <div className="pb-10 mt-10">
        <div className="my-4 text-xl font-poppins-semibold">CONDITIONS</div>
        <div className="border rounded border-slate-300 p-4 text-sm leading-relaxed" dangerouslySetInnerHTML={{ __html: quotationData?.quotationCondition?.condition || "" }} />
      </div>

      {/* Actions */}
      <div className="flex justify-end gap-3">
        <Button classNames="bg-sky-500 text-white px-6 py-2 rounded-md hover:bg-primary-dark" onClick={onSaveDraft}>
          Save as Draft
        </Button>
        <Button variant="primary" classNames="bg-primary text-white px-6 py-2 rounded-md hover:bg-primary-dark" onClick={onSubmitAer}>
          Submit AER
        </Button>
      </div>
    </section>
  );
};

export default ClppReviewContent;
