import { formatPeso } from "@helpers/currency";
import React from "react";

interface ProjectionTableProps {
  values: any;
  resultValues: any;
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  showCalculated: boolean;
}

const ProjectionTable: React.FC<ProjectionTableProps> = ({ values, resultValues, handleChange, showCalculated }) => {
  const rows = [
    { projection: "Net Premium", key: "totalPremiumNetRate" },
    { projection: "Gross Premium", key: "totalPremiumGrossRate" },
    { projection: "No. of Claims", key: "numberOfClaims" },
    { projection: "Amount of Claims", key: "amountOfClaims" },
    { projection: "Claims Ratio", key: "claimsRatio" },
  ];

  const nameOf = (k: string) => `projection.${k}`;

  return (
    <div className="border border-gray/10 rounded-md min-w-lg w-full flex gap-2">
      <table className="w-full border-collapse border border-zinc-200 rounded">
        <thead className="bg-primary text-white">
          <tr>
            <th className="border border-zinc-200 px-4 py-2 text-left">PROJECTION</th>
            {showCalculated && <th className="border border-zinc-200 px-4 py-2 text-left text-xs">CALCULATED</th>}
            <th className="border border-zinc-200 px-4 py-2 text-left text-xs ">CUSTOM</th>
          </tr>
        </thead>
        <tbody className="text-sm">
          {rows.map((row) => (
            <tr key={row.key}>
              <td className="border border-zinc-200 px-4 py-2">{row.projection}</td>
              {showCalculated && (
                <td className="border border-zinc-200 px-4 py-2">
                  <input type="number" className="w-full border border-zinc-200 rounded px-2 py-1 bg-zinc-50" value={resultValues[row.key] ?? ""} disabled />
                </td>
              )}
              <td className="border border-zinc-200 px-4 py-2">
                <input type="number" className="w-full border border-zinc-200 rounded px-2 py-1" name={nameOf(row.key)} value={values[row.key] ?? ""} onChange={handleChange} />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      <div className="text-center mt-8">
        <div className="p-2">
          <p className="text-sm w-full text-zinc-500 text0x">Projected Total Net Premium</p>
          <p className="text-2xl font-poppins-semibold text-primary">{formatPeso(values.projectedTotalNetPremium) ?? 0}</p>
        </div>
        <div className="p-2 mt-8">
          <p className="text-sm w-full text-zinc-500">Projected Total Gross Premium</p>
          <p className="text-2xl font-poppins-semibold text-primary">{formatPeso(values.projectedTotalPremium) ?? 0}</p>
        </div>
      </div>
    </div>
  );
};

export default ProjectionTable;
