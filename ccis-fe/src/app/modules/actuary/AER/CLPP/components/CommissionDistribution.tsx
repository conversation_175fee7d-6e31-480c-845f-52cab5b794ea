import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";
import { useSelector } from "react-redux";
import Button from "@components/common/Button";
import EditableTable from "@modules/sales/components/editable-table";
import Select, { TOptions } from "@modules/sales/components/select";
import Switch from "@components/switch";
import colorMode from "@modules/sales/utility/color";
import { PiMinusCircle, PiPlusCircle } from "react-icons/pi";
import { ISelectOptions } from "@interface/common.interface";
import { ICommissionType } from "@interface/commission-structure.interface";
import { IUtilitiesCommissionAgeTypes } from "@interface/utilities.interface";
import { RootState } from "@state/store";
import { useCommissionAgeTypeSManagementActions } from "@state/reducer/utilities-commission-age-types";
import { useCommissionTypeActions } from "@state/reducer/commision-type";
import { COMMISION_AGE_TYPES, COMMISSION_TYPES, HIDDEN_COMMISSION_ID } from "@modules/sales/clpp-quotations/components/constants";
import { toast } from "react-toastify";

export type TClppCommissionDistribution = {
  commissionTypeId: number;
  ageTypeId: number;
  ageFrom?: number;
  ageTo?: number;
  rate: number;
  groupId?: string; // To group commissions
};

type TClppCommissionDistributionInfoProps = {
  value?: TClppCommissionDistribution[];
  onChange?: (data: TClppCommissionDistribution[]) => void;
  quotationData?: any;
  benefits?: Array<{ ageFrom: number; ageTo: number }>;
};

export const ClppCommissionDistributionInfo = ({ value, onChange, benefits = [] }: TClppCommissionDistributionInfoProps) => {
  const { getCommissionTypes } = useCommissionTypeActions();
  const { getCommissionAgeType } = useCommissionAgeTypeSManagementActions();

  const commissionAgeTypeData = useSelector((state: RootState) => state.utilitiesCommissionsAgeTypes.commissionAgeTypes);
  const commissionTypesData = useSelector((state: RootState) => state.commissionType.getCommissionTypes);

  const [coopMode, setCoopMode] = useState(false);
  const [commissionDistribution, setCommissionDistribution] = useState<TClppCommissionDistribution[]>(value ?? []);
  const [selectedAgeTypeFilter, setSelectedAgeTypeFilter] = useState<string>("");

  const mgmtFeeRateRef = useRef<number | null>(null);

  // Check if there are any commissions to determine if age type should be locked
  const hasCommissions = useMemo(() => commissionDistribution.length > 0, [commissionDistribution]);

  // Map commissionAgeTypeData to select items for Age Types
  const commissionAgeTypesSelectItems = useMemo<ISelectOptions[]>(() => {
    return commissionAgeTypeData.map((item: IUtilitiesCommissionAgeTypes) => ({
      text: item.name,
      value: item.id.toString(),
    }));
  }, [commissionAgeTypeData]);

  // Map commissionTypesData to select items for Commission Types
  const commissionTypesSelectItems = useMemo<TOptions[]>(() => {
    if (!commissionTypesData?.data) return [];
    return commissionTypesData.data
      .filter((item: ICommissionType) => !HIDDEN_COMMISSION_ID.includes(item.id))
      .map((item: ICommissionType) => ({
        text: item.commissionName,
        value: item.id.toString(),
      }));
  }, [commissionTypesData]);

  // Group commissions by groupId
  const groupedCommissions = useMemo(() => {
    const groups: { [key: string]: TClppCommissionDistribution[] } = {};

    commissionDistribution.forEach((item) => {
      const groupKey = item.groupId || "ungrouped";
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(item);
    });

    return groups;
  }, [commissionDistribution]);

  const totalDistribution = useMemo(() => {
    return commissionDistribution.reduce((acc, curr) => acc + Number(curr.rate), 0);
  }, [commissionDistribution]);

  const isStandardSelected = useCallback((selectedAgeTypeId: number) => {
    return selectedAgeTypeId == COMMISION_AGE_TYPES.STANDARD;
  }, []);

  const isAgeBracketSelected = useCallback((selectedAgeTypeId: number) => {
    return selectedAgeTypeId == COMMISION_AGE_TYPES.AGE_BRACKET;
  }, []);

  const generateGroupId = () => {
    return `group_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const handleAgeTypeChange = (newAgeType: string) => {
    if (hasCommissions) {
      toast.warning("Please remove all commissions before changing the age type.");
      return;
    }
    setSelectedAgeTypeFilter(newAgeType);
  };

  const handleCreateCommissionDistribution = () => {
    const ageTypeId = selectedAgeTypeFilter ? Number(selectedAgeTypeFilter) : 0;

    if (!ageTypeId) {
      toast.info("Please select an Age Type first");
      return;
    }

    const data = [...commissionDistribution];
    const newGroupId = generateGroupId();

    if (isAgeBracketSelected(ageTypeId)) {
      if (benefits.length === 0) {
        toast.info("No benefit age brackets available. Please add benefits first.");
        return;
      }

      benefits.forEach((benefit) => {
        data.push({
          commissionTypeId: 0,
          ageTypeId: ageTypeId,
          ageFrom: benefit.ageFrom,
          ageTo: benefit.ageTo,
          rate: 0,
          groupId: newGroupId,
        });
      });

      toast.success(`Added commission with ${benefits.length} age brackets`);
    } else {
      data.push({
        commissionTypeId: 0,
        ageTypeId: ageTypeId,
        ageFrom: 0,
        ageTo: 0,
        rate: 0,
        groupId: newGroupId,
      });
    }

    setCommissionDistribution(data);
    onChange?.(data);
  };

  const handleDeleteGroup = (groupId: string) => {
    const updated = commissionDistribution.filter((item) => item.groupId !== groupId);

    captureMgmtFeeRate(updated);
    setCommissionDistribution(updated);
    onChange?.(updated);
  };

  const normalizeRows = (rows?: Partial<TClppCommissionDistribution>[]): TClppCommissionDistribution[] => {
    if (!Array.isArray(rows)) return [];

    const commissionTypeGroups = new Map<number, string>();

    return rows.map((r) => {
      const commissionTypeId = Number(r.commissionTypeId ?? 0);

      let groupId = r.groupId;
      if (!groupId) {
        if (!commissionTypeGroups.has(commissionTypeId)) {
          commissionTypeGroups.set(commissionTypeId, `group_${commissionTypeId}_${Date.now()}`);
        }
        groupId = commissionTypeGroups.get(commissionTypeId)!;
      }

      let rate: any;
      if (r.rate === undefined || r.rate === null || (typeof r.rate === "string" && r.rate === "")) {
        rate = r.rate === "" ? "" : 0;
      } else {
        rate = Number(r.rate);
      }

      return {
        commissionTypeId,
        ageTypeId: Number(r.ageTypeId ?? 0),
        ageFrom: r.ageFrom != null ? Number(r.ageFrom) : 0,
        ageTo: r.ageTo != null ? Number(r.ageTo) : 0,
        rate,
        groupId,
      };
    });
  };

  const captureMgmtFeeRate = (rows: TClppCommissionDistribution[]) => {
    const mf = rows.find((r) => r.commissionTypeId === COMMISSION_TYPES.COOP_COMMISSION);
    if (mf && typeof mf.rate === "number" && !Number.isNaN(mf.rate)) {
      mgmtFeeRateRef.current = mf.rate;
    }
  };

  const handleSwitchCoopMode = useCallback(
    (checked: boolean) => {
      if (!checked) {
        setCoopMode(false);
        return;
      }

      const ageTypeId = selectedAgeTypeFilter ? Number(selectedAgeTypeFilter) : COMMISION_AGE_TYPES.STANDARD;

      const existingCoopCommissions = commissionDistribution.filter((row) => row.commissionTypeId === COMMISSION_TYPES.COOP_COMMISSION);

      if (existingCoopCommissions.length > 0) {
        const filteredCommissions = commissionDistribution.filter((row) => row.commissionTypeId === COMMISSION_TYPES.COOP_COMMISSION);
        setCommissionDistribution(filteredCommissions);
        onChange?.(filteredCommissions);
        setCoopMode(true);
        return;
      }

      let seeded: TClppCommissionDistribution[] = [];
      const coopGroupId = "coop_mode";

      if (isAgeBracketSelected(ageTypeId)) {
        if (benefits.length === 0) {
          toast.info("No benefit age brackets available. Please add benefits first.");
          return;
        }

        seeded = benefits.map((benefit) => ({
          commissionTypeId: COMMISSION_TYPES.COOP_COMMISSION,
          ageTypeId: ageTypeId,
          ageFrom: benefit.ageFrom,
          ageTo: benefit.ageTo,
          rate: mgmtFeeRateRef.current ?? 0,
          groupId: coopGroupId,
        }));
      } else {
        seeded = [
          {
            commissionTypeId: COMMISSION_TYPES.COOP_COMMISSION,
            ageTypeId: ageTypeId,
            ageFrom: 0,
            ageTo: 0,
            rate: mgmtFeeRateRef.current ?? 0,
            groupId: coopGroupId,
          },
        ];
      }

      setCommissionDistribution(seeded);
      onChange?.(seeded);
      setCoopMode(true);
    },
    [commissionDistribution, onChange, selectedAgeTypeFilter, benefits]
  );

  useEffect(() => {
    getCommissionTypes();
    getCommissionAgeType({ filter: "" });
  }, []);

  useEffect(() => {
    const normalizedRows = normalizeRows(value ?? []);
    setCommissionDistribution(normalizedRows);

    if (normalizedRows.length > 0 && !selectedAgeTypeFilter) {
      setSelectedAgeTypeFilter(normalizedRows[0].ageTypeId.toString());
    }
  }, [value]);

  const getCommissionTypeName = (commissionTypeId: number) => {
    const commissionType = commissionTypesSelectItems.find((item) => item.value === commissionTypeId.toString());
    return commissionType?.text || "Not Selected";
  };

  const getFilteredCommissionTypes = (groupId: string) => {
    const selectedCommissionTypes = commissionDistribution.filter((item) => item.groupId !== groupId).map((item) => item.commissionTypeId);

    return commissionTypesSelectItems.filter((item) => !selectedCommissionTypes.includes(Number(item.value)));
  };

  return (
    <div
      className={colorMode({
        classLight: "border border-gray/10 bg-[#F6F6F680] p-6",
        classDark: "shadow-sm bg-black/10 p-6",
      })}
    >
      <h3
        className={colorMode({
          classLight: "text-lg text-center text-primary font-[600] text-[16px]",
          classDark: "text-lg text-center text-white/60 font-[600] text-[16px]",
        })}
      >
        COMMISSION LOADING
      </h3>
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center gap-4">
          <Select
            className="min-w-[200px]"
            placeholder="Select Age Type"
            options={commissionAgeTypesSelectItems}
            value={selectedAgeTypeFilter}
            onChange={(e) => handleAgeTypeChange(e.toString())}
            disabled={hasCommissions}
          />
        </div>
        <div className="flex flex-row flex-nowrap items-center gap-2">
          <div className="flex items-center gap-2">
            <span className="text-sm">COOP MODE</span>
            <Switch checked={coopMode} onChange={(v: any) => handleSwitchCoopMode(typeof v === "boolean" ? v : !!v?.target?.checked)} />
          </div>
          {!coopMode && (
            <Button classNames="block ms-auto" onClick={handleCreateCommissionDistribution}>
              <div className="flex flex-row items-center gap-2">
                <PiPlusCircle className="inline text-primary" />
                <span className="font-thin text-primary font-[300] text-sm">Add Commission</span>
              </div>
            </Button>
          )}
        </div>
      </div>

      <div className="space-y-6">
        {Object.entries(groupedCommissions).map(([groupId, groupRows], groupIndex) => {
          const firstRow = groupRows[0];
          const availableCommissionTypes = getFilteredCommissionTypes(groupId);

          return (
            <div key={groupId} className="grid grid-cols-12 gap-4">
              <div className="col-span-12">
                <div className="flex justify-between items-center mb-2">
                  <h4
                    className={colorMode({
                      classLight: "text-sm font-semibold text-gray-700",
                      classDark: "text-sm font-semibold text-white/70",
                    })}
                  >
                    Commission {groupIndex + 1}: {getCommissionTypeName(firstRow.commissionTypeId)}
                  </h4>
                  {!coopMode && (
                    <Button classNames="!w-fit !h-fit !px-3 !py-1 bg-red-500 hover:bg-red-600 text-white" onClick={() => handleDeleteGroup(groupId)}>
                      <div className="flex items-center gap-1">
                        <PiMinusCircle />
                        <span className="text-xs">Remove Group</span>
                      </div>
                    </Button>
                  )}
                </div>

                <EditableTable
                  className="border-b-0"
                  columns={[
                    {
                      key: "commissionType",
                      header: "COMMISSION TYPE",
                      className: "text-[14px] font-[500]",
                      locked: coopMode,
                      render(data, index) {
                        if (index !== 0) return null;

                        return (
                          <Select
                            placeholder="--- Select ---"
                            className="min-w-[250px]"
                            options={availableCommissionTypes}
                            disabled={coopMode}
                            value={data.commissionTypeId?.toString() ?? ""}
                            onChange={(e) => {
                              const chosenId = Number(e);
                              const updated = [...commissionDistribution];

                              updated.forEach((row) => {
                                if (row.groupId === groupId) {
                                  row.commissionTypeId = chosenId;
                                }
                              });

                              captureMgmtFeeRate(updated);
                              setCommissionDistribution(updated);
                              onChange?.(updated);
                            }}
                          />
                        );
                      },
                      rowSpan: (_rows, rowIndex) => (rowIndex === 0 ? groupRows.length : 0),
                    },
                    {
                      key: "ageFrom",
                      header: "AGE FROM",
                      className: "text-[14px] font-[500]",
                      number: true,
                      formatInput: true,
                      locked: (_, index) => isStandardSelected(groupRows[index].ageTypeId) || isAgeBracketSelected(groupRows[index].ageTypeId),
                    },
                    {
                      key: "ageTo",
                      header: "AGE TO",
                      className: "text-[14px] font-[500]",
                      number: true,
                      formatInput: true,
                      locked: (_, index) => isStandardSelected(groupRows[index].ageTypeId) || isAgeBracketSelected(groupRows[index].ageTypeId),
                    },
                    {
                      key: "rate",
                      header: "COMMISSION PERCENTAGE",
                      className: "text-[14px] font-[500]",
                      number: true,
                    },
                  ]}
                  rows={groupRows}
                  onChange={(updatedRows) => {
                    const updated = [...commissionDistribution];

                    groupRows.forEach((originalRow, idx) => {
                      const globalIndex = updated.findIndex((r) => r === originalRow);
                      if (globalIndex !== -1) {
                        updated[globalIndex] = updatedRows[idx] as TClppCommissionDistribution;
                      }
                    });

                    setCommissionDistribution(updated);
                    onChange?.(updated);
                    captureMgmtFeeRate(updated);
                  }}
                />
              </div>
            </div>
          );
        })}
      </div>

      {Object.keys(groupedCommissions).length > 0 && (
        <div
          className={colorMode({
            classLight: "bg-primary/10 p-4 border-2 border-primary mt-4 rounded",
            classDark: "bg-primary/20 p-4 border-2 border-primary mt-4 rounded",
          })}
        >
          <div className="flex justify-between">
            <span
              className={colorMode({
                className: "font-poppins-semibold text-[16px] font-[700]",
                classLight: " text-primary",
                classDark: "text-white",
              })}
            >
              OVERALL TOTAL
            </span>
            <span
              className={colorMode({
                classLight: "text-primary font-bold text-[16px]",
                classDark: "text-white font-bold text-[16px]",
              })}
            >
              {totalDistribution.toFixed(2)}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};
