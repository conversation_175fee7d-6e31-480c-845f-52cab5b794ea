// modules/actuary/CLPP/components/ClppReviewModal.tsx
import React from "react";
import { IoClose } from "react-icons/io5";
import Button from "@components/common/Button";
import ClppReviewContent, { ClppReviewContentProps } from "./CLPPReviewContent";

type Props = {
  isOpen: boolean;
  onClose: () => void;
} & Pick<ClppReviewContentProps, "quotationData" | "selectedQuotationProduct" | "signatoryTemplateId" | "status" | "aerID" | "onSubmitAer" | "onSaveDraft">;

const ClppReviewModal: React.FC<Props> = ({ isOpen, onClose, ...contentProps }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[999] bg-black/40 flex items-start md:items-center justify-center p-4 overflow-y-auto">
      <div className="w-full max-w-6xl bg-white rounded-xl shadow-xl">
        <div className="flex items-center justify-between border-b px-4 py-3">
          <h2 className="text-lg font-semibold text-primary">Review & Submit</h2>
          <Button classNames="!bg-transparent !text-slate-600 hover:!bg-slate-100" onClick={onClose}>
            <IoClose size={20} />
          </Button>
        </div>

        <div className="max-h-[80vh] overflow-y-auto">
          <ClppReviewContent {...contentProps} />
        </div>
      </div>
    </div>
  );
};

export default ClppReviewModal;
