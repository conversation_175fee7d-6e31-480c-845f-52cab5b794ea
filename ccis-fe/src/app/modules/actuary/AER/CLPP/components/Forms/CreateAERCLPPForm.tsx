/* eslint-disable react-hooks/exhaustive-deps */
import _ from "lodash";
import * as Yup from "yup";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { toast } from "react-toastify";
import Button from "@components/common/Button";
import TextField from "@components/form/TextField";
import Radio from "@components/form/Radio";
import { ROUTES } from "@constants/routes";
import { Statuses } from "@constants/global-constant-value";
import { RATE_PROJECT_COMMISSION_ALLOCATION } from "@constants/product-proposal";
import { RootState } from "@state/reducer";
import { useAppDispatch } from "@state/store";
import { useQuotationActions } from "@state/reducer/quotations";
import { useProductActions } from "@state/reducer/products";
import { useProductBenefitsManagementActions } from "@state/reducer/utilities-product-benefits";
import { useContestabilityActions } from "@state/reducer/contestability";
import { ProductCode, ProductName } from "@enums/product-code";
import { ProductStatus } from "@enums/product-status";
import { confirmSaveOrEdit, showSuccess } from "@helpers/prompt";
import { TCreateClppQuotationWithAerPayload } from "@state/types/quotation";
import { useFormik } from "formik";
import httpClient from "@clients/httpClient";
import colorMode from "@modules/sales/utility/color";
import { parseNumber } from "@modules/sales/utility/number";
import { useModalController } from "@modules/sales/controller";
import { COMMISION_AGE_TYPES } from "@modules/sales/gyrt-quotations/components/constants";
import DemographicModal from "@modules/actuary/modals/DemographicModa";
import BasisModal from "@modules/actuary/modals/BasisModal";
import { AlertError } from "@modules/sales/components/alert-error";
import { ClppCommissionDistributionInfo, TClppCommissionDistribution } from "../CommissionDistribution";
import { ClppBaseInfo, TClppBaseData } from "../ClppBasicInfo";
import { ClppMemberAndAverageInfo, TClppMemberAndAverageInfo } from "../ClppAverageInfo";
import { ClppLoanPortfolio, TClppLoanPortfolio } from "../ClppLoanPortfolio";
import { ClppBenefitInfo, TClppBenefitInfo } from "../ClppBenefitInfo";
import ProjectionTable from "../ClppProjection";
import ClppSavedOptionsPanel from "../ClppOptionInfo";

import { readQuotationFromStorage, writeQuotationToStorage } from "../Utils/quotation-storage";
import { ClppClaimsExperienceInfo } from "../ClppClaimsExperienceInfo";
import { putClppAER } from "@state/reducer/actuary-clpp-aer";
import ClppReviewModal from "../CLPPReviewModal";

export type TClppOptionProjection = {
  totalPremiumNetRate?: number | string;
  totalPremiumGrossRate?: number | string;
  numberOfClaims?: number | string;
  amountOfClaims?: number | string;
  claimsRatio?: number | string;
  udd?: number | string;
  projectedTotalNetPremium?: number | string;
  projectedTotalGrossPremium?: number | string;
};

export type TClppOptions = {
  option: number;
  benefits: TClppBenefitInfo[];
  conditions: string;
  projection?: TClppOptionProjection;
  commissionDistribution?: TClppCommissionDistribution[];
};

type TClppFormState = {
  baseInfo: TClppBaseData;
  memberAndAverageInfo: TClppMemberAndAverageInfo;
  loanPortfolio: TClppLoanPortfolio;
  benefits: TClppBenefitInfo[];
  status: String;
  quotationCondition: { condition: string };
  claimsExperience: {
    years: Array<{ year: string | number; numberOfDeaths: number; totalClaimAmount: number }>;
    ages: Array<{ ageFrom: number; ageTo: number; numberOfDeaths: number; totalClaimAmount: number }>;
    isAgeVisible: boolean;
  };
  commissionDistribution: TClppCommissionDistribution[];
};

const initialState = (): TClppFormState => ({
  baseInfo: { cooperativeId: 0, previousProvider: "", typeOfCoverage: "", contestabilityId: 0, productId: 0 },
  quotationCondition: { condition: "" },
  memberAndAverageInfo: { totalNumberOfMembers: 0 },
  loanPortfolio: { loanPortfolioYears: [], loanPortfolioAges: [] },
  claimsExperience: { years: [], ages: [], isAgeVisible: false },
  status: Statuses.DRAFT,
  commissionDistribution: [],
  benefits: [{ ageFrom: 18, ageTo: 0, maximumCoverage: 0, maximumTerm: 0, nml: 0, nel: 0, rate: 0 }],
});

// ---------------- Utils ----------------
const DEFAULT_OPTION = 1 as const;

const debounce = <F extends (...a: any[]) => void>(fn: F, ms = 500) => {
  let t: any;
  return (...args: Parameters<F>) => {
    clearTimeout(t);
    t = setTimeout(() => fn(...args), ms);
  };
};

const numOrNull = (v: any) => {
  const n = Number(v);
  return Number.isFinite(n) ? n : null;
};

const toNumOrStr = (v: any) => {
  if (v === null || v === undefined || v === "") return "";
  const n = Number(v);
  return Number.isFinite(n) ? n : String(v);
};

const collectProjectionRows = (root: any): any[] => {
  if (!root) return [];
  const a = Array.isArray(root.projection) ? root.projection : [];
  const b = Array.isArray(root.options?.projection) ? root.options.projection : [];
  return [...a, ...b];
};

export default function CreateAerCLPPForm() {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { getProducts } = useProductActions();
  const { clearClppQuotation } = useQuotationActions();

  const { getContestability } = useContestabilityActions();
  const { getProductBenefits } = useProductBenefitsManagementActions();

  // redux selects
  const products = useSelector((s: RootState) => s.products?.getProducts?.data?.data || []);
  const productBenefitsData = useSelector((s: RootState) => s.utilitiesProductBenefits.productBenefits);
  const aerData = useSelector((s: RootState) => s.quotation.createClppQuotationWithAer);
  const productData = useSelector((s: RootState) => s.products?.product);
  const mainProducts = useSelector((s: RootState) => s.products?.getMainProducts?.data?.data || []);
  const subProducts = useSelector((s: RootState) => s.products?.getSubProducts?.data?.data || []);

  // storage-first hydrate key — BUT prefer route state to avoid stale autosave
  const location = useLocation();
  const stored = readQuotationFromStorage();
  const routeQ = location.state?.quotationData ?? null;
  const sourceData = routeQ ?? stored ?? null;
  const quotationKey = sourceData?.id ?? "new";

  // local state
  const [formState, setFormState] = useState<TClppFormState>(() => initialState());
  const [conditions, setConditions] = useState<string>("");
  const [basisForCalculation, setBasisForCalculation] = useState<string>(RATE_PROJECT_COMMISSION_ALLOCATION.MASTERLIST.key);
  const [showConditionContainer, setShowConditionContainer] = useState(false);
  const [alertError, setAlertError] = useState<string | undefined>(undefined);
  const [hasDemographics, setHasDemographics] = useState<boolean>(false);
  const [fileName, setFileName] = useState<string>("");
  const [demographicData, setDemographicData] = useState<any>(null);
  const putClppAERLoading = useSelector((state: RootState) => state.clppAER.putClppAER?.success);
  const [selectBasisModal, setSelectBasisModal] = useState(false);
  const handleSelectBasisModal = () => setSelectBasisModal((p) => !p);

  // Signatory template state (panel will modify this)
  const [signatoryTemplateId, setSignatoryTemplateId] = useState<number | null>(() => {
    const fromLoc = location.state?.signatoryTemplateId ?? null;
    const fromStore = readQuotationFromStorage()?.quotations?.signatoryTemplateId ?? null;
    return fromLoc ?? fromStore ?? null;
  });

  // Review Modal
  const [showReviewModal, setShowReviewModal] = useState(false);

  // modals
  const demographicModalController = useModalController();

  // hydrate flags
  const [hydratedOnce, setHydratedOnce] = useState(false);
  const hydratingRef = useRef(false);

  // effects: initial fetches
  useEffect(() => {
    getProductBenefits({ filter: "" });
    getContestability({ filter: ProductCode.CLPP });
    getProducts({ params: { filter: "", statusFilter: ProductStatus.APPROVED, page: 1, pageSize: 9999 } });
  }, []);

  // ---------- Pre-hydrate (sync) from sourceData so Formik initialValues can use it ----------
  const hydrated = useMemo(() => {
    if (!sourceData) {
      return {
        form: initialState(),
        projectionForOption: undefined as TClppOptionProjection | undefined,
      };
    }

    const quotation = sourceData?.quotation ?? sourceData ?? {};
    const benefits = Array.isArray(quotation.clppBenefits) ? quotation.clppBenefits : [];
    let allBenefits = benefits;
    if (benefits.length > 0 && (benefits[0] as any).hasOwnProperty("option")) {
      const groupedBenefits = _.groupBy(benefits, "option");
      const firstOptionKey = Object.keys(groupedBenefits)[0];
      allBenefits = firstOptionKey ? (groupedBenefits as any)[firstOptionKey] : [];
    }

    const mappedBenefits = allBenefits.map((benefit: any) => ({
      ageFrom: Number(benefit.ageFrom || benefit.age_from || 18),
      ageTo: Number(benefit.ageTo || benefit.age_to || 0),
      maximumCoverage: Number(benefit.maximumCoverage || benefit.maximum_coverage || 0),
      maximumTerm: Number(benefit.maximumTerm || benefit.maximum_term || 0),
      nml: Number(benefit.nml || 0),
      nel: Number(benefit.nel || 0),
      rate: Number(benefit.rate || benefit.netRate || benefit.net_rate || 0),
      benefitId: Number(benefit.benefitId ?? benefit.benefit_id ?? 0),
    }));

    const loanPortfolioYears = Array.isArray(quotation.clppLoanPortfolioYears)
      ? quotation.clppLoanPortfolioYears.map((item: any) => ({
          year: item.year || new Date().getFullYear().toString(),
          minimumAge: item.minimumAge || 0,
          maximumAge: item.maximumAge || 0,
          minimumLoanAmount: item.minimumLoanAmount || 0,
          maximumLoanAmount: item.maximumLoanAmount || 0,
          totalLoanPortfolio: parseFloat((item.totalLoanPortfolio || "0").toString().replace(/,/g, "")),
          numberOfBorrowers: item.totalNumberOfBorrowers || item.numberOfBorrowers || 0,
          averageAge: item.averageAge || 0,
        }))
      : Array.isArray(quotation.loanPortfolioYears)
        ? quotation.loanPortfolioYears.map((item: any) => ({
            year: item.year || new Date().getFullYear().toString(),
            minimumAge: item.minimumAge || 0,
            maximumAge: item.maximumAge || 0,
            minimumLoanAmount: item.minimumLoanAmount || 0,
            maximumLoanAmount: item.maximumLoanAmount || 0,
            totalLoanPortfolio: parseFloat((item.totalLoanPortfolio || "0").toString().replace(/,/g, "")),
            numberOfBorrowers: item.numberOfBorrowers || item.totalNumberOfBorrowers || 0,
            averageAge: item.averageAge || 0,
          }))
        : [];

    const loanPortfolioAges = Array.isArray(quotation.clppLoanPortfolioAges)
      ? quotation.clppLoanPortfolioAges.map((item: any) => ({
          ageFrom: item.ageFrom || 0,
          ageTo: item.ageTo || 0,
          totalLoanPortfolio: parseFloat((item.totalLoanPortfolioAmount || "0").toString().replace(/,/g, "")),
          numberOfBorrowers: item.totalNumberOfMembers || 0,
          averageAge: parseNumber(item.averageAge),
        }))
      : Array.isArray(quotation.loanPortfolioAges)
        ? quotation.loanPortfolioAges.map((item: any) => ({
            ageFrom: item.ageFrom || 0,
            ageTo: item.ageTo || 0,
            totalLoanPortfolio: parseFloat((item.totalLoanPortfolio || "0").toString().replace(/,/g, "")),
            numberOfBorrowers: item.numberOfBorrowers || 0,
            averageAge: parseNumber(item.averageAge),
          }))
        : [];

    const claimsExperienceYears = Array.isArray(quotation.quotationClaimsExperienceYear)
      ? quotation.quotationClaimsExperienceYear.map((item: any) => ({
          year: item.year || new Date().getFullYear().toString(),
          numberOfDeaths: item.numberOfDeaths || 0,
          totalClaimAmount: parseFloat((item.totalClaimAmount || "0").toString()),
        }))
      : Array.isArray(quotation.claimsExperienceYears)
        ? quotation.claimsExperienceYears
        : [];

    const claimsExperienceAges = Array.isArray(quotation.quotationClaimsExperienceAge)
      ? quotation.quotationClaimsExperienceAge.map((item: any) => ({
          ageFrom: item.ageFrom || 0,
          ageTo: item.ageTo || 0,
          numberOfDeaths: item.numberOfDeaths || 0,
          totalClaimAmount: parseFloat((item.totalClaimAmount || "0").toString()),
        }))
      : Array.isArray(quotation.claimsExperienceAges)
        ? quotation.claimsExperienceAges
        : [];

    const commissionDistribution = Array.isArray(quotation.quotationCommissionDistribution)
      ? quotation.quotationCommissionDistribution.map((item: any) => ({
          commissionTypeId: Number(item.commissionTypeId || item.commissionType || 0),
          ageTypeId: Number(item.commissionAgeType || item.ageTypeId || item.commissionAgeTypeId || 1),
          ageFrom: item.ageFrom ? Number(item.ageFrom) : undefined,
          ageTo: item.ageTo ? Number(item.ageTo) : undefined,
          rate: Number(item.rate || 0),
          option: item.option != null ? Number(item.option) : undefined,
        }))
      : Array.isArray(quotation.commissionDistribution)
        ? quotation.commissionDistribution.map((item: any) => ({
            commissionTypeId: Number(item.commissionTypeId || 0),
            ageTypeId: Number(item.ageTypeId || 1),
            ageFrom: item.ageFrom ? Number(item.ageFrom) : undefined,
            ageTo: item.ageTo ? Number(item.ageTo) : undefined,
            rate: Number(item.rate || 0),
            option: item.option != null ? Number(item.option) : undefined,
          }))
        : Array.isArray(sourceData?.commissionDistributions)
          ? sourceData.commissionDistributions.map((item: any) => ({
              commissionTypeId: Number(item.commissionTypeId || 0),
              ageTypeId: Number(item.commissionAgeType || item.ageTypeId || 1),
              ageFrom: item.ageFrom ? Number(item.ageFrom) : undefined,
              ageTo: item.ageTo ? Number(item.ageTo) : undefined,
              rate: Number(item.rate || 0),
              option: item.option != null ? Number(item.option) : undefined,
            }))
          : [];

    // Pull projection
    const rawFromQuotation = collectProjectionRows(quotation);
    const rawFromSource = collectProjectionRows(sourceData);
    const rawProjectionRows = [...rawFromQuotation, ...rawFromSource];

    const normalizedProjectionByOption: Record<number, TClppOptionProjection & { option: number }> = {};
    for (const row of rawProjectionRows) {
      const opt = Number(row?.option ?? DEFAULT_OPTION);
      normalizedProjectionByOption[opt] = {
        option: opt,
        totalPremiumNetRate: toNumOrStr(row?.totalPremiumNetRate),
        totalPremiumGrossRate: toNumOrStr(row?.totalPremiumGrossRate),
        numberOfClaims: toNumOrStr(row?.numberOfClaims),
        amountOfClaims: toNumOrStr(row?.amountOfClaims),
        claimsRatio: toNumOrStr(row?.claimsRatio),
        udd: toNumOrStr(row?.udd),
        projectedTotalNetPremium: toNumOrStr(row?.projectedTotalNetPremium),
        projectedTotalGrossPremium: toNumOrStr(row?.projectedTotalGrossPremium ?? row?.projectedTotalPremium),
      };
    }

    const projForDefaultOpt = normalizedProjectionByOption[DEFAULT_OPTION];

    const conditionsText = quotation.quotationCondition?.condition || "";

    const form: TClppFormState = {
      status: Statuses.DRAFT,
      quotationCondition: { condition: sourceData?.quotation?.quotationCondition?.condition ?? "" },
      baseInfo: {
        cooperativeId: quotation.coopId || 0,
        previousProvider: quotation.previousProvider || "",
        typeOfCoverage: quotation.coverageTypeId?.toString() || "",
        contestabilityId: quotation.contestability || 0,
        productId: quotation.productId || 0,
      },
      memberAndAverageInfo: { totalNumberOfMembers: quotation.totalNumberOfMembers || 0 },
      loanPortfolio: { loanPortfolioYears, loanPortfolioAges },
      claimsExperience: { years: claimsExperienceYears, ages: claimsExperienceAges, isAgeVisible: claimsExperienceAges.length > 0 },
      commissionDistribution,
      benefits: mappedBenefits.length > 0 ? mappedBenefits : initialState().benefits,
    };

    return {
      form,
      projectionForOption: projForDefaultOpt,
      conditionsText,
      // hasDemographics: !!quotation.fileName,
      fileName: quotation.fileName || "",
    };
  }, [quotationKey]);

  // push hydrated (sync) into local state once
  useEffect(() => {
    if (hydratedOnce || hydratingRef.current) return;
    hydratingRef.current = true;

    setFormState(hydrated.form);
    setConditions((hydrated as any)?.conditionsText || "");
    // setHasDemographics(!!(hydrated as any)?.hasDemographics);
    setFileName((hydrated as any)?.fileName || "");

    const existingFileName: string | undefined = sourceData?.fileName;
    if (existingFileName) {
      setHasDemographics(true);
    }

    setHydratedOnce(true);
    hydratingRef.current = false;
  }, [hydrated, hydratedOnce]);

  useEffect(() => {
    if (!aerData?.data) return;
    navigate(ROUTES.SALES.quotationClppAerView.parse(aerData?.data?.quotationId ?? aerData?.data.id), { state: { demographicData } });
    toast.success("Quotation created successfully!");
    dispatch(clearClppQuotation());
    setFormState(initialState());
    setConditions("");
    setAlertError(undefined);
  }, [aerData]);

  // Average Claims — compute from claimsExperience (exclude 0s)
  const calculateInitialAverageClaims = (claimsExperience: any) => {
    const { years, ages, isAgeVisible } = claimsExperience;
    const source = isAgeVisible ? ages : years;
    const deaths = (source || []).map((r: any) => parseNumber(r.numberOfDeaths)).filter((n: number) => Number.isFinite(n) && n > 0);
    if (deaths.length === 0) return 0;
    const total = deaths.reduce((a: number, b: number) => a + b, 0);
    return total / deaths.length;
  };

  // ---------- Formik (Projection/Commission) ----------
  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      averageAge: "",
      averageClaims: calculateInitialAverageClaims(hydrated.form.claimsExperience), // <-- prefill on load
      lossRatio: "",
      projection: {
        totalPremiumNetRate: hydrated.projectionForOption?.totalPremiumNetRate ?? "",
        totalPremiumGrossRate: hydrated.projectionForOption?.totalPremiumGrossRate ?? "",
        numberOfClaims: hydrated.projectionForOption?.numberOfClaims ?? "",
        amountOfClaims: hydrated.projectionForOption?.amountOfClaims ?? "",
        claimsRatio: hydrated.projectionForOption?.claimsRatio ?? "",
        udd: hydrated.projectionForOption?.udd ?? "",
        projectedTotalNetPremium: hydrated.projectionForOption?.projectedTotalNetPremium ?? "",
        projectedTotalGrossPremium: hydrated.projectionForOption?.projectedTotalGrossPremium ?? "",
      },

      projectionResult: {
        totalPremiumNetRate: hydrated.projectionForOption?.totalPremiumNetRate ?? "",
        totalPremiumGrossRate: hydrated.projectionForOption?.totalPremiumGrossRate ?? "",
        numberOfClaims: hydrated.projectionForOption?.numberOfClaims ?? "",
        amountOfClaims: hydrated.projectionForOption?.amountOfClaims ?? "",
        claimsRatio: hydrated.projectionForOption?.claimsRatio ?? "",
      },
      totalNumberOfMembers: formState.memberAndAverageInfo.totalNumberOfMembers || 0,
      cooperativeId: formState.baseInfo.cooperativeId || 0,
      commissionDistribution: formState.commissionDistribution || [],
    },
    validationSchema: Yup.object({
      lossRatio: Yup.number().typeError("Loss Ratio must be a number").moreThan(0, "Loss Ratio must be > 0").required("Loss Ratio is required"),
      projection: Yup.object({
        udd: Yup.number().typeError("Mortality Rate must be a number").moreThan(0, "Mortality Rate must be > 0").required("Mortality Rate is required"),
      }),
      cooperativeId: Yup.number().moreThan(0, "Cooperative is required"),
      totalNumberOfMembers: Yup.number().moreThan(0, "Total Number of Members is required"),
    }),
    onSubmit: () => handleCalculate(),
  });

  // keep mirrors synced
  useEffect(() => {
    formik.setFieldValue("totalNumberOfMembers", formState.memberAndAverageInfo.totalNumberOfMembers);
  }, [formState.memberAndAverageInfo.totalNumberOfMembers]);

  useEffect(() => {
    formik.setFieldValue("cooperativeId", formState.baseInfo.cooperativeId);
  }, [formState.baseInfo.cooperativeId]);

  useEffect(() => {
    formik.setFieldValue("commissionDistribution", formState.commissionDistribution);
  }, [formState.commissionDistribution]);

  const hasCalculatedProjection = useMemo(() => Object.values(formik.values.projectionResult || {}).some((v) => v !== "" && v !== null && v !== undefined), [formik.values.projectionResult]);

  const isCalculateDisabled = useMemo(() => {
    const { lossRatio, projection, totalNumberOfMembers, cooperativeId } = formik.values as any;
    if (!basisForCalculation || !lossRatio || !projection.udd || !totalNumberOfMembers || !cooperativeId) return true;

    const yearsRows = formState.loanPortfolio.loanPortfolioYears ?? [];
    const agesRows = formState.loanPortfolio.loanPortfolioAges ?? [];
    if (yearsRows.length === 0 || agesRows.length === 0) return true;

    const hasValidCommission = (formik.values as any).commissionDistribution?.some((item: any) => item.commissionTypeId && parseFloat(item.rate) > 0);
    return !hasValidCommission;
  }, [formState, basisForCalculation, formik.values]);

  // keep condition string in form state
  useEffect(() => {
    if (formState.quotationCondition.condition !== conditions) {
      setFormState((p) => ({ ...p, quotationCondition: { condition: conditions } }));
    }
  }, [conditions]);

  // auto-fill total members from latest year borrowers, if no demographics
  useEffect(() => {
    if (!demographicData) {
      const borrowers = latestYearBorrowers(formState.loanPortfolio.loanPortfolioYears);
      if (borrowers > 0 && borrowers !== formState.memberAndAverageInfo.totalNumberOfMembers) {
        setFormState((p) => ({ ...p, memberAndAverageInfo: { ...p.memberAndAverageInfo, totalNumberOfMembers: borrowers } }));
      }
    }
  }, [demographicData, formState.loanPortfolio.loanPortfolioYears]);

  const needsDemographicsForSeniors = (benefits: TClppBenefitInfo[], loanPortfolio: TClppLoanPortfolio) => {
    const hasExisting = loanPortfolio.loanPortfolioYears.length > 0 || loanPortfolio.loanPortfolioAges.length > 0;
    if (hasExisting) return false;
    return (benefits ?? []).some((b) => Number(b.ageFrom ?? 0) >= 66);
  };

  const submitErrors = useMemo(() => {
    const errs: string[] = [];
    if (needsDemographicsForSeniors(formState.benefits, formState.loanPortfolio) && !hasDemographics) {
      errs.push("Upload demographics is required because a benefit age bracket starts at age 66 or above.");
    }
    if (!signatoryTemplateId) {
      errs.push("Please select a Signatory Template.");
    }
    return errs;
  }, [formState.benefits, formState.loanPortfolio, hasDemographics, signatoryTemplateId]);

  const canSubmit = submitErrors.length === 0;

  // Re-compute avg claims live
  const getAvgClaims = (rows: Array<{ numberOfDeaths: any }>) => {
    const deaths = rows.map((r) => parseNumber(r.numberOfDeaths)).filter((n) => Number.isFinite(n) && n > 0);
    if (deaths.length === 0) return 0;
    const total = deaths.reduce((a, b) => a + b, 0);
    return total / deaths.length;
  };

  useEffect(() => {
    const { years, ages, isAgeVisible } = formState.claimsExperience;
    const source = isAgeVisible ? ages : years;
    const avg = getAvgClaims(source);
    if (avg !== parseNumber(formik.values.averageClaims)) {
      formik.setFieldValue("averageClaims", avg);
    }
  }, [formState.claimsExperience.years, formState.claimsExperience.ages, formState.claimsExperience.isAgeVisible]);

  // ---------- Calculate ----------
  const handleCalculate = async () => {
    const yearsRows = formState.loanPortfolio.loanPortfolioYears;
    const calculatedAverageAge = yearsRows.length > 0 ? yearsRows.reduce((s, r) => s + (Number(r.averageAge) || 0), 0) / yearsRows.length : 0;

    const missingFields: string[] = [];
    const { averageAge, averageClaims, lossRatio, totalNumberOfMembers, cooperativeId, commissionDistribution } = formik.values as any;
    if (!lossRatio) missingFields.push("Loss Ratio");

    let avgAgeForCalc = averageAge || calculatedAverageAge;
    if (basisForCalculation === RATE_PROJECT_COMMISSION_ALLOCATION.MASTERLIST.key) {
      if (!avgAgeForCalc) missingFields.push("Average Age");
    }

    if (missingFields.length > 0) {
      toast.error(`Please fill out the following fields before calculating: ${missingFields.join(", ")}`);
      return;
    }

    if (avgAgeForCalc && avgAgeForCalc !== averageAge) formik.setFieldValue("averageAge", Number(avgAgeForCalc.toFixed(1)));

    const averageLoanAmount = yearsRows.length
      ? yearsRows.reduce((sum, r) => {
          const borrowers = Math.max(1, parseNumber(r.numberOfBorrowers));
          return sum + parseNumber(r.totalLoanPortfolio) / borrowers;
        }, 0) / yearsRows.length
      : 0;

    const commissionForCalc = (commissionDistribution ?? []).map((i: any) => ({
      commissionTypeId: i.commissionTypeId,
      rate: parseFloat(i.rate || "0") || 0,
    }));

    try {
      const requestBody = buildProjectionPayload({
        basisForCalculation,
        averageAge: avgAgeForCalc,
        averageClaims,
        lossRatio,
        averageLoanAmount,
        groupSize: totalNumberOfMembers,
        productId: basis.values.productId,
        cooperativeId,
        periodFrom: basis.values.periodFrom,
        periodTo: basis.values.periodTo,
        quotationCommissionDistribution: commissionForCalc,
      });

      const response = await httpClient.post("/clpp-calculation/getClppPremium", requestBody);
      if (response?.data) {
        const data = response.data;
        const projectedNet = data?.projectedTotalNetPremium ?? data?.projectedTotalNet ?? null;
        const projectedTotal = data?.projectedTotalPremium ?? data?.projectedTotalPremium ?? null;

        if (projectedNet != null) {
          formik.setFieldValue("projection.projectedTotalNetPremium", typeof projectedNet === "number" ? projectedNet.toFixed(2) : projectedNet);
        }
        if (projectedTotal != null) {
          formik.setFieldValue("projection.projectedTotalPremium", typeof projectedTotal === "number" ? projectedTotal.toFixed(2) : projectedTotal);
        }

        formik.setFieldValue("projection.claimsRatio", data?.claimsRatio ?? "");
        formik.setFieldValue("projection.totalPremiumNetRate", data?.clppYearlyNetRate?.toFixed?.(2) ?? data?.clppYearlyNetRate ?? "");
        formik.setFieldValue("projection.totalPremiumGrossRate", data?.clppYearlyGrossRate?.toFixed?.(2) ?? data?.clppYearlyGrossRate ?? "");
        formik.setFieldValue("projection.numberOfClaims", data?.expectedNumberOfClaims ?? "");
        formik.setFieldValue("projection.amountOfClaims", data?.expectedAmountOfClaims ?? "");

        formik.setFieldValue("projectionResult", {
          totalPremiumNetRate: data?.clppYearlyNetRate ?? "",
          totalPremiumGrossRate: data?.clppYearlyGrossRate ?? "",
          numberOfClaims: data?.expectedNumberOfClaims ?? "",
          amountOfClaims: data?.expectedAmountOfClaims ?? "",
          claimsRatio: data?.claimsRatio ?? "",
        });

        toast.success("Premiums calculated successfully.");
      }
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || "An error occurred while calculating premiums. Please try again.";
      toast.error(errorMessage);
    }
  };

  const buildProjectionPayload = ({
    basisForCalculation,
    averageAge,
    averageClaims,
    lossRatio,
    averageLoanAmount,
    groupSize,
    productId,
    cooperativeId,
    periodFrom,
    periodTo,
    quotationCommissionDistribution,
  }: {
    basisForCalculation: string;
    averageAge?: number | string;
    averageClaims?: number | string;
    lossRatio: number | string;
    averageLoanAmount?: number | string;
    groupSize: number | string;
    productId?: number | string;
    cooperativeId?: number | string;
    periodFrom?: string;
    periodTo?: string;
    quotationCommissionDistribution: Array<{ commissionTypeId: number; rate: number }>;
  }) => {
    const base: any = {
      basisForCalculation,
      lossRatio,
      productType: ProductCode.CLPP,
      quotationCommissionDistribution,
    };
    if (basisForCalculation === RATE_PROJECT_COMMISSION_ALLOCATION.COOP_EXISTING_PRODUCT.key) {
      return { ...base, productId, cooperativeId, periodFrom, periodTo, groupSize };
    }
    return { ...base, averageAge, averageClaims, averageLoanAmount, groupSize };
  };

  // ---------- Demographics ----------
  const handleShowDemographic = () => {
    if (hasDemographics) {
      if (demographicModalController.isOpen) {
        handleSelectBasisModal();
      } else {
        demographicModalController.openFn();
      }
    } else {
      handleSelectBasisModal();
    }
  };

  const basis = useFormik({
    initialValues: {
      productId: 0,
      subProductId: 0,
      baseFrom: "",
      periodType: "",
      periodFrom: "01-01-2023",
      periodTo: "31-12-2024",
      ageBasis: "",
      age: [
        { ageFrom: 18, ageTo: 49 },
        { ageFrom: 50, ageTo: 65 },
        { ageFrom: 66, ageTo: 69 },
        { ageFrom: 70, ageTo: 999 },
      ],
      productType: ProductCode.CLPP,
      cooperativeId: formState.baseInfo.cooperativeId ?? 0,
      fileName: fileName || "",
    },
    onSubmit: () => handleSelectBasisModal(),
  });

  useEffect(() => {
    basis.setFieldValue("cooperativeId", formState.baseInfo.cooperativeId || 0);
    basis.setFieldValue("fileName", fileName);
  }, [formState.baseInfo.cooperativeId, fileName]);

  const handleUpdateBasis = (data: any) => {
    basis.setValues(data);
  };

  const handleGenerate = async () => {
    if (!basis.values.cooperativeId) {
      toast.error("Please select a cooperative first.");
      return;
    }
    const formattedDate = {
      periodFrom: basis.values.periodFrom.split("-").reverse().join("-"),
      periodTo: basis.values.periodTo.split("-").reverse().join("-"),
    };
    const { age, periodFrom, periodTo, ...rest } = basis.values;
    const payload = { ...rest, ...formattedDate };
    try {
      const res = await httpClient.post("/quotations/actuaryDemographicData", payload);
      showSuccess("Demographic data generated successfully.").then(() => {
        setHasDemographics(true);
        setDemographicData(res.data);
        demographicModalController.openFn();
      });
    } catch (err: any) {
      const msg = err?.response?.data?.message || err?.response?.data?.error || JSON.stringify(err?.response?.data) || "An unexpected error occurred while generating the basis.";
      toast.error(msg);
    }
  };

  // ---------- Payload (single-option) ----------
  const createAerPayload = (): TCreateClppQuotationWithAerPayload => {
    const lifeInsuranceBenefit = productBenefitsData.find((benefit: { benefitName: string }) => benefit.benefitName === "Life Insurance");
    const existingTplId = signatoryTemplateId ?? location.state?.signatoryTemplateId ?? sourceData?.quotations?.signatoryTemplateId ?? 0;

    const option = DEFAULT_OPTION;

    const benefitsPayload = (formState.benefits ?? []).map((b) => ({
      ageFrom: parseNumber(b.ageFrom?.toString() ?? "0"),
      ageTo: parseNumber(b.ageTo?.toString() ?? "0"),
      benefitId: Number(lifeInsuranceBenefit?.id ?? 0),
      hasRider: false,
      maximumCoverage: parseNumber(b.maximumCoverage?.toString() ?? "0"),
      maximumTerm: parseNumber(b.maximumTerm?.toString() ?? "0"),
      nml: parseNumber(b.nml?.toString() ?? "0"),
      nel: parseNumber(b.nel?.toString() ?? "0"),
      rate: parseNumber(b.rate?.toString() ?? "0"),
      option,
      isGraduated: b.rate === 0 && b.ageTo >= 66,
    }));

    const premiumsPayload = (formState.benefits ?? []).map((benefit) => ({
      netPremium: parseNumber((benefit.rate ?? 0).toString()),
      grossPremium: parseNumber((benefit as any)?.grossRate?.toString?.() ?? "0"),
      option,
    }));

    const formProj = (formik?.values as any)?.projection ?? {};
    const projectionsPayload = [
      {
        option,
        totalPremiumNetRate: numOrNull(formProj.totalPremiumNetRate) ?? 0,
        totalPremiumGrossRate: numOrNull(formProj.totalPremiumGrossRate) ?? 0,
        numberOfClaims: numOrNull(formProj.numberOfClaims) ?? 0,
        amountOfClaims: numOrNull(formProj.amountOfClaims) ?? 0,
        claimsRatio: numOrNull(formProj.claimsRatio) ?? 0,
        udd: numOrNull(formProj.udd) ?? 0,
      },
    ];

    const commissionsPayload = (formState.commissionDistribution ?? []).map((data) => ({
      commissionTypeId: data.commissionTypeId,
      commissionAgeType: data.ageTypeId,
      option,
      ...(data.ageTypeId != COMMISION_AGE_TYPES.STANDARD ? { ageFrom: data.ageFrom } : {}),
      ...(data.ageTypeId != COMMISION_AGE_TYPES.STANDARD ? { ageTo: data.ageTo } : {}),
      rate: parseNumber((data?.rate as any) ?? 0),
    }));

    return {
      quotations: {
        coopId: parseNumber(formState.baseInfo.cooperativeId ?? "0"),
        previousProvider: formState.baseInfo.previousProvider,
        contestability: parseNumber(formState.baseInfo.contestabilityId ?? "0"),
        totalNumberOfMembers: parseNumber(formState.memberAndAverageInfo.totalNumberOfMembers),
        coverageTypeId: parseNumber(formState.baseInfo.typeOfCoverage ?? "1"),
        signatoryTemplateId: existingTplId,
        fileName: fileName || "",
      },
      status: Statuses.DRAFT,
      quotationCondition: { condition: `${conditions}` },

      claimsExperienceYears: formState.claimsExperience.years.map((item) => ({
        year: item.year?.toString(),
        numberOfDeaths: parseNumber(item.numberOfDeaths),
        totalClaimAmount: parseNumber(item.totalClaimAmount),
      })),
      claimsExperienceAges: formState.claimsExperience.isAgeVisible
        ? formState.claimsExperience.ages.map((item) => ({
            ageFrom: parseInt(item.ageFrom?.toString() ?? "0", 10),
            ageTo: parseInt(item.ageTo?.toString() ?? "0", 10),
            numberOfDeaths: parseInt(item.numberOfDeaths?.toString() ?? "0", 10),
            totalClaimAmount: parseNumber(item.totalClaimAmount),
          }))
        : [],

      commissionDistributions: commissionsPayload,

      options: {
        clppBenefits: benefitsPayload,
        quotationPremiums: premiumsPayload,
        aerOptions: JSON.stringify([option]),
        projection: projectionsPayload,
      },

      projection: projectionsPayload,

      loanPortfolioAges: formState.loanPortfolio.loanPortfolioAges.map((item) => ({
        ageFrom: parseNumber(item.ageFrom),
        ageTo: parseNumber(item.ageTo),
        totalNumberOfMembers: parseNumber(item.numberOfBorrowers),
        totalLoanPortfolioAmount: parseNumber(item.totalLoanPortfolio),
      })),
      loanPortfolioYears: formState.loanPortfolio.loanPortfolioYears.map((item) => ({
        year: item.year,
        minimumAge: parseNumber(item.minimumAge),
        maximumAge: parseNumber(item.maximumAge),
        minimumLoanAmount: parseNumber(item.minimumLoanAmount),
        maximumLoanAmount: parseNumber(item.maximumLoanAmount),
        totalNumberOfBorrowers: parseNumber(item.numberOfBorrowers),
        totalLoanPortfolio: parseNumber(item.totalLoanPortfolio),
        averageAge: parseNumber(item.averageAge),
      })),
    };
  };

  const saveDraft = useCallback(
    debounce(() => {
      try {
        const snapshot = createAerPayload();
        writeQuotationToStorage(snapshot);
      } catch {
        toast.error("Failed to save draft");
      }
    }, 400),
    [signatoryTemplateId, conditions, formState, formik.values]
  );

  useEffect(() => {
    saveDraft();
  }, [
    formState.baseInfo,
    formState.memberAndAverageInfo,
    formState.loanPortfolio,
    formState.claimsExperience,
    formState.benefits,
    formState.commissionDistribution,
    conditions,
    formik.values.projection,
    formik.values.projectionResult,
    signatoryTemplateId,
  ]);

  // ---------- Navigation  ----------

  useEffect(() => {
    if (fileName) fetchDemographicData();
  }, [fileName]);

  const fetchDemographicData = async () => {
    try {
      const res = await httpClient.post("/quotations/demographicData", { fileName, productType: ProductCode.CLPP });
      setHasDemographics(true);
      setDemographicData(res.data);
    } catch {
      toast.error("An unexpected error occurred. Please try again.");
    }
  };

  const latestYearBorrowers = (rows: TClppLoanPortfolio["loanPortfolioYears"]) => {
    if (!rows?.length) return 0;
    const latest = rows.reduce((acc, r) => {
      const y = parseInt((r.year ?? "").toString(), 10);
      const ay = parseInt((acc.year ?? "").toString(), 10);
      return (Number.isFinite(y) ? y : -Infinity) >= (Number.isFinite(ay) ? ay : -Infinity) ? r : acc;
    }, rows[0]);
    return Number(latest?.numberOfBorrowers || 0);
  };

  // ----- Review Modal callbacks (submit/draft are handled inside modal content normally) -----
  const openReview = () => setShowReviewModal(true);
  const closeReview = () => setShowReviewModal(false);

  const handleSubmitAER = async () => {
    const parsed = readQuotationFromStorage();
    if (!parsed) {
      toast.error("Missing data");
      return;
    }
    const { quotations = {} } = parsed;

    const payload = _.merge({}, parsed, {
      status: ProductStatus.FOR_SIGNATORY,
      quotations: { ...quotations, signatoryTemplateId: signatoryTemplateId ?? quotations?.signatoryTemplateId },
    });

    const sanitized = { ...payload, id: sourceData?.id ?? undefined };
    const isConfirmed = await confirmSaveOrEdit(`Do you want to submit the AER?`);
    if (isConfirmed) {
      try {
        await dispatch(putClppAER(sanitized));
        toast.success("AER successfully submitted.");
        if (putClppAERLoading) {
          navigate(ROUTES.ACTUARY.CLPP.key);
        }
      } catch {
        toast.error("Failed to submit AER");
      }
    }
  };

  const handleSaveDraft = async () => {
    const parsed = readQuotationFromStorage();
    if (!parsed) {
      toast.error("Missing data");
      return;
    }
    const { quotations = {} } = parsed;

    const draftPayload = _.merge({}, parsed, {
      status: ProductStatus.DRAFT,
      quotations: { ...quotations, signatoryTemplateId: signatoryTemplateId ?? quotations?.signatoryTemplateId },
    });

    const sanitized = { ...draftPayload, id: sourceData?.id ?? undefined };
    const isConfirmed = await confirmSaveOrEdit(`Do you want to save the AER as draft?`);
    if (isConfirmed) {
      try {
        await dispatch(putClppAER(sanitized));
        toast.success("AER saved as draft.");
      } catch {
        toast.error("Failed to save draft");
      }
    }
  };

  return (
    <section>
      {selectBasisModal && (
        <BasisModal
          isOpen={selectBasisModal}
          onClose={handleSelectBasisModal}
          data={basis.values}
          onUpdate={handleUpdateBasis}
          onGenerate={handleGenerate}
          mainProducts={mainProducts}
          products={products}
          subProducts={subProducts}
        />
      )}

      {demographicData && (
        <DemographicModal controller={demographicModalController} basisValues={basis.values} hasDemographics={hasDemographics} demographicData={demographicData} productName={ProductName.CLSP} />
      )}

      <ClppReviewModal
        isOpen={showReviewModal}
        onClose={closeReview}
        quotationData={createAerPayload()} // always pass the latest snapshot
        selectedQuotationProduct={productData}
        signatoryTemplateId={signatoryTemplateId}
        status={formState.status as string}
        aerID={sourceData?.id ?? null}
        onSubmitAer={handleSubmitAER}
        onSaveDraft={handleSaveDraft}
      />

      <div className="grid grid-cols-12 gap-2 px-4">
        <div className="block col-span-12">
          <h3 className={colorMode({ classLight: "text-2xl font-[600] mb-3 text-primary", classDark: "text-2xl font-[600] mb-3 text-white/60" })}>CLPP QUOTATION</h3>
          <p className={colorMode({ classLight: "text-gray text-base text-[12px]", classDark: "text-white/60 text-base text-[12px]" })}>
            Create a customized Coop Loan Protection Plan quotation based on cooperative-specific data.
          </p>
        </div>

        <div className="col-span-12 space-y-10">
          {alertError != undefined && <AlertError message={alertError} />}

          <ClppBaseInfo value={formState.baseInfo} onChange={(value) => setFormState((prev) => ({ ...prev, baseInfo: { ...value, productId: Number(productData?.id) || 0 } }))} />

          <hr className="my-10 border-gray/10" />

          <ClppMemberAndAverageInfo value={formState.memberAndAverageInfo} onChange={(value) => setFormState((prev) => ({ ...prev, memberAndAverageInfo: value }))} />

          <ClppLoanPortfolio
            value={formState.loanPortfolio}
            onChange={(v) => setFormState((prev) => ({ ...prev, loanPortfolio: v }))}
            hasDemographics={!!demographicData}
            demographics={demographicData}
          />

          <ClppClaimsExperienceInfo
            value={{
              ...formState.claimsExperience,
              years: formState.claimsExperience.years.map((item) => ({
                ...item,
                year: typeof item.year === "string" ? parseInt(item.year, 10) : item.year,
              })),
            }}
            onChange={(value) => setFormState((prev) => ({ ...prev, claimsExperience: { ...value, isAgeVisible: value.isAgeVisible ?? false } }))}
          />

          <ClppBenefitInfo value={formState.benefits} onChange={(next) => setFormState((prev) => (_.isEqual(prev.benefits, next) ? prev : { ...prev, benefits: next }))} />

          <div className="w-full mt-4 p-4 border border-zinc-300 rounded-md items-center h-max gap-2 flex">
            <div className="w-full flex items-center justify-between gap-2">
              <div className="text-sm text-primary flex items-center justify-center font-poppins-semibold">Demographic Data</div>
              <Button type="button" classNames="text-sm text-yellow-500 bg-yellow-100 rounded-md" onClick={handleShowDemographic}>
                Show Demographic
              </Button>
            </div>
          </div>

          {/* Rate Projection and Commission Allocation */}
          <div className="mb-3">
            <h2 className="text-[20px] font-poppins-medium font-[600] text-primary">RATE PROJECTION AND COMMISSION ALLOCATION</h2>
            <p className="text-gray text-[12px]">
              Please ensure to fill out Loss Ratio and Mortality Rate, select a basis for calculation, and set commission loading. The 'Calculate' button will generate the projection.
            </p>
          </div>

          <div>
            <div className="flex justify-between mb-2 mt-4">
              <div className="flex gap-4">
                <div className="space-y-2">
                  <label htmlFor="lossRatio" className={colorMode({ classLight: "text-[14px] font-poppins-medium font-[500]", classDark: "text-[14px] font-poppins-medium font-[500]" })}>
                    Loss Ratio
                  </label>
                  <TextField
                    name="lossRatio"
                    placeholder="Loss Ratio"
                    className={`!w/full ${formik.touched.lossRatio && formik.errors.lossRatio ? "!border-red-500" : "!border-[#00000040]"}`}
                    onChange={formik.handleChange}
                    value={(formik.values as any).lossRatio}
                    onBlur={formik.handleBlur}
                    error={!!(formik.touched as any).lossRatio && !!(formik.errors as any).lossRatio}
                    errorText={(formik.errors as any).lossRatio as string}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <label
                    htmlFor="uniformDistributionOfDeath"
                    className={colorMode({ classLight: "text-[14px] font-poppins-medium font-[500]", classDark: "text-[14px] font-poppins-medium font-[500]" })}
                  >
                    Mortality Rate
                  </label>
                  <TextField
                    name="projection.udd"
                    placeholder="Mortality Rate"
                    className={`!w/full ${(formik.touched as any).projection?.udd && (formik.errors as any).projection?.udd ? "!border-red-500" : "!border-[#00000040]"}`}
                    onChange={formik.handleChange}
                    value={(formik.values as any).projection.udd}
                    onBlur={formik.handleBlur}
                    error={!!(formik.touched as any).projection?.udd && !!(formik.errors as any).projection?.udd}
                    errorText={(formik.errors as any).projection?.udd as string}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <label
                    htmlFor="uniformDistributionOfDeath"
                    className={colorMode({ classLight: "text-[14px] font-poppins-medium font-[500]", classDark: "text-[14px] font-poppins-medium font-[500]" })}
                  >
                    Average Claims
                  </label>
                  <TextField name="averageClaims" placeholder="AverageClaims" onChange={formik.handleChange} value={formik.values.averageClaims} disabled required />
                </div>
              </div>
            </div>

            <label className="text-sm">Basis for Calculation</label>
            <div className="flex justify-between gap-2">
              <div className="flex gap-2 justify-start">
                <Radio
                  value={RATE_PROJECT_COMMISSION_ALLOCATION.NO_DATA.key}
                  label="No Data"
                  name="basisForCalculation"
                  checked={basisForCalculation === RATE_PROJECT_COMMISSION_ALLOCATION.NO_DATA.key}
                  onChange={(v) => setBasisForCalculation(v)}
                />
                <Radio
                  value={RATE_PROJECT_COMMISSION_ALLOCATION.MASTERLIST.key}
                  label="Masterlist"
                  name="basisForCalculation"
                  checked={basisForCalculation === RATE_PROJECT_COMMISSION_ALLOCATION.MASTERLIST.key}
                  onChange={(v) => setBasisForCalculation(v)}
                />
                <Radio
                  value={RATE_PROJECT_COMMISSION_ALLOCATION.COOP_EXISTING_PRODUCT.key}
                  label="Coop Existing Product"
                  name="basisForCalculation"
                  checked={basisForCalculation === RATE_PROJECT_COMMISSION_ALLOCATION.COOP_EXISTING_PRODUCT.key}
                  onChange={(v) => setBasisForCalculation(v)}
                />
              </div>
            </div>
          </div>

          <div className="flex gap-4">
            <ProjectionTable values={formik.values.projection} handleChange={formik.handleChange} resultValues={formik.values.projectionResult} showCalculated={hasCalculatedProjection} />
          </div>

          <ClppCommissionDistributionInfo
            quotationData={sourceData}
            value={formState.commissionDistribution}
            benefits={formState.benefits}
            onChange={(value) => setFormState((prev) => ({ ...prev, commissionDistribution: value }))}
          />

          <hr className="my-8 border-gray/10" />

          {/* Action Buttons */}
          <div className="flex justify-end gap-4 mt-8">
            <Button type="button" classNames={`text-white w-40 ${isCalculateDisabled ? "bg-slate-400 cursor-not-allowed" : "bg-green-500"}`} onClick={handleCalculate} disabled={isCalculateDisabled}>
              <span className="text-[14px] font-[400] font-poppins-medium">Calculate</span>
            </Button>
          </div>

          {/* Saved Options / Conditions / Signatory / Review */}
          <ClppSavedOptionsPanel
            show
            quotationCondition={{ condition: conditions }}
            onSetShowConditionContainer={setShowConditionContainer}
            showConditionContainer={showConditionContainer}
            onConditionChange={(content) => setConditions(content)}
            canSubmit={canSubmit}
            submitErrors={submitErrors}
            // New props expected in your updated panel:
            signatoryTemplateId={signatoryTemplateId}
            onChangeSignatoryTemplateId={(id: number | null) => {
              setSignatoryTemplateId(id);
              const latest = readQuotationFromStorage() ?? {};
              const updated = {
                ...latest,
                quotations: { ...(latest.quotations || {}), signatoryTemplateId: id ?? undefined },
              };
              writeQuotationToStorage(updated);
            }}
            onOpenReview={openReview}
          />
        </div>
      </div>
    </section>
  );
}
