import Tabs from "@components/common/Tabs";
import EditableTable from "@modules/sales/components/editable-table";
import colorMode from "@modules/sales/utility/color";
import { useEffect, useState } from "react";

export type TClppLoanPortfolio = {
  loanPortfolioYears: TClppLoanPortfolioYear[];
  loanPortfolioAges: TClppLoanPortfolioAge[];
};

type TClppLoanPortfolioYear = {
  year: string;
  minimumAge: number;
  minimumLoanAmount: number;
  maximumAge: number;
  maximumLoanAmount: number;
  totalLoanPortfolio: number;
  numberOfBorrowers: number;
  averageAge: number;
};

type TClppLoanPortfolioAge = {
  ageFrom: number;
  ageTo: number;
  numberOfBorrowers: number;
  totalLoanPortfolio: number;
  averageAge?: number;
};

type TClppLoanPortfolioProps = {
  value: TClppLoanPortfolio;
  onChange: (value: TClppLoanPortfolio) => void;
  hasDemographics?: boolean;
  demographics?: any;
};

export const ClppLoanPortfolio = ({ value, onChange }: TClppLoanPortfolioProps) => {
  const [fieldState, setFieldState] = useState<TClppLoanPortfolio>(
    value ?? {
      loanPortfolioYears: [],
      loanPortfolioAges: [],
    }
  );

  const handleChange = (key: keyof TClppLoanPortfolio, val: any) => {
    const data = { ...fieldState, [key]: val };
    setFieldState(data);
    onChange?.(data);
  };

  useEffect(() => {
    if (value) setFieldState(value);
  }, [value]);

  return (
    <div
      className={colorMode({
        classLight: "border border-gray/10 bg-[#F6F6F680] p-6",
        classDark: "shadow-sm bg-black/10 p-6",
      })}
    >
      <div className="flex justify-between items-center mb-3">
        <h3
          className={colorMode({
            classLight: "text-lg text-primary font-[600] text-[16px]",
            classDark: "text-lg text-white/60 font-[600] text-[16px]",
          })}
        >
          LOAN PORTFOLIO
        </h3>
      </div>

      <Tabs
        headers={[
          { label: "Year", key: "year" },
          { label: "Age", key: "age" },
        ]}
        contents={[
          // ===== YEAR TAB =====
          <div className="space-y-2">
            <EditableTable
              columns={[
                { key: "year", header: "Year(s)", disabled: true },
                {
                  key: "totalLoanAmount",
                  header: "Minimum",
                  children: [
                    { key: "minimumAge", header: "Age", number: true, formatInput: true, disabled: true },
                    { key: "minimumLoanAmount", header: "Loan Amount", number: true, formatInput: true, disabled: true },
                  ],
                  disabled: true,
                },
                {
                  key: "maximum",
                  header: "Maximum",
                  children: [
                    { key: "maximumAge", header: "Age", number: true, formatInput: true, disabled: true },
                    { key: "maximumLoanAmount", header: "Loan Amount", number: true, formatInput: true, disabled: true },
                  ],
                  disabled: true,
                },
                {
                  key: "total",
                  header: "Total",
                  children: [
                    { key: "numberOfBorrowers", header: "NO. OF BORROWERS", number: true, formatInput: true, disabled: true },
                    { key: "totalLoanPortfolio", header: "LOAN PORTFOLIO", number: true, formatInput: true, disabled: true },
                  ],
                  disabled: true,
                },
                { key: "averageAge", header: "AVERAGE AGE", number: true, formatInput: true, disabled: true },
              ]}
              rows={fieldState.loanPortfolioYears}
              onChange={(updatedRows) => {
                handleChange("loanPortfolioYears", updatedRows);
              }}
            />
          </div>,

          // ===== AGE TAB =====
          <div className="space-y-2">
            <EditableTable
              columns={[
                {
                  key: "ageFrom",
                  header: "Age From",
                  number: true,
                  dataList: ["18", "66", "71", "75"],
                  formatInput: true,
                  disabled: true,
                },
                {
                  key: "ageTo",
                  header: "Age To",
                  number: true,
                  dataList: ["65", "70", "75", "80"],
                  formatInput: true,
                  disabled: true,
                },
                {
                  key: "numberOfBorrowers",
                  header: "NO. OF BORROWERS",
                  number: true,
                  formatInput: true,
                  disabled: true,
                },
                {
                  key: "totalLoanPortfolio",
                  header: "LOAN PORTFOLIO AMOUNT",
                  number: true,
                  formatInput: true,
                  disabled: true,
                },
                {
                  key: "averageAge",
                  header: "AVERAGE AGE",
                  number: true,
                  formatInput: true,
                  disabled: true,
                },
              ]}
              rows={fieldState.loanPortfolioAges}
              onChange={(updatedRows) => {
                handleChange("loanPortfolioAges", updatedRows);
              }}
            />
          </div>,
        ]}
      />
    </div>
  );
};
