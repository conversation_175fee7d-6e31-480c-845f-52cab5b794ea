import { useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { useSignatoryTemplatesManagementActions } from "@state/reducer/utilities-signatory-template";
import { useSignatoryTypesManagementActions } from "@state/reducer/utilities-signatory-type";
import { useUserManagementActions } from "@state/reducer/users-management";
import Select from "@modules/sales/components/select";
import { FaUserCircle } from "react-icons/fa";
import { ISelectOptions } from "@interface/common.interface";

interface SignatorySelectorProps {
  selectedTemplateId: number | null;
  onTemplateChange: (templateId: number | null) => void;
  className?: string;
}

const SignatorySelector: React.FC<SignatorySelectorProps> = ({ selectedTemplateId, onTemplateChange, className = "" }) => {
  const { getSignatoryTemplate, getSignatoryTemplateID } = useSignatoryTemplatesManagementActions();
  const { getSignatoryType } = useSignatoryTypesManagementActions();
  const { getUsers } = useUserManagementActions();

  const selectedTemplate = useSelector((state: RootState) => state.utilitiesSignatoryTemplate.selectedSignatoryTemplate);
  const signatoryTemplates = useSelector((state: RootState) => state.utilitiesSignatoryTemplate.signatoryTemplates);
  const users = useSelector((state: RootState) => state.usersManagement.users);
  const signatoryTypes = useSelector((state: RootState) => state.utilitiesSignatoryType?.signatoryTypes);

  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize data on mount
  useEffect(() => {
    if (!isInitialized) {
      getSignatoryTemplate({ params: { filter: "" } });
      getUsers({ filter: "" });
      getSignatoryType({ filter: "" });
      setIsInitialized(true);
    }
  }, [isInitialized]); // eslint-disable-line

  // Load selected template details when templateId changes
  useEffect(() => {
    if (selectedTemplateId && selectedTemplateId > 0) {
      getSignatoryTemplateID({ id: selectedTemplateId });
    }
  }, [selectedTemplateId]); // eslint-disable-line

  const templateOptions = useMemo<ISelectOptions[]>(
    () => [
      { value: "", text: "Select Signatory Template" },
      ...signatoryTemplates.map((t) => ({
        value: t.id.toString(),
        text: t.templateName,
      })),
    ],
    [signatoryTemplates]
  );

  const signatories = useMemo(() => {
    const templateUsers = selectedTemplate?.data?.signatoriesTemplateUsers || [];
    return templateUsers
      .map((st: any) => {
        const user = users.find((u) => u.id === st.userId);
        return user
          ? {
              ...user,
              sequence: st.sequence,
              signatoryTypeId: st.signatoryTypeId,
            }
          : null;
      })
      .filter(Boolean)
      .sort((a: any, b: any) => a.sequence - b.sequence);
  }, [selectedTemplate, users]);

  const groupedSignatories = useMemo(() => {
    const grouped: Record<number, any[]> = {};
    for (const sig of signatories) {
      if (!sig) continue;
      if (!grouped[sig.signatoryTypeId]) grouped[sig.signatoryTypeId] = [];
      grouped[sig.signatoryTypeId].push(sig);
    }
    return grouped;
  }, [signatories]);

  const handleTemplateChange = (value: string) => {
    if (!value) {
      onTemplateChange(null);
      return;
    }
    const id = parseInt(value, 10);
    if (!id || Number.isNaN(id)) {
      onTemplateChange(null);
      return;
    }
    onTemplateChange(id);
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="border border-zinc-200 rounded-lg p-4">
        <h3 className="text-lg font-medium mb-4 text-primary">Signatory</h3>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">Select Template</label>
          <Select
            name="signatoryTemplateId"
            options={templateOptions}
            value={selectedTemplateId ? selectedTemplateId.toString() : ""}
            onChange={handleTemplateChange}
            placeholder="Choose a signatory template..."
            required
          />
        </div>

        {selectedTemplateId && Object.keys(groupedSignatories).length > 0 && (
          <div className="space-y-4">
            {Object.entries(groupedSignatories).map(([typeId, group]) => {
              const type = signatoryTypes.find((t: { id: number; signatoryTypeName: string }) => t.id === Number(typeId));
              return (
                <div key={typeId} className="space-y-2">
                  <h5 className="text-sm font-semibold text-primary uppercase">{type?.signatoryTypeName || "Unnamed Type"}</h5>

                  <div className="space-y-2">
                    {group.map((signatory: any) => (
                      <div key={signatory.id} className="flex items-center w-full h-16 rounded-xl p-4 bg-gray-50 border border-zinc-300 shadow">
                        <div className="px-2">
                          <FaUserCircle size={32} className="text-primary" />
                        </div>
                        <div className="flex flex-col flex-1 justify-center">
                          <div className="text-sm font-semibold text-gray-800">{[signatory.firstname, signatory.middlename, signatory.lastname].filter(Boolean).join(" ")}</div>
                          <div className="text-xs text-gray-500">{signatory.position?.positionName || "No position"}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {selectedTemplateId && Object.keys(groupedSignatories).length === 0 && <div className="text-sm text-gray-500 italic">No signatories found for this template</div>}
      </div>
    </div>
  );
};

export default SignatorySelector;
