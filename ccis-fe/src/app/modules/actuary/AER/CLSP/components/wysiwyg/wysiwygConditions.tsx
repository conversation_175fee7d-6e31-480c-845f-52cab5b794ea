// components/form/WysiwygConditionEditor.tsx
import { useState, useEffect, FC } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

interface WysiwygConditionEditorProps {
  value?: string;
  onChange: (content: string) => void;
  className?: string;
}

const WysiwygConditionEditor: FC<WysiwygConditionEditorProps> = ({
  value = "",
  onChange,
  className = "",
}) => {
  const [content, setContent] = useState(value);

  const handleChange = (html: string) => {
    setContent(html);
    onChange(html);
  };

  useEffect(() => {
    setContent(value);
  }, [value]);

  return (
    <div className={className}>
      <ReactQuill
        theme="snow"
        value={content}
        onChange={handleChange}
        className="min-h-[200px]"
        modules={{
          toolbar: [
            [{ header: [] }, { font: [] }],
            [{ size: [] }],
            ["bold", "italic", "underline", "strike", "blockquote"],
            [{ list: "ordered" }, { list: "bullet" }],
            [{ align: [] }],
            ["clean"],
          ],
        }}
      />
    </div>
  );
};

export default WysiwygConditionEditor;
