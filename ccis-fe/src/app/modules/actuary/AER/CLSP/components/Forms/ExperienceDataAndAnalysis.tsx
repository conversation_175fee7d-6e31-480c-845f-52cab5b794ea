import { EXPERIENCE_DATA_ANALYSIS } from "@constants/product-proposal";
import Table from "@modules/actuary/AER/components/Table";
import Button from "@components/common/Button";
import Details from "@modules/actuary/AER/components/Details";
import { experienceColumns, claimsColumns } from "@constants/clsp-data";
import colorMode from "@modules/sales/utility/color";
import { data7 } from "@modules/actuary/AER/GYRT/Forms/data";
interface Props {
  selectedDetail: string;
  onDetailSelect: (val: string | null) => void;
  handleUploadMasterList: () => void;
  handleShowDemographic: () => void;
  demographicExperienceSummary: any[];
}

export default function ExperienceAnalysis({ selectedDetail, onDetailSelect, handleShowDemographic, demographicExperienceSummary }: Props) {
  return (
    <div className="w-full min-h-80 h-96 gap-2 flex">
      <div className="w-[30%] ">
        <Details
          title="Experience and Data Analysis"
          details={Object.values(EXPERIENCE_DATA_ANALYSIS).map((item) => ({
            label: item.key
              .toLowerCase()
              .replace(/_/g, " ")
              .replace(/\b\w/g, (char) => char.toUpperCase()),
            value: item.key,
          }))}
          onDetailSelect={onDetailSelect}
          selected={selectedDetail}
        />
      </div>

      <div className="w-[70%] h-full overflow-auto border border-zinc-300 rounded-md p-4">
        <div className="flex justify-end gap-4">
          <Button classNames="text-sm text-yellow-500 bg-yellow-100 rounded-md  " onClick={handleShowDemographic}>
            Show Demographic
          </Button>

          <Button classNames="!bg-[#99999926] rounded-[5px]" variant="default" onClick={handleShowDemographic}>
            <span
              className={colorMode({
                classLight: "text-gray/50 text-[14px] font-[400] font-poppins-medium",
                classDark: "text-white/60 text-[14px] font-[400] font-poppins-medium",
              })}
            >
              Show Demographic
            </span>
          </Button>
        </div>

        {selectedDetail === EXPERIENCE_DATA_ANALYSIS.BASED_ON_MASTERLIST.key && <Table columns={experienceColumns} data={demographicExperienceSummary} headerClassNames="border-b border-zinc-300" />}

        {selectedDetail === EXPERIENCE_DATA_ANALYSIS.BASED_COOP_EXISTING_PRODUCT.key && (
          <Table columns={experienceColumns} data={demographicExperienceSummary} headerClassNames="border-b border-zinc-300" />
        )}

        {selectedDetail === EXPERIENCE_DATA_ANALYSIS.CLAIMS_DEATH_EXPERIENCE.key && (
          <div className="flex flex-col gap-10 mt-4">
            <Table columns={claimsColumns} data={data7} headerClassNames="border-b border-zinc-300" />
          </div>
        )}
      </div>
    </div>
  );
}
