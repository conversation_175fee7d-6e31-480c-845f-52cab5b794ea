import React from "react";
import Button from "@components/common/Button";
import EditableTable from "@modules/sales/components/editable-table";
import TextField from "@components/form/TextField";
import { PiMinusCircle, PiPlusCircle } from "react-icons/pi";

interface BenefitsSectionProps {
  benefits: any[];
  onAddBenefit: () => void;
  onRemoveBenefit: (index: number) => void;
  onUpdateBenefits: (updatedRows: any[]) => void;
}

const BenefitsSection: React.FC<BenefitsSectionProps> = ({ benefits, onAddBenefit, onRemoveBenefit, onUpdateBenefits }) => {
  const firstOption = benefits[0]?.option;

  const filteredBenefits = benefits.filter((benefit) => benefit.option === firstOption);
  return (
    <div className="border border-gray/10 bg-[#F6F6F680] p-6 mb-8 shadow-sm">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-lg text-primary font-[600] text-[16px]">BENEFITS</h3>
        <Button onClick={onAddBenefit}>
          <div className="flex flex-row items-center gap-2">
            <PiPlusCircle className="inline text-primary" />
            <span className="font-thin text-primary font-[300] text-sm">Add Age Bracket</span>
          </div>
        </Button>
      </div>

      <div className="grid grid-cols-12 gap-4">
        <div className="col-span-12">
          <EditableTable
            className="border-b-0"
            columns={[
              {
                key: "ageFrom",
                header: "AGE FROM",
                className: "text-[14px] font-[500]",
                number: true,
                formatInput: false,
              },
              {
                key: "ageTo",
                header: "AGE TO",
                className: "text-[14px] font-[500]",
                number: true,
                formatInput: false,
              },
              {
                key: "id",
                header: "BENEFITS",
                className: "text-[14px] font-[500]",
                locked: true,
                render: () => {
                  return <TextField className="!w-full" value="Life Insurance" disabled />;
                },
              },
              {
                key: "maximumCoverageAmount",
                header: "MAXIMUM COVERAGE AMOUNT",
                className: "text-[14px] font-[500]",
                number: true,
                formatInput: true,
              },
              {
                key: "rate",
                header: "RATE",
                className: "text-[14px] font-[500]",
                number: true,
                formatInput: false,
              },
              {
                key: "",
                header: "",
                align: "center",
                className: "text-[14px] font-[500] w-[50px]",
                render: (_: any, index: number) => {
                  return (
                    <Button classNames="!w-fit !h-fit !p-0" onClick={() => onRemoveBenefit(index)}>
                      <PiMinusCircle className="inline text-primary" />
                    </Button>
                  );
                },
              },
            ]}
            rows={filteredBenefits}
            onChange={(updatedRows) => onUpdateBenefits(updatedRows)}
          />
        </div>
      </div>
    </div>
  );
};

export default BenefitsSection;
