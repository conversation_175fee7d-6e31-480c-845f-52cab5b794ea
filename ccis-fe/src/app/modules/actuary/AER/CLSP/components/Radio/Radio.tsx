import { FaPlus } from "react-icons/fa";
import Radio from "@components/form/Radio";
import Button from "@components/common/Button";
import { RATE_PROJECT_COMMISSION_ALLOCATION } from "@constants/product-proposal";

interface RateProjectionCommissionSectionProps {
  formik: any;
  handleRadio: (value: string) => void;
  handleCreateCommissionDistribution: () => void;
}

export default function RateProjectionCommissionSection({ formik, handleRadio, handleCreateCommissionDistribution }: RateProjectionCommissionSectionProps) {
  return (
    <div className="mt-4 w-full">
      <div className="flex justify-between gap-4 text-xl">
        <div className="flex gap-2">
          <Radio
            value={RATE_PROJECT_COMMISSION_ALLOCATION.MASTERLIST.key}
            label="Masterlist"
            name="RateProjectionCommissionAllocation"
            checked={formik.values.basisForCalculation === RATE_PROJECT_COMMISSION_ALLOCATION.MASTERLIST.key}
            onChange={handleRadio}
            required
          />
          <Radio
            value={RATE_PROJECT_COMMISSION_ALLOCATION.COOP_EXISTING_PRODUCT.key}
            label="Coop Existing Product"
            name="RateProjectionCommissionAllocation"
            checked={formik.values.basisForCalculation === RATE_PROJECT_COMMISSION_ALLOCATION.COOP_EXISTING_PRODUCT.key}
            onChange={handleRadio}
            required
          />
        </div>
      </div>

      <div className="flex justify-end gap-2 mt-2 mb-2">
        <div>
          <Button classNames="block border border-primary btn-sm" onClick={handleCreateCommissionDistribution}>
            <div className="flex flex-row text-sm items-center text-primary gap-2">
              <FaPlus />
              Add
            </div>
          </Button>
        </div>
      </div>
    </div>
  );
}
