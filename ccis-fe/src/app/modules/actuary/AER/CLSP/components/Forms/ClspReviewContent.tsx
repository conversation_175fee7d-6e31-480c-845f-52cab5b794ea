// modules/actuary/shared/ClspReviewContent.tsx
import React, { useEffect, useMemo, useRef, useState } from "react";
import Button from "@components/common/Button";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { ICommissionType } from "@interface/commission-structure.interface";
import { useCooperativesManagementActions } from "@state/reducer/cooperatives";
import { useContestabilityActions } from "@state/reducer/contestability";
import { IContestability } from "@interface/contestability.interface";
import { useCommissionTypeActions } from "@state/reducer/commision-type";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";
import { ROUTES } from "@constants/routes";

export type ClspReviewContentProps = {
  quotationData: any;
  selectedQuotationProduct?: any;
  signatoryTemplateId?: number | null;
  status?: string;
  aerID?: string | number | null;
  onSubmitAer?: (payload: any) => Promise<void> | void;
  onSaveDraft?: (payload: any) => Promise<void> | void;
};

const ClspReviewContent: React.FC<ClspReviewContentProps> = ({ quotationData: baseData, selectedQuotationProduct, signatoryTemplateId, aerID, onSubmitAer, onSaveDraft }) => {
  const { getCooperatives, getCooperativeById } = useCooperativesManagementActions();
  const navigate = useNavigate();
  const cooperativesData = useSelector((s: RootState) => s.cooperatives?.cooperatives);
  const getCooperativeByIdState = useSelector((s: RootState) => s.cooperatives?.getCooperativeById);
  const { getContestability } = useContestabilityActions();
  const contestabilityData = useSelector((s: RootState) => s.contestability?.contestabilities);
  const { getCommissionTypes } = useCommissionTypeActions();
  const commissionTypesData = useSelector((s: RootState) => s.commissionType?.getCommissionTypes);

  const [quotationData, setQuotationData] = useState<any>(baseData);
  const [cooperativeName, setCooperativeName] = useState<string>("N/A");
  const fetchedCoopOnceRef = useRef(false);

  const resolvedAerID: string | number | null = useMemo(() => {
    const raw = baseData || {};
    return aerID ?? raw?.id ?? raw?.aerID ?? raw?.quotation?.id ?? raw?.quotation?.aerID ?? raw?.selectedQuotation?.id ?? raw?.selectedQuotation?.aerID ?? null;
  }, [aerID, baseData]);

  const coopId: number | undefined = useMemo(() => {
    const raw = quotationData || {};
    return Number(raw?.quotations?.coopId) || Number(raw?.quotation?.coopId) || Number(raw?.cooperativeId) || Number(raw?.quotation?.cooperative?.id) || undefined;
  }, [quotationData]);

  useEffect(() => {
    if (!quotationData) {
      toast.error("Missing quotation data");
      return;
    }
  }, []); // eslint-disable-line

  useEffect(() => {
    getCooperatives({ payload: { filter: "" } });
    getContestability({ filter: "" });
    getCommissionTypes();
  }, []); // eslint-disable-line

  useEffect(() => {
    if (!coopId) return;
    const fromList = cooperativesData?.find((c) => Number(c.id) === Number(coopId));
    if (fromList?.coopName) setCooperativeName(fromList.coopName);
  }, [coopId, cooperativesData]);

  useEffect(() => {
    if (!coopId) return;
    const inList = cooperativesData?.some((c) => Number(c.id) === Number(coopId));
    if (inList) return;
    if (fetchedCoopOnceRef.current) return;
    fetchedCoopOnceRef.current = true;
    getCooperativeById({ id: coopId });
  }, [coopId, cooperativesData, getCooperativeById]);

  useEffect(() => {
    if (getCooperativeByIdState?.success && getCooperativeByIdState?.data?.coopName) {
      setCooperativeName(getCooperativeByIdState.data.coopName);
    }
  }, [getCooperativeByIdState?.success, getCooperativeByIdState?.data?.coopName]);

  useEffect(() => {
    if (!quotationData) return;
    const merged = {
      ...quotationData,
      quotations: {
        ...(quotationData.quotations || quotationData.quotation || {}),
        signatoryTemplateId: signatoryTemplateId ?? quotationData?.quotations?.signatoryTemplateId ?? quotationData?.quotation?.signatoryTemplateId ?? null,
      },
    };
    setQuotationData(merged);
  }, []); // eslint-disable-line

  // Build a quick lookup for commission type names
  const commissionTypeMap = useMemo(() => {
    const map = new Map<string, string>();
    const list = commissionTypesData?.data as ICommissionType[] | undefined;
    if (Array.isArray(list)) {
      list.forEach((item) => map.set(String(item.id), item.commissionName ?? ""));
    }
    return map;
  }, [commissionTypesData]);

  const buildCreateAerPayload = (raw: any) => {
    if (!raw) return null;

    const selectedOptions: number[] = (() => {
      if (Array.isArray(raw.selectedOptions) && raw.selectedOptions.length) return raw.selectedOptions.map(Number);
      const fromString = raw?.options?.aerOptions;
      if (typeof fromString === "string") {
        const m = fromString.match(/\d+/g);
        if (m && m.length) return m.map((x: string) => Number(x));
      }
      const set = new Set<number>();
      const benefits = raw?.options?.clspBenefits || raw?.clspBenefits || [];
      benefits.forEach((b: any) => {
        const n = Number(b?.option);
        if (Number.isFinite(n)) set.add(n);
      });
      return Array.from(set);
    })();

    const payload: any = {
      ...raw,
      quotations: {
        ...(raw.quotations || raw.quotation || {}),
        signatoryTemplateId: signatoryTemplateId ?? raw?.quotations?.signatoryTemplateId ?? raw?.quotation?.signatoryTemplateId ?? null,
      },
    };

    payload.options = payload.options || {};
    if (!payload.options.aerOptions) {
      payload.options.aerOptions = `[${selectedOptions.join(",")}]`;
    }

    return payload;
  };

  const handleSubmit = async () => {
    if (!quotationData) return toast.error("Missing quotation data");
    if (!resolvedAerID) return toast.error("No AER ID found. Please open this review from an existing AER.");
    const payload = buildCreateAerPayload(quotationData);
    if (!payload) return toast.error("Unable to build submission payload");
    try {
      await onSubmitAer?.({ ...payload, id: resolvedAerID });
      navigate(ROUTES.ACTUARY.CLSP.key);
    } catch (error) {
      toast.error("Failed to submit AER");
    }
  };

  const handleSaveDraft = async () => {
    if (!quotationData) return toast.error("Missing quotation data");
    if (!resolvedAerID) return toast.error("No AER ID found. Please open this review from an existing AER.");
    const payload = buildCreateAerPayload(quotationData);
    if (!payload) return toast.error("Unable to build draft payload");
    await onSaveDraft?.({ ...payload, id: resolvedAerID });
  };

  return (
    <section className="px-6 py-4">
      {/* Optional inline warning if AER ID is missing */}
      {!resolvedAerID && <div className="mb-4 rounded border border-red-200 bg-red-50 text-red-700 px-4 py-2 text-sm">No AER ID found. Actions are disabled until an AER is selected.</div>}

      <h1 className="text-xl font-bold text-primary mb-4 mt-2">ACTUARY EVALUATION REPORT</h1>

      <div>
        <div className="grid grid-cols-4 gap-4 text-sm mb-4">
          <div className="text-slate-400">AER No.</div>
          <div>{resolvedAerID ?? "—"}</div>
        </div>

        {selectedQuotationProduct?.createdAt && (
          <div className="grid grid-cols-4 gap-4 text-sm mb-4">
            <div className="text-slate-400">Date</div>
            <div>
              {new Date(selectedQuotationProduct.createdAt).toLocaleDateString("en-US", {
                year: "numeric",
                month: "long",
                day: "2-digit",
              })}
            </div>
          </div>
        )}

        <div className="grid grid-cols-4 gap-4 text-sm mb-4">
          <div className="text-slate-400">Type of Plan</div>
          <div>Coop Life Savings Plan</div>
        </div>

        <div className="grid grid-cols-4 gap-4 text-sm mb-4">
          <div className="text-zinc-400">COOPERATIVE</div>
          <div className="w-full">{cooperativeName || "N/A"}</div>
        </div>

        <div className="grid grid-cols-4 gap-4 text-sm mb-4">
          <div className="text-slate-400">Previous Provider</div>
          <div>{quotationData?.quotations?.previousProvider || quotationData?.quotation?.previousProvider || quotationData?.previousProvider || "N/A"}</div>
        </div>

        <div className="grid grid-cols-4 gap-4 text-sm mb-4">
          <div className="text-slate-400">Total Members</div>
          <div>{quotationData?.quotations?.totalNumberOfMembers || quotationData?.quotation?.totalNumberOfMembers || quotationData?.totalNumberOfMembers || "N/A"}</div>
        </div>

        <div className="grid grid-cols-4 gap-4 text-sm mb-4">
          <div className="text-slate-400">Contestability</div>
          <div>
            {contestabilityData?.find((c: IContestability) => c.id === (quotationData?.quotations?.contestability || quotationData?.quotation?.contestability || quotationData?.contestabilityId))
              ?.label || "N/A"}
          </div>
        </div>
      </div>

      <hr className="my-10 border-gray/10" />

      {/* --- Recommendation Table(s) --- */}
      <div>
        <span className="text-[16px] font-poppins-medium">RECOMMENDATION</span>

        {(() => {
          const clspBenefits: any[] = quotationData?.options?.clspBenefits || [];
          const benefitsByOption: Record<number, any[]> = clspBenefits.reduce(
            (acc, row) => {
              const optId = Number(row.option);
              if (!Number.isFinite(optId)) return acc;
              acc[optId] ||= [];
              acc[optId].push(row);
              return acc;
            },
            {} as Record<number, any[]>
          );

          const hasOptionTaggedRows = Object.keys(benefitsByOption).length > 0;
          const optionIds = hasOptionTaggedRows
            ? Object.keys(benefitsByOption)
                .map((k) => Number(k))
                .sort((a, b) => a - b)
            : [0];

          // Normalize commissions array (supporting both possible sources)
          const allCommissionsRaw = quotationData?.commissionDistributions ?? quotationData?.quotation?.quotationCommissionDistribution ?? [];
          const allCommissions: any[] = Array.isArray(allCommissionsRaw) ? allCommissionsRaw : [];

          const renderOptionTable = (optId: number) => {
            const rows = hasOptionTaggedRows ? benefitsByOption[optId] || [] : clspBenefits;
            if (!rows.length) return null;

            // When rows are tagged by option, only show commissions matching that option.
            // Otherwise (single generic option), show all commissions.
            const optionComms = hasOptionTaggedRows ? allCommissions.filter((c) => Number(c.option) === optId) : allCommissions;

            // Show "-----" iff we are in per-option mode AND this specific option has no commissions.
            const showNoCommission = hasOptionTaggedRows && optionComms.length === 0;

            return (
              <div key={optId} className="border-b mb-8 border-zinc-300">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-primary font-semibold">{hasOptionTaggedRows ? `Option ${optId}` : `Option`}</div>
                </div>

                <table className="table-auto w-full border-collapse border border-zinc-300">
                  <thead className="bg-primary text-white">
                    <tr>
                      <th className="border border-zinc-300 px-4 py-2">BENEFITS</th>
                      <th className="border border-zinc-300 px-4 py-2" colSpan={2}>
                        COVERAGE
                      </th>
                      <th className="border border-zinc-300 px-4 py-2">ANNUAL RATES</th>
                      <th className="border border-zinc-300 px-4 py-2">TOTAL COMMISSION</th>
                    </tr>
                  </thead>
                  <tbody>
                    {rows.map((row: any, idx: number) => (
                      <tr key={idx} className="text-center text-sm">
                        {idx === 0 && (
                          <td rowSpan={rows.length} className="border border-zinc-300 px-4 py-2">
                            Life Insurance
                          </td>
                        )}

                        <td className="border border-zinc-300 px-4 py-2">
                          {row.ageFrom} - {row.ageTo} years old
                        </td>
                        <td className="border border-zinc-300 px-4 py-2">{Intl.NumberFormat("en-US").format(Number(row.maximumCoverageAmount))}</td>
                        <td className="border border-zinc-300 px-4 py-2">
                          {row.rate}% of the total {quotationData?.coverageBasis} of the cooperative per year
                        </td>

                        {idx === 0 && (
                          <td rowSpan={rows.length} className="border border-zinc-300 px-4 py-2 whitespace-pre-line text-left align-top">
                            {showNoCommission
                              ? "-----"
                              : optionComms
                                  .map((commission: any) => {
                                    const key = String(commission.commissionTypeId);
                                    const commissionName = commissionTypeMap.get(key) || commission.commissionTypeId;
                                    const pct = (parseFloat(String(commission.rate)) * 100).toFixed(2);
                                    return `${commissionName} - ${pct}%`;
                                  })
                                  .join("\n")}
                          </td>
                        )}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            );
          };

          return <div className="mt-4">{optionIds.map((optId) => renderOptionTable(optId))}</div>;
        })()}
      </div>

      <div className="pb-10 mt-16">
        <div className="my-4 text-xl font-poppins-semibold">CONDITIONS</div>
        <div
          className="border rounded border-slate-300 p-4 text-sm leading-relaxed"
          dangerouslySetInnerHTML={{
            __html: quotationData?.quotationCondition?.condition || "",
          }}
        />
      </div>

      <div className="flex justify-end">
        <div className="flex gap-4">
          <Button classNames="bg-sky-500 text-white px-6 py-2 rounded-md hover:bg-primary-dark" onClick={handleSaveDraft} disabled={!resolvedAerID}>
            Save as Draft
          </Button>

          <Button variant="primary" classNames="bg-primary text-white px-6 py-2 rounded-md hover:bg-primary-dark" onClick={handleSubmit} disabled={!resolvedAerID}>
            Submit AER
          </Button>
        </div>
      </div>
    </section>
  );
};

export default ClspReviewContent;
