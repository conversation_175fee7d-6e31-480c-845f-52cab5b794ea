import React, { useEffect, useMemo } from "react";
import { PiCaretRight } from "react-icons/pi";
import EditableTable from "@modules/sales/components/editable-table";
import Accordion, { AccordionItem, AccordionTrigger, AccordionContent } from "@components/common/Accordion";
import CheckBox from "@components/form/CheckBox";
import Button from "@components/common/Button";
import WysiwygConditionEditor from "../wysiwyg/wysiwygConditions";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { ISelectOptions } from "@interface/common.interface";
import { ICommissionType } from "@interface/commission-structure.interface";
import { getCommissionTypes } from "@state/reducer/commision-type";
import SignatorySelector from "./SignatorySelection";

interface SavedOptionsProps {
  options: any[];
  selectedOptions: number[];
  quotationCondition: { condition: string };
  onSelectOption: (optionId: number) => void;
  onSetShowConditionContainer: (visible: boolean) => void;
  showConditionContainer: boolean;
  onConditionChange: (content: string) => void;
  onSubmitToSignatory: () => void;
  onLoadOption: (optionId: number) => void;
  signatoryTemplateId: number | null;
  onTemplateChange: (id: number | null) => void;
}

const SavedOptions: React.FC<SavedOptionsProps> = ({
  options,
  selectedOptions,
  quotationCondition,
  onSelectOption,
  onSetShowConditionContainer,
  showConditionContainer,
  onConditionChange,
  onSubmitToSignatory,
  signatoryTemplateId,
  onLoadOption,
  onTemplateChange,
}) => {
  const isSubmitDisabled = selectedOptions.length === 0 || !quotationCondition.condition || !signatoryTemplateId;
  const commissionTypesData = useSelector((state: RootState) => state.commissionType.getCommissionTypes);
  const commisionTypesItems = useMemo<ISelectOptions[]>(() => {
    if (!commissionTypesData?.data) return [];
    return commissionTypesData.data.map((item: ICommissionType) => ({
      text: item.commissionName,
      value: item.id.toString(),
    }));
  }, [commissionTypesData]);

  useEffect(() => {
    getCommissionTypes();
  }, []);

  return (
    <>
      <hr className="my-4 border-gray/10" />
      <div className="mb-3">
        <h2 className="text-[20px] font-poppins-medium font-[600] text-primary">SAVED OPTIONS</h2>
        <p className="text-xs text-zinc-400">Please select options that you want to submit.</p>

        <Accordion className="mt-4">
          {options.map((option, index) => (
            <AccordionItem key={index} value={`item-${index}`} className="border border-gray/10 rounded-md mb-2 [&[data-state=open]_.caret]:rotate-90">
              <AccordionTrigger className="p-3 flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <CheckBox
                    checked={selectedOptions.includes(option.id)}
                    onChange={(e) => {
                      e.stopPropagation();
                      onSelectOption(option.id);
                    }}
                  />
                  <span className="text-[12px] font-[500]">Option {option.id}</span>
                </div>
                <PiCaretRight className="text-[12px] transition-transform duration-200 caret" />
              </AccordionTrigger>
              <AccordionContent className="p-3 border-t border-gray/10 text-[10px]">
                <div className="flex justify-end mb-2">
                  <button
                    className="text-[12px] bg-primary text-white px-2 py-1 rounded hover:opacity-90"
                    onClick={(e) => {
                      e.stopPropagation(); // don't toggle accordion
                      onLoadOption(option.id);
                    }}
                  >
                    Load Above
                  </button>
                </div>

                <div className="flex flex-col gap-2">
                  <div className="overflow-x-auto">
                    <EditableTable
                      columns={[
                        {
                          key: "ageFrom",
                          header: "AGE FROM",
                          className: "text-[14px] font-[500]",
                          number: true,
                          disabled: true,
                        },
                        {
                          key: "ageTo",
                          header: "AGE TO",
                          className: "text-[14px] font-[500]",
                          number: true,
                          disabled: true,
                        },
                        {
                          key: "benefitId",
                          header: "BENEFITS",
                          className: "text-[14px] font-[500]",
                          locked: true,
                          render: () => (
                            <div className="min-w-[250px]">
                              <span>Life Insurance</span>
                            </div>
                          ),
                        },
                        {
                          key: "maximumCoverageAmount",
                          header: "MAXIMUM COVERAGE AMOUNT",
                          className: "text-[14px] font-[500]",
                          number: true,
                          formatInput: true,
                          disabled: true,
                        },
                        {
                          key: "rate",
                          header: "RATE (%)",
                          className: "text-[14px] font-[500]",
                          number: true,
                          disabled: true,
                        },
                      ]}
                      rows={
                        option.options.map((opt: any) => ({
                          ageFrom: opt.ageFrom,
                          ageTo: opt.ageTo,
                          benefitId: opt.benefitId,
                          maximumCoverageAmount: parseFloat(opt.maximumCoverageAmount).toFixed(2),
                          rate: parseFloat(opt.rate).toFixed(2),
                        })) || []
                      }
                      editable={false}
                    />
                  </div>
                  <div>
                    <span className="text-[14px] font-[500]">Commission Distribution</span>
                    <div className="overflow-x-auto mt-2">
                      <EditableTable
                        columns={[
                          {
                            key: "commissionTypeId",
                            header: "COMMISSION TYPE",
                            className: "text-[14px] font-[500]",
                            render: (data) => {
                              const commissionType = commisionTypesItems.find((item) => item.value === data.commissionTypeId.toString());
                              return <span>{commissionType?.text || "N/A"}</span>;
                            },
                          },
                          {
                            key: "rate",
                            header: "RATE (%)",
                            className: "text-[14px] font-[500]",
                            number: true,
                          },
                        ]}
                        rows={option.commissionDistribution || []}
                        editable={false}
                      />
                    </div>
                  </div>
                  <div>
                    <span className="text-[14px] font-[500]">Projection</span>
                    <EditableTable
                      columns={[
                        { key: "totalPremiumNetRate", header: "TOTAL NET PREMIUM" },
                        { key: "totalPremiumGrossRate", header: "TOTAL GROSS PREMIUM" },
                        { key: "numberOfClaims", header: "NUMBER OF CLAIMS" },
                        { key: "amountOfClaims", header: "AMOUNT OF CLAIMS" },
                        { key: "claimsRatio", header: "CLAIMS RATIO" },
                      ]}
                      rows={
                        option.projection
                          ? [
                              {
                                totalPremiumNetRate: Number(option.projection.totalPremiumNetRate ?? 0).toFixed(2),
                                totalPremiumGrossRate: Number(option.projection.totalPremiumGrossRate ?? 0).toFixed(2),
                                numberOfClaims: option.projection.numberOfClaims,
                                amountOfClaims: Number(option.projection.amountOfClaims ?? 0).toFixed(2),
                                claimsRatio: Number(option.projection.claimsRatio ?? 0).toFixed(2),
                              },
                            ]
                          : []
                      }
                      editable={false}
                    />
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>

        {/* Create Condition */}
        <div className="flex justify-end">
          {!quotationCondition.condition && (
            <Button classNames="elevation-sm shadow-md rounded-[5px] !px-12 mt-8" variant="primary" onClick={() => onSetShowConditionContainer(true)}>
              <div className="flex flex-row items-center gap-2">Create Condition</div>
            </Button>
          )}
        </div>

        {/* WYSIWYG Editor */}
        {(showConditionContainer || quotationCondition.condition) && (
          <div className="mb-3">
            <h2 className="text-[20px] font-poppins-medium font-[600] text-primary mt-4">CONDITIONS</h2>
            <WysiwygConditionEditor value={quotationCondition.condition} onChange={onConditionChange} className="border border-gray/20 rounded-md bg-white" />
          </div>
        )}

        <div className="mt-6 mb-4">
          <SignatorySelector selectedTemplateId={signatoryTemplateId} onTemplateChange={onTemplateChange} />
        </div>

        {signatoryTemplateId && (
          <div className="flex justify-end">
            <Button variant="primary" onClick={onSubmitToSignatory} disabled={isSubmitDisabled}>
              <div className="flex flex-row items-center gap-2">
                <span className="text-[14px] font-[400] font-poppins-medium">Review & Submit</span>
              </div>
            </Button>
          </div>
        )}
      </div>
    </>
  );
};

export default SavedOptions;
