import { useFormik } from "formik";
import colorMode from "@modules/sales/utility/color";
import Select, { TOptions } from "@modules/sales/components/select";
import Button from "@components/common/Button";
import { useEffect, useMemo, useRef, useState } from "react";
import { useModalController } from "@modules/sales/controller";
import { useCooperativesManagementActions } from "@state/reducer/cooperatives";
import { useContestabilityActions } from "@state/reducer/contestability";
import { useProductBenefitsManagementActions } from "@state/reducer/utilities-product-benefits";
import { useCommissionAgeTypeSManagementActions } from "@state/reducer/utilities-commission-age-types";
import { useCommissionTypeActions } from "@state/reducer/commision-type";
import { useDispatch, useSelector } from "react-redux";
import { IUtilitiesProductBenefits } from "@interface/utilities.interface";
import { IContestability } from "@interface/contestability.interface";
import { ISelectOptions } from "@interface/common.interface";
import { RootState } from "@state/reducer";
import { ISharesCoopInformation } from "@interface/shares.interface";
import { ICommissionType } from "@interface/commission-structure.interface";
import { parseNumber } from "@modules/sales/utility/number";
import { toast } from "react-toastify";
import { useLocation, useNavigate } from "react-router-dom";
import { ICda } from "@interface/cooperatives-cda";
import DemographicModal from "@modules/actuary/modals/DemographicModa";
import { FaChevronLeft, FaPlus } from "react-icons/fa";
import TextField from "@components/form/TextField";
import { ROUTES } from "@constants/routes";
import { getFormikBorderClass } from "@constants/clsp";
import httpClient from "@clients/httpClient";
import { ProposalStatus } from "@enums/proposal-status";
import BasisModal from "@modules/actuary/modals/BasisModal";
import { ProductCode, ProductName } from "@enums/product-code";
import { showSuccess } from "@helpers/prompt";
import { useProductActions } from "@state/reducer/products";
import { RATE_PROJECT_COMMISSION_ALLOCATION } from "@constants/product-proposal";
import Radio from "@components/form/Radio";
import BenefitsSection from "./BenefitSection";
import PortfolioSection from "./PortfolioSection";
import ProjectionTable from "./ProjectionSection";
import CommissionLoading from "./commissionLoadingSection";
import SavedOptions from "./SavedOptions";
import { ProductStatus } from "@enums/product-status";
import { ClspClaimsExperienceInfo } from "@modules/sales/clsp-quotations/components/ClaimsExperience";
import ClspReviewModal from "../Modals/CLSPReview";
import { useClspAERActions } from "@state/reducer/actuary-clsp-aer";
import { PortFolioCLSP } from "@enums/aer-status";

type CommissionRow = {
  commissionTypeId: number;
  rate: string | number;
  proposedRate?: string | number;
};

type Projection = {
  totalPremiumNetRate: number | string;
  totalPremiumGrossRate: number | string;
  numberOfClaims: number | string;
  amountOfClaims: number | string;
  claimsRatio: number | string;
  udd?: number | string;
};

export interface Option {
  id: number;
  options: any[];
  condition: string;
  commissionDistribution?: CommissionRow[];
  projection?: Projection;
}

const formField = () => ({
  cooperativeId: 0,
  branch: "",
  parentId: "",
  previousProvider: "",
  contestabilityId: "",
  coverageBasis: "",
  totalNumberOfMembers: "",
  signatoryTemplateId: 0,
  status: ProposalStatus.for_signatory,
  averageAge: "",
  fileName: "",
  averageClaims: "",
  productType: ProductCode.CLSP,
  lossRatio: "",
  projection: {
    totalPremiumNetRate: "",
    totalPremiumGrossRate: "",
    numberOfClaims: "",
    amountOfClaims: "",
    claimsRatio: "",
    udd: "",
  },
  projectionResult: {
    totalPremiumNetRate: "",
    totalPremiumGrossRate: "",
    numberOfClaims: "",
    amountOfClaims: "",
    claimsRatio: "",
    projectedTotalPremium: "",
  },
  portfolio: {
    years: {
      shareCapital: [{ years: "", minimumAmount: "", maximumAmount: "", totalAmount: "", averageCoverage: "" }],
      savings: [{ years: "", minimumAmount: "", maximumAmount: "", totalAmount: "", averageCoverage: "" }],
      timeDeposit: [{ years: "", minimumAmount: "", maximumAmount: "", totalAmount: "", averageCoverage: "" }],
    },
    ages: {
      shareCapital: [{ ageFrom: "", ageTo: "", totalNumberOfMembers: "", totalShareCapital: "", averageCoverage: "" }],
      savings: [{ ageFrom: "", ageTo: "", totalNumberOfMembers: "", totalSavingsCapital: "", averageCoverage: "" }],
      timeDeposit: [{ ageFrom: "", ageTo: "", totalNumberOfMembers: "", totalTimeDepositCapital: "", averageCoverage: "" }],
    },
  },
  claimExperience: {
    years: [{ year: new Date().getFullYear(), numberOfDeaths: "0", totalClaimAmount: "0" }],
    ages: [{ ageFrom: 0, ageTo: 0, numberOfDeaths: 0, totalClaimAmount: 0 }],
  },
  benefits: [{ ageFrom: "", ageTo: "", benefitId: 0, maximumCoverageAmount: "", rate: "" }],
  coopMode: false,
  commissionDistribution: [{ commissionTypeId: 0, rate: "", proposedRate: "" }],
  options: [] as Option[],
  selectedOptions: [] as number[],
  quotationCondition: { condition: "" },
});

type FormValues = ReturnType<typeof formField>;

const COVERAGE_BASIS = Object.freeze({
  SHARE_CAPITAL: "Share Capital",
  SAVINGS: "Savings",
  TIME_DEPOSIT: "Time Deposit",
});

export default function CLSPQuotation() {
  const navigate = useNavigate();
  const location = useLocation();
  const reviewModalController = useModalController();
  const [reviewData, setReviewData] = useState<any>(null);
  const dispatch = useDispatch();

  const formik = useFormik<FormValues>({
    initialValues: formField(),
    enableReinitialize: true,
    onSubmit: () => {
      const payload = {
        ...createAerPayload,
        status: formik.values.status,
        quotations: {
          ...(createAerPayload?.quotations ?? {}),
          signatoryTemplateId: formik.values.signatoryTemplateId,
        },
      };
      // prepare data for the modal
      setReviewData(payload);
      reviewModalController.openFn();
    },
  });

  const { getCooperatives, getCooperativeById } = useCooperativesManagementActions();
  const { getContestability } = useContestabilityActions();
  const { getProductBenefits } = useProductBenefitsManagementActions();
  const { getCommissionAgeType } = useCommissionAgeTypeSManagementActions();
  const { getCommissionTypes } = useCommissionTypeActions();
  const { getMainProducts, getProducts } = useProductActions();

  const { putClspAER, getClspAERS } = useClspAERActions();
  const putClspAERLoading = useSelector((state: RootState) => state.clspAER.putClspAER?.success);

  const getCooperativeByIdState = useSelector((state: RootState) => state.cooperatives.getCooperativeById);
  const mainProducts = useSelector((state: RootState) => state.products?.getMainProducts?.data?.data || []);
  const products = useSelector((state: RootState) => state.products?.getProducts?.data?.data || []);
  const subProducts = useSelector((state: RootState) => state.products?.getSubProducts?.data?.data || []);
  const cooperativesData = useSelector((state: RootState) => state.cooperatives.cooperatives);
  const contestabilityData = useSelector((state: RootState) => state.contestability.contestabilities);
  const productBenefitsData = useSelector((state: RootState) => state.utilitiesProductBenefits.productBenefits);
  const commissionTypesData = useSelector((state: RootState) => state.commissionType.getCommissionTypes);

  const [showConditionContainer, setShowConditionContainer] = useState(false);
  const [dynamicCooperatives, setDynamicCooperatives] = useState<ICda[]>([]);
  const demographicModalController = useModalController();
  const [basisForCalculation, setBasisForCalculation] = useState<string>(RATE_PROJECT_COMMISSION_ALLOCATION.MASTERLIST.key);
  const fetchingCoopRef = useRef<Set<number>>(new Set());
  const processedCoopRef = useRef<Set<number>>(new Set());
  const [formState, setFormState] = useState<FormValues>(() => formField());
  const [hasDemographics, setHasDemographics] = useState<boolean>(false);
  const [fileName, setFileName] = useState<string>("");
  const [demographicData, setDemographicData] = useState<any>(null);
  const rawData = (location.state as any)?.quotationData || (location.state as any)?.selectedQuotation || {};
  const aerID = rawData.id ?? null;
  useEffect(() => {
    getCooperatives({ payload: { filter: "" } });
    getContestability({ filter: ProductCode.CLSP });
    getProductBenefits({ filter: "" });
    getCommissionAgeType({ filter: "" });
    getCommissionTypes();
    getProducts({ params: { filter: "", statusFilter: ProductStatus.APPROVED, page: 1, pageSize: 9999 } });
    getMainProducts();
  }, []); // eslint-disable-line

  const cooperativesSelectItems = useMemo<TOptions[]>(() => {
    const items = [
      ...cooperativesData.map((c: ISharesCoopInformation) => ({ text: c.coopName, value: c.id?.toString() ?? "" })),
      ...dynamicCooperatives.map((c) => ({ text: c.coopName, value: c.coopCdaId.toString() })),
    ];
    const seen = new Set<string>();
    return items.filter((it) => {
      if (seen.has(it.value)) return false;
      seen.add(it.value);
      return true;
    });
  }, [cooperativesData, dynamicCooperatives]);

  const contestabilitySelectItems = useMemo<ISelectOptions[]>(() => {
    if (!contestabilityData) return [];
    return contestabilityData.map((item: IContestability) => ({ text: item.label, value: item.id?.toString() ?? "" }));
  }, [contestabilityData]);

  const coverageBasisSelectItems = useMemo<ISelectOptions[]>(
    () => [
      { text: COVERAGE_BASIS.SHARE_CAPITAL, value: COVERAGE_BASIS.SHARE_CAPITAL },
      { text: COVERAGE_BASIS.SAVINGS, value: COVERAGE_BASIS.SAVINGS },
      { text: COVERAGE_BASIS.TIME_DEPOSIT, value: COVERAGE_BASIS.TIME_DEPOSIT },
    ],
    []
  );

  const benefitsSelectItems = useMemo<ISelectOptions[]>(() => {
    if (!productBenefitsData) return [];
    return productBenefitsData.map((item: IUtilitiesProductBenefits) => ({ text: item.benefitName, value: item.id.toString() }));
  }, [productBenefitsData]);

  const commisionTypesSelectItems = useMemo<ISelectOptions[]>(() => {
    if (!commissionTypesData?.data) return [];
    return commissionTypesData.data.map((item: ICommissionType) => ({ text: item.commissionName, value: item.id.toString() }));
  }, [commissionTypesData]);

  const normalizeProjection = (p: any) => ({
    totalPremiumNetRate: parseNumber(p.totalPremiumNetRate),
    totalPremiumGrossRate: parseNumber(p.totalPremiumGrossRate),
    numberOfClaims: parseNumber(p.numberOfClaims),
    amountOfClaims: parseNumber(p.amountOfClaims),
    claimsRatio: parseNumber(p.claimsRatio),
    udd: parseNumber(p.udd),
  });

  const createAerPayload = useMemo(() => {
    const coverageBasis = formik.values.coverageBasis;

    let portfolioAges: Record<string, any>[] = [];
    let portfolioYears: Record<string, any>[] = [];

    switch (coverageBasis) {
      case COVERAGE_BASIS.SHARE_CAPITAL:
        portfolioAges = formik.values.portfolio.ages.shareCapital.map((item) => ({
          ageFrom: parseNumber(item.ageFrom),
          ageTo: parseNumber(item.ageTo),
          totalNumberOfMembers: parseNumber(item.totalNumberOfMembers),
          totalCapital: parseNumber(item.totalShareCapital),
          portfolioType: "Total " + coverageBasis,
          averageCoverage: parseNumber(item.averageCoverage),
        }));
        portfolioYears = formik.values.portfolio.years.shareCapital.map((item) => ({
          year: item.years.toString(),
          minimumAmount: parseNumber(item.minimumAmount),
          maximumAmount: parseNumber(item.maximumAmount),
          totalPortfolio: parseNumber(item.totalAmount),
          portfolioType: "Total " + coverageBasis,
          averageCoverage: parseNumber(item.averageCoverage),
        }));
        break;
      case COVERAGE_BASIS.SAVINGS:
        portfolioAges = formik.values.portfolio.ages.savings.map((item) => ({
          ageFrom: parseNumber(item.ageFrom),
          ageTo: parseNumber(item.ageTo),
          totalNumberOfMembers: parseNumber(item.totalNumberOfMembers),
          totalCapital: parseNumber(item.totalSavingsCapital),
          portfolioType: "Total " + coverageBasis,
          averageCoverage: parseNumber(item.averageCoverage),
        }));
        portfolioYears = formik.values.portfolio.years.savings.map((item) => ({
          year: item.years.toString(),
          minimumAmount: parseNumber(item.minimumAmount),
          maximumAmount: parseNumber(item.maximumAmount),
          totalPortfolio: parseNumber(item.totalAmount),
          portfolioType: "Total " + coverageBasis,
          averageCoverage: parseNumber(item.averageCoverage),
        }));
        break;
      case COVERAGE_BASIS.TIME_DEPOSIT:
        portfolioAges = formik.values.portfolio.ages.timeDeposit.map((item) => ({
          ageFrom: parseNumber(item.ageFrom),
          ageTo: parseNumber(item.ageTo),
          totalNumberOfMembers: parseNumber(item.totalNumberOfMembers),
          totalCapital: parseNumber(item.totalTimeDepositCapital),
          portfolioType: "Total " + coverageBasis,
          averageCoverage: parseNumber(item.averageCoverage),
        }));
        portfolioYears = formik.values.portfolio.years.timeDeposit.map((item) => ({
          year: item.years.toString(),
          minimumAmount: parseNumber(item.minimumAmount),
          maximumAmount: parseNumber(item.maximumAmount),
          totalPortfolio: parseNumber(item.totalAmount),
          portfolioType: "Total " + coverageBasis,
          averageCoverage: parseNumber(item.averageCoverage),
        }));
        break;
    }

    const selectedIds = new Set<number>(formik.values.selectedOptions);
    const firstSelectedOpt = formik.values.selectedOptions[0];

    const flattenedOptionCommissionsAll =
      (formik.values.options as Option[]).flatMap((opt) =>
        (opt.commissionDistribution ?? []).map((cd) => ({
          commissionTypeId: cd.commissionTypeId,
          rate: parseFloat(String(cd.rate ?? "0")) || 0,
          proposedRate: parseFloat(String(cd.proposedRate ?? cd.rate ?? "0")) || 0,
          option: String(opt.id),
        }))
      ) || [];

    const flattenedOptionCommissions = flattenedOptionCommissionsAll.filter((c) => selectedIds.has(Number(c.option)));

    const fallbackCommissionDistributions =
      flattenedOptionCommissions.length === 0 && firstSelectedOpt != null
        ? formik.values.commissionDistribution.map((i) => ({
            commissionTypeId: i.commissionTypeId,
            rate: parseFloat(String(i.rate || "0")) || 0,
            option: String(firstSelectedOpt),
          }))
        : [];

    const flattenedOptionProjectionsAll = (formik.values.options as Option[]).flatMap((opt) => (opt.projection ? [{ ...normalizeProjection(opt.projection), option: String(opt.id) }] : [])) || [];

    const flattenedOptionProjections = flattenedOptionProjectionsAll.filter((p) => selectedIds.has(Number(p.option)));

    const fallbackProjection = flattenedOptionProjections.length === 0 && firstSelectedOpt != null ? [{ ...normalizeProjection(formik.values.projection), option: String(firstSelectedOpt) }] : [];

    return {
      status: formik.values.status,
      coverageBasis: formik.values.coverageBasis,
      averageAge: parseNumber(formik.values.averageAge),

      quotations: {
        coopId: formik.values.cooperativeId,
        previousProvider: formik.values.previousProvider,
        branch: formik.values.branch,
        contestability: formik.values.contestabilityId,
        totalNumberOfMembers: parseNumber(formik.values.totalNumberOfMembers),
        signatoryTemplateId: 0,
        fileName,
      },

      quotationCondition: { condition: formik.values.quotationCondition.condition },

      claimsExperienceYears: formik.values.claimExperience.years.map((item) => ({
        year: item.year.toString(),
        numberOfDeaths: parseNumber(item.numberOfDeaths),
        totalClaimAmount: parseNumber(item.totalClaimAmount),
      })),

      claimsExperienceAges: formik.values.claimExperience.ages.map((item) => ({
        ageFrom: parseNumber(item.ageFrom),
        ageTo: parseNumber(item.ageTo),
        numberOfDeaths: parseNumber(item.numberOfDeaths),
        totalClaimAmount: parseNumber(item.totalClaimAmount),
      })),

      commissionDistributions: flattenedOptionCommissions.length > 0 ? flattenedOptionCommissions : fallbackCommissionDistributions,

      clspPortfolioAges: portfolioAges,
      clspPortfolioYears: portfolioYears,

      options: {
        clspBenefits: (formik.values.options as Option[]).filter((item) => selectedIds.has(item.id)).flatMap((item) => item.options),
        aerOptions: "[" + formik.values.selectedOptions.join(",") + "]",
      },

      projection: flattenedOptionProjections.length > 0 ? flattenedOptionProjections : fallbackProjection,

      averageClaims: parseNumber(formik.values.averageClaims),
      lossRatio: parseNumber(formik.values.lossRatio),
    };
  }, [formik.values, fileName]);

  const formatDate = (dateStr: string): string => {
    const [yyyy, mm, dd] = dateStr.split("-");
    return `${dd}-${mm}-${yyyy}`;
  };

  const isSelected = (value: string) => formik.values.coverageBasis === value;

  const basis = useFormik({
    initialValues: {
      productId: 0,
      subProductId: 0,
      baseFrom: "",
      periodType: "",
      periodFrom: "01-01-2023",
      periodTo: "31-12-2024",
      ageBasis: "",
      age: [
        { ageFrom: 18, ageTo: 49 },
        { ageFrom: 50, ageTo: 65 },
        { ageFrom: 66, ageTo: 69 },
        { ageFrom: 70, ageTo: 999 },
      ],
      productType: ProductCode.CLSP,
      cooperativeId: 0,
      fileName,
    },
    onSubmit: () => handleSelectBasisModal(),
  });

  const [selectBasisModal, setSelectBasisModal] = useState<boolean>(false);
  const handleSelectBasisModal = () => setSelectBasisModal((prev) => !prev);

  const lifeInsuranceBenefitId = useMemo(() => benefitsSelectItems.find((item) => item.text.toLowerCase().trim() === "life insurance")?.value, [benefitsSelectItems]);

  const handleCreateBenefit = () => {
    formik.setFieldValue("benefits", [...formik.values.benefits, { ageFrom: "", ageTo: "", benefitId: parseInt(lifeInsuranceBenefitId ?? "0"), maximumCoverageAmount: "", rate: "" }]);
  };

  const handleCreateCommissionDistribution = () => {
    formik.setFieldValue("commissionDistribution", [...formik.values.commissionDistribution, { commissionTypeId: 0, proposedRate: "", rate: "" }]);
  };

  const handleSelectOption = (optionId: number) => {
    const already = formik.values.selectedOptions.includes(optionId);
    if (already) {
      formik.setFieldValue(
        "selectedOptions",
        formik.values.selectedOptions.filter((id) => id !== optionId)
      );
      return;
    }

    const nextSelected = [...formik.values.selectedOptions, optionId];
    formik.setFieldValue("selectedOptions", nextSelected);

    const picked: Option | undefined = (formik.values.options as Option[]).find((o) => o.id === optionId);
    if (!picked) return;

    if (picked.options?.length) {
      formik.setFieldValue(
        "benefits",
        picked.options.map((b: any) => ({
          benefitId: b.benefitId,
          ageFrom: b.ageFrom?.toString() ?? "",
          ageTo: b.ageTo?.toString() ?? "",
          maximumCoverageAmount: b.maximumCoverageAmount?.toString() ?? "",
          rate: b.rate?.toString() ?? "",
        }))
      );
    }

    if (picked.commissionDistribution?.length) {
      formik.setFieldValue(
        "commissionDistribution",
        picked.commissionDistribution.map((c) => ({
          commissionTypeId: c.commissionTypeId,
          proposedRate: (c.proposedRate ?? "").toString(),
          rate: (c.rate ?? "").toString(),
        }))
      );
    }

    if (picked.projection) {
      formik.setFieldValue("projection", {
        ...formik.values.projection,
        totalPremiumNetRate: (picked.projection.totalPremiumNetRate ?? "").toString(),
        totalPremiumGrossRate: (picked.projection.totalPremiumGrossRate ?? "").toString(),
        numberOfClaims: (picked.projection.numberOfClaims ?? "").toString(),
        amountOfClaims: (picked.projection.amountOfClaims ?? "").toString(),
        claimsRatio: (picked.projection.claimsRatio ?? "").toString(),
        udd: (picked.projection.udd ?? "").toString(),
      });
    }
  };

  const checkBenefits = (): string[] => {
    const errors: string[] = [];
    const seenByPair = new Map<string, Set<string>>();

    formik.values.benefits.forEach((item, index) => {
      const ageFrom = parseNumber(item.ageFrom);
      const ageTo = parseNumber(item.ageTo);
      const maxAmount = parseNumber(item.maximumCoverageAmount);
      const rate = parseNumber(item.rate);

      if (!Number.isFinite(ageFrom) || !Number.isFinite(ageTo) || ageFrom <= 0 || ageTo <= 0) {
        errors.push(`Benefit row ${index + 1}: Age From/To must be greater than 0.`);
      }
      if (!Number.isFinite(maxAmount) || maxAmount <= 0) {
        errors.push(`Benefit row ${index + 1}: Maximum Coverage Amount must be greater than 0.`);
      }
      if (!Number.isFinite(rate) || rate <= 0) {
        errors.push(`Benefit row ${index + 1}: Rate must be greater than 0.`);
      }
      if (Number.isFinite(ageFrom) && Number.isFinite(ageTo) && ageFrom >= ageTo) {
        errors.push(`Benefit row ${index + 1}: Age From must be less than Age To.`);
      }

      const pairKey = `${ageFrom}-${ageTo}`;
      const comboKey = `${maxAmount}|${rate}`;

      if (!seenByPair.has(pairKey)) {
        seenByPair.set(pairKey, new Set<string>([comboKey]));
      } else {
        const combos = seenByPair.get(pairKey)!;
        if (combos.has(comboKey)) {
          errors.push(`Benefit row ${index + 1}: exact duplicate for age range ${pairKey}.`);
        } else {
          combos.add(comboKey);
        }
      }
    });

    for (let i = 0; i < formik.values.benefits.length; i++) {
      const A = formik.values.benefits[i];
      const aFrom = parseNumber(A.ageFrom);
      const aTo = parseNumber(A.ageTo);

      for (let j = i + 1; j < formik.values.benefits.length; j++) {
        const B = formik.values.benefits[j];
        const bFrom = parseNumber(B.ageFrom);
        const bTo = parseNumber(B.ageTo);

        const samePair = aFrom === bFrom && aTo === bTo;
        if (samePair) continue;

        const overlaps = Number.isFinite(aFrom) && Number.isFinite(aTo) && Number.isFinite(bFrom) && Number.isFinite(bTo) && Math.max(aFrom, bFrom) <= Math.min(aTo, bTo);

        if (overlaps) {
          errors.push(`Benefit rows ${i + 1} and ${j + 1} overlap (${aFrom}-${aTo} & ${bFrom}-${bTo}).`);
        }
      }
    }

    return errors;
  };

  const handleSaveOption = () => {
    const errors = checkBenefits();
    if (errors.length > 0) {
      toast.error(errors.pop() ?? "Please correct the form errors before proceeding.");
      return;
    }

    const optionId = Math.max(0, ...formik.values.options.map((opt: Option) => opt.id ?? 0)) + 1;

    const currentBenefits = formik.values.benefits.map((item) => ({
      benefitId: item.benefitId,
      ageFrom: parseNumber(item.ageFrom),
      ageTo: parseNumber(item.ageTo),
      maximumCoverageAmount: parseNumber(item.maximumCoverageAmount),
      rate: parseNumber(item.rate),
      option: optionId,
    }));

    const currentOptionCommission = formik.values.commissionDistribution.map((dist) => ({
      ...dist,
      option: optionId,
    }));

    const { totalPremiumNetRate, totalPremiumGrossRate, numberOfClaims, amountOfClaims, claimsRatio, udd } = formik.values.projection;
    const currentProjection: Projection = {
      totalPremiumNetRate: parseNumber(totalPremiumNetRate),
      totalPremiumGrossRate: parseNumber(totalPremiumGrossRate),
      numberOfClaims: parseNumber(numberOfClaims),
      amountOfClaims: parseNumber(amountOfClaims),
      claimsRatio: parseNumber(claimsRatio),
      udd: parseNumber(udd),
    };

    const totalCommissionPct = (formik.values.commissionDistribution ?? []).reduce((sum, row) => sum + (parseFloat(String(row.rate)) || 0), 0);

    if (totalCommissionPct > 35) {
      toast.error("Total Commission Distribution must not exceed 35%");
      return;
    }

    const isDuplicate = (formik.values.options as Option[]).some((opt) => {
      const sameBenefits =
        opt.options.length === currentBenefits.length &&
        [...opt.options]
          .sort((a, b) => a.benefitId - b.benefitId)
          .every((a, idx) => {
            const b = [...currentBenefits].sort((x, y) => x.benefitId - y.benefitId)[idx];
            return a.benefitId === b.benefitId && a.ageFrom === b.ageFrom && a.ageTo === b.ageTo && a.maximumCoverageAmount === b.maximumCoverageAmount && a.rate === b.rate;
          });

      return sameBenefits;
    });

    if (isDuplicate) {
      toast.error("Option already exists");
      return;
    }

    formik.setFieldValue("options", [
      ...formik.values.options,
      { id: optionId, condition: formik.values.quotationCondition.condition, options: currentBenefits, projection: currentProjection, commissionDistribution: currentOptionCommission },
    ] as Option[]);

    toast.success("Option created successfully");
  };

  const handleRadio = (value: string) => setBasisForCalculation(value);

  const getSelectedPortfolioYears = (coverageBasis: string, portfolio: FormValues["portfolio"]) => {
    switch (coverageBasis) {
      case COVERAGE_BASIS.SHARE_CAPITAL:
        return portfolio.years.shareCapital;
      case COVERAGE_BASIS.SAVINGS:
        return portfolio.years.savings;
      case COVERAGE_BASIS.TIME_DEPOSIT:
        return portfolio.years.timeDeposit;
      default:
        return [];
    }
  };

  const isCalculateDisabled = useMemo(() => {
    const { averageAge, lossRatio, projection, totalNumberOfMembers, cooperativeId, coverageBasis, portfolio } = formik.values;

    if (!averageAge || !basisForCalculation || !lossRatio || !projection.udd || !totalNumberOfMembers || !cooperativeId || !coverageBasis) {
      return true;
    }

    const yearsRows = getSelectedPortfolioYears(coverageBasis, portfolio);
    const agesRows = coverageBasis === COVERAGE_BASIS.SHARE_CAPITAL ? portfolio.ages.shareCapital : coverageBasis === COVERAGE_BASIS.SAVINGS ? portfolio.ages.savings : portfolio.ages.timeDeposit;

    if (yearsRows.length === 0 || agesRows.length === 0) {
      return true;
    }

    const hasValidCommission = formik.values.commissionDistribution.some((item) => item.commissionTypeId && parseFloat(String(item.rate)) > 0);
    return !hasValidCommission;
  }, [formik.values, basisForCalculation]);

  const handleLoadOption = (optionId: number) => {
    const picked: Option | undefined = (formik.values.options as Option[]).find((o) => o.id === optionId);
    if (!picked) return;

    formik.setFieldValue("selectedOptions", [optionId]);

    if (picked.options?.length) {
      formik.setFieldValue(
        "benefits",
        picked.options.map((b: any) => ({
          benefitId: b.benefitId,
          ageFrom: b.ageFrom?.toString() ?? "",
          ageTo: b.ageTo?.toString() ?? "",
          maximumCoverageAmount: b.maximumCoverageAmount?.toString() ?? "",
          rate: b.rate?.toString() ?? "",
        }))
      );
    }

    if (picked.commissionDistribution?.length) {
      formik.setFieldValue(
        "commissionDistribution",
        picked.commissionDistribution.map((c) => ({
          commissionTypeId: c.commissionTypeId,
          proposedRate: (c.proposedRate ?? c.rate ?? "").toString(),
          rate: (c.rate ?? "").toString(),
        }))
      );
    }

    if (picked.projection) {
      formik.setFieldValue("projection", {
        ...formik.values.projection,
        totalPremiumNetRate: (picked.projection.totalPremiumNetRate ?? "").toString(),
        totalPremiumGrossRate: (picked.projection.totalPremiumGrossRate ?? "").toString(),
        numberOfClaims: (picked.projection.numberOfClaims ?? "").toString(),
        amountOfClaims: (picked.projection.amountOfClaims ?? "").toString(),
        claimsRatio: (picked.projection.claimsRatio ?? "").toString(),
        udd: (picked.projection.udd ?? "").toString(),
      });
    }

    if (picked.condition) {
      formik.setFieldValue("quotationCondition.condition", picked.condition);
    }
    toast.success(`Option ${optionId} loaded successfully`);
  };

  const getAvgClaimsFromExperience = (years: Array<{ totalClaimAmount: any; numberOfDeaths: any }>) => {
    // Filter years where the number of deaths is greater than 0
    const filteredYears = years.filter((y) => parseNumber(y.numberOfDeaths) > 0);

    // If there are no valid years with deaths, return an empty string
    if (filteredYears.length === 0) return "";

    // Calculate the total number of deaths for the valid years
    const totalDeaths = filteredYears.reduce((acc, curr) => acc + parseNumber(curr.numberOfDeaths), 0);

    // The number of years with non-zero deaths
    const validYearsCount = filteredYears.length;

    // Calculate the average claims (total deaths / number of valid years)
    const avgClaims = totalDeaths / validYearsCount;

    return avgClaims;
  };

  useEffect(() => {
    const avg = getAvgClaimsFromExperience(formik.values.claimExperience.years);
    if (avg !== "" && avg !== parseNumber(formik.values.averageClaims)) {
      formik.setFieldValue("averageClaims", avg);
    }
  }, [formik.values.claimExperience.years]);

  const handleCalculate = async () => {
    const missingFields: string[] = [];
    const { averageAge, lossRatio, projection, totalNumberOfMembers, cooperativeId, coverageBasis, commissionDistribution, portfolio } = formik.values;

    // Check for missing fields
    if (!averageAge) missingFields.push("Average Age");
    if (!lossRatio) missingFields.push("Loss Ratio");
    if (!projection.udd) missingFields.push("Uniform Distribution of Death (UDD)");
    if (!totalNumberOfMembers) missingFields.push("Demographics is required");
    if (!cooperativeId) missingFields.push("Cooperative");
    if (!coverageBasis) missingFields.push("Coverage Basis");
    const yearsRows = getSelectedPortfolioYears(coverageBasis, portfolio);
    if (!yearsRows.length) missingFields.push(`Portfolio Years - ${coverageBasis}`);

    const hasValidCommission = commissionDistribution.every((item) => item.commissionTypeId && parseFloat(String(item.rate)) > 0);
    if (!hasValidCommission) missingFields.push("Commission Distribution rows must have a valid rate greater than 0");

    if (missingFields.length > 0) {
      toast.error(`Please fill out the following fields before calculating: ${missingFields.join(", ")}`);
      return;
    }

    // Step 1: Calculate total number of deaths (sum of deaths for valid years)
    const totalDeaths = formik.values.claimExperience.years.reduce((acc, item) => {
      return acc + (parseNumber(item.numberOfDeaths) > 0 ? parseNumber(item.numberOfDeaths) : 0);
    }, 0);

    // Step 2: Calculate the number of valid years (where deaths > 0)
    const validYearsCount = formik.values.claimExperience.years.filter((item) => parseNumber(item.numberOfDeaths) > 0).length;

    // Step 3: Calculate average claims (total deaths / number of valid years)
    const calculatedAverageClaims = validYearsCount > 0 ? totalDeaths / validYearsCount : 0;

    // Set the calculated average claims
    formik.setFieldValue("averageClaims", calculatedAverageClaims);

    // Step 4: Continue with other calculations and set the projection data
    const totalAmountFromBasis = yearsRows.reduce((acc, item) => acc + parseNumber(item.totalAmount), 0);
    const averageCoverageFromBasis = yearsRows.reduce((acc, item) => acc + parseNumber(item.averageCoverage), 0);
    const commissionForCalc = formik.values.commissionDistribution.map((i) => ({
      commissionTypeId: i.commissionTypeId,
      rate: parseFloat(String(i.rate || "0")) || 0,
    }));

    try {
      const response = await httpClient.post("/clsp-calculation/getClspPremium", {
        averageAge,
        averageClaims: calculatedAverageClaims, // Use the calculated averageClaims here
        productType: ProductCode.CLSP,
        lossRatio,
        totalSavingsAmount: totalAmountFromBasis,
        averageSavingsAmount: averageCoverageFromBasis,
        quotationCommissionDistribution: commissionForCalc,
        groupSize: totalNumberOfMembers,
        basisForCalculation,
        udd: projection.udd,
        cooperativeId,
        periodFrom: formatDate(basis.values.periodFrom),
        periodTo: formatDate(basis.values.periodTo),
        productId: basis.values.productId,
        claimsRatio: projection.claimsRatio,
      });

      if (response) {
        const data = response.data;

        formik.setFieldValue("projectionResult.claimsRatio", data?.claimsRatio ?? "");
        formik.setFieldValue("projectionResult.totalPremiumNetRate", data?.clspYearlyNetRate?.toFixed(2) ?? "0.00");
        formik.setFieldValue("projectionResult.totalPremiumGrossRate", data?.clspYearlyGrossRate?.toFixed(2) ?? "0.00");
        formik.setFieldValue("projectionResult.numberOfClaims", data?.expectedNumberOfClaims ?? "");
        formik.setFieldValue("projectionResult.amountOfClaims", data?.expectedAmountOfClaims ?? "");
        formik.setFieldValue("projectionResult.projectedTotalPremium", data?.projectedTotalPremium ?? "");
        formik.setFieldValue("projection", {
          ...formik.values.projection,
          totalPremiumNetRate: data?.clspYearlyNetRate?.toFixed(2) ?? "0.00",
          totalPremiumGrossRate: data?.clspYearlyGrossRate?.toFixed(2) ?? "0.00",
          numberOfClaims: data?.expectedNumberOfClaims ?? "",
          amountOfClaims: data?.expectedAmountOfClaims ?? "",
          claimsRatio: data?.claimsRatio ?? "",
        });
        toast.success("Premiums calculated successfully.");
      }
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || "An error occurred while calculating premiums. Please try again.";
      toast.error(errorMessage);
    }
  };

  const handleShowDemographic = () => {
    if (hasDemographics) {
      if (demographicModalController.isOpen) {
        handleSelectBasisModal();
      } else {
        demographicModalController.openFn();
      }
    } else {
      handleSelectBasisModal();
    }
  };

  const handleUpdateBasis = (data: any) => {
    basis.setValues(data);
  };

  const handleGenerate = async () => {
    const formattedDate = {
      periodFrom: basis.values.periodFrom.split("-").reverse().join("-"),
      periodTo: basis.values.periodTo.split("-").reverse().join("-"),
    };
    const { age, periodFrom, periodTo, ...rest } = basis.values;
    const data = { ...rest, ...formattedDate };

    try {
      const response = await httpClient.post("/quotations/actuaryDemographicData", data);
      if (response) {
        showSuccess("Demographic data generated successfully.").then(() => {
          demographicModalController.openFn();
          setHasDemographics(true);
          setDemographicData(response.data);
        });
      }
    } catch (err: any) {
      const msg = err?.response?.data?.message || err?.response?.data?.error || JSON.stringify(err?.response?.data) || "An unexpected error occurred while generating the basis.";
      toast.error(msg);
    }
  };

  useEffect(() => {
    const rawData = (location.state as any)?.quotationData || (location.state as any)?.selectedQuotation || localStorage.getItem("quotationData");
    if (!rawData) return;

    const qd = typeof rawData === "string" ? JSON.parse(rawData) : rawData;

    const q = qd?.quotation ?? qd?.quotations ?? qd;
    if (!q) return;

    const existingFileName: string | undefined = q.fileName;
    if (existingFileName) {
      setFileName(existingFileName);
      setHasDemographics(true);
    }

    const clsp = q.clspQuotation ?? {};
    const portfolioYears = q.clspPortfolioYears ?? [];
    const portfolioAges = q.clspPortfolioAges ?? [];
    const commissionDistributions = q.quotationCommissionDistribution ?? qd?.quotation?.quotationCommissionDistribution ?? qd?.commissionDistributions ?? [];
    const clspBenefits = q.options?.clspBenefits ?? q.clspBenefits ?? [];

    const claimsExperienceYears = q.quotationClaimsExperienceYear || rawData.quotationClaimsExperienceYear || [];
    const claimsExperienceAges = q.quotationClaimsExperienceAge || rawData.quotationClaimsExperienceAge || [];

    const processedClaimsYears =
      claimsExperienceYears.length > 0
        ? claimsExperienceYears.map((item: { year: string; numberOfDeaths: string; totalClaimAmount: string }) => ({
            year: parseInt(item.year) || new Date().getFullYear(),
            numberOfDeaths: item.numberOfDeaths?.toString() || "0",
            totalClaimAmount: item.totalClaimAmount?.toString() || "0",
          }))
        : [{ year: new Date().getFullYear(), numberOfDeaths: "0", totalClaimAmount: "0" }];

    const processedClaimsAges =
      claimsExperienceAges.length > 0
        ? claimsExperienceAges.map((item: { ageFrom: number; ageTo: number; numberOfDeaths: number; totalClaimAmount: number }) => ({
            ageFrom: parseInt(item.ageFrom?.toString() || "0", 10),
            ageTo: parseInt(item.ageTo?.toString() || "0", 10),
            numberOfDeaths: parseInt(item.numberOfDeaths?.toString() || "0", 10),
            totalClaimAmount: parseFloat(item.totalClaimAmount?.toString() || "0"),
          }))
        : [{ ageFrom: 0, ageTo: 0, numberOfDeaths: 0, totalClaimAmount: 0 }];
    // selected options
    let selectedOptions: number[] = [];
    if (Array.isArray(q.options?.aerOptions)) {
      selectedOptions = (q.options.aerOptions as string[]).map((n: string) => Number(n)).filter((n: number) => Number.isFinite(n));
    } else if (typeof q.options?.aerOptions === "string") {
      const m = q.options.aerOptions.match(/\d+/g);
      if (m) selectedOptions = m.map((s: string) => Number(s)).filter((n: number) => Number.isFinite(n));
    }
    if (!selectedOptions.length) {
      selectedOptions = Array.from(new Set((clspBenefits || []).map((b: { option: string | number }) => Number(b.option)).filter((n: number) => Number.isFinite(n))));
    }

    // group commissions by option
    const commissionsByOption: Record<number, Array<{ commissionTypeId: number; rate: number; proposedRate: number }>> = commissionDistributions.reduce((acc: any, c: any) => {
      const optId = Number(c.option);
      if (!Number.isFinite(optId)) return acc;
      acc[optId] ||= [];
      acc[optId].push({
        commissionTypeId: Number(c.commissionTypeId),
        proposedRate: parseFloat(c.rate ?? "0"),
        rate: parseFloat(c.rate ?? "0"),
      });
      return acc;
    }, {});

    const projectionArray = Array.isArray(q.projection) ? q.projection : Array.isArray(qd?.quotation?.projection) ? qd.quotation.projection : [];
    const projectionByOption: Record<number, any> = (projectionArray || []).reduce((acc: any, p: any) => {
      const opt = Number(p.option);
      if (Number.isFinite(opt)) {
        acc[opt] = {
          totalPremiumNetRate: parseFloat(p.totalPremiumNetRate ?? 0),
          totalPremiumGrossRate: parseFloat(p.totalPremiumGrossRate ?? 0),
          numberOfClaims: parseFloat(p.numberOfClaims ?? 0),
          amountOfClaims: parseFloat(p.amountOfClaims ?? 0),
          claimsRatio: parseFloat(p.claimsRatio ?? 0),
          udd: parseFloat(p.udd ?? 0),
        };
      }
      return acc;
    }, {});

    const optionsFromBenefits: Option[] = selectedOptions.map((optionId: number) => ({
      id: optionId,
      options: (clspBenefits || [])
        .filter((b: any) => Number(b.option) === optionId)
        .map((b: any) => ({
          benefitId: b.benefitId,
          ageFrom: b.ageFrom,
          ageTo: b.ageTo,
          option: Number(b.option),
          maximumCoverageAmount: parseFloat(b.maximumCoverageAmount),
          rate: parseFloat(b.rate),
        })),
      commissionDistribution: commissionsByOption[optionId] ?? [],
      projection: projectionByOption[optionId],
      condition: q.quotationCondition?.condition ?? "",
    }));

    const getPortfolioBucket = (t: unknown) => {
      const s = String(t || "").toLowerCase();
      if (s.includes(PortFolioCLSP.share) && s.includes(PortFolioCLSP.capital)) return "share";
      if (s.includes(PortFolioCLSP.savings)) return "savings";
      if (s.includes(PortFolioCLSP.time) && s.includes(PortFolioCLSP.deposit)) return "timeDeposit";
      return "unknown";
    };

    formik.setValues({
      ...formik.values,
      cooperativeId: q.cooperative?.id ?? q.coopId ?? q.cooperativeId ?? 0,
      previousProvider: q.previousProvider ?? "",
      contestabilityId: q.contestability ?? "",
      coverageBasis: q.coverageBasis ?? clsp.coverageBasis ?? "",
      status: qd.status ?? "",
      fileName: existingFileName ?? "",
      averageAge: q.averageAge ?? clsp.averageAge ?? "",
      signatoryTemplateId: q.signatoryTemplateId ?? 0,
      totalNumberOfMembers: q.totalNumberOfMembers ?? 0,

      quotationCondition: {
        condition: q.quotationCondition?.condition ?? "",
      },

      averageClaims: q.averageClaims ?? "",
      lossRatio: q.lossRatio ?? "",

      projection: {
        totalPremiumNetRate: q.projection?.totalPremiumNetRate ?? "",
        totalPremiumGrossRate: q.projection?.totalPremiumGrossRate ?? "",
        numberOfClaims: q.projection?.numberOfClaims ?? "",
        amountOfClaims: q.projection?.amountOfClaims ?? "",
        claimsRatio: q.projection?.claimsRatio ?? "",
        udd: q.projection?.udd ?? "",
      },

      commissionDistribution: (commissionDistributions || []).map((dist: any) => ({
        commissionTypeId: Number(dist.commissionTypeId),
        proposedRate: parseFloat(dist.rate ?? "0"),
        rate: "",
      })),

      benefits: (clspBenefits || []).map((b: any) => ({
        ageFrom: b.ageFrom,
        ageTo: b.ageTo,
        option: Number(b.option),
        benefitId: b.benefitId,
        maximumCoverageAmount: parseFloat(b.maximumCoverageAmount),
        rate: parseFloat(b.rate),
      })),

      selectedOptions,

      options: optionsFromBenefits,

      portfolio: {
        years: {
          shareCapital: (portfolioYears || [])
            .filter((p: any) => getPortfolioBucket(p.portfolioType) === "share")
            .map((p: any) => ({
              years: parseInt(p.year),
              minimumAmount: p.minimumAmount,
              maximumAmount: p.maximumAmount,
              totalAmount: p.totalPortfolio,
              averageCoverage: p.averageCoverage,
            })),
          savings: (portfolioYears || [])
            .filter((p: any) => getPortfolioBucket(p.portfolioType) === "savings")
            .map((p: any) => ({
              years: parseInt(p.year),
              minimumAmount: p.minimumAmount,
              maximumAmount: p.maximumAmount,
              totalAmount: p.totalPortfolio,
              averageCoverage: p.averageCoverage,
            })),
          timeDeposit: (portfolioYears || [])
            .filter((p: any) => getPortfolioBucket(p.portfolioType) === "timeDeposit")
            .map((p: any) => ({
              years: parseInt(p.year),
              minimumAmount: p.minimumAmount,
              maximumAmount: p.maximumAmount,
              totalAmount: p.totalPortfolio,
              averageCoverage: p.averageCoverage,
            })),
        },
        ages: {
          shareCapital: (portfolioAges || [])
            .filter((p: any) => getPortfolioBucket(p.portfolioType) === "share")
            .map((p: any) => ({
              ageFrom: p.ageFrom,
              ageTo: p.ageTo,
              totalNumberOfMembers: p.totalNumberOfMembers,
              totalShareCapital: p.totalCapital,
              averageCoverage: p.averageCoverage,
            })),
          savings: (portfolioAges || [])
            .filter((p: any) => getPortfolioBucket(p.portfolioType) === "savings")
            .map((p: any) => ({
              ageFrom: p.ageFrom,
              ageTo: p.ageTo,
              totalNumberOfMembers: p.totalNumberOfMembers,
              totalSavingsCapital: p.totalCapital,
              averageCoverage: p.averageCoverage,
            })),
          timeDeposit: (portfolioAges || [])
            .filter((p: any) => getPortfolioBucket(p.portfolioType) === "timeDeposit")
            .map((p: any) => ({
              ageFrom: p.ageFrom,
              ageTo: p.ageTo,
              totalNumberOfMembers: p.totalNumberOfMembers,
              totalTimeDepositCapital: p.totalCapital,
              averageCoverage: p.averageCoverage,
            })),
        },
      },
      claimExperience: {
        years: processedClaimsYears,
        ages: processedClaimsAges,
      },
    });
  }, [location.state]); // eslint-disable-line

  useEffect(() => {
    const options = formik.values.options as Option[];
    if (!options?.length) return;
    const defaultOptionId = options.some((o) => o.id === 1) ? 1 : Math.min(...options.map((o) => o.id));
    const def = options.find((o) => o.id === defaultOptionId);
    if (def?.commissionDistribution) {
      formik.setFieldValue("selectedOptions", [defaultOptionId]);
      formik.setFieldValue(
        "commissionDistribution",
        def.commissionDistribution.map((c) => ({
          commissionTypeId: c.commissionTypeId,
          proposedRate: (c.proposedRate ?? c.rate ?? "").toString(),
          rate: (c.rate ?? "").toString(),
        }))
      );
    }
  }, [formik.values.options]); // eslint-disable-line

  const hasCalculatedProjection = Object.values(formik.values.projectionResult || {}).some((v) => v !== "" && v !== null && v !== undefined);

  useEffect(() => {
    basis.setValues((prev) => ({
      ...prev,
      cooperativeId: formik.values.cooperativeId,
      fileName: fileName,
    }));
  }, [formik.values.cooperativeId, fileName]);

  useEffect(() => {
    setFormState(formik.values);
  }, [formik.values]);

  useEffect(() => {
    const state = getCooperativeByIdState;
    if (!state) return;
    if (state.success && state.data) {
      const data = state.data;
      const coopId = data.id!;
      if (processedCoopRef.current.has(coopId)) return;
      processedCoopRef.current.add(coopId);

      const normalized: ICda = { coopCdaId: coopId, coopName: data.coopName } as ICda;
      setDynamicCooperatives((prev) => (prev.some((c) => c.coopCdaId === normalized.coopCdaId) ? prev : [...prev, normalized]));
      fetchingCoopRef.current.delete(coopId);
    }
    if (state.error) {
      fetchingCoopRef.current.clear();
      toast.warn("Failed to fetch cooperative by ID");
    }
  }, [getCooperativeByIdState?.success, getCooperativeByIdState?.error, getCooperativeByIdState?.data?.id]); // eslint-disable-line

  useEffect(() => {
    const coopId = formState.cooperativeId;
    if (!coopId) return;

    const inRedux = cooperativesData.some((c: ISharesCoopInformation) => c.id === coopId);
    const inDynamic = dynamicCooperatives.some((c: ICda) => c.coopCdaId === coopId);
    const isAlreadyFetching = fetchingCoopRef.current.has(coopId);

    if (inRedux || inDynamic || isAlreadyFetching) return;
    fetchingCoopRef.current.add(coopId);
    getCooperativeById({ id: coopId });
  }, [formState.cooperativeId, cooperativesData, dynamicCooperatives, getCooperativeById]); // eslint-disable-line

  const searchTimeoutRef = useRef<number | null>(null);
  const handleCooperativeSearch = (searchTerm?: string) => {
    if (searchTimeoutRef.current) window.clearTimeout(searchTimeoutRef.current);
    searchTimeoutRef.current = window.setTimeout(() => getCooperatives({ payload: { filter: searchTerm ?? "" } }), 300);
  };

  const coopValueStr = formik.values.cooperativeId ? String(formik.values.cooperativeId) : "";

  useEffect(() => {
    if (formik.values.cooperativeId && basis.values.cooperativeId !== formik.values.cooperativeId) {
      basis.setFieldValue("cooperativeId", formik.values.cooperativeId);
    }
  }, [formik.values.cooperativeId]);

  return (
    <section>
      {selectBasisModal && (
        <BasisModal
          isOpen={selectBasisModal}
          onClose={handleSelectBasisModal}
          data={basis.values}
          onUpdate={handleUpdateBasis}
          onGenerate={handleGenerate}
          mainProducts={mainProducts}
          products={products}
          subProducts={subProducts}
        />
      )}

      {demographicData && (
        <DemographicModal controller={demographicModalController} basisValues={basis.values} hasDemographics={hasDemographics} demographicData={demographicData} productName={ProductName.CLSP} />
      )}

      <div className="grid grid-cols-12 gap-2 px-4">
        <div>
          <Button
            classNames="bg-primary text-sm flex gap-2 mb-4"
            variant="default"
            onClick={() => {
              localStorage.clear();
              navigate(ROUTES.ACTUARY.CLSP.key);
            }}
          >
            <FaChevronLeft className="mt-1" /> Back
          </Button>
        </div>

        <div className="block col-span-12">
          <h3 className={colorMode({ classLight: "text-2xl font-[600] mb-3 text-primary", classDark: "text-2xl font-[600] mb-3 text-white/60" })}>CLSP QUOTATION</h3>
          <p className={colorMode({ classLight: "text-gray text-base text-[12px]", classDark: "text-white/60 text-base text-[12px]" })}>
            Create a customized Coop Life Savings Plan quotation based on cooperative-specific data.
          </p>
        </div>

        <div className="col-span-12">
          <div className="grid grid-cols-12 gap-2 md:gap-y-7 py-6">
            <div className="col-span-12 md:col-span-2 flex flex-col">
              <span className={colorMode({ classLight: "mt-[8px] font-[500] text-[14px] text-gray", classDark: "mt-[8px] font-[500] text-[14px] text-white/60" })}>Cooperative</span>
            </div>
            <div className="col-span-12 md:col-span-10">
              <Select
                name="cooperativeId"
                placeholder="Select Coop"
                options={cooperativesSelectItems}
                onSearch={handleCooperativeSearch}
                value={coopValueStr}
                onChange={(v) => v && formik.setFieldValue("cooperativeId", parseNumber(v))}
                onBlur={formik.handleBlur}
                error={formik.touched.cooperativeId && !!formik.errors.cooperativeId}
                errorText={formik.errors.cooperativeId}
                allowSearch
                placeHolderCenter={false}
                disabled
              />
            </div>

            <div className="col-span-12 md:col-span-2 flex flex-col">
              <span className={colorMode({ classLight: "mt-[8px] font-[500] text-[14px] text-gray", classDark: "mt-[8px] font-[500] text-[14px] text-white/60" })}>Previous Provider</span>
            </div>
            <div className="col-span-12 md:col-span-10">
              <TextField
                className={`!w-full ${getFormikBorderClass(formik.touched.previousProvider, formik.errors.previousProvider)}`}
                name="previousProvider"
                placeholder="Enter previous provider"
                onChange={formik.handleChange}
                value={formik.values.previousProvider}
                onBlur={formik.handleBlur}
                error={formik.touched.previousProvider && !!formik.errors.previousProvider}
                errorText={formik.errors.previousProvider}
                required
                disabled
              />
            </div>

            <div className="col-span-12 md:col-span-2 flex flex-col">
              <span className={colorMode({ classLight: "mt-[8px] font-[500] text-[14px] text-gray", classDark: "mt-[8px] font-[500] text-[14px] text-white/60" })}>Contestabilty</span>
            </div>
            <div className="col-span-12 md:col-span-10 flex justify-between gap-4 max-w-full">
              <Select
                name="contestabilityId"
                placeholder="Select"
                options={contestabilitySelectItems}
                onChange={(e) => formik.setFieldValue("contestabilityId", e)}
                className="max-w-80"
                value={formik.values.contestabilityId}
                required
                disabled
              />
            </div>
          </div>

          <div className="flex gap-4">
            <div className="space-y-2">
              <label htmlFor="totalNumberOfMembers" className={colorMode({ classLight: "text-[14px] font-poppins-medium font-[500]", classDark: "text-[14px] font-poppins-medium font-[500]" })}>
                Total Number of Members
              </label>
              <TextField
                name="totalNumberOfMembers"
                placeholder="Number of Members"
                className={`!w-full ${getFormikBorderClass(formik.touched.totalNumberOfMembers, formik.errors.totalNumberOfMembers)}`}
                onChange={formik.handleChange}
                value={formik.values.totalNumberOfMembers}
                onBlur={formik.handleBlur}
                error={formik.touched.totalNumberOfMembers && !!formik.errors.totalNumberOfMembers}
                errorText={formik.errors.totalNumberOfMembers}
                required
                disabled
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="averageAge" className={colorMode({ classLight: "text-[14px] font-poppins-medium font-[500]", classDark: "text-[14px] font-poppins-medium font-[500]" })}>
                Average Age
              </label>
              <TextField
                name="averageAge"
                placeholder="Average age"
                className={`!w-full ${getFormikBorderClass(formik.touched.averageAge, formik.errors.averageAge)}`}
                onChange={formik.handleChange}
                value={formik.values.averageAge}
                onBlur={formik.handleBlur}
                error={formik.touched.averageAge && !!formik.errors.averageAge}
                errorText={formik.errors.averageAge}
                required
                disabled
              />
            </div>
          </div>

          <hr className="my-4 border-gray/10" />

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-10">
            <div className="space-y-2">
              <label htmlFor="coverageBasis" className={colorMode({ classLight: "text-[14px] font-poppins-medium font-[500]", classDark: "text-[14px] font-poppins-medium font-[500]" })}>
                Coverage Basis
              </label>
              <Select
                name="coverageBasis"
                placeholder="Select"
                options={coverageBasisSelectItems}
                onChange={(e) => formik.setFieldValue("coverageBasis", e)}
                value={formik.values.coverageBasis}
                disabled
              />
            </div>
          </div>

          <PortfolioSection
            coverageBasis={formik.values.coverageBasis}
            portfolio={formik.values.portfolio}
            onRemoveItem={(path, index) => {
              const rows = path.split(".").reduce((acc: any, key) => acc[key], formik.values);
              if (Array.isArray(rows)) rows.splice(index, 1);
              formik.setFieldValue(path, rows);
            }}
            onUpdatePortfolio={(path, rows) => formik.setFieldValue(path, rows)}
            isSelected={isSelected}
            COVERAGE_BASIS={COVERAGE_BASIS}
          />

          <ClspClaimsExperienceInfo value={formik.values.claimExperience} onChange={(data) => formik.setFieldValue("claimExperience", data)} readOnly />

          <hr className="my-4 border-gray/10" />

          <BenefitsSection
            benefits={formik.values.benefits}
            onAddBenefit={handleCreateBenefit}
            onRemoveBenefit={(index) => {
              const updated = [...formik.values.benefits];
              updated.splice(index, 1);
              formik.setFieldValue("benefits", updated);
            }}
            onUpdateBenefits={(updatedRows) => formik.setFieldValue("benefits", updatedRows)}
          />

          <hr className="my-10 border-gray/10" />

          <div className="w-full mt-2 p-4 border border-zinc-300 rounded-md items-center h-max gap-2 flex mb-2">
            <div className="w-full flex items-center justify-between gap-2">
              <div className="text-sm text-primary font-poppins-semibold">Demographic Data</div>
              <Button classNames="text-sm text-yellow-500 bg-yellow-100 rounded-md" onClick={handleShowDemographic}>
                Show Demographic
              </Button>
            </div>
          </div>

          <div className="mb-3">
            <h2 className="text-[20px] font-poppins-medium font-[600] text-primary">RATE PROJECTION AND COMMISSION ALLOCATION</h2>
            <p className="text-gray text-[12px]">
              Please ensure to fill out loss Ratio and Mortality Rate, select a basis for calculation, and set commission loading. The 'Calculate' button will automatically generate the projection
              data.
            </p>
          </div>

          <div>
            <div className="flex justify-between mb-2 mt-4">
              <div className="flex gap-4">
                <div className="space-y-2">
                  <label htmlFor="lossRatio" className={colorMode({ classLight: "text-[14px] font-poppins-medium font-[500]", classDark: "text-[14px] font-poppins-medium font-[500]" })}>
                    Loss Ratio
                  </label>
                  <TextField
                    name="lossRatio"
                    placeholder="Loss Ratio"
                    className={`!w-full ${formik.touched.lossRatio && formik.errors.lossRatio ? "!border-red-500" : "!border-[#00000040]"}`}
                    onChange={formik.handleChange}
                    value={formik.values.projection.claimsRatio}
                    onBlur={formik.handleBlur}
                    error={formik.touched.lossRatio && !!formik.errors.lossRatio}
                    errorText={formik.errors.lossRatio}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label
                    htmlFor="uniformDistributionOfDeath"
                    className={colorMode({ classLight: "text-[14px] font-poppins-medium font-[500]", classDark: "text-[14px] font-poppins-medium font-[500]" })}
                  >
                    Mortality Rate
                  </label>
                  <TextField
                    name="projection.udd"
                    placeholder="Mortality Rate"
                    className={`!w-full ${formik.touched.projection?.udd && formik.errors.projection?.udd ? "!border-red-500" : "!border-[#00000040]"}`}
                    onChange={formik.handleChange}
                    value={formik.values.projection.udd}
                    onBlur={formik.handleBlur}
                    error={formik.touched.projection?.udd && !!formik.errors.projection?.udd}
                    errorText={formik.errors.projection?.udd}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <label
                    htmlFor="uniformDistributionOfDeath"
                    className={colorMode({ classLight: "text-[14px] font-poppins-medium font-[500]", classDark: "text-[14px] font-poppins-medium font-[500]" })}
                  >
                    Average Claims
                  </label>
                  <TextField name="averageClaims" placeholder="AverageClaims" onChange={formik.handleChange} value={formik.values.averageClaims} disabled required />
                </div>
              </div>
            </div>

            <label className="text-sm">Basis for Calculation</label>
            <div className="flex justify-between gap-2">
              <div className="flex justify-start">
                <Radio
                  value={RATE_PROJECT_COMMISSION_ALLOCATION.MASTERLIST.key}
                  label="Masterlist"
                  name="basisForCalculation"
                  checked={basisForCalculation === RATE_PROJECT_COMMISSION_ALLOCATION.MASTERLIST.key}
                  onChange={handleRadio}
                />
                <Radio
                  value={RATE_PROJECT_COMMISSION_ALLOCATION.COOP_EXISTING_PRODUCT.key}
                  label="Coop Existing Product"
                  name="basisForCalculation"
                  checked={basisForCalculation === RATE_PROJECT_COMMISSION_ALLOCATION.COOP_EXISTING_PRODUCT.key}
                  onChange={handleRadio}
                />
              </div>

              <div className="flex justify-end gap-2">
                <Button classNames="block ms-auto border border-primary btn-sm" onClick={handleCreateCommissionDistribution}>
                  <div className="flex flex-row items-center gap-2">
                    <FaPlus className="inline text-primary" size={10} />
                    <span className="font-thin text-primary font-[300] text-sm">Add</span>
                  </div>
                </Button>
              </div>
            </div>
          </div>

          <div className="flex gap-4">
            <ProjectionTable values={formik.values.projection} handleChange={formik.handleChange} resultValues={formik.values.projectionResult} showCalculated={hasCalculatedProjection} />

            <CommissionLoading
              coopMode={formik.values.coopMode}
              commissionDistribution={formik.values.commissionDistribution}
              onChange={(updatedRows) => formik.setFieldValue("commissionDistribution", updatedRows)}
              onRemove={(index) => {
                const updated = [...formik.values.commissionDistribution];
                updated.splice(index, 1);
                formik.setFieldValue("commissionDistribution", updated);
              }}
              commissionTypes={commisionTypesSelectItems}
              onSelectChange={(index, value) => {
                const updated = [...formik.values.commissionDistribution];
                updated[index].commissionTypeId = value;
                formik.setFieldValue("commissionDistribution", updated);
              }}
            />
          </div>

          <hr className="my-8 border-gray/10" />

          <div className="flex justify-end gap-4 mt-8">
            <Button classNames={`text-white w-40 ${isCalculateDisabled ? "bg-slate-400 cursor-not-allowed" : "bg-green-500"}`} onClick={handleCalculate} disabled={isCalculateDisabled}>
              <span className="text-[14px] font-[400] font-poppins-medium">Calculate</span>
            </Button>

            <Button variant="primary" onClick={handleSaveOption}>
              <div className="flex flex-row items-center gap-2">
                <span className="text-[14px] font-[400] font-poppins-medium">Save as Option</span>
              </div>
            </Button>
          </div>

          {formik.values.options.length > 0 && (
            <SavedOptions
              options={formik.values.options}
              selectedOptions={formik.values.selectedOptions}
              quotationCondition={formik.values.quotationCondition}
              onSelectOption={handleSelectOption}
              onSetShowConditionContainer={setShowConditionContainer}
              showConditionContainer={showConditionContainer}
              onConditionChange={(content) => formik.setFieldValue("quotationCondition.condition", content)}
              onSubmitToSignatory={async () => {
                const missing: string[] = [];
                if (!formik.values.selectedOptions.length) missing.push("Select at least one option");
                if (!formik.values.quotationCondition.condition) missing.push("Provide a condition");

                if (missing.length) {
                  toast.error(missing.join(", "));
                  return;
                }

                await formik.submitForm();
              }}
              onLoadOption={handleLoadOption}
              signatoryTemplateId={formik.values.signatoryTemplateId || null}
              onTemplateChange={(id) => formik.setFieldValue("signatoryTemplateId", id ?? 0)}
            />
          )}
        </div>
      </div>
      <div className="min-h-40">
        {reviewModalController.isOpen && reviewData && (
          <ClspReviewModal
            isOpen={reviewModalController.isOpen}
            onClose={reviewModalController.closeFn}
            quotationData={reviewData}
            selectedQuotationProduct={(location.state as any)?.product}
            signatoryTemplateId={formik.values.signatoryTemplateId || null}
            status={formik.values.status}
            aerID={aerID}
            onSubmitAer={async (payload) => {
              // submit as FOR_SIGNATORY
              await dispatch(putClspAER({ ...payload, status: ProductStatus.FOR_SIGNATORY }) as any);
              await dispatch(getClspAERS({ params: { filter: "" } }) as any);
              reviewModalController.closeFn();
              toast.success("AER successfully submitted.");
              localStorage.removeItem("quotationData");
              if (putClspAERLoading) {
                navigate(ROUTES.ACTUARY.CLSP.key);
              }
            }}
            onSaveDraft={async (payload) => {
              await dispatch(putClspAER({ ...payload, status: ProductStatus.DRAFT }) as any);
              await dispatch(getClspAERS({ params: { filter: "" } }) as any);
              reviewModalController.closeFn();
              toast.success("AER saved as draft.");
            }}
          />
        )}
      </div>
    </section>
  );
}
