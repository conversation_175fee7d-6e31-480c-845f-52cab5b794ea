import React, { useMemo, useState } from "react";
import EditableTable from "@modules/sales/components/editable-table";
import Tabs from "@components/common/Tabs";
interface PortfolioSectionProps {
  coverageBasis: string;
  portfolio: any;

  onRemoveItem: (type: string, index: number) => void;
  onUpdatePortfolio: (path: string, rows: any[]) => void;
  isSelected: (value: string) => boolean;
  COVERAGE_BASIS: {
    SHARE_CAPITAL: string;
    SAVINGS: string;
    TIME_DEPOSIT: string;
  };
}

type TabKey = 0 | 1; // 0 = YEAR, 1 = AGE

const PortfolioSection: React.FC<PortfolioSectionProps> = ({ coverageBasis, portfolio, onRemoveItem, onUpdatePortfolio, isSelected, COVERAGE_BASIS }) => {
  // keep tab state per section
  const [activeTab, setActiveTab] = useState<{
    shareCapital: TabKey;
    savings: TabKey;
    timeDeposit: TabKey;
  }>({
    shareCapital: 0,
    savings: 0,
    timeDeposit: 0,
  });

  const getConfig = useMemo(() => {
    return (basis: "shareCapital" | "savings" | "timeDeposit", tab: TabKey) => {
      const isYear = tab === 0;

      const rows = isYear ? portfolio.years[basis] : portfolio.ages[basis];
      const updatePath = isYear ? `portfolio.years.${basis}` : `portfolio.ages.${basis}`;

      let columns: any[] = [];

      if (basis === "shareCapital") {
        columns = isYear
          ? [
              { header: "Years", key: "years", number: true, disabled: true },
              { header: "Minimum Amount", key: "minimumAmount", number: true, formatInput: true, disabled: true },
              { header: "Maximum Amount", key: "maximumAmount", number: true, formatInput: true, disabled: true },
              { header: "Total Amount", key: "totalAmount", number: true, formatInput: true, disabled: true },
              { header: "Average Coverage", key: "averageCoverage", number: true, formatInput: true, disabled: true },
            ]
          : [
              { header: "AGE FROM", key: "ageFrom", number: true, disabled: true },
              { header: "AGE TO", key: "ageTo", number: true, disabled: true },
              { header: "TOTAL NUMBER OF MEMBERS", key: "totalNumberOfMembers", number: true, disabled: true },
              { header: "TOTAL SHARE CAPITAL", key: "totalShareCapital", number: true, formatInput: true, disabled: true },
              { header: "AVERAGE", key: "averageCoverage", number: true, formatInput: true, disabled: true },
            ];
      }

      if (basis === "savings") {
        columns = isYear
          ? [
              { header: "Years", key: "years", number: true },
              { header: "Minimum Amount", key: "minimumAmount", number: true, formatInput: true },
              { header: "Maximum Amount", key: "maximumAmount", number: true, formatInput: true },
              { header: "Total Amount", key: "totalAmount", number: true, formatInput: true },
              { header: "Total Savings", key: "totalSavings", number: true, formatInput: true },
              { header: "Average", key: "averageCoverage", number: true, formatInput: true },
            ]
          : [
              { header: "AGE FROM", key: "ageFrom", number: true },
              { header: "AGE TO", key: "ageTo", number: true },
              { header: "TOTAL NUMBER OF MEMBERS", key: "totalNumberOfMembers", number: true },
              { header: "TOTAL SAVINGS", key: "totalSavingsCapital", number: true, formatInput: true },
              { header: "AVERAGE", key: "averageCoverage", number: true, formatInput: true },
            ];
      }

      if (basis === "timeDeposit") {
        columns = isYear
          ? [
              { header: "Years", key: "years", number: true },
              { header: "Minimum Amount", key: "minimumAmount", number: true, formatInput: true },
              { header: "Maximum Amount", key: "maximumAmount", number: true, formatInput: true },
              { header: "Total Amount", key: "totalAmount", number: true, formatInput: true },
              { header: "Average", key: "averageCoverage", number: true, formatInput: true },
            ]
          : [
              { header: "AGE FROM", key: "ageFrom", number: true },
              { header: "AGE TO", key: "ageTo", number: true },
              { header: "TOTAL NUMBER OF MEMBERS", key: "totalNumberOfMembers", number: true },
              { header: "TOTAL TIME DEPOSIT", key: "totalTimeDepositCapital", number: true, formatInput: true },
              { header: "AVERAGE", key: "averageCoverage", number: true, formatInput: true },
            ];
      }

      return { columns, rows, updatePath };
    };
  }, [portfolio, onRemoveItem]);

  const renderSection = (basisKey: "shareCapital" | "savings" | "timeDeposit", title: string, tab: TabKey, setTab: (k: TabKey) => void) => {
    const { columns, rows, updatePath } = getConfig(basisKey, tab);

    return (
      <div className="col-span-12 space-y-1">
        <span className="block text-center text-primary text-[18px] font-poppins-medium">{title}</span>

        {/* Use your Tabs: contents are empty, we mirror active index via onTabChange */}
        <Tabs
          headers={["YEAR", "AGE"]}
          contents={[<></>, <></>]}
          onTabChange={(idx) => setTab(idx as TabKey)}
          // avoid double padding/border from the internal content wrapper:
          contentClass="!p-0 !border-0"
          fullWidthHeader
        />

        {/* Single EditableTable driven by active tab */}
        <EditableTable columns={columns} rows={rows} onChange={(r) => onUpdatePortfolio(updatePath, r)} editable={false} />
      </div>
    );
  };

  return (
    <div className="border border-gray/10 bg-[#F6F6F680] p-6 mb-8 shadow-sm">
      <h3 className="text-lg text-primary font-[600] text-[16px] mb-3">PORTFOLIO</h3>

      <div className="grid grid-cols-12 gap-10">
        {coverageBasis.length <= 0 && (
          <div className="col-span-12 p-10 flex flex-row flex-nowrap items-center justify-center">
            <span className="text-gray/50">Select Coverage Basis to Deploy Portfolio</span>
          </div>
        )}

        {isSelected(COVERAGE_BASIS.SHARE_CAPITAL) && renderSection("shareCapital", "TOTAL SHARE CAPITAL", activeTab.shareCapital, (k) => setActiveTab((s) => ({ ...s, shareCapital: k })))}

        {isSelected(COVERAGE_BASIS.SAVINGS) && renderSection("savings", "TOTAL SAVINGS", activeTab.savings, (k) => setActiveTab((s) => ({ ...s, savings: k })))}

        {isSelected(COVERAGE_BASIS.TIME_DEPOSIT) && renderSection("timeDeposit", "TOTAL TIME DEPOSIT", activeTab.timeDeposit, (k) => setActiveTab((s) => ({ ...s, timeDeposit: k })))}
      </div>
    </div>
  );
};

export default PortfolioSection;
