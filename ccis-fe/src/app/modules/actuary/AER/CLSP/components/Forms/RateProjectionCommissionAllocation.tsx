import Radio from "@components/form/Radio";
import Button from "@components/common/Button";
import EditableTable from "@modules/sales/components/editable-table";
import { FaPlus } from "react-icons/fa";
import { PiMinusCircle } from "react-icons/pi";
import { RATE_PROJECT_COMMISSION_ALLOCATION } from "@constants/product-proposal";
import Select from "@components/form/Select";
import { ProductInsuranceType } from "@enums/product-status";

export default function RateProjectionCommissionAllocation({
  formik,
  benefitsSelectItems,
  commisionTypesSelectItems,
  handleCreateCommissionDistribution,
}: {
  formik: any;
  benefitsSelectItems: any[];
  commisionTypesSelectItems: any[];
  handleCreateCommissionDistribution: () => void;
}) {
  const handleRadio = (value: string) => {
    formik.setFieldValue("rateProjectionCommissionAllocation", value);
  };

  return (
    <div>
      <div className="mb-3">
        <h2 className="text-[20px] font-poppins-medium font-[600] text-primary">
          RATE PROJECTION AND COMMISSION ALLOCATION
        </h2>
        <p className="text-[12px] text-gray/50">
          Select the basis for the Calculation
        </p>
      </div>
      <div className="flex justify-between gap-4 text-xl mt-4 w-full">
        <div>
          <div className="flex gap-2">
            <Radio
              key="1"
              value={RATE_PROJECT_COMMISSION_ALLOCATION.MASTERLIST.key}
              label="Masterlist"
              name="RateProjectionCommissionAllocation"
              checked={
                formik.values.rateProjectionCommissionAllocation ===
                RATE_PROJECT_COMMISSION_ALLOCATION.MASTERLIST.key
              }
              onChange={handleRadio}
              required
            />
            <Radio
              key="2"
              value={
                RATE_PROJECT_COMMISSION_ALLOCATION.COOP_EXISTING_PRODUCT.key
              }
              label="Coop Existing Product"
              name="RateProjectionCommissionAllocation"
              checked={
                formik.values.rateProjectionCommissionAllocation ===
                RATE_PROJECT_COMMISSION_ALLOCATION.COOP_EXISTING_PRODUCT.key
              }
              onChange={handleRadio}
              required
            />
          </div>
          <div className="flex mt-2">
            <label className="text-sm w-full mt-2">Rate base from</label>
            <Select className="text-sm w-full" placeholder="Select options" />
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <Button
            classNames="block ms-auto border border-primary"
            onClick={handleCreateCommissionDistribution}
          >
            <div className="flex flex-row items-center gap-2">
              <FaPlus className="inline text-primary" size={10} />
              <span className="font-thin text-primary font-[300] text-sm">
                Add
              </span>
            </div>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
        <div className="flex flex-col border border-gray/10 rounded-md">
          <table className="w-full border-collapse border border-zinc-200 rounded">
            <thead className="bg-primary text-white">
              <tr>
                <th className="border border-zinc-200 px-4 py-2 text-left">
                  PROJECTION
                </th>
                <th className="border border-zinc-200 px-4 py-2 text-left text-xs">
                  Net Rate
                </th>
                <th className="border border-zinc-200 px-4 py-2 text-left text-xs">
                  Gross Rate
                </th>
              </tr>
            </thead>
            <tbody className="text-sm">
              {["Total Premium", "No. of Claims", "Amount of Claims", "Claims Ratio"].map((projection, index) => (
                <tr key={index}>
                  <td className="border border-zinc-200 px-4 py-2">{projection}</td>
                  <td className="border border-zinc-200 px-4 py-2">
                    <input
                      type="number"
                      className="w-full border border-zinc-200 rounded px-2 py-1"
                    />
                  </td>
                  <td className="border border-zinc-200 px-4 py-2">
                    <input
                      type="number"
                      className="w-full border border-zinc-200 rounded px-2 py-1"
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="flex flex-col border border-gray/10 rounded-md">
          <table className="w-full border-collapse border border-zinc-200 rounded">
            <thead className="bg-primary text-white">
              <tr>
                <th className="border border-zinc-200 px-4 py-2 text-left" colSpan={2}>
                  RATING
                </th>
              </tr>
            </thead>
            <tbody className="text-sm">
              {formik.values.benefits.slice(0, 1).map((benefit: { rate: string }, index: number) => (
                <tr key={index}>
                  <td className="border border-zinc-200 px-4 py-2">
                    {
                      benefitsSelectItems.find(
                        (b) => b.text.toLowerCase().trim() === ProductInsuranceType.LIFE_INSURANCE
                      )?.text ?? "N/A"
                    }
                  </td>
                  <td className="border border-zinc-200 px-4 py-2">
                    <input
                      type="number"
                      className="w-full border border-zinc-200 rounded px-2 py-1"
                      value={benefit.rate}
                      onChange={(e) => {
                        const updatedBenefits = [...formik.values.benefits];
                        updatedBenefits[index].rate = (
                          parseFloat(e.target.value) || 0
                        ).toString();
                        formik.setFieldValue("benefits", updatedBenefits);
                      }}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="flex flex-col border border-gray/10 rounded-md">
          <EditableTable
            className="border-b-0"
            columns={[
              {
                key: "commissionType",
                header: "COMMISSION LOADING",
                className: "text-[14px] font-[500]",
                locked: formik.values.coopMode,
                render(data, index) {
                  return (
                    <Select
                      placeholder="--- Select ---"
                      className="min-w-[100px]"
                      options={commisionTypesSelectItems}
                      onChange={(e) => {
                        const updatedCommissions = [...formik.values.commissionDistribution];
                        updatedCommissions[index].commissionType = parseInt(e.target.value);
                        formik.setFieldValue("commissionDistribution", updatedCommissions);
                      }}
                      value={data.commissionType ?? ""}
                      disabled={formik.values.coopMode}
                    />
                  );
                },
              },
              {
                key: "rate",
                header: "",
                className: "text-[14px] font-[500]",
                number: true,
              },
              {
                key: "",
                header: "",
                align: "center",
                className: "text-[14px] font-[500] w-[50px]",
                render: (_, index) => (
                  <Button
                    classNames="!w-fit !h-fit !p-0"
                    disabled={formik.values.coopMode}
                    onClick={() => {
                      const updatedCommissions = [...formik.values.commissionDistribution];
                      updatedCommissions.splice(index, 1);
                      formik.setFieldValue("commissionDistribution", updatedCommissions);
                    }}
                  >
                    <PiMinusCircle className="inline text-primary" />
                  </Button>
                ),
              },
            ]}
            rows={formik.values.commissionDistribution}
            onChange={(updatedRows) => {
              formik.setFieldValue("commissionDistribution", updatedRows);
            }}
          />
        </div>
      </div>
    </div>
  );
}
