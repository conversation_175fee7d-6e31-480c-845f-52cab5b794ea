import React from "react";
import EditableTable from "@modules/sales/components/editable-table";
import Select from "@modules/sales/components/select";
import Button from "@components/common/Button";
import { PiMinusCircle } from "react-icons/pi";
import { ISelectOptions } from "@interface/common.interface";

interface CommissionLoadingProps {
  coopMode: boolean;
  commissionDistribution: {
    commissionTypeId: number;
    rate: string;
  }[];
  onChange: (updatedRows: any[]) => void;
  onRemove: (index: number) => void;
  commissionTypes: ISelectOptions[];
  onSelectChange: (index: number, commissionTypeId: number) => void;
}

const CommissionLoading: React.FC<CommissionLoadingProps> = ({ coopMode, commissionDistribution, onChange, onRemove, commissionTypes, onSelectChange }) => {
  return (
    <div className="w-full border border-gray/10 rounded-md">
      <EditableTable
        className="border-b-0"
        columns={[
          {
            key: "commissionTypeId",
            header: "COMMISSION LOADING",
            className: "text-[14px] font-[500]",
            locked: coopMode,
            render(data, index) {
              return (
                <Select
                  placeholder="--- Select ---"
                  className="min-w-[100px]"
                  options={commissionTypes}
                  onChange={(e) => onSelectChange(index, parseInt(e))}
                  value={data.commissionTypeId ? String(data.commissionTypeId) : ""}
                  disabled={coopMode}
                />
              );
            },
          },

          {
            key: "rate",
            header: "Rate",
            className: "text-[14px] font-[500]",
            number: true,
          },
          {
            key: "",
            header: "",
            align: "center",
            className: "text-[14px] font-[500] w-[50px]",
            render: (_, index) => (
              <Button
                classNames="!w-fit !h-fit !p-0"
                disabled={coopMode}
                onClick={() => {
                  if (!coopMode) onRemove(index);
                }}
              >
                <PiMinusCircle className="inline text-primary" />
              </Button>
            ),
          },
        ]}
        rows={commissionDistribution}
        onChange={onChange}
      />
    </div>
  );
};

export default CommissionLoading;
