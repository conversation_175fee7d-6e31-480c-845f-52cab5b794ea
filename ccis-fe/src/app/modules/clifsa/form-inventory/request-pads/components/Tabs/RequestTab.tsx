import React, { useState } from "react";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";

import { IActions } from "@interface/common.interface";
import { GoVersions } from "react-icons/go";
import { useNavigate } from "react-router-dom";
import { ROUTES } from "@constants/routes";

import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useSelectOptions } from "@hooks/useSelectOptions";
import { useDebouncedSearch } from "@hooks/useDebouncedSearch";
import { createDateChangeHandler, createSelectChangeHandler } from "@helpers/handlers";
import RequestPadTable from "../table/request-pad";
import { getColumns } from "../table/request-pad/column/clifsa-column";
import { useFetchWithParams } from "@hooks/useFetchWithParams";
import { usePadRequestActions } from "@state/reducer/gam-pad-request";
import { IGamPadRequest } from "@interface/gam-request-pads";
import TableFilter from "../table/request-pad/filter/TableFilter";
import { RequestPadStatus } from "@enums/request-pads";
import { useAreaActions } from "@state/reducer/utilities-areas";

const RequestTab: React.FC = () => {
  const navigate = useNavigate();

  // States
  const [resetCounter, setResetCounter] = useState(0);
  const [divisionFilter, setDivisionFilter] = useState<number>(0);
  const [type, setType] = useState<number>(0);
  const [areaFilter, setAreaFilter] = useState<number>(0);
  const [dateFrom, setDateFrom] = useState<string>("");
  const [dateTo, setDateTo] = useState<string>("");
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Global state
  const { gamPadRequesTable } = useSelector((state: RootState) => state.gamPadRequest);

  // for Select options
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const types = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const areas = useSelector((state: RootState) => state.utilitiesAreas.areas);

  // Actions
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getPadRequests } = usePadRequestActions();
  const { getAreas } = useAreaActions();

  // Custom hooks
  const { value: searchText, handleChange: handleSearch, setValue: setSearchText } = useDebouncedSearch();

  const divisionOptions = useSelectOptions({
    data: divisions,
    firstOptionText: "Select Division",
    valueKey: "id",
    textKey: "divisionName",
  });

  const typeOptions = useSelectOptions({
    data: types,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: "formTypeName",
  });

  const areaOptions = useSelectOptions({
    data: areas,
    firstOptionText: "Select Area",
    valueKey: "id",
    textKey: "areaName",
  });

  // Select handlers
  const handleDivisionChange = createSelectChangeHandler(setDivisionFilter);
  const handleTypeChange = createSelectChangeHandler(setType);
  const handleAreaChange = createSelectChangeHandler(setAreaFilter);

  // Date handlers
  const handleDateFromChange = createDateChangeHandler(setDateFrom);
  const handleDateToChange = createDateChangeHandler(setDateTo);

  // Declare column action and add it
  const getActionEvents = (row: IGamPadRequest): IActions<any>[] => {
    const actions: IActions<any>[] = [
      {
        name: "View",
        event: () => {
          if (row.id !== undefined) {
            navigate(ROUTES.CLIFSAADMIN.requestPadForm.parse(row.id.toString()));
          }
        },
        icon: GoVersions,
        color: "primary",
      },
    ];

    return actions;
  };
  const columns = getColumns(getActionEvents);

  useFetchWithParams(
    [getDivisions, getFormTypes, getAreas],
    {
      filter: "",
    },
    [],
    false
  );

  // Fetch padRequests with filters and pagination
  useFetchWithParams(
    getPadRequests,
    {
      page,
      pageSize,
      nameFilter: searchText,
      divisionFilter,
      formtypeFilter: type,
      areaFilter,
      dateFrom,
      dateTo,
      statusFilter: `${RequestPadStatus.PENDING},${RequestPadStatus.FOR_APPROVAL}`,
    },
    [searchText, divisionFilter, type, areaFilter, page, pageSize, dateFrom, dateTo] // triggers refetch
  );

  const handleClearAll = () => {
    setSearchText("");
    setDivisionFilter(0);
    setType(0);
    setAreaFilter(0);
    setDateFrom("");
    setDateTo("");
    setResetCounter((prev) => prev + 1);
  };

  return (
    <div className="p-4">
      <div className="mt-8">
        <div className="flex flex-row justify-between">
          <TableFilter
            divisionFilter={divisionFilter}
            divisionOptions={divisionOptions}
            handleClearAll={handleClearAll}
            handleDivisionChange={handleDivisionChange}
            searchText={searchText}
            handleSearch={handleSearch}
            handleTypeChange={handleTypeChange}
            resetCounter={resetCounter}
            type={type}
            typeOptions={typeOptions}
            areaFilter={areaFilter}
            areaOptions={areaOptions}
            handleAreaChange={handleAreaChange}
            dateFrom={dateFrom}
            handleDateFromChange={handleDateFromChange}
            dateTo={dateTo}
            handleDateToChange={handleDateToChange}
          />
        </div>
        {/* Table Section */}
        <RequestPadTable
          data={gamPadRequesTable?.tableData?.data?.data}
          columns={columns}
          loading={gamPadRequesTable?.tableData?.loading}
          totalCount={gamPadRequesTable?.tableData?.data?.meta?.total !== undefined ? gamPadRequesTable.tableData.data.meta.total : [].length}
          onChangeRowsPerPage={(newPageSize, newPage) => {
            setPageSize(newPageSize);
            setPage(newPage);
          }}
          onPaginate={(newPage) => setPage(newPage)}
        />
      </div>
    </div>
  );
};

export default RequestTab;
