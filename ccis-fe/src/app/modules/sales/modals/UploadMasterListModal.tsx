import Button from "@components/common/Button";
import FileDropzone from "@components/common/FileDropzone";
import Modal from "@components/common/Modal";
import React, { useState } from "react";
import { PiCloudArrowUpThin } from "react-icons/pi";
import { toast } from "react-toastify";
import { ModalController } from "@modules/sales/controller";
import colorMode from "@modules/sales/utility/color";
import httpClient from "@clients/httpClient";
import { SiGooglesheets } from "react-icons/si";
import { ProductCode } from "@enums/product-code";
import { PiMicrosoftExcelLogoFill } from "react-icons/pi";

interface UploadMasterListModalProps {
  controller: ModalController;
  onUpload?: (file: File[] | any[], fileType: string) => Promise<void>;
  data?: any;
  setFileName?: (fileName: string) => void;
}

const spinnerStyles = `
  @keyframes spinner-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  .custom-spinner {
    transform-origin: center;
    animation: spinner-spin 1s linear infinite;
  }
`;

const UploadMasterListModal: React.FC<UploadMasterListModalProps> = ({ controller, onUpload, data, setFileName }) => {
  const [isUploading, setIsUploading] = useState(false);
  const [csvFiles, setCsvFiles] = useState<File[] | any[]>([]);
  const productCode = localStorage.getItem("productCode");
  const handleUpload = async (files: File[] | any[]) => {
    setCsvFiles(files);
  };

  const handleDone = async () => {
    const isInvalidQuotationCoopIdFip = !data?.quotations?.coopId || data?.quotations?.coopId === 0;
    const checkProductIdFip = data?.prodId === 0 || data?.prodId === undefined;
    const isInvalidQuotationCoopIdOtherProduct = data?.baseInfo?.cooperativeId === 0 || data?.baseInfo?.cooperativeId === undefined;
    const checkOtherProductId = data?.baseInfo?.productId === 0 || data?.baseInfo?.productId === undefined;

    if (ProductCode.FIP === productCode) {
      if (isInvalidQuotationCoopIdFip || checkProductIdFip) {
        const message = isInvalidQuotationCoopIdFip ? "Please select a cooperative first." : "Please select a product.";
        toast.error(message);
        return;
      }
    } else {
      if (isInvalidQuotationCoopIdOtherProduct || checkOtherProductId) {
        const message = isInvalidQuotationCoopIdOtherProduct ? "Please select a cooperative first." : "Please select a product.";
        toast.error(message);
        return;
      }
    }

    setIsUploading(true);

    const formData = new FormData();
    formData.append("masterlist", csvFiles[0]);
    formData.append("cooperativeId", data?.baseInfo?.cooperativeId ? data?.baseInfo?.cooperativeId : data.quotations.coopId || data?.cooperativeId || "0");
    formData.append("productId", data?.baseInfo?.productId || data?.prodId || "0");

    try {
      const response = await httpClient.post("/cooperative-masterlist/upload/bulk", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      if (response) {
        setIsUploading(false);
        controller.closeFn();
        toast.success("Uploaded successfully");
        setFileName && setFileName(response.data.fileName);
        onUpload?.(csvFiles, "csv");
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("Failed to upload masterlist. Please try again.");
      setIsUploading(false);
    }
  };
  const handleDownload = () => {
    const downloadUrl = `https://ccis-stage.s3.ap-southeast-1.amazonaws.com/uploads/attachments/templates/masterfiles/masterfile_format.csv`;
    window.open(downloadUrl, "_blank");
  };

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: spinnerStyles }} />
      <Modal
        isOpen={controller.isOpen}
        onClose={() => controller.closeFn()}
        modalContainerClassName="!w-[572px] !rounded-2xl overflow-hidden"
        modalBgColor={colorMode({
          classLight: "!bg-gradient-to-b !from-[#FFFFFF] !to-[#E8EEFF] !via-[#FDFDFD] !rotate-160",
          classDark: "!bg-[#1D232A]",
        })}
        showCloseButton={false}
      >
        {isUploading ? (
          <div className="flex flex-col gap-3 items-center justify-center w-full h-[400px]">
            <div className="relative h-[128px] w-[128px]">
              <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                <defs>
                  <linearGradient id="spinner-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#E8EEFF" />
                    <stop offset="100%" stopColor="#6C2CFF" />
                  </linearGradient>
                </defs>
                <circle cx="50" cy="50" r="42" fill="none" stroke="#F0F0FF" strokeWidth="10" />
                <path d="M50 8 A42 42 0 0 1 92 50" fill="none" stroke="url(#spinner-gradient)" strokeWidth="10" strokeLinecap="round" className="custom-spinner" />
              </svg>
            </div>
            <span
              className={colorMode({
                classLight: "text-gray text-[16px] font-[500]",
                classDark: "text-white text-[16px] font-[500]",
              })}
            >
              PROCESSING...
            </span>
          </div>
        ) : (
          <>
            <div className="flex flex-nowrap items-center gap-2">
              <PiCloudArrowUpThin
                size={30}
                className={colorMode({
                  classLight: "text-gray",
                  classDark: "text-white",
                })}
              />
              <h2
                className={colorMode({
                  classLight: "text-h2 text-[20px] font-[500]",
                  classDark: "text-white text-[20px] font-[500]",
                })}
              >
                Upload Masterlist
              </h2>
            </div>

            <div className="block w-full h-[303px]">
              {csvFiles.length > 0 && (
                <div className="w-full pt-20 ">
                  <div className="w-full flex items-center justify-center text-green-600">
                    <SiGooglesheets size={100} />
                  </div>
                  <div className="text-center text-primary font-semibold mt-4">{csvFiles[0].name}</div>
                  <div className="flex items-center justify-center mt-4">
                    <p className="p-4 py-2 bg-red-600 hover:bg-red-500 cursor-pointer text-white rounded-md w-20 text-center text-xs" onClick={() => setCsvFiles([])}>
                      Remove
                    </p>
                  </div>
                </div>
              )}
              {csvFiles.length === 0 && (
                <FileDropzone allowedTypes={{ "text/csv": [".csv"] }} setFiles={handleUpload}>
                  <div className="block mt-12 w-full">
                    <div className="flex flex-row items-center justify-center !w-full gap-2 border-dashed border-2 border-primary rounded-xl px-20 py-10">
                      <span
                        className={colorMode({
                          classLight: "text-gray font-[400] text-[14px] font-poppins-medium",
                          classDark: "text-white font-[400] text-[14px] font-poppins-medium",
                        })}
                      >
                        <b>Drag & drop</b> your files or{" "}
                        <u
                          className={colorMode({
                            classLight: "cursor-pointer text-primary",
                            classDark: "cursor-pointer text-white",
                          })}
                        >
                          browse
                        </u>
                      </span>
                    </div>
                    <div className="flex flex-row items-center justify-between mt-2">
                      <span
                        className={colorMode({
                          classLight: "block text-xs text-gray text-[10px] font-[400]",
                          classDark: "block text-xs text-white text-[10px] font-[400]",
                        })}
                      >
                        Supported format: csv
                      </span>
                      <span
                        className={colorMode({
                          classLight: "block text-xs text-gray text-[10px] font-[400]",
                          classDark: "block text-xs text-white text-[10px] font-[400]",
                        })}
                      >
                        Max: 40 MB
                      </span>
                    </div>
                  </div>
                </FileDropzone>
              )}
            </div>

            <hr className="border-gray/20" />

            <div className="flex  gap-2 items-center justify-between mt-2">
              <div className="flex  items-center hover:scale-110 transition duration-300">
                <div>
                  {" "}
                  <PiMicrosoftExcelLogoFill size={25} className="text-green-700" />
                </div>
                <div className="ml-1 border-b px-1 font-poppins-semibold cursor-pointer border-green-700  flex items-center justify-center text-green-700 text-xs" onClick={handleDownload}>
                  {" "}
                  Download Masterlist Format{" "}
                </div>
              </div>
              <div className="flex gap-2">
                <Button onClick={() => controller.closeFn()} classNames="text-white rounded-xl   px-16 py-[8px]">
                  <span
                    className={colorMode({
                      classLight: "text-gray/50 text-sm",
                      classDark: "text-gray/80 text-sm",
                    })}
                  >
                    CANCEL
                  </span>
                </Button>
                <Button
                  variant="default"
                  type="submit"
                  classNames="bg-info rounded-xl font-poppins-semibold px-16 py-[8px]"
                  disabled={isUploading}
                  onClick={() => {
                    if (isUploading) return;
                    handleDone();
                  }}
                >
                  DONE
                </Button>
              </div>
            </div>
          </>
        )}
      </Modal>
    </>
  );
};

export default UploadMasterListModal;
