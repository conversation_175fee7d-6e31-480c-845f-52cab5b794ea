import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import TextField from "@components/form/TextField";
import { ModalController } from "@modules/sales/controller";
import { ICda } from "@interface/cooperatives-cda";
import { useCooperativesCdaManagementActions } from "@state/reducer/cooperatives-cda";
import { RootState } from "@state/reducer";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { PiMagnifyingGlass, PiXCircle } from "react-icons/pi";
import colorMode from "@modules/sales/utility/color";

// Sort imports alphabetically

// Define types for cooperative data
const CooperativeModal = ({ controller, onSelect = () => void 0 }: { controller: ModalController; onSelect?: (coop: ICda) => void }) => {
  const { getCooperativesCda } = useCooperativesCdaManagementActions();

  const cooperativesCda = useSelector((state: RootState) => state.cooperativesCda.cooperativesCda);

  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedCoop, setSelectedCoop] = useState<ICda | null>(null);

  const handleCancel = useCallback(() => {
    controller.closeFn();
  }, [controller]);

  const handleSelect = useCallback(() => {
    if (!selectedCoop) return;
    onSelect?.(selectedCoop);
    controller.closeFn();
  }, [controller, selectedCoop, onSelect]);

  const handleCoopSelect = useCallback(
    (coop: ICda) => {
      setSelectedCoop(coop);
    },
    [setSelectedCoop]
  );

  const filteredCooperatives = useMemo(() => {
    if (!searchTerm.trim()) return cooperativesCda;
    const lowerCaseSearch = searchTerm.toLowerCase();
    return cooperativesCda.filter((coop: ICda) => coop.coopName.toLowerCase().includes(lowerCaseSearch));
  }, [searchTerm, cooperativesCda]);

  useEffect(() => {
    if (!controller.isOpen) return;
    getCooperativesCda({ filter: "" });
  }, [controller.isOpen]);

  return (
    <Modal
      isOpen={controller.isOpen}
      onClose={() => controller.closeFn()}
      modalContainerClassName="!w-[890px] !rounded-2xl overflow-hidden"
      modalBgColor={colorMode({
        classLight: "!bg-gradient-to-b !from-[#FFFFFF] !to-[#E8EEFF] !via-[#FDFDFD] !rotate-160",
        classDark: "!bg-gradient-to-b !from-[#1A1A1A] !to-[#1A1A1A] !via-[#1A1A1A] !rotate-160",
      })}
      showCloseButton={false}
    >
      <div className="block w-full">
        <div className="flex flex-row flex-nowrap justify-between items-center w-full">
          <h2
            className={colorMode({
              classLight: "text-[24px] font-[500] text-gray",
              classDark: "text-[24px] font-[500] text-white",
            })}
          >
            Cooperatives List from CDA
          </h2>
          <button className="absolute top-10 right-4 w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors" onClick={() => controller.closeFn()}>
            <PiXCircle size={24} />
          </button>
        </div>
        <hr className="my-4 border-gray/10" />

        <div className="flex flex-row flex-nowrap justify-between items-center w-full">
          <div className="relative w-full">
            <TextField
              type="text"
              placeholder="Search"
              rightIcon={<PiMagnifyingGlass size={24} />}
              className="w-full  py-2 border border-gray/20 rounded-lg focus:outline-none focus:border-primary"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <div className="mt-4 overflow-y-auto h-[300px] border border-gray/20 rounded-lg">
          {filteredCooperatives.length > 0 ? (
            filteredCooperatives.map((coop: any) => (
              <div
                key={coop.coopCdaId}
                className={`select-none p-4 border-b border-gray/10 hover:bg-gray-50 cursor-pointer ${selectedCoop?.coopCdaId === coop.coopCdaId ? "bg-gray/5" : ""}`}
                onClick={() => handleCoopSelect(coop)}
              >
                <h3
                  className={colorMode({
                    classLight: "text-[12px] font-[400] text-black",
                    classDark: "text-[12px] font-[400] text-white",
                  })}
                >
                  {coop.coopName}
                </h3>
              </div>
            ))
          ) : (
            <div
              className={colorMode({
                classLight: "p-4 text-center text-gray-500",
                classDark: "p-4 text-center text-gray-500",
              })}
            >
              No cooperatives found matching "{searchTerm}"
            </div>
          )}
        </div>

        <div className="grid grid-cols-12 gap-2 mt-4">
          <div className="col-span-12 md:col-span-3">
            <Button variant="danger" classNames="min-w-[195px] rounded-lg" onClick={handleCancel} block>
              Cancel
            </Button>
          </div>
          <div className="col-span-12 md:col-span-3 md:col-start-10">
            <Button variant="primary" classNames="min-w-[195px] rounded-lg" onClick={handleSelect} disabled={!selectedCoop} block>
              Select
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default CooperativeModal;
