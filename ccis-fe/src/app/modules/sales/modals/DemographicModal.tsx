// import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@components/common/Accordion";
import Button from "@components/common/Button";
import EditableTable from "@modules/sales/components/editable-table";
import Empty from "../icons/EmptyData.png";
import Modal from "@components/common/Modal";
import SideDrawer from "@components/template/Drawer";
import { ModalController } from "@modules/sales/controller";
import { PiArrowLeft, PiXCircle } from "react-icons/pi";
// import { PiArrowLeft, PiBookThin, PiCaretRight, PiMagnifyingGlass, PiXCircle } from "react-icons/pi";
// import TextField from "@components/form/TextField";
import colorMode from "@modules/sales/utility/color";

// import { useEffect } from "react";
// Types for demographic data structure
interface MasterlistSummary {
  totalMembers: number;
  minimumAge: number;
  maximumAge: number;
  averageAge: number;
  mortalityRate: number;
  managementFee: number;
}

interface AgeBracket {
  bracket: string;
  insuredCount: number;
  relativeFrequency: number;
}

interface StatisticalMean {
  category: string;
  arithmeticMean: number;
  geometricMean: number;
  harmonicMean: number;
}

interface ClaimData {
  ageBracket: string;
  numberOfClaims: number;
  totalClaimAmount: number;
  projectedGrossPremium: number;
  projectedNetPremium: number;
  claimsRatio: number;
}

interface ClimbsExperience {
  plans: { name: string; description: string }[];
}

interface DemographicData {
  masterlistSummary: MasterlistSummary;
  ageBrackets: AgeBracket[];
  statisticalMeans: StatisticalMean[];
  claimsData: ClaimData[];
  climbsExperience: ClimbsExperience;
}

interface DemographicModalProps {
  controller: ModalController;
  hasDemographics?: boolean;
  data?: DemographicData | any;
}

const DemographicModal = ({ controller, hasDemographics = true, data }: DemographicModalProps) => {
  if (!hasDemographics) {
    return (
      <Modal
        isOpen={controller.isOpen}
        onClose={() => controller.closeFn()}
        modalContainerClassName="!w-[572px] !rounded-2xl overflow-hidden"
        modalBgColor={colorMode({
          classLight: "!bg-gradient-to-b !from-[#FFFFFF] !to-[#E8EEFF] !via-[#FDFDFD] !rotate-160",
          classDark: "!bg-gradient-to-b !from-[#1A1A1A] !to-[#1A1A1A] !via-[#1A1A1A] !rotate-160",
        })}
        showCloseButton={false}
      >
        <div className="flex flex-col gap-3 items-center justify-center w-full h-[400px]">
          <button className="absolute top-4 right-4 w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors" onClick={() => controller.closeFn()}>
            <PiXCircle size={24} />
          </button>
          <div className="flex flex-col gap-3 items-center justify-center w-full h-[400px]">
            <img src={Empty} />
            <h1 className="text-h2 text-[24px] font-[600]">No Demographic Data Available</h1>
            <p
              className={colorMode({
                classLight: "text-gray text-[14px] text-center",
                classDark: "text-white text-[14px] text-center",
              })}
            >
              It seems there is no demographic data to display at the moment. Please upload a master list to proceed.
            </p>
            <Button
              variant="default"
              type="submit"
              classNames="bg-info rounded-xl px-16 py-[8px] mt-14"
              onClick={() => {
                controller.closeFn();
              }}
            >
              Upload Masterlist
            </Button>
          </div>
        </div>
      </Modal>
    );
  }

  return (
    <SideDrawer
      isOpen={controller.isOpen}
      handleDrawer={() => controller.setVisibility(false)}
      direction="right"
      className={colorMode({
        className: "!w-auto",
        classLight: "!bg-gradient-to-b !from-[#FFFFFF] !to-[#E8EEFF] !via-[#FDFDFD] !rotate-160",
        classDark: "!bg-gradient-to-b !from-[#1A1A1A] !to-[#1A1A1A] !via-[#1A1A1A] !rotate-160",
      })}
    >
      <div className="p-6 min-w-[572px]">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <PiArrowLeft size={24} className="text-gray" />
            <div className="text-lg font-medium">Demographic Information</div>
          </div>
        </div>

        <div className="grid grid-cols-12 gap-y-16 mt-6">
          {/* Based on submitted masterlist */}
          <div className="col-span-12">
            <div className="flex items-center justify-between">
              <div className="text-lg font-medium">BASED ON SUBMITTED MASTERLIST</div>
            </div>

            <div className="mt-6">
              <div className="grid grid-cols-2 gap-x-8 gap-y-4">
                {/* Left column */}
                <div className="space-y-4">
                  <ul className="space-y-4">
                    <li className="flex items-center text-[10px] font-[400]">
                      <div className="w-2 h-2 rounded-full bg-purple-700 mr-2"></div>
                      <span className="flex-1">TOTAL NO. OF MEMBERS</span>
                      <span className="font-medium">{data?.totalNumberOfMembers}</span>
                    </li>

                    <li className="flex items-center text-[10px] font-[400]">
                      <div className="w-2 h-2 rounded-full bg-purple-700 mr-2"></div>
                      <span className="flex-1">MINIMUM AGE</span>
                      <span className="font-medium">{data?.minimumAge}</span>
                    </li>

                    <li className="flex items-center text-[10px] font-[400]">
                      <div className="w-2 h-2 rounded-full bg-purple-700 mr-2"></div>
                      <span className="flex-1">MAXIMUM AGE</span>
                      <span className="font-medium">{data?.maximumAge}</span>
                    </li>
                  </ul>
                </div>

                {/* Right column */}
                <div className="space-y-4">
                  <ul className="space-y-4">
                    <li className="flex items-center text-[10px] font-[400]">
                      <div className="w-2 h-2 rounded-full bg-purple-700 mr-2"></div>
                      <span className="flex-1">AVERAGE AGE</span>
                      <span className="font-medium">{data?.averageAge}</span>
                    </li>

                    <li className="flex items-center text-[10px] font-[400]">
                      <div className="w-2 h-2 rounded-full bg-purple-700 mr-2"></div>
                      <span className="flex-1">MORTALITY RATE</span>
                      <span className="font-medium">{data?.mortalityRate ?? ""}</span>
                    </li>

                    <li className="flex items-center text-[10px] font-[400]">
                      <div className="w-2 h-2 rounded-full bg-purple-700 mr-2"></div>
                      <span className="flex-1">MANAGEMENT FEE</span>
                      <span className="font-medium">{data?.managementFee ?? ""}</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          {/*  */}
          <div className="col-span-12">
            <EditableTable
              columns={[
                {
                  header: "Age Bracket",
                  key: "ageBracket",
                  align: "center",
                  className: "text-[10px] font-[500]",
                },
                {
                  header: "No. of Insured",
                  key: "numberOfInsured",
                  align: "center",
                  className: "text-[10px] font-[500]",
                },
                {
                  header: "Relative Frequency",
                  key: "relativeFrequency",
                  align: "center",
                  className: "text-[10px] font-[500]",
                },
              ]}
              rows={data?.ageBracketCounts}
              editable={false}
            />
          </div>

          {/* TO BE USED LATER */}
          {/* <div className="col-span-12">
            <EditableTable
              columns={[
                {
                  header: "Category",
                  key: "category",
                  align: "center",
                  className: "text-[10px]  font-[500] text-nowrap whitespace-nowrap",
                },
                {
                  header: "Arithmetic Mean",
                  key: "arithmeticMean",
                  align: "center",
                  className: "text-[10px] font-[500] text-nowrap whitespace-nowrap",
                },
                {
                  header: "Geometric Mean",
                  key: "geometricMean",
                  align: "center",
                  className: "text-[10px] font-[500] text-nowrap whitespace-nowrap",
                },
                {
                  header: "Harmonic Mean",
                  key: "harmonicMean",
                  align: "center",
                  className: "text-[10px] font-[500] text-nowrap whitespace-nowrap",
                },
              ]}
              rows={data?.categoryProfile}
              editable={false}
            />
          </div> */}
          {/*  */}
          {/* TO BE USED LATER */}
          {/* <div className="col-span-12">
            <EditableTable
              columns={[
                {
                  header: "Age Bracket",
                  key: "ageBracket",
                  align: "center",
                  className: "text-[10px] font-[500] text-nowrap whitespace-nowrap",
                },
                {
                  header: "No. of Claims",
                  key: "claimsExperience",
                  align: "center",
                  className: "text-[10px] font-[500] text-nowrap whitespace-nowrap",
                },
                {
                  header: "Total Claim Amount",
                  key: "totalAmountOfClaims",
                  align: "center",
                  className: "text-[10px] font-[500] text-nowrap whitespace-nowrap",
                },
                {
                  header: "Projected Gross Premium",
                  key: "projectedGrossPremium",
                  align: "center",
                  className: "text-[10px] font-[500] text-nowrap whitespace-nowrap",
                },
                {
                  header: "Projected Net Premium",
                  key: "projectedNetPremium",
                  align: "center",
                  className: "text-[10px] font-[500] text-nowrap whitespace-nowrap",
                },
                {
                  header: "Claims Ratio",
                  key: "claimsRatio",
                  align: "center",
                  className: "text-[10px] font-[500] text-nowrap whitespace-nowrap",
                },
              ]}
              rows={data?.ageBracketProfile}
              editable={false}
            /> */}
        </div>
        {/* TO BE USED LATER */}
        {/* <div className="col-span-12">
            <hr className="mb-4 border-gray/10" />

            <div className="flex items-center justify-between">
              <div className="text-[14px] font-[500]">BASED ON CLIMBS EXPERIENCE</div>
              <TextField
                type="text"
                placeholder="Searched demographics based on climbs experience"
                size="xs"
                className="max-w-[300px] !h-[40px] border-gray/10"
                rightIcon={<PiMagnifyingGlass size={16} />}
              />
            </div>

            <Accordion type="single" className="mt-4">
              {demographicData.climbsExperience.plans.map((plan, index) => (
                <AccordionItem key={index} value={`item-${index}`} className="border border-gray/10 rounded-md mb-2 [&[data-state=open]_.caret]:rotate-90">
                  <AccordionTrigger className="p-3 flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <PiBookThin />
                      <span className="text-[12px] font-[500]">{plan.name}</span>
                    </div>
                    <PiCaretRight className="text-[12px] transition-transform duration-200 caret" />
                  </AccordionTrigger>
                  <AccordionContent className="p-3 border-t border-gray/10 text-[10px]">{plan.description}</AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </div> */}
      </div>
    </SideDrawer>
  );
};

export default DemographicModal;
