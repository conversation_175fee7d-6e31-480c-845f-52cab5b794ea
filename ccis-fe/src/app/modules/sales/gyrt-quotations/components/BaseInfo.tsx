import { ChangeEvent, useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { useCooperativesManagementActions } from "@state/reducer/cooperatives";
import { useContestabilityActions } from "@state/reducer/contestability";
import { ICda } from "@interface/cooperatives-cda";
import { IContestability } from "@interface/contestability.interface";
import { ISharesCoopInformation } from "@interface/shares.interface";
import Input from "@modules/sales/components/input";
import Select, { TOptions } from "@modules/sales/components/select";
import colorMode from "@modules/sales/utility/color";
import { useModalController } from "@modules/sales/controller";
import CooperativeModal from "@modules/sales/modals/CooperativeModal";
import { ProductId } from "@enums/product-id";
import { ProductCode } from "@enums/product-code";

export type TGyrtBaseData = {
  cooperativeId: number;
  // branch: string;
  previousProvider: string;
  premiumBudget: number;
  contestabilityId: number;
  lossRatio: number;
  basisForCalculation: string;
  productId: ProductId;
};

type TBaseInfoProps = {
  value?: TGyrtBaseData;
  onChange?: (data: TGyrtBaseData) => void;
  hasDemographics?: boolean;
  disabledContestability?: boolean;
};

export const GyrtBaseInfo = ({ value, onChange, hasDemographics, disabledContestability }: TBaseInfoProps) => {
  const { getCooperatives } = useCooperativesManagementActions();
  const { getContestability } = useContestabilityActions();

  const cooperativesData = useSelector((state: RootState) => state.cooperatives.cooperatives);

  const contestabilityData = useSelector((state: RootState) => state.contestability.contestabilities);

  // const [fieldState, setFieldState] = useState<TGyrtBaseData>(
  //   value ?? {
  //     cooperativeId: 0,
  //     // branch: "",
  //     previousProvider: "",
  //     premiumBudget: 0,
  //     contestabilityId: 0,
  //     lossRatio: 0,
  //     basisForCalculation: "",
  //     productId: ProductId.GYRT,
  //   }
  // );

  const [dynamicCooperatives, setDynamicCooperatives] = useState<ICda[]>([]);
  const cooperativeModalController = useModalController();

  const cooperativesSelectItems: TOptions[] = useMemo(
    () => [
      ...cooperativesData.map(({ coopName, id }: ISharesCoopInformation) => ({
        text: coopName,
        value: id?.toString() ?? "",
      })),
      ...dynamicCooperatives.map(({ coopName, coopCdaId }) => ({
        text: coopName,
        value: coopCdaId.toString(),
      })),
    ],
    [cooperativesData, dynamicCooperatives]
  );

  const contestabilitySelectItems = useMemo<TOptions[]>(
    () =>
      Array.isArray(contestabilityData)
        ? contestabilityData.map((item: IContestability) => ({
            text: item.label,
            value: item.id?.toString() ?? "",
          }))
        : [],
    [contestabilityData]
  );

  const handleChange = (key: keyof TGyrtBaseData, variable: string | number) => {
    const data: TGyrtBaseData = {
      cooperativeId: value?.cooperativeId ?? 0,
      previousProvider: value?.previousProvider ?? "",
      premiumBudget: value?.premiumBudget ?? 0,
      contestabilityId: value?.contestabilityId ?? 0,
      lossRatio: value?.lossRatio ?? 0,
      basisForCalculation: value?.basisForCalculation ?? "",
      productId: value?.productId ?? ProductId.GYRT,
      [key]: variable,
    };
    onChange?.(data);
  };

  useEffect(() => {
    getCooperatives({ payload: { filter: "" } });
    getContestability({ filter: ProductCode.GYRT });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (Array.isArray(contestabilityData) && typeof onChange === "function" && value) {
      const result = contestabilityData.find((item: IContestability) => item.label.toLowerCase() === "1 year");

      onChange({
        ...value,
        contestabilityId: result?.id ?? value.contestabilityId,
      });
    }
  }, [contestabilityData, onChange, value]);

  useEffect(() => {
    if (typeof onChange === "function" && value) {
      onChange(value);
    }
  }, [value?.contestabilityId, onChange, value]);

  // Function to handle cooperative search
  const handleCooperativeSearch = (searchTerm?: string) => {
    getCooperatives({ payload: { filter: searchTerm } });
  };

  return (
    <>
      <CooperativeModal
        controller={cooperativeModalController}
        onSelect={(coop: ICda) => {
          setDynamicCooperatives((prev) => [...prev, coop]);
          handleChange("cooperativeId", coop.coopCdaId);
        }}
      />
      <div className="grid grid-cols-12 gap-2 md:gap-y-7 py-6 ">
        <div className="col-span-12 md:col-span-2 flex flex-col">
          <span
            className={colorMode({
              classLight: "mt-[8px] font-[500] text-[14px] text-gray",
              classDark: "mt-[8px] font-[500] text-[14px] text-white/60",
            })}
          >
            Cooperative
          </span>
        </div>
        <div className="col-span-12 md:col-span-10 px-4">
          <Select
            name="cooperativeId"
            placeholder="Select Coop"
            options={cooperativesSelectItems}
            onChange={(e) => handleChange("cooperativeId", parseInt(e))}
            value={value?.cooperativeId.toString()}
            allowSearch={true}
            onSearch={handleCooperativeSearch}
            placeHolderCenter={false}
          >
            <span onClick={cooperativeModalController.openFn}>Add New Coop</span>
          </Select>
        </div>

        {/* <div className="col-span-12 md:col-span-2 flex flex-col">
          <span
            className={colorMode({
              classLight: "mt-[8px] font-[500] text-[14px] text-gray",
              classDark: "mt-[8px] font-[500] text-[14px] text-white/60",
            })}
          >
            Branch
          </span>
        </div>
        <div className="col-span-12 md:col-span-10">
          <Input
            name="branch"
            placeholder="Enter branch"
            value={fieldState.branch}
            onChange={(e: ChangeEvent<HTMLInputElement>) =>
              handleChange("branch", e.target.value)
            }
          />
        </div> */}

        <div className="col-span-12 md:col-span-2 flex flex-col">
          <span
            className={colorMode({
              classLight: "mt-[8px] font-[500] text-[14px] text-gray",
              classDark: "mt-[8px] font-[500] text-[14px] text-white/60",
            })}
          >
            Previous Provider
          </span>
        </div>
        <div className="col-span-12 md:col-span-10">
          <Input
            name="previousProvider"
            placeholder="Enter previous provider"
            value={value?.previousProvider || ""}
            onChange={(e: ChangeEvent<HTMLInputElement>) => handleChange("previousProvider", e.target.value)}
          />
        </div>

        <div className="col-span-12 md:col-span-2 flex flex-col">
          <span
            className={colorMode({
              classLight: "mt-[8px] font-[500] text-[14px] text-gray",
              classDark: "mt-[8px] font-[500] text-[14px] text-white/60",
            })}
          >
            Premium Budget
          </span>
        </div>
        <div className="col-span-12 md:col-span-10 lg:col-span-3">
          <Input
            name="premiumBudget"
            placeholder="Enter premium budget"
            value={value?.premiumBudget || ""}
            number={true}
            onChange={(e: ChangeEvent<HTMLInputElement>) => handleChange("premiumBudget", Number(e.target.value))}
          />
        </div>

        <div className="col-span-12 md:col-span-2 lg:col-span-2 xl:col-span-1 col-start-1 lg:col-start-8 xl:col-start-9 flex flex-col">
          <span
            className={colorMode({
              classLight: "mt-[8px] font-[500] text-[14px] text-gray",
              classDark: "mt-[8px] font-[500] text-[14px] text-white/60",
            })}
          >
            Contestabilty
          </span>
        </div>
        <div className="col-span-12 md:col-span-10 lg:col-span-3 px-4">
          <Select
            name="contestabilityId"
            placeholder="Select"
            options={contestabilitySelectItems}
            disabled={!hasDemographics || disabledContestability}
            value={value?.contestabilityId?.toString()}
            onChange={(e) => handleChange("contestabilityId", parseInt(e))}
          />
        </div>
        {/* 
        <div className="col-span-12 md:col-span-2 lg:col-span-2 xl:col-span-2 col-start-1 lg:col-start-8 xl:col-start-1 flex flex-col">
          <span
            className={colorMode({
              classLight: "mt-[8px] font-[500] text-[14px] text-gray",
              classDark: "mt-[8px] font-[500] text-[14px] text-white/60",
            })}
          >
            Basis for Calculation
          </span>
        </div> 
        <div className="col-span-12 md:col-span-10 lg:col-span-3 px-4">
          <Select name="basisForCalculation" placeholder="Select" options={STATUS_OPTIONS} value={fieldState.basisForCalculation} onChange={(e) => handleChange("basisForCalculation", e)} />
        </div> */}
      </div>
    </>
  );
};
