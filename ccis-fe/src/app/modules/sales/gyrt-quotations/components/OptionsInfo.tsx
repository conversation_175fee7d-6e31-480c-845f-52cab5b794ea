import { useEffect, useMemo } from "react";
import { useSelector } from "react-redux";
import { PiCaretRight } from "react-icons/pi";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@components/common/Accordion";
import Button from "@components/common/Button";
import CheckBox from "@components/form/CheckBox";
import { IUtilitiesProductBenefits } from "@interface/utilities.interface";
import { RootState } from "@state/store";
import { useContestabilityActions } from "@state/reducer/contestability";
import { TOptions } from "@modules/sales/components/select";
import { ProductCode } from "@enums/product-code";
import { CommissionDistribution } from "@modules/sales/types/types";

export type TGyrtOptions = {
  option: number;
  benefits: TSavedGyrtOption[];
  premium: TSavedGyrtPremium[];
  commissionDistribution?: CommissionDistribution[];
  age?: any;
  conditions: string;
  // commissionDistribution: TCommissionDistribution[];
};

type TSavedGyrtOption = {
  benefitId: number;
  coverage: number;
};

type TSavedGyrtPremium = {
  grossPremium: number;
  netPremium: number;
};

type OptionsInfoProps = {
  show: boolean;
  selectedOptions: number[];
  options: TGyrtOptions[];
  commission?: CommissionDistribution[];
  ages?: any;
  onSelectOption?: (option: number) => void;
  onCreateAer?: () => void;
  onSaveAsDraft?: () => void;
};

export const GyrtOptionsInfo = ({ show, selectedOptions, options, onSelectOption, onCreateAer, onSaveAsDraft, commission, ages }: OptionsInfoProps) => {
  const { getContestability } = useContestabilityActions();

  const productBenefitsData = useSelector((state: RootState) => state.utilitiesProductBenefits.productBenefits);
  const commissionTypesData = useSelector((state: RootState) => state.commissionType.getCommissionTypes.data);

  const benefitsSelectItems = useMemo<TOptions[]>(() => {
    if (!productBenefitsData) return [];
    return productBenefitsData.map((item: IUtilitiesProductBenefits) => ({
      text: item.benefitName,
      value: item.id.toString(),
    }));
  }, [productBenefitsData]);

  const handleSelectOption = (option: number) => {
    onSelectOption?.(option);
  };

  const handleSaveAsDraft = () => {
    onSaveAsDraft?.();
  };

  const handleCreateAer = () => {
    onCreateAer?.();
  };

  useEffect(() => {
    getContestability({ filter: ProductCode.GYRT });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!show) return null;
  return (
    <>
      <hr className="my-4 border-gray/10" />
      <div className="mb-3">
        <h2 className="text-[20px] font-poppins-medium font-[600] text-primary">SAVED OPTIONS</h2>
        <Accordion className="mt-4">
          {options.map((option, index) => (
            <AccordionItem key={index} value={`item-${index}`} className="border border-gray/10 rounded-md mb-2 [&[data-state=open]_.caret]:rotate-90">
              <AccordionTrigger className="p-3 flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <CheckBox
                    checked={false}
                    onChange={(e) => {
                      e.stopPropagation();
                      handleSelectOption(option?.option ?? 0);
                    }}
                  />
                  <span className="text-[12px] font-[500]">Option {option?.option}</span>
                </div>
                <PiCaretRight className="text-[12px] transition-transform duration-200 caret" />
              </AccordionTrigger>
              <AccordionContent className=" border-t border-gray/10 text-[12px]">
                {/* Table benefits */}
                <div className="flex flex-col gap-2">
                  <div className="overflow-x-auto flex gap-2">
                    <table className="w-1/2">
                      <thead>
                        <tr className="border-b border-gray/10 font-poppins-semibold">
                          <th className="text-left p-2 min-w-[200px] w-[30%]">Benefits</th>
                          <th className="text-left border-l border-gray/10 p-2">Coverage</th>
                        </tr>
                      </thead>
                      <tbody>
                        {option?.benefits.map((benefit, idx) => (
                          <>
                            <tr key={idx}>
                              <td className="p-3 min-w-[200px] w-[30%]">{benefitsSelectItems.find((b) => b.value == benefit.benefitId?.toString())?.text}</td>
                              <td className="text-left border-l border-gray/10 p-3">
                                <span className="pr-1">₱</span>
                                {parseFloat((benefit as any)?.coverage).toLocaleString(undefined, {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2,
                                  useGrouping: true,
                                })}
                              </td>
                            </tr>
                          </>
                        ))}
                        {option?.premium && option.premium.length > 0 && (
                          <tr>
                            <td className="p-3 font-poppins-semibold">Net Annual Premium</td>
                            <td className="text-left border-l border-gray/10 p-3 font-poppins-semibold">
                              <span className="pr-1">₱</span>
                              {option.premium[0].netPremium.toLocaleString(undefined, {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                                useGrouping: true,
                              })}
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                    <table className="w-1/2 border-l border-gray/10">
                      <thead>
                        <tr className="border-b border-gray/10 font-poppins-semibold">
                          <th className="text-left p-2 min-w-[200px] w-[30%]">Commission</th>
                        </tr>
                      </thead>
                      <tbody>
                        {commission &&
                          commission.length > 0 &&
                          commission
                            .filter((c: any) => Number(c.option) === Number(option.option))
                            .map((c: any, idx) => (
                              <tr key={`commission-${idx}`}>
                                <td className="p-3 ">
                                  {c.commissionTypeId ? (
                                    <span className="ml-2 text-xs text-gray-500">
                                      {Array.isArray(commissionTypesData) ? commissionTypesData.find((type: any) => type.id === c.commissionTypeId)?.commissionName : "Cannot find name"}
                                    </span>
                                  ) : null}
                                </td>
                                <td className="text-left border-l border-gray/10 p-3 font-poppins-semibold">
                                  {(c.rate * 100).toLocaleString(undefined, {
                                    useGrouping: true,
                                  })}
                                  <span className="ml-[1px]"> %</span>
                                </td>
                              </tr>
                            ))}
                      </tbody>
                    </table>
                  </div>
                </div>
                {/* New Table */}
                <div className="overflow-x-auto flex gap-2 mt-10 ">
                  <table className="w-full border-t border-gray/10 ">
                    <thead>
                      <tr className="border-b border-gray/10 font-poppins-semibold">
                        <th className=" text-left p-2 min-w-[120px]">Age Type</th>
                        <th className=" text-left p-2 min-w-[120px]">Minimum Age</th>
                        <th className=" text-left p-2 min-w-[120px]">Maximum Age</th>
                        <th className="text-left p-2 min-w-[120px]">Exit Age</th>
                      </tr>
                    </thead>
                    <tbody>
                      {ages
                        ?.filter((age: any) => Number(age.option) === option.option)
                        .map((age: any, idx: number) => (
                          <tr key={idx}>
                            <td className="p-2">{age.ageType}</td>
                            <td className="p-2">{age.minimum}</td>
                            <td className="p-2">{age.maximum}</td>
                            <td className="p-2">{age.exitAge}</td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                </div>
                {/* Conditions */}
                {/* to be used later on! */}
                {/* <div className="mt-4">
                  <pre className="text-sm font-poppins-medium text-wrap">
                    {option.conditions}
                  </pre>
                </div> */}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>

        <div className="justify-end flex gap-2 text-end mt-3">
          <Button onClick={handleSaveAsDraft} classNames="bg-amber-400 text-white rounded-md p-2 text-sm">
            Save as draft
          </Button>

          <Button
            classNames={`${!(selectedOptions.length > 0) ? "!bg-[#99999926]" : "!bg-info"} rounded-[5px] !px-12`}
            variant={!(selectedOptions.length > 0) ? "default" : undefined}
            onClick={handleCreateAer}
            disabled={selectedOptions.length > 0}
          >
            <div className="flex flex-row items-center gap-2">
              <span className={`${selectedOptions.length > 0 ? "text-white" : "text-gray/50"} text-[14px] font-[400] font-poppins-medium`}>Submit Request</span>
            </div>
          </Button>
        </div>
      </div>
    </>
  );
};
