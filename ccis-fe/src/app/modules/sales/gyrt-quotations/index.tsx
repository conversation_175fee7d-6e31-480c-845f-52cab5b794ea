/* eslint-disable react-hooks/exhaustive-deps */
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { toast } from "react-toastify";
import { PiCloudArrowUp } from "react-icons/pi";
import Button from "@components/common/Button";
import { ROUTES } from "@constants/routes";
import { RootState } from "@state/reducer";
import { useAppDispatch } from "@state/store";
import { useCommissionAgeTypeSManagementActions } from "@state/reducer/utilities-commission-age-types";
import { useCommissionTypeActions } from "@state/reducer/commision-type";
import { useCooperativesManagementActions } from "@state/reducer/cooperatives";
import { useProductBenefitsManagementActions } from "@state/reducer/utilities-product-benefits";
import { useQuotationActions } from "@state/reducer/quotations";
import { CalculationBenefits, TCreateQuotationWithAerPayload, TGyrtCalculationPayload } from "@state/types/quotation";
// import { AlertError } from "../components/alert-error";
import DemographicModal from "@modules/sales/modals/DemographicModal";
import UploadMasterListModal from "@modules/sales/modals/UploadMasterListModal";
import { useModalController } from "@modules/sales/controller";
import colorMode from "@modules/sales/utility/color";
import { parseNumber } from "@modules/sales/utility/number";
import { AERStatus } from "@enums/aer-status";
import { GyrtActionButton } from "./components/ActionButton";
import { AGE_TYPE_ALL, GyrtAgeInfo, TGyrtAgeInfo } from "./components/AgeInfo";
import { GyrtBaseInfo, TGyrtBaseData } from "./components/BaseInfo";
import { GyrtBenefitInfo, TGyrtBenefitInfo } from "./components/BenefitInfo";
import { GyrtClaimsExperienceInfo, TGyrtClaimsExperienceInfo } from "./components/ClaimsExperience";
import { GyrtCommissionDistributionInfo, TGyrtCommissionDistribution } from "./components/CommisionDistributionInfo";
import { COMMISION_AGE_TYPES } from "./components/constants";
import { GyrtGrossAndNetPremium } from "./components/GrossAndNetPremium";
import { GyrtMembersAndAverageInfo, TGyrtMembersAndAverageInfo } from "./components/MemberAndAverageInfo";
import { GyrtOptionsInfo, TGyrtOptions } from "./components/OptionsInfo";
import httpClient from "@clients/httpClient";
import { ProductId } from "@enums/product-id";
import { BENEFITS } from "./components/constants";
import { RATE_PROJECT_COMMISSION_ALLOCATION } from "@constants/product-proposal";
import { showError, showSuccess } from "@helpers/prompt";
import { IProjection } from "@interface/quotation.interface";
import { HIDDEN_COMMISSION_ID } from "./components/constants";
import { AGE_TYPE_BRACKET } from "./components/AgeInfo";
import { setQuotationResetSuccess } from "@state/reducer/quotations";

/**
 * BEGIN: Local Types
 */

type GyrtFormState = {
  baseInfo: TGyrtBaseData;
  membersAndAverageInfo: TGyrtMembersAndAverageInfo;
  claimsExperience: TGyrtClaimsExperienceInfo;
  ages: TGyrtAgeInfo[];
  benefits: TGyrtBenefitInfo[];
  commissionDistribution: TGyrtCommissionDistribution[];
  projection: IProjection[];
};

const initialState = (): GyrtFormState => ({
  baseInfo: {
    cooperativeId: 0,
    // branch: "",
    previousProvider: "",
    premiumBudget: 0,
    contestabilityId: 0,
    lossRatio: 0,
    basisForCalculation: "",
    productId: ProductId.GYRT,
  },
  membersAndAverageInfo: {
    totalNumberOfMembers: 0,
    averageAge: 0,
    averageClaims: 0,
  },
  claimsExperience: {
    years: [
      {
        year: new Date().getFullYear(),
        numberOfDeaths: 0.0,
        totalClaimAmount: 0.0,
      },
    ],
    ages: [],
  },
  ages: [
    {
      ageType: "",
      minimum: 0,
      maximum: 0,
      exitAge: 0,
    },
  ],
  benefits: [
    {
      benefitId: 0,
      coverage: 0,
    },
  ],
  commissionDistribution: [
    // {
    //   commissionTypeId: 0,
    //   ageTypeId: COMMISION_AGE_TYPES.STANDARD,
    //   ageFrom: 0,
    //   ageTo: 0,
    //   rate: 0,
    //   option: 0,
    // },
  ],
  projection: [],
});

export default function GYRTQuotation() {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const location = useLocation();

  // Actions
  const { getCooperatives } = useCooperativesManagementActions();
  const { getProductBenefits } = useProductBenefitsManagementActions();
  const { getCommissionAgeType } = useCommissionAgeTypeSManagementActions();
  const { getCommissionTypes } = useCommissionTypeActions();
  const { calculateQuotation, createQuotationWithAer, resetCalculateQuotation, clearGyrtQuotation, putQuotationWithAer, clearPutGyrtQuotation } = useQuotationActions();

  // Selectors
  const quotationData = useSelector((state: RootState) => state.quotation.calculateQuotation.data);
  const aerData = useSelector((state: RootState) => state.quotation.createQuotationWithAer);
  const calculated = useSelector((state: RootState) => state.quotation.calculateQuotation.success);
  const updateSuccess = useSelector((state: RootState) => state.quotation.putQuotationWithAer.success);

  const [conditions, setConditions] = useState<string>("");
  const [savedOptions, setSavedOptions] = useState<TGyrtOptions[]>([]);
  const [selectedOptions, setSelectedOptions] = useState<number[]>([]);
  const [alertError, setAlertError] = useState<string | undefined>(undefined);
  const [hasDemographics, setHasDemographics] = useState<boolean>(false);
  const [fileName, setFileName] = useState<string>("");
  const [demographicData, setDemographicData] = useState<any>(null);
  const [ageEligibilityValidation, setAgeEligibilityValidation] = useState<boolean>(false);
  const [claimsExperienceAgesValidation, setClaimsExperienceAgesValidation] = useState<boolean>(false);
  const [toggleAddAges, setToggleAddAges] = useState<boolean>(false);
  const [projection, setProjection] = useState<IProjection[]>([]);
  const [commissionDistribution, setCommissionDistribution] = useState<any[]>([]);
  const [ages, setAges] = useState<any[]>([]);
  const [rating, setRating] = useState<CalculationBenefits[]>([]);
  const [isDraft, setIsDraft] = useState<boolean>(false);
  const [disabledContestability, setDisabledContestability] = useState<boolean>(false);
  const [disabledHeader, setDisabledHeader] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  // State
  const [formState, setFormState] = useState<GyrtFormState>(() => {
    if (location.state?.data && location.state?.action === "edit") {
      const data = location?.state?.data;
      // Remove commissionDistribution with commissionTypeId === 7 (HIDDEN_COMMISSION_ID)

      return {
        ...initialState(),
        baseInfo: {
          cooperativeId: data?.quotation?.coopId ?? 0,
          previousProvider: data?.quotation?.previousProvider ?? "",
          premiumBudget: data?.quotation?.gyrtQuotations?.premiumBudget ?? 0,
          contestabilityId: data?.quotation?.contestability ?? 0,
          lossRatio: data?.quotation?.lossRatio ?? 0,
          basisForCalculation: data?.quotation?.basisForCalculation ?? "",
          productId: data?.quotation?.productId ?? ProductId.GYRT,
        },
        membersAndAverageInfo: {
          totalNumberOfMembers: data?.quotation?.totalNumberOfMembers ?? 0,
          averageAge: data?.quotation?.gyrtQuotations?.averageAge ?? 0,
          averageClaims: data?.quotation?.gyrtQuotations?.averageClaims ?? 0,
        },
        claimsExperience: {
          years: data?.quotation?.quotationClaimsExperienceYear ?? [],
          ages: data?.quotation?.quotationClaimsExperienceAge ?? [],
        },
        ages: data?.quotation?.gyrtAge ?? [],
        benefits: data?.quotation?.gyrtBenefits ?? [],
        commissionDistribution: (data?.quotation?.quotationCommissionDistribution ?? []).filter((item: any) => !HIDDEN_COMMISSION_ID.includes(item.commissionTypeId)),
      };
    }
    return initialState();
  });
  useEffect(() => {
    dispatch(setQuotationResetSuccess());
  }, [
    formState.baseInfo,
    formState.membersAndAverageInfo,
    formState.claimsExperience,
    formState.ages,
    formState.benefits,
    formState.commissionDistribution,
    // do not include formState.projection
  ]);

  // Modal controllers
  const uploadMasterListModalController = useModalController();
  const demographicModalController = useModalController();

  // Initial data loading
  useEffect(() => {
    getProductBenefits({ filter: "" });
    getCommissionAgeType({ filter: "" });
    getCommissionTypes();
  }, []);

  useEffect(() => {
    if (location?.state?.data) {
      getCooperatives({ payload: { filter: location?.state?.data?.quotation?.cooperative?.coopName } });
    } else {
      getCooperatives({ payload: { filter: "" } });
    }

    if (location.state?.data) {
      const data = location?.state?.data;
      setFormState({
        ...initialState(),
        baseInfo: {
          cooperativeId: data?.quotation?.coopId ?? 0,
          previousProvider: data?.quotation?.previousProvider ?? "",
          premiumBudget: Number(data?.quotation?.gyrtQuotations?.premiumBudget) ?? 0,
          contestabilityId: data?.quotation?.contestability ?? 0,
          lossRatio: data?.quotation?.lossRatio ?? 0,
          basisForCalculation: data?.quotation?.basisForCalculation ?? "",
          productId: data?.quotation?.productId ?? ProductId.GYRT,
        },
        membersAndAverageInfo: {
          totalNumberOfMembers: data?.quotation?.totalNumberOfMembers ?? 0,
          averageAge: data?.quotation?.gyrtQuotations?.averageAge ?? 0,
          averageClaims: data?.quotation?.gyrtQuotations?.averageClaims ?? 0,
        },
        claimsExperience: {
          years: data?.quotation?.quotationClaimsExperienceYear ?? [],
          ages: data?.quotation?.quotationClaimsExperienceAge ?? [],
        },
        ages: data?.quotation?.gyrtAge ?? [],
        benefits: data?.quotation?.gyrtBenefits ?? [],
        commissionDistribution: (data?.quotation?.quotationCommissionDistribution ?? []).filter((item: any) => !HIDDEN_COMMISSION_ID.includes(item.commissionTypeId)),
      });
    }

    setAges(location?.state?.data?.quotation?.gyrtAge ?? []);
    setCommissionDistribution((location?.state?.data?.quotation?.quotationCommissionDistribution ?? []).filter((item: any) => !HIDDEN_COMMISSION_ID.includes(item.commissionTypeId)));
  }, [location?.state?.data]);

  useEffect(() => {
    if (location?.state?.data) {
      const { gyrtBenefits = [], gyrtPremiums = [] } = location.state.data.quotation || {};

      // Group benefits and premiums by their option attribute
      const optionsMap: Record<number, TGyrtOptions> = {};

      gyrtBenefits.forEach((benefit: any) => {
        const opt = benefit.option ?? 0;
        if (!optionsMap[opt]) {
          optionsMap[opt] = {
            option: opt,
            benefits: [],
            premium: [],
            conditions: "", // Set this if you have a value for conditions
          };
        }
        optionsMap[opt].benefits.push({
          benefitId: benefit.benefitId,
          coverage: benefit.coverage,
        });
      });
      gyrtPremiums.forEach((premium: any) => {
        const opt = premium.option ?? 0;
        if (!optionsMap[opt]) {
          optionsMap[opt] = {
            option: opt,
            benefits: [],
            premium: [],
            conditions: "",
          };
        }
        optionsMap[opt].premium.push({
          grossPremium: premium.grossPremium,
          netPremium: premium.netPremium,
        });
      });

      // Convert map to array and set as initial savedOptions
      setSavedOptions(Object.values(optionsMap));
    }
  }, [location?.state?.data]);

  // Handle successful AER creation
  useEffect(() => {
    if (!aerData?.data) return;
    if (aerData?.data?.status === AERStatus.draft) {
      navigate(ROUTES.SALES.quotations.key);
    } else {
      navigate(ROUTES.SALES.quotationGyrtAerView.parse(aerData?.data?.quotationId));
    }
    toast.success("Quotation created successfully!");
    reset();
  }, [aerData]);

  // Show success message when calculation is complete
  useEffect(() => {
    if (calculated) toast.success("Quotation calculated successfully!");
    if (quotationData?.calculationResults) {
      // Use the same method as handleSaveOption to get the current number of options
      const numberOfOptions = Array.from(new Set(savedOptions.map((data) => data.option))).length;
      // Exclude results that already exist in rating (by option)
      const existingOptions = new Set(rating.map((item) => item.option));
      const resultsWithOption = quotationData.calculationResults
        .map((result: any, idx: number) => ({
          ...result,
          option: numberOfOptions + 1 + idx,
        }))
        .filter((result: any) => !existingOptions.has(result.option));
      setRating((prev) => [...prev, ...resultsWithOption]);
    }
  }, [calculated]);

  // Memoized values
  const minimumAge = useMemo<number>(() => {
    const sortedAges = [...formState.ages].sort((a, b) => a.minimum - b.minimum);
    return sortedAges.length > 0 ? sortedAges[0]?.minimum : 0;
  }, [formState]);

  const maximumAge = useMemo<number>(() => {
    const sortedAges = [...formState.ages].sort((a, b) => a.minimum - b.minimum);
    return sortedAges.length > 0 ? sortedAges[sortedAges.length - 1]?.maximum : 0;
  }, [formState]);

  const maxExitAge = useMemo<number>(() => {
    const sortedAges = [...formState.ages].sort((a, b) => a.exitAge - b.exitAge);
    return sortedAges.length > 0 ? sortedAges[0]?.exitAge : 0;
  }, [formState]);

  const isAllIn = useMemo<boolean>(() => {
    return formState.ages.filter((age) => age.ageType.includes("All")).length == formState.ages.length;
  }, [formState]);

  const calculationData = useMemo(() => {
    if (!quotationData?.data)
      return {
        net: 0,
        gross: 0,
      };
    return {
      net: quotationData?.netPremium,
      gross: quotationData?.grossPremium,
    };
  }, [quotationData, calculateQuotation]);

  // Helper functions
  const reset = () => {
    dispatch(clearGyrtQuotation());
    dispatch(resetCalculateQuotation());
    setFormState(initialState());
    setSavedOptions([]);
    setSelectedOptions([]);
    setConditions("");
    setAlertError(undefined);
  };

  const checkBenefits = useCallback(() => {
    const errors: string[] = [];

    const isOptionSelected = selectedOptions.length > 0;

    if (formState.benefits.length < 1 && !isOptionSelected) {
      errors.push("At least one benefit is required.");
    } else {
      formState.benefits.forEach((benefit, index) => {
        if (!benefit.benefitId) {
          errors.push(`Benefit type is required for benefit #${index + 1}.`);
        }

        if (!benefit.coverage) {
          errors.push(`Coverage amount is required for benefit #${index + 1}.`);
        }
      });
    }

    return errors.reverse();
  }, [formState.benefits, selectedOptions]);

  const checkForm = useCallback(() => {
    const errors: string[] = [];

    // Cooperative validation
    if (!formState.baseInfo.cooperativeId || formState.baseInfo.cooperativeId === 0) {
      errors.push("Please select a cooperative.");
    }

    // Branch validation
    // if (!formState.baseInfo.branch || formState.baseInfo.branch.trim() === "") {
    //   errors.push("Please enter a branch name.");
    // }

    // Contestability validation
    if (!formState.baseInfo.contestabilityId || formState.baseInfo.contestabilityId === 0) {
      errors.push("Please select a contestability period.");
    }

    // Members and average info validation
    if (!formState.membersAndAverageInfo.totalNumberOfMembers) {
      errors.push("Total number of members is required.");
    }

    if (formState.membersAndAverageInfo.totalNumberOfMembers < 25) {
      errors.push("Total number of members must be at least 25.");
    }

    if (!formState.membersAndAverageInfo.averageAge) {
      errors.push("Average age is required.");
    }

    // TO BE USED LATER
    // const isOptionSelected = selectedOptions.length > 0;
    // to be used in the future
    // Commission distribution validation
    // COMMENTED SINCE GI OPTIONAL LANG SYA PERO MAYBE E BALIK SOON
    // if (formState.commissionDistribution.length === 0 && !isOptionSelected) {
    //   errors.push("At least one commission distribution is required.");
    // } else {
    //   formState.commissionDistribution.forEach((commission, index) => {
    //     const isStandard = commission.ageTypeId === COMMISION_AGE_TYPES.STANDARD;

    //     if (!commission.commissionTypeId) {
    //       errors.push(`Commission type is required for distribution #${index + 1}.`);
    //     }

    //     if (!isStandard && !commission.ageFrom) {
    //       errors.push(`Starting age is required for distribution #${index + 1}.`);
    //     }

    //     if (!isStandard && !commission.ageTo) {
    //       errors.push(`Ending age is required for distribution #${index + 1}.`);
    //     }

    //     if (!commission.rate) {
    //       errors.push(`Rate is required for distribution #${index + 1}.`);
    //     }
    //   });
    // }

    // Benefits validation
    errors.push(...checkBenefits());

    return errors.reverse();
  }, [formState, selectedOptions]);

  const handleSelectOption = (option: number) => {
    const currentSelectedOptions = [...selectedOptions];
    const optionIndex = currentSelectedOptions.indexOf(option);
    if (optionIndex !== -1) {
      currentSelectedOptions.splice(optionIndex, 1);
    } else {
      currentSelectedOptions.push(option);
    }
    setSelectedOptions(currentSelectedOptions);
  };

  // Event handlers
  const handleUploadMasterList = () => {
    uploadMasterListModalController.openFn();
  };

  const handleShowDemographic = () => {
    demographicModalController.openFn();
  };

  const handleSaveOption = useCallback(() => {
    if (!calculated) return;

    setDisabledHeader(true);
    const errors = checkBenefits();
    if (errors.length > 0) {
      setAlertError(errors?.pop() ?? "Please correct the form errors before proceeding.");
      return;
    } else {
      setAlertError(undefined);
    }

    const option: TGyrtOptions = {
      option: 0,
      benefits: formState.benefits.map((benefit) => ({
        benefitId: benefit.benefitId,
        coverage: benefit.coverage,
      })),
      premium: [
        {
          grossPremium: quotationData.grossPremium,
          netPremium: quotationData.netPremium,
        },
      ],

      conditions: `${conditions}`,
    };

    const existingOptions: TGyrtOptions[] = [
      ...savedOptions.map((data) => ({
        option: 0,
        benefits: data.benefits.map((benefit) => ({
          benefitId: benefit.benefitId,
          coverage: benefit.coverage,
        })),
        premium: data.premium.map((premium) => ({
          grossPremium: premium.grossPremium,
          netPremium: premium.netPremium,
        })),

        conditions: data.conditions,
      })),
    ];

    // Check if any new benefit already exists in saved benefits
    const optionExists = _.some(existingOptions, (existingOption) => _.isEqual(existingOption, option));

    if (optionExists) {
      showError("Duplicate Option", "This option already exists in your saved options.");
      return;
    }

    const numberOfOptions = Array.from(new Set(savedOptions.map((option) => option.option))).length;

    setCommissionDistribution((prev) => [
      ...prev,
      ...formState.commissionDistribution.map((cd) => ({
        ...cd,
        option: numberOfOptions + 1,
      })),
    ]);

    setProjection((prev) => [
      ...prev,
      {
        totalPremiumNetRate: quotationData.netPremium,
        totalPremiumGrossRate: quotationData.grossPremium,
        numberOfClaims: quotationData.expectedNumberOfDeaths, // Placeholder, update as needed
        amountOfClaims: quotationData.expectedClaimsAmount, // Placeholder, update as needed
        claimsRatio: quotationData.lossRatio, // Placeholder, update as needed
        option: numberOfOptions + 1, // Add option number
      },
    ]);

    setAges((prev) => [
      ...prev,
      ...formState.ages.map((age) => ({
        ...age,
        option: numberOfOptions + 1,
      })),
    ]);

    setSavedOptions((prev) => {
      const data = [...prev];
      data.push({
        ...option,
        option: numberOfOptions + 1,
      });
      return data;
    });
    toast.success("Option saved successfully!");
    dispatch(resetCalculateQuotation());
  }, [formState, calculationData, calculated, savedOptions]);

  const handleToggleAddAges = () => {
    setToggleAddAges((prev) => !prev);
  };

  const handleCalculate = async () => {
    setDisabledContestability(true);
    if (hasDemographics) {
      fetchDemographicData();
    }

    const hasZeroRate = formState.commissionDistribution.some((obj: any) => Number(obj.rate) === 0);
    const hasZeroId = formState.commissionDistribution.some((obj: any) => obj.commissionTypeId === 0);
    if (hasZeroId) {
      showError("Validation Error", "There is a commission distribution with no value. Remove if not necessary.");
      return;
    }
    if (hasZeroRate) {
      showError("Validation Error", "There is a commission distribution with a zero rate value. Remove if not necessary.");
      return;
    }

    if (formState.commissionDistribution.reduce((acc, curr) => acc + Number(curr.rate), 0) > 0.35) {
      showError("Validation Error", "Total commission rate should not exceed 0.35");
      return;
    }
    // Check if all objects in ages array have no minimum or maximum (0 or "")
    const allNumberOfDeathsAndtotalClaimAmount = formState.claimsExperience.years.every(
      (item: any) => item.numberOfDeaths === 0 || item.numberOfDeaths === "" || item.totalClaimAmount === 0 || item.totalClaimAmount === ""
    );
    if (allNumberOfDeathsAndtotalClaimAmount && !hasDemographics) {
      showError("Validation Error", "Please add claims experience years to calculate.");
      return;
    }

    if (!hasDemographics && formState.ages.length === 0) {
      showError("Validation Error", "Please add ages before calculating since you don't have a masterlist uploaded.");
      return;
    }

    if (formState.claimsExperience.years.length !== 0) {
      if (formState.claimsExperience.ages.length === 0 && toggleAddAges) {
        showError("Validation Error", "Please add claims experience ages before calculating.");
        return;
      }
      if (formState.claimsExperience.years.some((item: any) => Object.values(item).some((value) => value === "0" || value === "" || value === 0))) {
        showError("Validation Error", "All claims experience year fields must have non-zero values.");
        return;
      }
    }
    ageEligibilityValidator();
    claimsExperienceValidator();

    //validator sa claimsExperience
    const latestYearObject = formState.claimsExperience.years.reduce((max, item) => (item.year > max.year ? item : max));
    const totalNumberOfDeaths = formState.claimsExperience.ages.reduce((sum, item) => sum + Number(item.numberOfDeaths || 0), 0);

    if (formState.claimsExperience.ages.length > 0) {
      if (Number(latestYearObject.numberOfDeaths) !== totalNumberOfDeaths) {
        showError(
          "Validation Error",
          "The total number of deaths in the age bracket entries must equal the number of deaths recorded in the latest year. Please correct the age bracket values accordingly."
        );
        return;
      }
    }

    // Check for duplicate benefitId in formState.benefits
    if (formState.benefits) {
      const benefitIds = formState.benefits.map((item) => item.benefitId);
      const duplicates = benefitIds.filter((id, idx) => benefitIds.indexOf(id) !== idx && id !== 0);
      if (duplicates.length > 0) {
        showError("Validation Error", "Duplicate benefit types are not allowed. Please ensure each benefit type is unique.");
        return;
      }
    }

    if (formState.ages) {
      const hasAgeOver70 = formState.ages.some((ageObj: any) => ageObj.maximum > 70);

      if (hasAgeOver70 && !hasDemographics) {
        showError("Validation Error", "One or more ages have range over 70 and no demographic data is provided.");
        return;
      }
    }

    if (formState?.ages?.[0]?.ageType === AGE_TYPE_BRACKET) {
      // Validate that minimum, maximum, and exitAge are not 0 or ""
      const firstAge: any = formState?.ages?.[0];
      if (firstAge && (firstAge.minimum === 0 || firstAge.minimum === "" || firstAge.maximum === 0 || firstAge.maximum === "" || firstAge.exitAge === 0 || firstAge.exitAge === "")) {
        showError("Validation Error", "Minimum age, maximum age, and exit age must not be zero or empty for the first age bracket.");
        return;
      }
    }

    const hasRequiredBenefit = formState.benefits.some((b) => Number(b.benefitId) === BENEFITS.LIFE_INSURANCE || Number(b.benefitId) === BENEFITS.NATURAL_DEATH);

    if (!hasRequiredBenefit) {
      showError("Validation Error", "At least one benefit must be Life Insurance or Natural Death.");
      return;
    }

    if (ageEligibilityValidation) {
      showError("Validation Error", "Please correct the age eligibility validation errors before proceeding.");
      return;
    }
    if (claimsExperienceAgesValidation) {
      showError("Validation Error", "Please correct the claims experience ages validation errors before proceeding.");
      return;
    }
    const errors = checkForm();
    if (errors.length > 0) {
      setAlertError(errors?.pop() ?? "Please correct the form errors before proceeding.");
      errors.forEach((err) => showError("Validation Error", err));
      return;
    } else {
      setAlertError(undefined);
    }

    // Get the least minimum value from formState.ages
    const leastMinimumAge = formState.ages.reduce((min, age) => (age.minimum < min ? age.minimum : min), formState.ages.length > 0 ? formState.ages[0].minimum : 0);
    const greatestMaximumAge = formState.ages.reduce((max, age) => (age.maximum > max ? age.maximum : max), formState.ages.length > 0 ? formState.ages[0].maximum : 0);

    const payload: TGyrtCalculationPayload = {
      fileName: fileName,
      ageType: formState.ages[0].ageType,
      ageFrom: formState.ages[0].ageType === AGE_TYPE_ALL ? 0 : leastMinimumAge,
      ageTo: formState.ages[0].ageType === AGE_TYPE_ALL ? 200 : greatestMaximumAge,
      contestabilityId: formState.baseInfo.contestabilityId,
      basisForCalculation: hasDemographics ? RATE_PROJECT_COMMISSION_ALLOCATION.MASTERLIST.key : RATE_PROJECT_COMMISSION_ALLOCATION.NO_DATA.key,
      averageAge: formState.membersAndAverageInfo.averageAge,
      averageClaims: formState.membersAndAverageInfo.averageClaims,
      groupSize: formState.membersAndAverageInfo.totalNumberOfMembers,
      gyrtBenefits: formState.benefits.map((data) => ({
        benefitId: data.benefitId,
        coverage: data.coverage,
        memberType: "Member",
      })),
      quotationCommissionDistribution: formState.commissionDistribution.map((data) => ({
        commissionTypeId: data.commissionTypeId,
        ageTypeId: data.ageTypeId,
        ...(data.ageTypeId != COMMISION_AGE_TYPES.STANDARD ? { ageFrom: data.ageFrom } : {}),
        ...(data.ageTypeId != COMMISION_AGE_TYPES.STANDARD ? { ageTo: data.ageTo } : {}),
        rate: parseNumber(data?.rate),
      })) as TGyrtCommissionDistribution[],
    };

    try {
      dispatch(calculateQuotation(payload));
    } catch (err) {
      toast.error("An error occurred while calculating the quotation. Please try again.");
    }
  };

  const handleSaveAsDraft = async () => {
    if (isSubmitting) return;
    setIsSubmitting(true);
    if (selectedOptions.length === 0) {
      setIsDraft(true);
      setSelectedOptions(savedOptions.map((option: any) => option.option).sort((a: number, b: number) => a - b));
    } else {
      setIsDraft(true);
      handleCreateAer();
    }
  };

  useEffect(() => {
    if (selectedOptions.length === selectedOptions.length && isDraft) {
      handleCreateAer();
    }
  }, [selectedOptions]);

  useEffect(() => {
    if (updateSuccess) {
      showSuccess("Quotation updated successfully!").then(() => {
        clearPutGyrtQuotation();
      });
      setIsSubmitting(false);
      navigate(ROUTES.SALES.quotations.key);
    }
  }, [updateSuccess]);

  useEffect(() => {
    if (alertError) {
      showError("Validation Error", alertError);
    }
  }, [alertError]);

  const handleCreateAer = () => {
    toast.info(isDraft ? "Saving as draft..." : "Creating AER...");
    if (!isDraft) {
      if (selectedOptions.length <= 0) {
        showError("Validation Error", "Please select at least one option before creating an AER.");
        return;
      }

      const errors = checkForm();
      if (errors.length > 0) {
        setAlertError(errors?.pop() ?? "Please correct the form errors before proceeding.");
        errors.forEach((err) => showError("Validation Error", err));
        return;
      } else {
        setAlertError(undefined);
      }
    }

    // const hasAgeOver70 = formState.ages.some((age) => age.maximum >= 70);
    // const AERstatus = formState?.ages?.[0].ageType === AGE_TYPE_ALL && !hasAgeOver70 ? AERStatus.for_approval : AERStatus.approved;

    const createAerPayload: TCreateQuotationWithAerPayload = {
      id: location?.state?.action === "edit" ? location.state.data?.id : undefined,
      status: isDraft ? AERStatus.draft : AERStatus.for_approval,
      premiumBudget: parseNumber(formState.baseInfo.premiumBudget),
      averageAge: parseNumber(formState.membersAndAverageInfo.averageAge),
      averageClaims: parseNumber(formState.membersAndAverageInfo.averageClaims) ?? 0,
      quotations: {
        coopId: formState.baseInfo.cooperativeId,
        previousProvider: formState.baseInfo.previousProvider,
        // branch: formState.baseInfo.branch,
        contestability: formState.baseInfo.contestabilityId,
        totalNumberOfMembers: parseNumber(formState.membersAndAverageInfo.totalNumberOfMembers),
        fileName: fileName,
      },
      quotationCondition: {
        condition: conditions,
      },
      gyrtAges: ages.map((age) => ({
        ageType: age.ageType,
        ...(age.minimum ? { minimum: age.minimum } : {}),
        ...(age.maximum ? { maximum: age.maximum } : {}),
        exitAge: parseNumber(age.exitAge),
        option: age?.option,
      })) as TGyrtAgeInfo[],
      claimsExperienceYears: formState.claimsExperience.years.map((year) => ({
        year: String(year.year),
        numberOfDeaths: parseNumber(year.numberOfDeaths),
        totalClaimAmount: parseNumber(year.totalClaimAmount),
      })),
      claimsExperienceAges: formState.claimsExperience.ages.map((age) => ({
        ageFrom: parseNumber(age.ageFrom),
        ageTo: parseNumber(age.ageTo),
        numberOfDeaths: parseNumber(age.numberOfDeaths),
        totalClaimAmount: parseNumber(age.totalClaimAmount),
      })),
      // to be used
      // commissionDistributions: formState.commissionDistribution.map((commission) => ({
      //   commissionTypeId: commission.commissionTypeId,
      //   ageTypeId: commission.ageTypeId,
      //   ...(commission.ageTypeId != COMMISION_AGE_TYPES.STANDARD ? { ageFrom: commission.ageFrom } : {}),
      //   ...(commission.ageTypeId != COMMISION_AGE_TYPES.STANDARD ? { ageTo: commission.ageTo } : {}),
      //   rate: commission.rate,
      //   option: commission.option,
      // })),
      commissionDistributions: commissionDistribution
        .filter((commission) => selectedOptions.includes(commission.option))
        .map((commission) => ({
          commissionTypeId: commission.commissionTypeId,
          ageTypeId: commission.ageTypeId,
          ...(commission.ageTypeId != COMMISION_AGE_TYPES.STANDARD ? { ageFrom: commission.ageFrom } : {}),
          ...(commission.ageTypeId != COMMISION_AGE_TYPES.STANDARD ? { ageTo: commission.ageTo } : {}),
          rate: commission.rate,
          option: parseNumber(commission.option),
        })),

      rating: rating
        .filter((item: any) => selectedOptions.includes(item?.option))
        .map((item: any) => ({
          ...item,
          option: parseNumber(item?.option),
        })),
      options: {
        gyrtBenefits: savedOptions
          .filter((data) => selectedOptions.includes(parseNumber(data.option)))
          .map((data) =>
            data.benefits.map((benefit) => ({
              benefitId: benefit.benefitId,
              coverage: benefit.coverage,
              option: parseNumber(data.option),
            }))
          )
          .flat(),
        quotationPremiums: savedOptions
          .filter((data) => selectedOptions.includes(parseNumber(data.option)))
          .map((data) =>
            data.premium.map((premium) => ({
              grossPremium: premium.grossPremium,
              netPremium: premium.netPremium,
              option: parseNumber(data.option),
            }))
          )
          .flat(),
        aerOptions: "[" + selectedOptions.join(",") + "]",
      },
      // projection: {
      // totalPremiumNetRate: parseNumber(formState.projection.totalPremiumNetRate ?? 0),
      // totalPremiumGrossRate: parseNumber(formState.projection.totalPremiumGrossRate ?? 0),
      // numberOfClaims: parseNumber(quotationData?.expectedNumberOfDeaths ?? 0),
      // amountOfClaims: parseNumber(quotationData?.expectedClaimsAmount ?? 0),
      // claimsRatio: parseNumber(quotationData?.lossRatio ?? 0),
      // udd: parseNumber(formState.projection.udd ?? 0),
      // },
      projection: projection.map((item: any) => ({
        totalPremiumNetRate: Number(item?.totalPremiumNetRate),
        totalPremiumGrossRate: Number(item?.totalPremiumGrossRate),
        numberOfClaims: Number(item?.numberOfClaims),
        amountOfClaims: Number(item?.amountOfClaims),
        claimsRatio: Number(item?.claimsRatio),
        option: Number(item?.option),
      })),
    };

    if (location.state?.action === "edit") {
      putQuotationWithAer(createAerPayload);
    } else if (location.state?.action === "copy") {
      createQuotationWithAer(createAerPayload);
    }
  };

  const handleSetFileName = (fileName: string) => {
    setFileName(fileName);
  };

  useEffect(() => {
    if (fileName) {
      fetchDemographicData(true);
    }
  }, [fileName]);

  const fetchDemographicData = async (showDemographic?: boolean) => {
    if (!fileName) {
      toast.error("Please upload a masterlist file first.");
      return;
    }
    // Find the object in data?.ages with the least minimum and the one with the least maximum
    let minAgeObj = null;
    let maxAgeObj = null;
    if (Array.isArray(formState?.ages) && formState.ages.length > 0) {
      minAgeObj = formState.ages.reduce((minObj: any, currObj: any) => {
        if (!minObj) return currObj;
        return (currObj.minimum ?? 0) < (minObj.minimum ?? 0) ? currObj : minObj;
      }, null);

      maxAgeObj = formState.ages.reduce((minObj: any, currObj: any) => {
        if (!minObj) return currObj;
        return (currObj.maximum ?? 0) < (minObj.maximum ?? 0) ? currObj : minObj;
      }, null);
    }

    try {
      const response = await httpClient.post("/quotations/demographicData", {
        fileName: fileName,
        productType: localStorage.getItem("productCode") ?? "",
        ageFrom: minAgeObj?.minimum || 0,
        ageTo: maxAgeObj?.maximum || 200,
      });
      if (response) {
        setDemographicData(response.data);
        formState.membersAndAverageInfo.totalNumberOfMembers = response.data.totalNumberOfMembers;
        formState.membersAndAverageInfo.averageAge = response.data.averageAge;
        formState.membersAndAverageInfo.averageClaims = response.data.averageClaims;
        toast.success("Demographic data fetched and updated successfully!");
        if (showDemographic) {
          demographicModalController.openFn();
        }
      }
    } catch (error) {
      toast.error("An unexpected error occurred. Please try again.");
    }
  };

  // useEffect(() => {
  //   if (demographicModalController.isOpen) {
  //     fetchDemographicData();
  //   }
  // }, [demographicModalController.isOpen]);

  useEffect(() => {
    const validYears = formState.claimsExperience.years.filter((item) => Number(item.numberOfDeaths) !== 0);
    const total = validYears.reduce((sum, item) => sum + Number(item.numberOfDeaths || 0), 0);
    const averageDeaths = validYears.length > 0 ? total / validYears.length : 0;

    setFormState((prev) => ({
      ...prev,
      membersAndAverageInfo: {
        ...prev.membersAndAverageInfo,
        averageClaims: averageDeaths,
      },
    }));
  }, [formState.claimsExperience.years]);

  const ageEligibilityValidator = () => {
    const ranges = formState.ages
      .filter((item) => item.minimum !== undefined && item.maximum !== undefined)
      .map((item, idx) => ({ idx, minimum: item.minimum, maximum: item.maximum }))
      .sort((a, b) => a.minimum - b.minimum);

    // Check for minimum greater than maximum
    const invalidRange = ranges.find((range) => range.minimum > range.maximum);
    if (invalidRange) {
      showError("Validation Error", `In age bracket #${invalidRange.idx + 1}, minimum (${invalidRange.minimum}) should not be greater than maximum (${invalidRange.maximum}).`);
      setAgeEligibilityValidation(true);
      return;
    }

    // Check for overlapping/duplicate ranges
    let duplicateRange: { minimum: number; maximum: number } | null = null;
    for (let i = 1; i < ranges.length; i++) {
      if (ranges[i].minimum <= ranges[i - 1].maximum) {
        duplicateRange = ranges[i];
        break;
      }
    }

    if (duplicateRange) {
      showError(
        "Validation Error",
        `Age bracket ${duplicateRange.minimum}–${duplicateRange.maximum} has already been entered or overlaps with another. Duplicate or overlapping age ranges are not allowed.`
      );
      setAgeEligibilityValidation(true);
    } else {
      setAgeEligibilityValidation(false);
    }
  };

  const claimsExperienceValidator = () => {
    const ranges = formState.claimsExperience.ages
      .filter((item) => item.ageFrom !== undefined && item.ageTo !== undefined)
      .map((item, idx) => ({ idx, minimum: item.ageFrom, maximum: item.ageTo }))
      .sort((a, b) => a.minimum - b.minimum);

    // Check for minimum greater than maximum
    const invalidRange = ranges.find((range) => range.minimum > range.maximum);
    if (invalidRange) {
      showError("Validation Error", `In age bracket #${invalidRange.idx + 1}, age from (${invalidRange.minimum}) should not be greater than age to (${invalidRange.maximum}).`);
      setClaimsExperienceAgesValidation(true);

      return;
    }

    // Check for overlapping/duplicate ranges
    let duplicateRange: { minimum: number; maximum: number } | null = null;
    for (let i = 1; i < ranges.length; i++) {
      if (ranges[i].minimum <= ranges[i - 1].maximum) {
        duplicateRange = ranges[i];
        break;
      }
    }

    if (duplicateRange) {
      showError(
        "Validation Error",
        `Age bracket ${duplicateRange.minimum}–${duplicateRange.maximum} has already been entered or overlaps with another. Duplicate or overlapping age ranges are not allowed.`
      );
      setClaimsExperienceAgesValidation(true);
    } else {
      setClaimsExperienceAgesValidation(false);
    }
  };

  useEffect(() => {
    if (quotationData) {
      setFormState((prev) => ({
        ...prev,
        projection: {
          ...prev.projection,
          claimsRatio: parseNumber(quotationData.lossRatio),
          totalPremiumGrossRate: parseNumber(quotationData.grossPremium),
          totalPremiumNetRate: parseNumber(quotationData.netPremium),
        },
      }));
    }
  }, [quotationData]);

  return (
    <section>
      <UploadMasterListModal
        controller={uploadMasterListModalController}
        // TODO: Change for the actual logic!
        onUpload={(files: any[], _: string) => {
          setHasDemographics(files.length > 0);
          return Promise.resolve();
        }}
        data={formState}
        setFileName={handleSetFileName}
      />
      {demographicData && <DemographicModal controller={demographicModalController} hasDemographics={hasDemographics} data={demographicData} />}
      <div className="grid grid-cols-12 gap-2 px-4">
        <div className="block col-span-12">
          <h3
            className={colorMode({
              classLight: "text-2xl font-[600] mb-3 text-primary",
              classDark: "text-2xl font-[600] mb-3 text-white/60",
            })}
          >
            GYRT QUOTATION
          </h3>
          <p
            className={colorMode({
              classLight: "text-gray text-base text-[12px]",
              classDark: "text-white/60 text-base text-[12px]",
            })}
          >
            Create a customized Group Yearly Renewable Term quotation based on cooperative-specific data.
          </p>
        </div>
        {/* Main Content */}
        <div className="col-span-12 space-y-10">
          {/* Alert Section */}
          {/* {alertError != undefined && <AlertError message={alertError} />} */}

          {/* Base Info */}
          <GyrtBaseInfo
            value={formState.baseInfo}
            disabledContestability={disabledContestability}
            hasDemographics={hasDemographics}
            onChange={(data) => {
              setFormState((prev) => ({
                ...prev,
                baseInfo: data,
              }));
            }}
          />

          {/* Demographics */}
          <div className="my-6 flex flex-wrap gap-4">
            {/* Masterlist */}
            <Button isSubmitting={disabledHeader} classNames="bg-info elevation-sm shadow-md rounded-[5px]" variant="default" onClick={handleUploadMasterList}>
              <div className="flex flex-row items-center gap-2">
                <PiCloudArrowUp size={25} />
                <span className="text-[14px] font-[400] font-poppins-medium">Upload Masterlist</span>
              </div>
            </Button>
            {/* Demographic */}
            <Button classNames="!bg-[#99999926] rounded-[5px]" variant="default" onClick={handleShowDemographic}>
              <div className="flex flex-row items-center gap-2">
                <span
                  className={colorMode({
                    classLight: "text-gray/50 text-[14px] font-[400] font-poppins-medium",
                    classDark: "text-white/60 text-[14px] font-[400] font-poppins-medium",
                  })}
                >
                  Show Demographic
                </span>
              </div>
            </Button>
          </div>

          <hr className="my-10 border-gray/10" />

          {/* Members and Average Info */}
          <GyrtMembersAndAverageInfo
            disabledHeader={disabledHeader}
            value={formState.membersAndAverageInfo}
            hasDemographics={hasDemographics}
            onChange={(data) => {
              setFormState((prev) => ({
                ...prev,
                membersAndAverageInfo: data,
              }));
            }}
          />

          {/* Claims Experience */}
          <GyrtClaimsExperienceInfo
            hasDemographics={hasDemographics}
            onToggleAddAges={handleToggleAddAges}
            value={formState.claimsExperience}
            onChange={(data) => {
              setFormState((prev) => ({
                ...prev,
                claimsExperience: data,
              }));
            }}
          />

          {/* Age */}
          <GyrtAgeInfo
            onRecalculate={fetchDemographicData}
            value={formState.ages}
            onChange={(data) => {
              setFormState((prev) => ({
                ...prev,
                ages: data,
              }));
            }}
          />

          {/* Benefits */}
          <GyrtBenefitInfo
            value={formState.benefits}
            onChange={(data) => {
              setFormState((prev) => ({
                ...prev,
                benefits: data,
              }));
            }}
          />

          {/* Commission Distribution */}
          <GyrtCommissionDistributionInfo
            value={formState.commissionDistribution}
            onChange={(data) => {
              setFormState((prev) => ({
                ...prev,
                commissionDistribution: data,
              }));
            }}
          />

          {/* Total Gross and Net Premium */}
          <GyrtGrossAndNetPremium />

          <hr className="my-4 border-gray/10" />

          {/* Action Buttons */}
          <GyrtActionButton
            hasDemographics={hasDemographics}
            ageType={formState?.ages?.[0]?.ageType}
            calculated={calculated}
            selectedOptions={selectedOptions}
            membersCount={formState.membersAndAverageInfo.totalNumberOfMembers}
            allIn={isAllIn}
            minimumAge={minimumAge}
            maximumAge={maximumAge}
            maxExitAge={maxExitAge}
            contestabilityId={formState.baseInfo.contestabilityId}
            onCalculate={handleCalculate}
            onSaveOption={handleSaveOption}
            emitConditions={setConditions}
          />

          {/* Options Info */}
          <GyrtOptionsInfo
            ages={ages ? ages : []}
            show={savedOptions.length > 0}
            selectedOptions={selectedOptions}
            options={savedOptions}
            commission={commissionDistribution ? commissionDistribution : []}
            onSelectOption={handleSelectOption}
            onCreateAer={handleCreateAer}
            onSaveAsDraft={handleSaveAsDraft}
          />
        </div>
      </div>
      {/* Sticky Calculate Button - hides when at bottom of page */}

      <div className="aboslute sticky bottom-10 left-4 w-max  ">
        <Button
          classNames={`z-50 elevation-sm shadow-md rounded-[5px] !px-12 ${!hasDemographics && formState?.ages?.[0]?.ageType === AGE_TYPE_ALL ? "!bg-opacity-50 cursor-not-allowed" : "!bg-success"}`}
          variant="success"
          onClick={handleCalculate}
          isSubmitting={!hasDemographics && formState?.ages?.[0]?.ageType === AGE_TYPE_ALL}
        >
          <div className="flex flex-row items-center gap-2">
            <span className="text-[14px] font-[400] font-poppins-medium">Calculate</span>
          </div>
        </Button>
      </div>
    </section>
  );
}
