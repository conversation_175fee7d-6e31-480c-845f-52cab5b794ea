export const condition = (numberOfSelectedOptions: number, membersCount: number, isAllIn: boolean, minimum: number, maximum: number, maxExitAge: number, contestability: string) => `
    <ol>
      ${numberOfSelectedOptions !== 1 ? `<li>Coop must choose only (1) one option for the whole group.</li>` : ""}
      <li>100% participation of all qualified members of the cooperative or a minimum of ${membersCount} enrollees in ONE TIME ENROLLMENT. Failure to meet the minimum participation requirement may result in a premium refund and evaluation if they wish to re-enrol.</li>
      
      <li>
        ${isAllIn ? "Age eligibility: All In." : `Age eligibility: of ${minimum} to ${maximum} years old (exit age ${maxExitAge} years old).`}
      </li>
      
      <li>One ${contestability} contestability period for all members for pre-existing illnesses.</li>
      
      <li>Accidental death ends on 65th birthday of the insured.</li>
      
      <li>
        Both parties agree that in the event of delayed premium refund due to disqualifications or adjustment, the provisions outlined in this contract shall prevail over any conflicting terms regarding claims arising from such delay. That CLIMBS shall process claims based on terms and conditions of this contract, even if premium refund is delayed.
      </li>
  
      <li>
        Subject to actuarial evaluation after (1) one year coverage, Rate may change depending on the previous years insurance experience.
      </li>
      
      <li>
        This Actuarial Evaluation Report is for CLIMBS internal copy only and should not be forwarded to coop.
      </li>
    </ol>
`;
