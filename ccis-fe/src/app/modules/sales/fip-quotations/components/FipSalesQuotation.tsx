import { FC, useCallback, useEffect, useMemo, useState } from "react";
import colorMode from "@modules/sales/utility/color";
import Select from "@modules/sales/components/select";
// import Input from "@modules/sales/components/input"; // for future use
import TextField from "@components/form/TextField";
import Button from "@components/common/Button";
import { PiCloudArrowUp } from "react-icons/pi";
import FipPremiunDetails from "./FipPremiumDetails";
import CommissionDistribution from "./CommissionDistribution";
import ConditionAndOption from "./ConditionAndOption";
import { eAgeType, eInsuredType, Statuses, typeCoInsured } from "@constants/global-constant-value";
import {
  IQuotations,
  IQuotationCondition,
  ISalesFipQuotation,
  IClaimsExperienceYears,
  IClaimsExperienceAges,
  ICommissionDistributions,
  IOptions,
  IFipCoInsuredDependents,
  IFipCoInsuredDependentsBenefits,
  IFipPrincipalMemberRequestData,
  IFipPrincipalMemberBenefitRequestData,
  IFilteredDataCondition,
  IFipAges,
  IRating,
  IProjection,
} from "@interface/quotation.interface";
import { postDemographicDataService } from "@services/quotation/quotation.service";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { useCooperativesManagementActions } from "@state/reducer/cooperatives";
import { ISharesCoopInformation } from "@interface/shares.interface";
import CooperativeModal from "@modules/sales/modals/CooperativeModal";
import { useModalController } from "@modules/sales/controller";
import { ICda } from "@interface/cooperatives-cda";
import { useFormik } from "formik";
import { ISelectOptions } from "@interface/common.interface";
import { useCommissionTypeActions } from "@state/reducer/commision-type";
import { ICommissionType } from "@interface/commission-structure.interface";
import { IUtilitiesCommissionAgeTypes, IUtilitiesProductBenefits } from "@interface/utilities.interface";
import { useCommissionAgeTypeSManagementActions } from "@state/reducer/utilities-commission-age-types";
import { useContestabilityActions } from "@state/reducer/contestability";
import { IContestability } from "@interface/contestability.interface";
import testData from "@constants/json-file/data.json";
import { useProductBenefitsManagementActions } from "@state/reducer/utilities-product-benefits";
import { useSalesFipQuotationManagementActions } from "@state/reducer/quotation-sales-fip";
import { toast } from "react-toastify";
import { TSalesFipCalculationPayload } from "@state/types/sales-fip-quotation";
import { useAppDispatch } from "@state/store";
import { useLocation, useNavigate } from "react-router-dom";
import { ROUTES } from "@constants/routes";
import { confirmSaveOrEdit } from "@helpers/prompt";
import DemographicModal from "@modules/sales/modals/DemographicModal";
import { addError, validateArray, sortErrorsByPriority, validationMessagesFip } from "@helpers/validationHelper";
import UploadMasterListModal from "@modules/sales/modals/UploadMasterListModal";
import { getProductNameService } from "@services/products/products.service";
import { useProductActions } from "@state/reducer/products";
import { removeProperties } from "@helpers/objects";
import { findItem } from "@helpers/array";
import { ProductCode } from "@enums/product-code";
import { capitalizeFirstLetterWords } from "@helpers/text";
import { formatNumberWithCommas } from "@helpers/format-number";
const FipSalesQuotation: FC = () => {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const { quotationData, productCodeName, isEdit } = location.state || {};
  const { getCooperatives } = useCooperativesManagementActions();
  const { getCommissionTypes } = useCommissionTypeActions();
  const { getCommissionAgeType } = useCommissionAgeTypeSManagementActions();
  const { getContestability } = useContestabilityActions();
  const { getProductBenefits } = useProductBenefitsManagementActions();
  const { setSelectedProduct, clearSelectedProduct } = useProductActions();
  const { postFipCalculation, clearSelectedSalesFipCalculation, clearSalesFipQuotation, postSalesFipQuotation, putSalesFipQuotation } = useSalesFipQuotationManagementActions();
  const productCode = localStorage.getItem("productCode");
  const cooperativesData = useSelector((state: RootState) => state.cooperatives.cooperatives);
  const fipPremium = useSelector((state: RootState) => state.salesFipQuotation.postFipCalculation?.data);
  const successFipPremium = useSelector((state: RootState) => state.salesFipQuotation.postFipCalculation?.success);
  const commissionTypesData = useSelector((state: RootState) => state.commissionType.getCommissionTypes);
  const commissionAgeTypeData = useSelector((state: RootState) => state.utilitiesCommissionsAgeTypes.commissionAgeTypes);
  const contestabilityData = useSelector((state: RootState) => state.contestability.contestabilities);
  const productBenefitsData = useSelector((state: RootState) => state.utilitiesProductBenefits.productBenefits);
  const createFipSuccess = useSelector((state: RootState) => state.salesFipQuotation.postSalesFipQuotation?.success);
  const updateFipSuccess = useSelector((state: RootState) => state.salesFipQuotation.putSalesFipQuotation?.success);
  const createFipData = useSelector((state: RootState) => state.salesFipQuotation.selectedSalesFipQuotation?.data);
  const productData = useSelector((state: RootState) => state?.products?.product);
  const commissionAgeTypesData = useSelector((state: RootState) => state.utilitiesCommissionsAgeTypes?.commissionAgeTypes);
  // Modal controllers
  const uploadMasterListModalController = useModalController();
  const demographicModalController = useModalController();
  const [demographicData, setDemographicData] = useState<any>(null);
  const [fileName, setFileName] = useState<string>("");
  const [hasDemographics, setHasDemographics] = useState<boolean>(false);
  const navigate = useNavigate();
  const [optionNumber, setOptionNumber] = useState(() => {
    return quotationData ? quotationData?.quotation?.fipPrincipalMember?.slice(-1)[0]?.option + 1 || 1 : 1;
  });
  const [isCalculating, setIsCalculating] = useState(true);
  const [isSelectedCalculation, setIsSelectedCalculation] = useState<boolean>(false);
  const [isCoopMode, setIsCoopMode] = useState(true);
  const [isOptionNumber, setIsOptionNumber] = useState<{ [index: number]: boolean }>({}); // Track state
  const [checkDuplicateCoInsureBenefit, setCheckDuplicateCoInsureBenefit] = useState<boolean>(false);
  const [isIncremental, setIsIncremental] = useState<boolean>(false);
  const [claimExperience, setClaimExperience] = useState(() => {
    return quotationData
      ? {
          years: quotationData?.quotation?.quotationClaimsExperienceYear?.map((item: IClaimsExperienceYears) => ({
            id: item.id,
            year: item.year,
            numberOfDeaths: item.numberOfDeaths,
            totalClaimAmount: parseFloat(item?.totalClaimAmount?.toString() || "0"),
            type: item.type === eInsuredType.principal ? eInsuredType.principal : eInsuredType.coInsured,
          })),
          ages: quotationData?.quotation?.quotationClaimsExperienceAge?.map((item: IClaimsExperienceAges) => ({
            id: item.id,
            ageFrom: item.ageFrom || 0,
            ageTo: item.ageTo || 0,
            numberOfDeaths: item.numberOfDeaths,
            totalClaimAmount: parseFloat(item?.totalClaimAmount?.toString() || "0"),
            type: item.type === eInsuredType.principal ? eInsuredType.principal : eInsuredType.coInsured,
          })),
        }
      : {
          years: [
            {
              id: 1,
              year: "2025",
              numberOfDeaths: 0,
              totalClaimAmount: 0,
              type: eInsuredType.principal,
            },

            {
              id: 2,
              year: "2025",
              numberOfDeaths: 0,
              totalClaimAmount: 0,
              type: eInsuredType.coInsured,
            },
          ] as IClaimsExperienceYears[],
          ages: [
            {
              id: 1,
              ageFrom: 0,
              ageTo: 0,
              numberOfDeaths: 0,
              totalClaimAmount: 0,
              type: eInsuredType.principal,
            },

            {
              id: 2,
              ageFrom: 0,
              ageTo: 0,
              numberOfDeaths: 0,
              totalClaimAmount: 0,
              type: eInsuredType.coInsured,
            },
          ] as IClaimsExperienceAges[],
        };
  });
  const [selectedCommissionDistribution, setSelectedCommissionDistribution] = useState<ICommissionDistributions[]>(() => {
    return quotationData && Array.isArray(quotationData?.quotation?.quotationCommissionDistribution)
      ? quotationData?.quotation?.quotationCommissionDistribution?.map((item: ICommissionDistributions) => ({
          isCoopMode: item.isCoopMode || false,
          commissionType: item.commissionType || "",
          commissionTypeId: item.commissionTypeId || null,
          ageFrom: item.ageFrom || null,
          ageTo: item.ageTo || null,
          rate: item.rate || null,
          commissionAgeType: item.commissionAgeType || null,
          option: item.option || 1,
          isSelected: item.isSelected || false,
        }))
      : [{}];
  });
  const [selectedFipAges, setSelectedFipAges] = useState<IFipAges[]>(() => {
    return quotationData && Array.isArray(quotationData?.quotation?.fipAges)
      ? quotationData?.quotation?.fipAges?.map((item: IFipAges) => ({
          ageType: item.ageType || "",
          minimum: item.minimum || 0,
          maximum: item.maximum || 0,
          exitAge: item.exitAge || null,
          memberType: item.memberType || "",
          option: item.option || 1,
        }))
      : [{}];
  });
  const [selectedRating, setSelectedRating] = useState<IRating[]>(() => {
    return quotationData && Array.isArray(quotationData?.quotation.rating)
      ? quotationData.quotation.rating.map((item: IRating) => ({
          id: item.id || 0,
          benefitId: item.benefitId || 0,
          rate: item.rate || 0,
          option: item.option || 0,
        }))
      : [{}];
  });

  const [projectionSelection, setProjectionSelection] = useState<IProjection[]>(() => {
    return quotationData && Array.isArray(quotationData?.quotation?.projection)
      ? quotationData.quotation.projection.map((item: IProjection) => ({
          id: item.id || 0,
          totalPremiumNetRate: item.totalPremiumNetRate || "",
          totalPremiumGrossRate: item.totalPremiumGrossRate || 0,
          numberOfClaims: item.numberOfClaims || 0,
          amountOfClaims: item.amountOfClaims || 0,
          claimsRatio: item.claimsRatio || 0,
          option: item.option || 1,
          isSelected: false,
        }))
      : [{}];
  });
  const [premiumOption, setPremiumOption] = useState<IOptions[]>(
    quotationData?.quotation
      ? () => {
          const uniqueOptions = [
            ...new Set([
              ...quotationData.quotation.fipCoInsuredDependent.map((dependent: any) => dependent.option),
              ...quotationData.quotation.fipCoInsuredDependentBenefit.map((benefit: any) => benefit.option),
              ...quotationData.quotation.fipPrincipalMember.map((member: any) => member.option),
              ...quotationData.quotation.fipPrincipalMemberBenefit.map((benefit: any) => benefit.option),
            ]),
          ];

          const groupedOptions = uniqueOptions.map((option) => ({
            fipCoInsuredDependents: removeProperties(
              quotationData.quotation.fipCoInsuredDependent.filter((dependent: any) => dependent.option === option),
              ["quotationId"]
            ),
            fipCoInsuredDependentsBenefits: removeProperties(
              quotationData.quotation.fipCoInsuredDependentBenefit.filter((benefit: any) => benefit.option === option),
              ["quotationId"]
            ),
            fipPrincipalMemberRequestData: removeProperties(
              quotationData.quotation.fipPrincipalMember.filter((member: any) => member.option === option),
              ["quotationId"]
            ),
            fipPrincipalMemberBenefitRequestData: removeProperties(
              quotationData.quotation.fipPrincipalMemberBenefit.filter((benefit: any) => benefit.option === option),
              ["quotationId"]
            ),
            aerOptions: [],
            isSelected: false,
            showOptions: true,
          }));
          return groupedOptions;
        }
      : [
          {
            fipCoInsuredDependents: [] as IFipCoInsuredDependents[],
            fipCoInsuredDependentsBenefits: [],
            fipPrincipalMemberRequestData: [] as IFipPrincipalMemberRequestData[],
            fipPrincipalMemberBenefitRequestData: [],
            aerOptions: [] as string[],
            isSelected: false,
            showOptions: false,
          },
        ]
  );

  useEffect(() => {
    if (isIncremental) {
      setOptionNumber((prev: number) => prev + 1);
    }
    // If isIncremental is false, do nothing (retain current value)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isIncremental]);
  const selectedOptionPremiumData = (premiumOption: IOptions[]) => {
    return premiumOption
      .filter((item: any) => item.isSelected)
      .reduce(
        (acc, curr) => {
          const formattedCoInsuredMembers = curr.fipCoInsuredDependents.map((item) => ({
            ...item,
            maximumTotalNumberOfDependents: typeof item.maximumTotalNumberOfDependents === "string" ? item.maximumTotalNumberOfDependents.replace(/[,.]/g, "") : item.maximumTotalNumberOfDependents,
            totalNumberOfMembers: typeof item.totalNumberOfMembers === "string" ? item.totalNumberOfMembers.replace(/[,.]/g, "") : item.totalNumberOfMembers,
            premiumBudget: typeof item.premiumBudget === "string" ? item.premiumBudget.replace(/[,.]/g, "") : item.premiumBudget,
          }));
          acc.fipCoInsuredDependents.push(...formattedCoInsuredMembers);
          acc.fipCoInsuredDependentsBenefits.push(...curr.fipCoInsuredDependentsBenefits);

          // Format totalNumberOfMembers and premiumBudget for each principal member
          const formattedPrincipalMembers = curr.fipPrincipalMemberRequestData.map((item) => ({
            ...item,
            totalNumberOfMembers: typeof item.totalNumberOfMembers === "string" ? item.totalNumberOfMembers.replace(/[,.]/g, "") : item.totalNumberOfMembers,
            premiumBudget: typeof item.premiumBudget === "string" ? item.premiumBudget.replace(/[,.]/g, "") : item.premiumBudget,
          }));
          acc.fipPrincipalMemberRequestData.push(...formattedPrincipalMembers);

          acc.fipPrincipalMemberBenefitRequestData.push(...curr.fipPrincipalMemberBenefitRequestData);

          acc.aerOptions = JSON.stringify([
            ...(typeof acc.aerOptions === "string" ? JSON.parse(acc.aerOptions.replace(/\\/g, "")) : acc.aerOptions),
            ...curr.fipPrincipalMemberRequestData.map((item) => item.option),
          ]);
          return acc;
        },
        {
          fipCoInsuredDependents: [],
          fipCoInsuredDependentsBenefits: [],
          fipPrincipalMemberRequestData: [],
          fipPrincipalMemberBenefitRequestData: [],
          aerOptions: [],
        }
      );
  };
  const selectedAges = selectedFipAges.filter((item) => item.isSelected === true);
  const selectedProjection = projectionSelection.filter((item) => item.isSelected === true);
  const selectedRate = selectedRating.filter((item) => item.isSelected === true);
  const selectCommissionDistribution = selectedCommissionDistribution.filter((item) => item.isSelected === true);
  const selectedOptionPremium = selectedOptionPremiumData(premiumOption); // This will give you the selected option data in one array

  const handlePremiumCalculation = () => {
    formik.setFieldValue("options", selectedOptionPremium, false); // mao dayun ni mag butang value formik option
    formik.setFieldValue("fipAges", selectedAges, false);
    formik.setFieldValue("projection", selectedProjection, false);
    formik.setFieldValue("rating", selectedRate, false);
    formik.setFieldValue("commissionDistributions", selectCommissionDistribution, false);
  };

  const [coopQuotationDetails, setCoopQuotationDetails] = useState<IQuotations>({
    coopId: 0,
    previousProvider: quotationData ? quotationData?.quotation?.previousProvider : "",
    // branch: "",
    contestability: "",
    totalNumberOfMembers: 0,
  });

  const baseValueCommissionDistribution = (isCoopMode: boolean) => {
    if (isCoopMode) {
      return [
        {
          isCoopMode: true,
          commissionTypeId: isCoopMode ? 1 : 0,
          rate: null,
          option: 0,
        },
      ];
    } else {
      return [
        {
          isCoopMode: false,
          commissionTypeId: null,
          rate: null,
          option: 0,
        },
        {
          isCoopMode: false,
          commissionTypeId: null,
          rate: null,
          commissionAgeType: null,
          option: 0,
        },
        {
          isCoopMode: false,
          commissionTypeId: 0,
          rate: null,
          option: 0,
        },
      ];
    }
  };

  const [isCoopModeCommissionDistribution, setIsCoopModeCommissionDistribution] = useState<ICommissionDistributions[]>(() => {
    if (quotationData?.quotation?.quotationCommissionDistribution?.length) {
      const lastOption = Math.max(...quotationData.quotation.quotationCommissionDistribution.map((item: ICommissionDistributions) => Number(item.option)));

      return quotationData.quotation.quotationCommissionDistribution
        .filter((item: ICommissionDistributions) => Number(item.option) === Number(lastOption))
        .map((item: ICommissionDistributions) => ({
          isCoopMode: item.commissionTypeId === 1 ? true : false,
          commissionTypeId: item.commissionTypeId || 0,
          rate: parseFloat(item?.rate?.toString() ?? "0") || null,
          option: item.option,
          isSelected: item.isSelected || false,
        }));
    } else {
      return baseValueCommissionDistribution(isCoopMode); // Initialize with base values based on isCoopMode
    }
  }); // hold the data if coopMode false changes
  const [isCoopModeCommissionDistributionTrue, setIsCoopModeCommissionDistributionTrue] = useState<ICommissionDistributions[]>(() => {
    return baseValueCommissionDistribution(true); // Initialize with base values based on isCoopMode
  }); // hold the data if coopMode true changes

  const [commissionDistribution, setCommissionDistribution] = useState<ICommissionDistributions[]>(() => {
    if (quotationData?.quotation?.quotationCommissionDistribution?.length) {
      const lastOption = Math.max(...quotationData.quotation.quotationCommissionDistribution.map((item: ICommissionDistributions) => Number(item.option)));

      return quotationData.quotation.quotationCommissionDistribution
        .filter((item: ICommissionDistributions) => Number(item.option) === Number(lastOption))
        .map((item: ICommissionDistributions) => ({
          isCoopMode: item.commissionTypeId === 1 ? true : false,
          commissionTypeId: item.commissionTypeId === 1 ? item.commissionTypeId : null,
          rate: parseFloat((item.rate ?? "0").toString()) || null,
          option: item.option,
          isSelected: item.isSelected || false,
        }));
    } else {
      if (!isCoopMode) {
        return [
          {
            isCoopMode: false,
            commissionTypeId: null,
            rate: null,
            option: 0,
          },
          {
            isCoopMode: false,
            commissionTypeId: null,
            rate: null,
            option: 0,
          },
          {
            isCoopMode: false,
            commissionTypeId: null,
            rate: null,
            option: 0,
          },
        ];
      } else {
        return [
          {
            isCoopMode: true,
            commissionTypeId: null,
            rate: null,
            option: 0,
          },
          {
            isCoopMode: true,
            commissionTypeId: null,
            rate: null,
            option: 0,
          },
        ];
      }
    }
  });

  useEffect(() => {
    if (isCoopMode) {
      setIsCoopModeCommissionDistribution(commissionDistribution);
      setCommissionDistribution(isCoopModeCommissionDistributionTrue);
    }
  }, [isCoopMode]);
  useEffect(() => {
    if (!isCoopMode) {
      setCommissionDistribution(isCoopModeCommissionDistribution);
      setIsCoopModeCommissionDistributionTrue(isCoopModeCommissionDistributionTrue);
    }
  }, [isCoopMode]);

  const [coInsuredDependent, setCoInsuredDependent] = useState<IFipCoInsuredDependents>(() => {
    return quotationData
      ? quotationData?.quotation?.fipCoInsuredDependent
          ?.map((item: IFipCoInsuredDependents) => ({
            premiumBasis: item.premiumBasis || "",
            premiumBudget: parseFloat(item.premiumBudget?.toString() || "0"),
            maximumTotalNumberOfDependents: item.maximumTotalNumberOfDependents || 0,
            totalNumberOfMembers: item.totalNumberOfMembers || 0,
            grossPremium: parseFloat(item.grossPremium?.toString() || "0"),
            netPremium: parseFloat(item.netPremium?.toString() || "0"),
            option: item.option || 1,
            contestabilityId: item.contestabilityId || null,
          }))
          .slice(-1)[0]
      : {
          premiumBasis: "",
          premiumBudget: 0,
          maximumTotalNumberOfDependents: 0,
          totalNumberOfMembers: 0,
          grossPremium: 0,
          netPremium: 0,
          option: 1,
          contestabilityId: null,
        };
  });

  const [rating, setRating] = useState<IRating[]>(() => {
    return quotationData && Array.isArray(quotationData?.rating)
      ? quotationData.rating.slice(-1)[0]?.map((item: IRating) => ({
          id: item?.id || 0,
          benefitId: item.benefitId || 0,
          rate: item.rate || 0,
          option: item.option,
        }))
      : [
          {
            id: 0,
            benefitId: 0,
            rate: 0,
            option: 0,
          },
        ];
  });
  const [projection, setProjection] = useState<IProjection[]>(() => {
    return quotationData && Array.isArray(quotationData?.projection)
      ? quotationData.projection?.slice(-1)[0]?.map((item: IProjection) => [
          {
            id: item.id || 0,
            totalPremiumNetRate: item.totalPremiumNetRate || "",
            totalPremiumGrossRate: item.totalPremiumGrossRate || 0,
            numberOfClaims: item.numberOfClaims || 0,
            amountOfClaims: item.amountOfClaims || 0,
            claimsRatio: item.claimsRatio || 0,
            option: item.option,
          },
        ])
      : [
          {
            id: 0,
            totalPremiumNetRate: "",
            totalPremiumGrossRate: 0,
            numberOfClaims: 0,
            amountOfClaims: 0,
            claimsRatio: 0,
            option: 1,
          },
        ];
  });

  const [fipAges, setFipAges] = useState<IFipAges[]>(() => {
    if (quotationData && Array.isArray(quotationData?.quotation?.fipAges) && quotationData.quotation.fipAges.length > 0) {
      // Find the highest option number
      const lastOption = Math.max(...quotationData.quotation.fipAges.map((item: IFipAges) => Number(item.option)));
      // Filter and map only items with the last option number
      return quotationData.quotation.fipAges
        .filter((item: IFipAges) => Number(item.option) === lastOption)
        .map((item: IFipAges) => ({
          ageType: item.ageType || "",
          minimum: item.minimum || 0,
          maximum: item.maximum || 0,
          exitAge: item.exitAge || null,
          memberType: item.memberType || "",
          option: item.option || 1,
        }));
    }
    return [
      {
        ageType: "",
        minimum: null,
        maximum: null,
        exitAge: null,
        memberType: eInsuredType.principalMember,
        option: 1,
      },
      {
        ageType: capitalizeFirstLetterWords(eAgeType.AGE_BRACKET, " "),
        minimum: null,
        maximum: null,
        exitAge: null,
        memberType: eInsuredType.coInsuredPrimary,
        option: 1,
      },
      {
        ageType: capitalizeFirstLetterWords(eAgeType.AGE_BRACKET, " "),
        minimum: null,
        maximum: null,
        exitAge: null,
        memberType: eInsuredType.coInsuredSecondary,
        option: 1,
      },
    ];
  });
  const [coInsuredDependentBenefits, setCoInsuredDependentBenefits] = useState<IFipCoInsuredDependentsBenefits[]>(() => {
    return premiumOption?.slice(-1)[0].fipCoInsuredDependentsBenefits.length > 0
      ? premiumOption?.slice(-1)[0].fipCoInsuredDependentsBenefits?.map((item: IFipCoInsuredDependentsBenefits) => ({
          id: item?.id || 0,
          premiumBasis: item?.premiumBasis || "",
          relationship: item?.relationship || "",
          maximumNumberOfInsured: item?.maximumNumberOfInsured || null,
          benefitId: Number(item?.benefitId) || "",
          coverage: Number(item?.coverage) || null,
          dividedEqually: item?.dividedEqually || "",
          type: item?.type,
          option: item?.option || 1,
        }))
      : [
          {
            id: 1,
            premiumBasis: "",
            relationship: "",
            maximumNumberOfInsured: null,
            benefitId: "",
            coverage: null,
            dividedEqually: "",
            type: eInsuredType.coInsuredPrimary,
            option: 1,
          },
          {
            id: 2,
            premiumBasis: "",
            relationship: "",
            maximumNumberOfInsured: null,
            benefitId: "",
            coverage: null,
            dividedEqually: "",
            type: eInsuredType.coInsuredSecondary,
            option: 1,
          },
        ];
  });
  const [isRelationshipPrimary, setIsRelationshipPrimary] = useState<string>(() => {
    return premiumOption?.slice(-1)[0].fipCoInsuredDependentsBenefits.length > 0
      ? premiumOption?.slice(-1)[0].fipCoInsuredDependentsBenefits?.find((item) => item.type === eInsuredType.coInsuredPrimary)?.relationship || ""
      : "";
  });
  const [isRelationship, setIsRelationship] = useState<string>(() => {
    return premiumOption?.slice(-1)[0].fipCoInsuredDependentsBenefits.length > 0
      ? premiumOption?.slice(-1)[0].fipCoInsuredDependentsBenefits?.find((item) => item.type === eInsuredType.coInsuredSecondary)?.relationship || ""
      : "";
  });
  const selectedRelationship = (value: string) => {
    setIsRelationship(value);
  };
  const [agePrincipalMember, setAgePrincipalMember] = useState<IFipPrincipalMemberBenefitRequestData[]>(() => {
    return premiumOption?.slice(-1)[0].fipPrincipalMemberBenefitRequestData.length > 0
      ? premiumOption?.slice(-1)[0].fipPrincipalMemberBenefitRequestData?.map((item: IFipPrincipalMemberBenefitRequestData) => ({
          premiumBasis: item?.premiumBasis || "",
          benefitId: item?.benefitId || "",
          coverage: Number(item?.coverage) || "",
          option: item?.option || 1,
        }))
      : [
          {
            premiumBasis: "",
            benefitId: "",
            coverage: "",
            option: 1,
          },
        ];
  });

  const [principalMemberData, setPrincipalMemberData] = useState<IFipPrincipalMemberRequestData>(() => {
    return quotationData
      ? quotationData?.quotation?.fipPrincipalMember
          .map((item: IFipPrincipalMemberRequestData) => ({
            premiumBasis: item.premiumBasis || "",
            premiumBudget: parseFloat(item.premiumBudget?.toString() || "0"),
            averageAge: item.averageAge || 0,
            totalNumberOfMembers: item.totalNumberOfMembers || 0,
            contestabilityId: item.contestabilityId || 0,
            grossPremium: parseFloat(item.grossPremium?.toString() || "0"),
            netPremium: parseFloat(item.netPremium?.toString() || "0"),
            option: item.option || 0,
          }))
          .slice(-1)[0]
      : {
          premiumBasis: "",
          premiumBudget: 0,
          averageAge: demographicData?.averageAge || 0,
          totalNumberOfMembers: demographicData?.totalNumberOfMembers || 0,
          contestabilityId: 0,
          grossPremium: 0,
          netPremium: 0,
          option: 0,
        };
  });
  const [filteredPrincipalMemberCondition, _setFilteredPrincipalMemberCondition] = useState<IFilteredDataCondition[]>([]);
  const [filteredCoInsuredDependentCondition, setFilteredCoInsuredDependentCondition] = useState<IFilteredDataCondition[]>([]);
  const [principalMemberCondition, setPrincipalMemberCondition] = useState<IFilteredDataCondition[]>([]);
  const [coInsuredDependentCondition, setCoInsuredDependentCondition] = useState<Partial<IFilteredDataCondition>[]>([]);
  const [filterData, setFilterData] = useState<IFilteredDataCondition[]>([]);
  // Actual Data! - Contestability
  const contestabilitySelectItems = useMemo<ISelectOptions[]>(() => {
    if (!contestabilityData) return [];
    return contestabilityData.map((item: IContestability) => ({
      text: item.label,
      value: item.id?.toString() ?? "",
    }));
  }, [contestabilityData]);
  const getTextFromValue = (value: string | number, options: { value: string; text: string }[]): string => {
    const option = options.find((opt) => opt.value === value);
    return option ? option.text : "";
  };
  const convertToArrayAndCount = (data: string): number => {
    if (data.length === 0) return 0; // Handle empty string case
    const array = JSON.parse(data); // Convert string to array
    return array.length; // Count the number of elements
  };
  // // Example usage
  const data = selectedOptionPremium.aerOptions; // This should be a JSON string representation of an array
  const countAerOptions = convertToArrayAndCount(data as any);
  const isOneYear = useMemo(() => {
    const textValue = getTextFromValue(Number(principalMemberCondition?.[0]?.contestability), contestabilitySelectItems);
    return textValue === "1 Year";
  }, [principalMemberCondition, contestabilitySelectItems]);

  const sampleConditions = useMemo(() => {
    const conditions = [
      ...(countAerOptions > 1 ? [`Coop must choose only (1) option for the whole group.`] : []),
      `100% participation of all qualified members of the cooperative or a minimum of(1,900 enrollees of principal members in ONE TIME ENROLLMENT. Failure to meet the minimum participation requirement may result in a premium refund and re-evaluation if they wish to re-enrol.`,
      [
        `In case the principal members is single, co-insured dependents are as follows:`,
        `Spouse can be replaced by parent`,
        `Child can be replaced by sibling provided that still dependent on the support of principal holder.`,
      ],
      // for future use - basin gusto nila e dynamic
      // [
      //   `Age Requirements:`,
      //   ...(filteredPrincipalMemberCondition?.map((item) => `Principal: ${item?.minimumAge} - ${item?.maximumAge} years old (exit age ${item?.exitAge}).`) || []),
      //   ...(filteredCoInsuredDependentCondition?.map(
      //     (item) => `${item.relationship}: ${item?.minimumAge || item?.maximumAge} - ${item?.maximumAge || item?.maximumAge} years old (exit age ${item?.exitAge || Number(item?.maximumAge) + 1}).`
      //   ) || []),
      // ],
      [
        `Age Requirement:`,
        `Principal:  - 18 - 69 years old (exit age 70 years old)`,
        `Spouse/Parent:  - 18 - 64 yrs old (exit age 65 years old)`,
        `Children: - 4  - 17 years old.`,
        `Siblings: - 4 - 17 years old.`,
      ],
      `The coverage of the co-insured dependents will cease its effect if that person becomes a principal holder.`,
      `Any co insured dependen(s) that was declared already cannot be declared again by another principal holder.`,
      isOneYear
        ? `One (1) year contestability period for all members for pre-existing illnesses.`
        : `One (1) year contestability period for new & incoming members. However, waive contestability period for all members included in the Masterlist submitted and enrolled at the same time on the FIRST REMITTANCE.`,
      `One policy per year. No double coverage.`,
      `In the event of delayed premium refund due to disqualifications or adjustment, the provisions outlined in this contract shall prevail over any conflicting terms regarding claims arising from such delay. That CLIMBS shall process claims based on terms and conditions of this contract, even if premium refund is delayed.`,
      `Subject to actuarial evaluation after (1) one year coverage, Rate may change depending on the previous years insurance experience.`,
      `All other standard exclusiond shall apply.`,
      `This Actuarial Evaluation Report is for CLIMBS internal copy only and should not be forwarded to coop.`,
    ];
    return conditions;
  }, [filteredPrincipalMemberCondition, filteredCoInsuredDependentCondition, countAerOptions, principalMemberCondition]);
  const [initialValues, setInitialValues] = useState<ISalesFipQuotation>({
    status: isEdit ? Statuses.FOR_APPROVAL : Statuses.DRAFT,
    prodId: Number(productData?.id) || 0,
    quotations: {
      coopId: 0,
      previousProvider: quotationData ? quotationData?.quotation?.previousProvider : "",
      // branch: "",
      totalNumberOfMembers: 0,
      contestability: "",
    } as IQuotations,
    quotationCondition: {
      condition: `<ol>${sampleConditions.map((condition) => `<li>${condition}</li>`).join("")}</ol>`,
    } as IQuotationCondition,
    claimsExperienceYears: claimExperience.years,
    claimsExperienceAges: claimExperience.ages,
    commissionDistributions: [] as ICommissionDistributions[],
    options: {
      fipCoInsuredDependents: [] as IFipCoInsuredDependents[],
      fipCoInsuredDependentsBenefits: [] as IFipCoInsuredDependentsBenefits[],
      fipPrincipalMemberRequestData: [] as IFipPrincipalMemberRequestData[],
      fipPrincipalMemberBenefitRequestData: [] as IFipPrincipalMemberBenefitRequestData[],
      aerOptions: [],
    } as IOptions,
    fipAges: [] as IFipAges[],
    rating: [] as IRating[],
    projection: [] as IProjection[],
  });

  const handleSwitchCoopMode = (checked: boolean) => {
    setIsCoopMode(checked);
  };
  const handleDataChange = (data: Partial<IFipPrincipalMemberRequestData>) => {
    setPrincipalMemberData((prev) => ({ ...prev, ...data }));
  };
  const handleDataChangeContestability = (data: Partial<IQuotations>) => {
    setCoopQuotationDetails((prev) => ({ ...prev, ...data }));
  };
  const onUpdatePrimaryRelationship = (data: Partial<IFipCoInsuredDependentsBenefits>) => {
    setCoInsuredDependentBenefits((prev) => prev.map((item) => (item.type === eInsuredType.coInsuredPrimary ? { ...item, relationship: data.relationship ?? item.relationship } : item)));
    setIsRelationshipPrimary(data.relationship ?? "");
  };
  useEffect(() => {
    if (successFipPremium && fipPremium) {
      const createUpdatedData = (premium: any) => ({
        grossPremium: premium ? Number(premium) : 0,
        netPremium: premium ? Number(premium) : 0,
      });

      const updatedDataPrincipal = createUpdatedData(fipPremium?.principalPremium);
      if (updatedDataPrincipal.grossPremium > 0 && updatedDataPrincipal.netPremium > 0) {
        setPrincipalMemberData((prev: IFipPrincipalMemberRequestData) => ({
          ...prev,
          grossPremium: updatedDataPrincipal.grossPremium || 0,
          netPremium: updatedDataPrincipal.netPremium || 0,
        }));
      }

      if (fipPremium.principalGrossPremium && fipPremium.principalGrossPremium > 0 && fipPremium.principalPremium && fipPremium.principalPremium > 0) {
        setCoInsuredDependent((prev: IFipCoInsuredDependents) => ({
          ...prev,
          grossPremium: fipPremium.principalGrossPremium || 0,
          netPremium: fipPremium.principalPremium || 0,
        }));
      }

      setProjection([
        {
          totalPremiumNetRate: fipPremium?.principalPremium || 0,
          totalPremiumGrossRate: fipPremium?.principalGrossPremium || 0,
          numberOfClaims: fipPremium?.calculation?.expectedNumberOfDeaths || 0,
          amountOfClaims: fipPremium?.calculation?.expectedClaimsAmount || 0,
          claimsRatio: fipPremium?.calculation?.calculatedLossRatio || 0,
          option: optionNumber,
        },
      ]);
      setRating(() =>
        fipPremium?.calculation?.calculationResults?.map((item: IRating) => ({
          benefitId: item.benefitId || 0,
          rate: item.rate || 0,
          option: item.option || optionNumber,
        }))
      );
    }
  }, [successFipPremium, fipPremium]);

  useEffect(() => {
    if (isSelectedCalculation) {
      setPremiumOption((prevOptions) => {
        const newOption = {
          fipCoInsuredDependents: [
            {
              ...coInsuredDependent,
              option: optionNumber,
              grossPremium: fipPremium?.coInsuredDependentGrossPremium,
              netPremium: fipPremium?.coInsuredDependentPremium,
            },
          ],
          fipCoInsuredDependentsBenefits: coInsuredDependentBenefits.map((item) => ({
            ...item,
            option: optionNumber,
          })),
          fipPrincipalMemberRequestData: [
            {
              ...principalMemberData,
              option: optionNumber,
              grossPremium: fipPremium?.principalGrossPremium || 0,
              netPremium: fipPremium?.principalPremium || 0,
            },
          ],
          fipPrincipalMemberBenefitRequestData: agePrincipalMember.map((item) => ({ ...item, option: optionNumber })),
          aerOptions: [],
          isSelected: false,
          showOptions: true,
        };

        // Deep clean function to remove 'option' from specified arrays
        const deepClean = (obj: any) => {
          const omitKeys = ["aerOptions", "isSelected", "showOptions"];
          const cleaned = { ...obj };
          omitKeys.forEach((key) => delete cleaned[key]);
          // Remove 'option' from each array element
          ["fipCoInsuredDependents", "fipCoInsuredDependentsBenefits", "fipPrincipalMemberRequestData", "fipPrincipalMemberBenefitRequestData"].forEach((arrKey) => {
            if (Array.isArray(cleaned[arrKey])) {
              cleaned[arrKey] = cleaned[arrKey].map((item: any) => {
                const { option, ...rest } = item;
                return rest;
              });
            }
          });
          return cleaned;
        };

        const isOptionDuplicate = (a: IOptions, b: IOptions) => JSON.stringify(deepClean(a)) === JSON.stringify(deepClean(b));

        const exists = prevOptions.some((opt) => isOptionDuplicate(opt, newOption));
        if (exists) {
          setIsIncremental(false);
          toast.error("This option already exists and cannot be added.");
          return prevOptions;
        }

        const cleanedOptions = prevOptions.length > 0 && prevOptions.some((item) => item.fipCoInsuredDependents.length === 0) ? prevOptions.slice(1) : prevOptions;
        setIsIncremental(true);
        return [...cleanedOptions, newOption];
      });
      setSelectedFipAges((prev) => {
        // Remove any initial/empty values (all fields empty or zero)
        const cleanedAges = prev.filter((item) => item && Object.keys(item).length > 0 && !(item.option === 0 || item.option === undefined));
        return [
          ...cleanedAges,
          ...fipAges?.map((item: IFipAges) => ({
            ageType: item.ageType || "",
            minimum: item.minimum || null,
            maximum: item.maximum || null,
            exitAge: item.exitAge !== null && item.exitAge !== undefined ? item.exitAge : null,
            memberType: item.memberType || "",
            option: optionNumber,
            isSelected: false,
          })),
        ];
      });
      setProjectionSelection((prevProjections) => {
        // Remove any initial empty object ({}), or any projection with all values falsy
        const cleanedProjections = prevProjections.filter((item) => item && Object.keys(item).length > 0 && (item.numberOfClaims || item.amountOfClaims || item.claimsRatio));

        // Always add new data (since useEffect already checks isSelectedCalculation)
        return [
          ...cleanedProjections,
          {
            id: (cleanedProjections[cleanedProjections.length - 1]?.id ?? 0) + 1,
            totalPremiumNetRate: fipPremium?.principalPremium || 0,
            totalPremiumGrossRate: fipPremium?.principalGrossPremium || 0,
            numberOfClaims: fipPremium?.calculation?.expectedNumberOfDeaths || 0,
            amountOfClaims: fipPremium?.calculation?.expectedClaimsAmount || 0,
            claimsRatio: fipPremium?.calculation?.calculatedLossRatio || 0,
            option: optionNumber,
            isSelected: false,
          },
        ];
      });
      setSelectedRating((prevRatings) => {
        // Remove any initial empty object ({}), or any rating with all values falsy
        const cleanedRatings = prevRatings.filter((item) => item && Object.keys(item).length > 0 && (item.rate || item.benefitId || item.option));

        // Always add new data (since useEffect already checks isSelectedCalculation)
        return [
          ...cleanedRatings,
          ...(fipPremium?.calculation?.calculationResults || []).map((item: IRating) => ({
            id: item.id || 0,
            benefitId: item.benefitId || 0,
            rate: item.rate || 0,
            option: optionNumber,
            isSelected: false,
          })),
        ];
      });
      setSelectedCommissionDistribution((prevCom) => {
        const cleanedAges = prevCom.filter((item) => item && Object.keys(item).length > 0 && !(item.option === 0 || item.option === undefined));
        return [
          ...cleanedAges,
          ...commissionDistribution?.map((item: ICommissionDistributions) => ({
            isCoopMode: item.isCoopMode,
            commissionTypeId: item.commissionTypeId || 0,
            rate: item?.rate || null,
            option: optionNumber,
            isSelected: false,
          })),
        ];
      });
    }

    clearSelectedSalesFipCalculation();
  }, [isSelectedCalculation]);
  useEffect(() => {
    // Only use valid option numbers
    const uniqueOptions = Array.from(new Set(selectedFipAges.filter((item) => item && typeof item.option !== "undefined" && !isNaN(Number(item.option))).map((item) => Number(item.option))));

    // Only add new option numbers that do not already exist in isOptionNumber
    setIsOptionNumber((prev) => {
      const updated = { ...prev };
      let changed = false;
      uniqueOptions.forEach((option) => {
        if (!(option in updated)) {
          updated[option] = false;
          changed = true;
        }
      });
      return changed ? updated : prev;
    });
  }, [selectedFipAges]);

  const handleDataChangeCoInsuredDependent = (data: Partial<IFipCoInsuredDependents>) => {
    setCoInsuredDependent((prev) => ({ ...prev, ...data }));
  };
  const formik = useFormik({
    initialValues: initialValues, // Call the function to get the initial values
    onSubmit(_) {},
  });

  useEffect(() => {
    const coInsuredDependentBenefit = formik.values.options.fipCoInsuredDependentsBenefits
      .map((data) => {
        if (data.type === typeCoInsured.primary) {
          return {
            relationship: data.relationship,
          };
        } else if (data.type === typeCoInsured.secondary) {
          return {
            relationship: data.relationship,
          };
        }
        return null; // Exclude other types
      })
      .filter(Boolean); // Remove null values

    // Update state
    setFilteredCoInsuredDependentCondition(coInsuredDependentBenefit as IFilteredDataCondition[]);
  }, [formik.values.options]);

  const handleFilterByIndex = (index: number) => {
    const getAgeDataByOptionAndMemberType = (index: number, member: eInsuredType.principalMember | eInsuredType.coInsuredPrimary | eInsuredType.coInsuredSecondary) => {
      const ageData = selectedFipAges.find((age) => Number(age.option) === Number(index) && String(age.memberType).toUpperCase() === member);
      return {
        minimumAge: ageData ? Number(ageData.minimum) : undefined,
        maximumAge: ageData ? Number(ageData.maximum) : undefined,
        exitAge: ageData ? Number(ageData.exitAge) : undefined,
      };
    };

    const netPremiumPrincipal = (option: number): string | undefined => {
      for (const item of premiumOption) {
        const found = item.fipPrincipalMemberRequestData.find((d) => d.option === option);
        if (found) {
          return String(found.netPremium); // return as string
        }
      }
      return undefined; // not found
    };
    const principalMember = premiumOption
      .flatMap((item) =>
        (item.fipPrincipalMemberBenefitRequestData || [])
          .filter((benefit) => Number(benefit.option) === Number(index))
          .map((benefit) => {
            const member = (item.fipPrincipalMemberRequestData || []).find((m) => Number(m.option) === Number(index));
            return member
              ? {
                  coverage: Number(benefit.coverage),
                  netPremium: netPremiumPrincipal(index),
                  benefitId: Number(benefit.benefitId),
                  contestability: premiumOption.flatMap((item) => item.fipPrincipalMemberRequestData).find((m) => Number(m.option) === Number(index))?.contestabilityId,
                }
              : null;
          })
      )
      .filter(Boolean);

    const coInsuredDependent = premiumOption
      .flatMap((item) =>
        (item.fipCoInsuredDependentsBenefits || [])
          .filter((benefit) => Number(benefit.option) === Number(index))
          .map((benefit) => {
            const member = (item.fipCoInsuredDependents || []).find((m) => Number(m.option) === Number(index));
            if (!member) return null;

            return {
              benefit: benefit.benefitId,
              relationship: benefit.type === typeCoInsured.primary ? `${benefit.relationship} - ${typeCoInsured.primary}` : benefit.relationship,
              coverage: benefit.coverage,
              grossPremium: Number(member.grossPremium),
              netPremium: Number(member.netPremium),
            };
          })
      )
      .filter(Boolean);

    setPrincipalMemberCondition(principalMember as IFilteredDataCondition[]);
    setCoInsuredDependentCondition(coInsuredDependent as IFilteredDataCondition[]);

    const principalBenefits = premiumOption
      .find((opt) => Array.isArray(opt.fipPrincipalMemberRequestData) && opt.fipPrincipalMemberRequestData.some((m) => Number(m.option) === Number(index)))
      ?.fipPrincipalMemberBenefitRequestData.reduce((acc: IFilteredDataCondition[], benefit: IFipPrincipalMemberBenefitRequestData) => {
        // Find the member with the same option number
        const member = (
          premiumOption.find((opt) => Array.isArray(opt.fipPrincipalMemberRequestData) && opt.fipPrincipalMemberRequestData.some((m) => Number(m.option) === Number(index)))
            ?.fipPrincipalMemberRequestData || []
        ).find((m: any) => Number(m.option) === Number(index));

        const { minimumAge, maximumAge, exitAge } = getAgeDataByOptionAndMemberType(index, eInsuredType.principalMember);

        if (member) {
          acc.push({
            benefitId: Number(benefit.benefitId),
            coverage: Number(benefit.coverage),
            relationship: eInsuredType.principal.charAt(0).toUpperCase() + eInsuredType.principal.slice(1).toLowerCase(),
            option: Number(benefit.option),
            netPremium: Number(member?.netPremium),
            minimumAge,
            maximumAge,
            exitAge,
            contestability: Number(member.contestabilityId),
            premiumBudgets: Number(member.premiumBudget),
          });
        }
        return acc;
      }, []);

    const coInsuredBenefits = premiumOption
      .find((opt) => Array.isArray(opt.fipCoInsuredDependents) && opt.fipCoInsuredDependents.some((m) => Number(m.option) === Number(index)))
      ?.fipCoInsuredDependentsBenefits.reduce((acc: IFilteredDataCondition[], benefit: IFipCoInsuredDependentsBenefits) => {
        // Find the member with the same option number
        const member = (
          premiumOption.find((opt) => Array.isArray(opt.fipCoInsuredDependents) && opt.fipCoInsuredDependents.some((m) => Number(m.option) === Number(index)))?.fipCoInsuredDependents || []
        ).find((m: any) => Number(m.option) === Number(index));

        // const { minimumAge, maximumAge, exitAge } = getAgeDataByOptionAndMemberType(index, eInsuredType.coInsuredPrimary);
        const { minimumAge, maximumAge, exitAge } = getAgeDataByOptionAndMemberType(
          index,
          benefit.type === eInsuredType.coInsuredPrimary ? eInsuredType.coInsuredPrimary : eInsuredType.coInsuredSecondary
        );

        if (member) {
          acc.push({
            benefitId: Number(benefit.benefitId),
            coverage: Number(benefit.coverage),
            relationship: benefit.type === eInsuredType.coInsuredPrimary ? `Primary - ${benefit.relationship}` : `Secondary - ${benefit.relationship}`,
            option: Number(benefit.option),
            netPremium: Number(member.netPremium),
            minimumAge,
            maximumAge,
            exitAge,
            premiumBudgets: Number(member.premiumBudget),
          });
        }
        return acc;
      }, []);

    const dataFiltered = [...(principalBenefits ?? []), ...(coInsuredBenefits ?? [])];
    setFilterData(dataFiltered);
  };

  useEffect(() => {
    const setFormikFields = async (fields: Record<string, any>) => {
      for (const [key, value] of Object.entries(fields)) {
        await formik.setFieldValue(key, value, false); // Wait for each field to be set
      }
    };

    const commonFields = {
      claimsExperienceYears: claimExperience.years,
      claimsExperienceAges: claimExperience.ages,
      quotations: coopQuotationDetails,
      averageClaims: 0, // epa klaru sa UI/UX
      groupSize: principalMemberData.totalNumberOfMembers,
      premiumBasis: agePrincipalMember[0]?.premiumBasis,
      commissionDistributions: commissionDistribution,
      prodId: productData?.id || 0,
      fipAges: fipAges,
      projection: projection,
      rating: rating,
    };

    const conditionalFields = {
      "options.fipPrincipalMemberBenefitRequestData": agePrincipalMember,
      "options.fipCoInsuredDependents": coInsuredDependent,
      "options.fipCoInsuredDependentsBenefits": coInsuredDependentBenefits,
      "options.fipPrincipalMemberRequestData": { ...principalMemberData, totalNumberOfMembers: principalMemberData.totalNumberOfMembers.toString().replace(/,/g, "") },
    };

    const updateFieldsAndPost = async () => {
      // Always set common fields first
      await setFormikFields(commonFields);
      if (!isCalculating) {
        const timeout = setTimeout(() => {
          isEdit ? putSalesFipQuotation({ id: quotationData?.id, ...formik.values }) : postSalesFipQuotation(formik.values);
          setIsCalculating(false);
          clearSelectedProduct();
        }, 500);

        return () => clearTimeout(timeout);
      }

      // Set conditional fields only if isCalculating is true
      await setFormikFields(conditionalFields); //IMPORTANTE:this will update the option formik - {e update una tanan values sa katung mga naka useState bago ebutang sa formik}
    };

    updateFieldsAndPost();
  }, [agePrincipalMember, coInsuredDependentBenefits, principalMemberData, commissionDistribution, isCalculating]);

  const handleTextFieldChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setCoopQuotationDetails((prev) => ({ ...prev, [field]: value }));
  };

  const onAddAgePrincipalMember = () => {
    setAgePrincipalMember((prev) => [
      ...prev,
      {
        premiumBasis: "",
        benefitId: "",
        coverage: "",
        option: Number(prev[prev.length - 1]?.option ?? 0) + 1,
      },
    ]);
  };
  const onAddCoInsuredDependentsBenefits = (type: eInsuredType.coInsuredPrimary | eInsuredType.coInsuredSecondary) => {
    type === eInsuredType.coInsuredPrimary
      ? setCoInsuredDependentBenefits((prev) => [
          ...prev,
          {
            id: (prev[prev.length - 1]?.id ?? 0) + 1,
            premiumBasis: coInsuredDependentBenefits?.[0]?.premiumBasis,
            relationship: isRelationshipPrimary,
            maximumNumberOfInsured: 0,
            benefitId: "",
            coverage: "",
            dividedEqually: "",
            type: type.toUpperCase(),
            option: (prev[prev.length - 1]?.id ?? 0) + 1,
          },
        ])
      : setCoInsuredDependentBenefits((prev) => [
          ...prev,
          {
            id: (prev[prev.length - 1]?.id ?? 0) + 1,
            premiumBasis: coInsuredDependentBenefits?.[0]?.premiumBasis,
            relationship: "",
            maximumNumberOfInsured: null,
            benefitId: "",
            coverage: null,
            dividedEqually: "",
            type: type.toUpperCase(),
            option: (prev[prev.length - 1]?.id ?? 0) + 1,
          },
        ]);
  };

  const handleAddCommissionDistribution = () => {
    setCommissionDistribution((prev) => [
      ...prev,
      {
        isCoopMode: false,
        commissionTypeId: null,
        rate: null,
        option: 0,
      },
    ]);
  };

  const totalDistribution = (data: ICommissionDistributions[]): number => {
    return data.reduce((total, item) => total + parseFloat((item.rate ?? 0).toString()), 0);
  };

  const handleUpdatedCommissionDistribution = (updatedCommissionDistribution: ICommissionDistributions[]) => {
    setCommissionDistribution(updatedCommissionDistribution);
    //AYAW E DELETE
    // to update the initial values
    setInitialValues((prev) => ({
      ...prev,
      commissionDistributions: updatedCommissionDistribution,
    }));
  };

  const handleUpdateAddAgePrincipalMember = (updateAddAgePrincipalMember: IFipPrincipalMemberBenefitRequestData[]) => {
    const fillPremiumBasis = (data: IFipPrincipalMemberBenefitRequestData[]) => {
      const firstPremiumBasis = data.find((item) => item.premiumBasis)?.premiumBasis || "";

      return data.map((item) => ({
        ...item,
        premiumBasis: item.premiumBasis || firstPremiumBasis,
      }));
    };

    const updatedData = fillPremiumBasis(updateAddAgePrincipalMember);
    setAgePrincipalMember(updatedData);
  };
  const handleUpdateCoInsuredDependentsBenefits = (updateCoInsuredDependentsBenefits: IFipCoInsuredDependentsBenefits[]) => {
    setCoInsuredDependentBenefits(updateCoInsuredDependentsBenefits);
    checkDuplicateBenefitSecondary(updateCoInsuredDependentsBenefits, eInsuredType.coInsuredPrimary, setCoInsuredDependentBenefits);
  };
  const handleUpdatedFipAges = (updateFipAges: IFipAges[]) => {
    setFipAges(updateFipAges);
  };

  const handleUpdateYears = (updatedYears: IClaimsExperienceYears[]) => {
    setClaimExperience((prev) => ({ ...prev, years: updatedYears }));
  };

  const handleUpdateAges = (updatedAges: IClaimsExperienceAges[]) => {
    setClaimExperience((prev) => ({ ...prev, ages: updatedAges }));
  };
  const updateBenefitIdByTypeAndIndex = (data: Array<any>, type: eInsuredType.coInsuredPrimary | eInsuredType.coInsuredSecondary, index?: number, newBenefitId?: string): Array<any> => {
    return data.map((item, idx) => {
      if (item.type === type && idx === index) {
        return {
          ...item,
          benefitId: newBenefitId,
        };
      }
      return item;
    });
  };
  const handleAddYear = (types: eInsuredType.principal | eInsuredType.coInsured) => {
    setClaimExperience((prev) => {
      const highestYear = Math.max(...prev.years.map((y: { year: string }) => parseInt(y.year, 10)));
      const newId = Math.max(...prev.years.map((y: { id: number }) => y.id || 0), 0);

      return {
        ...prev,
        years: [
          ...prev.years,
          {
            id: newId + 1,
            year: (highestYear + 1).toString(),
            numberOfDeaths: null,
            totalClaimAmount: null,
            type: types === eInsuredType.principal ? eInsuredType.principal : eInsuredType.coInsured,
          },
        ],
      };
    });
  };

  const handleAddAge = (types: eInsuredType.principal | eInsuredType.coInsured) => {
    setClaimExperience((prev) => {
      const newId = Math.max(...prev.ages.map((x: { id: number }) => x.id || 0), 0);
      return {
        ...prev,
        ages: [
          ...prev.ages,
          {
            id: newId + 1,
            ageFrom: null,
            ageTo: null,
            numberOfDeaths: null,
            totalClaimAmount: null,
            type: types === eInsuredType.principal ? eInsuredType.principal : eInsuredType.coInsured,
          },
        ],
      };
    });
  };
  const cooperativeModalController = useModalController();

  useEffect(() => {
    // getCooperatives({ filter: "" });
    getCooperatives({ payload: { filter: "" } });
    getContestability({ filter: ProductCode.FIP });
    getCommissionAgeType({ filter: "" });
    getCommissionTypes();
    getProductBenefits({ filter: "" });
  }, []);

  useEffect(() => {
    if (!createFipSuccess && createFipData?.id === 0) return;
    if (!createFipSuccess) return;
    navigate(ROUTES.SALES.quotationFipAerView.parse(createFipData?.id?.toString() ?? "0"), { state: { isShowStatus: true, isShowSignaturStatus: true, demographicData } });
    clearSalesFipQuotation();
  }, [createFipSuccess, createFipData]);

  useEffect(() => {
    if (!updateFipSuccess) return;
    navigate(ROUTES.SALES.quotationFipAerView.parse(createFipData?.id?.toString() ?? "0"), { state: { isShowStatus: true, isShowSignaturStatus: true, demographicData } });
    clearSalesFipQuotation();
  }, [updateFipSuccess]);

  // Actual Data! - Cooperatives
  const cooperativesSelectItems = useMemo(
    () =>
      cooperativesData.map((cooperative: ISharesCoopInformation) => ({
        text: cooperative.coopName,
        value: cooperative.id?.toString() ?? "",
      })),
    [cooperativesData]
  );
  // Actual Data! - Benefits
  const benefitsSelectItems = useMemo<ISelectOptions[]>(() => {
    if (!productBenefitsData) return [];
    const priority = ["Life Insurance", "Natural Death"];
    return productBenefitsData
      .map((item: IUtilitiesProductBenefits) => ({
        text: item.benefitName,
        value: item.id.toString(),
      }))
      .sort((a, b) => {
        const aPriority = priority.indexOf(a.text);
        const bPriority = priority.indexOf(b.text);
        if (aPriority !== -1 && bPriority !== -1) return aPriority - bPriority;
        if (aPriority !== -1) return -1;
        if (bPriority !== -1) return 1;
        return 0;
      });
  }, [productBenefitsData]);
  // Actual Data! - Commission Age Types
  const commissionAgeTypesSelectItems = useMemo<ISelectOptions[]>(() => {
    return commissionAgeTypeData.map((item: IUtilitiesCommissionAgeTypes) => ({
      text: item.name,
      value: item?.id.toString(),
    }));
  }, [commissionAgeTypeData]);
  // Actual Data! - Commission Types
  const commisionTypesSelectItems = useMemo<ISelectOptions[]>(() => {
    if (!commissionTypesData?.data) return [];
    return commissionTypesData.data.map((item: ICommissionType) => ({
      text: item.commissionName,
      value: item.id.toString(),
    }));
  }, [commissionTypesData]);

  const relationshipSelectItems = useMemo(() => {
    if (!testData?.relationship) return [];
    return Object.entries(testData.relationship).map(([id, label]) => ({
      text: label,
      value: id,
    }));
  }, [testData?.relationship]);
  const handlePrincipalMemberBenefitSelectItems = useMemo(() => {
    if (!testData?.ageType) return [];
    return Object.entries(testData.ageType).map(([id, label]) => ({
      text: label,
      value: id,
    }));
  }, [testData?.ageType]);
  const checkForm = useCallback(() => {
    const errors: string[] = [];

    // Cooperative validation
    addError(
      errors,
      !cooperativesSelectItems.find((item) => Number(item.value) === Number(formik.values.quotations.coopId || coopQuotationDetails.coopId)),
      validationMessagesFip.cooperative.required
    );
    // for future use
    // addError(
    //   errors,
    //   !coopQuotationDetails.branch,
    //   validationMessagesFip.cooperative.branchRequired
    // );
    // addError(errors, !coopQuotationDetails.previousProvider, validationMessagesFip.cooperative.previousProviderRequired);

    // Principal Member validation
    addError(errors, !principalMemberData, validationMessagesFip.principalMember.required);
    addError(errors, principalMemberData.premiumBasis === "", validationMessagesFip.principalMember.premiumBasisRequired);
    addError(errors, principalMemberData.averageAge <= 18 || principalMemberData.averageAge >= 100, validationMessagesFip.principalMember.averageAgeInvalid);
    addError(errors, !principalMemberData.contestabilityId, validationMessagesFip.principalMember.contestabilityRequired);
    addError(errors, !principalMemberData.premiumBudget, validationMessagesFip.principalMember.premiumBudgetRequired);
    addError(errors, Number(principalMemberData.totalNumberOfMembers) < 999, validationMessagesFip.principalMember.totalNumberInvalid);

    validateArray(errors, agePrincipalMember, [
      {
        condition: (member) => !member.premiumBasis,
        message: validationMessagesFip.principalMemberArray.premiumBasisRequired,
      },

      {
        condition: (member) => !member.benefitId,
        message: validationMessagesFip.principalMemberArray.benefitRequired,
      },
      {
        condition: (member) => !member.coverage,
        message: validationMessagesFip.principalMemberArray.coverageRequired,
      },
    ]);
    // for fipAges
    validateArray(errors, fipAges, [
      {
        condition: (fipAge) => !fipAge.ageType,
        message: validationMessagesFip.fipAges.ageType,
      },

      {
        condition: (fipAge) =>
          fipAge.memberType.toUpperCase() === eInsuredType.principalMember && fipAge.ageType.toUpperCase() === eAgeType.AGE_BRACKET && (fipAge.minimum === null || fipAge.minimum === ""),
        message: validationMessagesFip.fipAges.minimum,
      },
      {
        condition: (fipAge) => (!fipAge.maximum || fipAge.maximum === "") && fipAge.ageType.toUpperCase() === eAgeType.AGE_BRACKET && fipAge.memberType.toUpperCase() === eInsuredType.principalMember,
        message: validationMessagesFip.fipAges.maximum,
      },
      {
        condition: (fipAge) =>
          (fipAge.exitAge === null || fipAge.exitAge === "") && fipAge.ageType.toUpperCase() === eAgeType.AGE_BRACKET && fipAge.memberType.toUpperCase() === eInsuredType.principalMember,
        message: validationMessagesFip.fipAges.exitAge,
      },

      {
        condition: (fipAge) => !fipAge.memberType,
        message: validationMessagesFip.fipAges.memberType,
      },
      {
        condition: (fipAge) => !fipAge.option,
        message: validationMessagesFip.fipAges.option,
      },
      {
        condition: (fipAge) => Number(fipAge.minimum) >= Number(fipAge.maximum) && fipAge.ageType.toUpperCase() === eAgeType.AGE_BRACKET,
        message: validationMessagesFip.fipAges.maxAgeLessThanMin,
      },
    ]);
    // Commision Distribution validation
    validateArray(errors, commissionDistribution, [
      {
        condition: (commision) => !commision.commissionTypeId,
        message: validationMessagesFip.commissionDistribution.commissionTypeId,
      },

      {
        condition: (commision) => commision.ageFrom === null && String(findItem(commissionAgeTypesData, "id", Number(commision.commissionAgeType) || "N/A")).toUpperCase() === eAgeType.AGE_BRACKET,
        message: validationMessagesFip.commissionDistribution.ageFrom,
      },

      {
        condition: (commision) => commision.rate <= 0,

        message: validationMessagesFip.commissionDistribution.rateLessThanZero,
      },
      {
        condition: (commision) => commision.ageTo === null && String(findItem(commissionAgeTypesData, "id", Number(commision.commissionAgeType) || "N/A")).toUpperCase() === eAgeType.AGE_BRACKET,
        message: validationMessagesFip.commissionDistribution.ageTo,
      },
      {
        condition: (commision) => commision.rate >= 0.35,

        message: validationMessagesFip.commissionDistribution.rateIsGreater,
      },
    ]);
    // for future use
    // Claim Experience validation
    // validateArray(errors, claimExperience.years, [
    //   {
    //     condition: (year) => !year.year,
    //     message: validationMessagesFip.claimExperience.yearRequired,
    //   },
    //   {
    //     condition: (year) => !year.numberOfDeaths,
    //     message: validationMessagesFip.claimExperience.numberOfDeathsRequired,
    //   },
    //   {
    //     condition: (year) => !year.totalClaimAmount,
    //     message: validationMessagesFip.claimExperience.totalClaimAmountRequired,
    //   },
    // ]);

    // validateArray(errors, claimExperience.ages, [
    //   {
    //     condition: (age) => !age.ageFrom,
    //     message: validationMessagesFip.claimExperience.yearRequired,
    //   },
    //   {
    //     condition: (age) => !age.ageTo,
    //     message: validationMessagesFip.claimExperience.yearRequired,
    //   },
    //   {
    //     condition: (age) => !age.numberOfDeaths,
    //     message: validationMessagesFip.claimExperience.numberOfDeathsRequired,
    //   },
    //   {
    //     condition: (age) => !age.totalClaimAmount,
    //     message: validationMessagesFip.claimExperience.totalClaimAmountRequired,
    //   },
    // ]);

    // Co-Insured Dependent validation
    addError(
      errors,
      coInsuredDependent.contestabilityId !== (formik.values.options.fipCoInsuredDependents as any).contestabilityId && coInsuredDependent.premiumBasis === "Per Unit Rate",
      validationMessagesFip.coInsuredDependent.contestabilityRequired
    );
    addError(
      errors,
      coInsuredDependent.premiumBudget !== (formik.values.options.fipCoInsuredDependents as any).premiumBudget && coInsuredDependent.premiumBasis === "Per Unit Rate",
      validationMessagesFip.coInsuredDependent.premiumBudgetRequired
    );
    addError(
      errors,
      coInsuredDependent.maximumTotalNumberOfDependents !== (formik.values.options.fipCoInsuredDependents as any).maximumTotalNumberOfDependents,
      validationMessagesFip.coInsuredDependent.maximumTotalNumberRequired
    );
    addError(
      errors,
      coInsuredDependent.totalNumberOfMembers !== (formik.values.options.fipCoInsuredDependents as any).totalNumberOfMembers && coInsuredDependent.premiumBasis === "Per Unit Rate",
      validationMessagesFip.coInsuredDependent.totalNumberRequired
    );

    // Co-Insured Dependent Benefits validation
    validateArray(errors, coInsuredDependentBenefits, [
      {
        condition: (benefit) => !benefit.relationship,
        message: validationMessagesFip.coInsuredDependentBenefits.relationshipRequired,
      },
      {
        condition: (benefit) => !benefit.benefitId,
        message: validationMessagesFip.coInsuredDependentBenefits.benefitRequired,
      },
      {
        condition: (benefit) => !benefit.coverage,
        message: validationMessagesFip.coInsuredDependentBenefits.coverageRequired,
      },
      {
        condition: (benefit) => benefit.maximumNumberOfInsured === "",
        message: validationMessagesFip.coInsuredDependentBenefits.maxNumberOfInsuredRequired,
      },
      {
        condition: (benefit) => !benefit.premiumBasis,
        message: validationMessagesFip.coInsuredDependentBenefits.premiumBasisRequired,
      },
      //ayaw sa e delete gamiton pani
      // {
      //   condition: (benefit) => !benefit.minimumAge && benefit.type === typeCoInsured.primary,
      //   message: validationMessagesFip.coInsuredDependentBenefits.ageFromRequired,
      // },
      // {
      //   condition: (benefit) => !benefit.maximumAge && benefit.type === typeCoInsured.primary,
      //   message: validationMessagesFip.coInsuredDependentBenefits.maximumAgeRequired,
      // },
      // {
      //   condition: (benefit) => !benefit.maximumAge && benefit.type === typeCoInsured.secondary,
      //   message: validationMessagesFip.coInsuredDependentBenefits.maximumAgeRequired,
      // },
      // {
      //   condition: (benefit) => !benefit.minimumAge && benefit.type === typeCoInsured.secondary,
      //   message: validationMessagesFip.coInsuredDependentBenefits.minimumAgeRequired,
      // },
      // {
      //   condition: (benefit) => !benefit.exitAge && benefit.type === typeCoInsured.secondary,
      //   message: validationMessagesFip.coInsuredDependentBenefits.exitAgeRequired,
      // },
    ]);

    // Sort errors by priority
    const priority: Record<string, number> = {
      [validationMessagesFip.cooperative.required]: 1,
      // [validationMessagesFip.cooperative.branchRequired]: 2, // for future use
      // [validationMessagesFip.cooperative.previousProviderRequired]: 3,
      [validationMessagesFip.principalMemberArray.premiumBasisRequired]: 4,
      [validationMessagesFip.principalMember.premiumBudgetRequired]: 5,
      [validationMessagesFip.principalMember.averageAgeInvalid]: 6,
      [validationMessagesFip.principalMember.totalNumberInvalid]: 7,
      [validationMessagesFip.principalMember.contestabilityRequired]: 8,
      [validationMessagesFip.principalMemberArray.ageTypeRequired]: 9,
      [validationMessagesFip.principalMemberArray.minimumAgeRequired]: 10,
      [validationMessagesFip.principalMemberArray.maximumAgeRequired]: 11,
      [validationMessagesFip.principalMemberArray.exitAgeRequired]: 12,
      [validationMessagesFip.principalMemberArray.benefitRequired]: 13,
      [validationMessagesFip.principalMemberArray.coverageRequired]: 14,
      [validationMessagesFip.coInsuredDependentBenefits.relationshipRequired]: 15,
      [validationMessagesFip.coInsuredDependentBenefits.ageFromRequired]: 16,
      [validationMessagesFip.coInsuredDependentBenefits.maximumAgeRequired]: 18,
      [validationMessagesFip.coInsuredDependentBenefits.minimumAgeRequired]: 17,
      [validationMessagesFip.coInsuredDependentBenefits.benefitRequired]: 19,
      [validationMessagesFip.coInsuredDependentBenefits.coverageRequired]: 20,
      // for future use
      // [validationMessagesFip.claimExperience.yearRequired]: 21,
      // [validationMessagesFip.claimExperience.numberOfDeathsRequired]: 22,
      // [validationMessagesFip.claimExperience.totalClaimAmountRequired]: 23,
      [validationMessagesFip.commissionDistribution.commissionTypeId]: 24,
      // [validationMessagesFip.commissionDistribution.ageType]: 25, // for future use
      [validationMessagesFip.commissionDistribution.rateIsGreater]: 25,
      [validationMessagesFip.commissionDistribution.ageFrom]: 26,
      [validationMessagesFip.commissionDistribution.ageTo]: 27,
      [validationMessagesFip.commissionDistribution.rateLessThanZero]: 28,
      [validationMessagesFip.fipAges.ageType]: 29,
      [validationMessagesFip.fipAges.minimum]: 30,
      [validationMessagesFip.fipAges.maximum]: 31,
      [validationMessagesFip.fipAges.exitAge]: 32,
      [validationMessagesFip.fipAges.memberType]: 33,
      [validationMessagesFip.fipAges.maxAgeLessThanMin]: 34,
      [validationMessagesFip.fipAges.option]: 35,
    };

    return sortErrorsByPriority(errors, priority);
  }, [formik]);
  const handleCalculate = async () => {
    const errors = checkForm();
    if (errors.length > 0) {
      toast.error(errors?.pop() ?? "Something went wrong");
      return;
    }

    const payload: TSalesFipCalculationPayload = {
      averageAge: Number((formik.values.options.fipPrincipalMemberRequestData as any).averageAge) || 0,
      averageClaims: Number((formik.values.options.fipPrincipalMemberRequestData as any).averageAge),
      groupSize: Number((formik.values.options.fipPrincipalMemberRequestData as any).totalNumberOfMembers.toString().replace(/,/g, "")),
      fipPrincipalMemberBenefit: formik.values.options.fipPrincipalMemberBenefitRequestData.map((data) => ({
        premiumBasis: data.premiumBasis,
        benefitId: Number(data.benefitId),
        coverage: Number(data.coverage),
        option: data.option,
      })),
      fipCoInsuredDependentBenefit: formik.values.options.fipCoInsuredDependentsBenefits.map((data) => ({
        premiumBasis: data.premiumBasis,
        relationship: data.relationship,
        maximumNumberOfInsured: Number(data.maximumNumberOfInsured) || null,
        benefitId: Number(data.benefitId),
        coverage: Number(data.coverage),
        dividedEqually: data.dividedEqually,
        type: data.type,
        option: data.option,
      })),
      quotationCommissionDistribution: formik.values.commissionDistributions.map((data) => ({
        commissionTypeId: Number(data.commissionTypeId),
        rate: data.rate !== null ? Number(data.rate) : null,
      })),
      premiumBasis: (formik.values.options.fipCoInsuredDependents as any)?.premiumBasis,
      contestabilityId: Number((formik.values.options.fipPrincipalMemberRequestData as any).contestabilityId) || 0,
      ageType: capitalizeFirstLetterWords(String(formik.values.fipAges.find((ages) => ages.memberType === eInsuredType?.principalMember)?.ageType || ""), " "),
      fileName: String(fileName || ""),
      productType: productCode as string, // Replace with actual product type if needed
      basisForCalculation: fileName && fileName !== "" ? "MASTERLIST" : "NO_DATA",
      productId: Number(productData?.id),
    };
    // Check for duplicate calculation
    // if (
    //   JSON.stringify(payload.fipPrincipalMemberBenefit) === JSON.stringify(checkCalculationPayload.fipPrincipalMemberBenefitRequestData) &&
    //   JSON.stringify(payload.fipCoInsuredDependentBenefit) === JSON.stringify(checkCalculationPayload.fipCoInsuredDependentsBenefits)
    // ) {
    //   toast.error("Duplicate Calculation is not allowed");
    //   return;
    // }
    // setCheckCalculationPayload({
    //   fipPrincipalMemberBenefitRequestData: payload.fipPrincipalMemberBenefit,
    //   fipCoInsuredDependentsBenefits: payload.fipCoInsuredDependentBenefit,
    // });
    try {
      await dispatch(postFipCalculation(payload as any));
      setIsSelectedCalculation(false); //temporary
      setIsIncremental(false);
    } catch (err) {
      toast.error("Something went in calculation");
    }
  };
  const selectedOptionNumber = (id: number) => {
    setIsOptionNumber((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  useEffect(() => {
    // Whenever isOptionNumber changes, update selectedRating accordingly
    setPremiumOption((prevOptions) =>
      prevOptions.map((item) => {
        // Get the option number from fipPrincipalMemberRequestData (assume first entry)
        const optionNumber = item.fipPrincipalMemberRequestData?.[0]?.option;
        // If optionNumber is defined, use isOptionNumber[optionNumber] for isSelected
        return typeof optionNumber !== "undefined" ? { ...item, isSelected: !!isOptionNumber[optionNumber] } : item;
      })
    );
    setSelectedRating((prevRatings) => prevRatings.map((item) => (typeof item.option !== "undefined" && isOptionNumber[item.option] ? { ...item, isSelected: true } : { ...item, isSelected: false })));
    setProjectionSelection((prevProjections) =>
      prevProjections.map((item) => (typeof item.option !== "undefined" && isOptionNumber[item.option] ? { ...item, isSelected: true } : { ...item, isSelected: false }))
    );
    setSelectedFipAges((prevAges) => prevAges.map((item) => (typeof item.option !== "undefined" && isOptionNumber[item.option] ? { ...item, isSelected: true } : { ...item, isSelected: false })));
    setSelectedCommissionDistribution((prevDistribution) =>
      prevDistribution.map((item) => (typeof item.option !== "undefined" && isOptionNumber[item.option] ? { ...item, isSelected: true } : { ...item, isSelected: false }))
    );
  }, [isOptionNumber]);

  const handleShowAllOptions = async (type: "option" | "aer") => {
    if (type === "option") {
      setIsSelectedCalculation(true);
      // setIsIncremental(true);
    } else if (type === "aer") {
      if ((selectedOptionPremiumData(premiumOption)?.aerOptions?.length ?? 0) > 0) {
        const isConfirmed = await confirmSaveOrEdit("Confirmation", `Would you like to ${isEdit ? "update" : "save"} your Quotation FIP together with the AER now?`);

        if (isConfirmed) {
          setIsCalculating(false);
          handlePremiumCalculation(); //trigger para ma insert sa formik option ang values
        } else {
          setIsCalculating(true);
        }
      } else {
        toast.error("Please select at least one AER option.");
      }
    }
  };
  const handleSetFileName = (fileName: string) => {
    setFileName(fileName);
  };
  const handleUploadMasterList = () => {
    uploadMasterListModalController.openFn();
  };
  const handleShowDemographic = () => {
    demographicModalController.openFn();
  };

  const fetchData = async () => {
    try {
      const { data } = await postDemographicDataService({
        fileName: fileName,
        productType: productCode as string, // Replace with actual product type if needed
        ageFrom: 0,
        ageTo: 200,
      });
      if (data) {
        setDemographicData(data);
        setPrincipalMemberData((prev) => ({
          ...prev,
          averageAge: data.averageAge || 0,
          totalNumberOfMembers: formatNumberWithCommas(data.totalNumberOfMembers),
        }));
        setCoInsuredDependent((prev) => ({
          ...prev,
          averageAge: data.averageAge || 0,
          totalNumberOfMembers: formatNumberWithCommas(data.totalNumberOfMembers),
        }));
      }
    } catch (error) {
      toast.error("Error fetching product proposal");
    }
  };
  useEffect(() => {
    if (fileName) {
      fetchData();
    }
  }, [fileName]);

  useEffect(() => {
    if (demographicModalController.isOpen) {
      fetchData();
    }
  }, [demographicModalController.isOpen]);

  const fetchProduct = async () => {
    const { data } = await getProductNameService(productCodeName || productCode || "");
    if (!data) return;
    setSelectedProduct(data[0]);
  };

  useEffect(() => {
    if (productData) return;
    fetchProduct();
  }, []);
  useEffect(() => {
    setInitialValues((prev) => ({
      ...prev,
      prodId: (productData as any)?.id || 0,
    }));
  }, [productData]);
  useEffect(() => {
    if (commissionDistribution.reduce((acc, curr) => acc + Number(curr.rate), 0) > 0.35) {
      toast.error("Total commission rate should not exceed 0.35");
    }
  }, [commissionDistribution]);
  const handleCooperativeSearch = (searchTerm?: string) => {
    getCooperatives({ payload: { filter: searchTerm } });
  };
  const checkDuplicateBenefitSecondary = (data: any[], type: eInsuredType.coInsuredSecondary | eInsuredType.coInsuredPrimary, setCoInsuredDependentBenefits: (fn: (prev: any[]) => any[]) => void) => {
    // Group by relationship for COINSURED SECONDARY
    const secondaryGroups = data
      .filter((item) => item.type === type && item.relationship)
      .reduce(
        (acc, item) => {
          const rel = item.relationship;
          if (!acc[rel]) acc[rel] = [];
          acc[rel].push(item);
          return acc;
        },
        {} as Record<string, any[]>
      );

    // Check for duplicate benefitId in each group
    for (const [relationship, items] of Object.entries(secondaryGroups)) {
      const seen = new Set();
      const itemsArray = items as any[];
      for (const item of itemsArray) {
        if (seen.has(item.benefitId)) {
          toast.error(`Duplicate benefit: ${findItem(productBenefitsData, "id", item.benefitId, "benefitName")} already exists for relationship "${relationship}".`);
          // Clear relationship for all items with this relationship
          if (type === eInsuredType.coInsuredSecondary) {
            setCoInsuredDependentBenefits((prev) => prev.map((benefit) => (benefit.type === type && benefit.relationship === relationship ? { ...benefit, relationship: "" } : benefit)));
          } else if (type === eInsuredType.coInsuredPrimary) {
            setCoInsuredDependentBenefits((prev) => {
              // Find the last matching index
              const lastIdx = [...prev]
                .map((benefit, idx) => ({ ...benefit, idx }))
                .filter((benefit) => benefit.type === type && benefit.relationship === relationship)
                .map((benefit) => benefit.idx)
                .pop();

              if (typeof lastIdx === "undefined") return prev;

              return prev.map((benefit, idx) => (idx === lastIdx ? { ...benefit, benefitId: "" } : benefit));
            });
          }
          setCheckDuplicateCoInsureBenefit(false);
          return true;
        }
        seen.add(item.benefitId);
      }
    }
    type === eInsuredType.coInsuredSecondary ? toast.success("Relationship updated successfully.") : null;
    setCheckDuplicateCoInsureBenefit(false);
    return false;
  };
  const addedRelationship = () => {
    setCoInsuredDependentBenefits((prev) => {
      let errorReason = "";

      if (prev.some((item) => item.relationship === isRelationship && item.type === eInsuredType.coInsuredSecondary)) {
        errorReason = `The relationship "${isRelationship === "" ? "not specified" : isRelationship}" already exists for ${capitalizeFirstLetterWords(eInsuredType.coInsuredSecondary, " ")}.`;
      } else if (prev.some((item) => item.type === eInsuredType.coInsuredSecondary && (item.coverage === null || item.coverage === ""))) {
        errorReason = "There is at least one item with an empty coverage.";
      } else if (prev.some((item) => item.benefitId === "")) {
        errorReason = "There is at least one item with an empty benefit.";
      }

      if (errorReason) {
        toast.error(`${errorReason}`);
        return prev;
      }

      let updated = false;
      const newArr = prev.map((item) => {
        if (item.type === eInsuredType.coInsuredSecondary && item.relationship === "") {
          updated = true;
          return { ...item, relationship: isRelationship };
        }
        return item;
      });

      if (updated) {
        setCheckDuplicateCoInsureBenefit(true);
      } else {
        toast.error(`Add Relationship for ${capitalizeFirstLetterWords(eInsuredType.coInsuredSecondary, " ")}`);
      }
      return newArr;
    });
  };
  useEffect(() => {
    if (checkDuplicateCoInsureBenefit) {
      checkDuplicateBenefitSecondary(coInsuredDependentBenefits, eInsuredType.coInsuredSecondary, setCoInsuredDependentBenefits);
    }
  }, [checkDuplicateCoInsureBenefit]);
  return (
    <>
      <div className="grid grid-cols-12 gap-2 px-4">
        <div className="block col-span-12">
          <h3
            className={colorMode({
              classLight: "text-2xl font-[600] mb-3 text-primary",
              classDark: "text-2xl font-[600] mb-3 text-white/60",
            })}
          >
            FIP QUOTATION
          </h3>
          <p
            className={colorMode({
              classLight: "text-gray text-base text-[12px]",
              classDark: "text-white/60 text-base text-[12px]",
            })}
          >
            Create a customized Family Insurance Plan quotation based on cooperative-specific data.
          </p>

          <div className="grid grid-cols-12 gap-2 md:gap-y-7 py-6">
            {/* Cooperative */}
            <div className="col-span-12 md:col-span-2 flex flex-col">
              <span
                className={colorMode({
                  classLight: "mt-[8px] font-[500] text-[14px] text-gray",
                  classDark: "mt-[8px] font-[500] text-[14px] text-white/60",
                })}
              >
                Cooperative
              </span>
            </div>
            <div className="col-span-12 md:col-span-10">
              <Select
                name="cooperativeId"
                placeholder="Select Coop"
                options={cooperativesSelectItems}
                onChange={(e) => {
                  const value = Number(e);
                  setCoopQuotationDetails((prevState) => ({
                    ...prevState,
                    coopId: value, // Directly update the coopId field
                  }));
                  setInitialValues((prev) => ({
                    ...prev,
                    quotations: {
                      ...prev.quotations,
                      coopId: value,
                    },
                  }));
                }}
                value={quotationData?.quotation?.coopId || coopQuotationDetails?.coopId || ""}
                allowSearch={true}
                placeHolderCenter={false}
                onSearch={handleCooperativeSearch}
              >
                <span
                  onClick={() => {
                    cooperativeModalController.openFn();
                  }}
                >
                  Add New Coop
                </span>
              </Select>
            </div>
            {/* Note: for Possible Future Use */}
            {/* Branch */}
            {/* <div className="col-span-12 md:col-span-2 flex flex-col">
              <span
                className={colorMode({
                  classLight: "mt-[8px] font-[500] text-[14px] text-gray",
                  classDark: "mt-[8px] font-[500] text-[14px] text-white/60",
                })}
              >
                Branch
              </span>
            </div>
            <div className="col-span-12 md:col-span-10">
              <Input
                name="branch"
                placeholder="Enter branch"
                onChange={handleTextFieldChange("branch")}
                value={coopQuotationDetails?.branch || ""}
              />
            </div> */}
            {/* Previous Provider */}

            <div className="col-span-12 md:col-span-2 flex flex-col">
              <span
                className={colorMode({
                  classLight: "mt-[8px] font-[500] text-[14px] text-gray",
                  classDark: "mt-[8px] font-[500] text-[14px] text-white/60",
                })}
              >
                Previous Provider
              </span>
            </div>
            <div className="col-span-12 md:col-span-10">
              <TextField
                name="previousProvider"
                placeholder="Enter previous provider"
                className={colorMode({
                  classLight: "mt-[4px] font-[500] text-[14px] text-gray",
                  classDark: "mt-[4px] font-[500] text-[14px] text-white/60",
                })}
                onChange={handleTextFieldChange("previousProvider")}
                value={quotationData?.quotation?.previousProvider || coopQuotationDetails?.previousProvider || ""}
                variant="default"
              />
            </div>
          </div>
        </div>
      </div>
      <UploadMasterListModal
        controller={uploadMasterListModalController}
        // TODO: Change for the actual logic!
        onUpload={(files: any[], _: string) => {
          setHasDemographics(files.length > 0);
          return Promise.resolve();
        }}
        data={initialValues}
        setFileName={handleSetFileName}
      />
      {demographicData && <DemographicModal controller={demographicModalController} hasDemographics={hasDemographics} data={demographicData} />}
      <div className="mt-6 flex flex-wrap gap-4">
        {/* Masterlist */}
        <Button classNames="bg-info elevation-sm shadow-md rounded-[5px]" variant="default" onClick={handleUploadMasterList}>
          <div className="flex flex-row items-center gap-2">
            <PiCloudArrowUp size={25} />
            <span className="text-[14px] font-[400] font-poppins-medium">Upload Masterlist</span>
          </div>
        </Button>
        {/* Demographic */}
        <Button classNames="!bg-[#99999926] rounded-[5px]" variant="default" onClick={handleShowDemographic}>
          <div className="flex flex-row items-center gap-2">
            <span
              className={colorMode({
                classLight: "text-gray/50 text-[14px] font-[400] font-poppins-medium",
                classDark: "text-white/60 text-[14px] font-[400] font-poppins-medium",
              })}
            >
              Show Demographic
            </span>
          </div>
        </Button>
      </div>
      <CooperativeModal
        controller={cooperativeModalController}
        onSelect={(coop: ICda) => {
          formik.setFieldValue("cooperativeId", coop.coopCdaId);
        }}
      />
      {/* Premium Basis */}
      <div className="mt-12">
        <FipPremiunDetails
          claimExperience={claimExperience}
          onUpdateYears={handleUpdateYears}
          onUpdateAges={handleUpdateAges}
          onAddYear={handleAddYear}
          onAddAge={handleAddAge}
          onDataChange={handleDataChange}
          contestabilitySelectItems={contestabilitySelectItems}
          onAddAgePrincipalMember={onAddAgePrincipalMember}
          agePrincipalMemberBenefit={agePrincipalMember}
          onUpdatedAgePrincipalMember={handleUpdateAddAgePrincipalMember as any}
          onDataChangeCoInsuredDependents={handleDataChangeCoInsuredDependent}
          contestabilityCoInsuredSelectItems={contestabilitySelectItems}
          fipCoInsuredDependents={coInsuredDependent}
          fipPrincipalMemberRequestData={principalMemberData}
          relationshipCoInsuredSelectItems={relationshipSelectItems}
          fipCoInsuredDependentsBenefits={coInsuredDependentBenefits}
          onUpdatedCoInsuredDependentsBenefits={handleUpdateCoInsuredDependentsBenefits as any}
          onAddCoInsuredDependentsBenefits={onAddCoInsuredDependentsBenefits}
          coInsuredDependentSelectItems={benefitsSelectItems}
          updateBenefitId={updateBenefitIdByTypeAndIndex}
          principalMemberBenefitSelectItems={handlePrincipalMemberBenefitSelectItems}
          relationshipPrincipalMemberSelectItems={benefitsSelectItems}
          onDataChangeContestability={handleDataChangeContestability}
          fipAges={fipAges}
          onUpdatedFipAges={handleUpdatedFipAges as any}
          fipAgesCoInsured={fipAges}
          onUpdatedFipAgesCoInsure={handleUpdatedFipAges as any}
          onUpdateRelationship={selectedRelationship}
          addedRelationship={addedRelationship}
          hasDemographics={hasDemographics}
          onUpdatePrimaryRelationship={onUpdatePrimaryRelationship}
        />
      </div>
      <div className="mt-12">
        <CommissionDistribution
          commissionDistribution={commissionDistribution}
          onAddCommissionDistribution={handleAddCommissionDistribution}
          handleSwitchCoopMode={handleSwitchCoopMode}
          isCoopMode={isCoopMode}
          onUpdatedCommissionDistribution={handleUpdatedCommissionDistribution as any}
          commisionTypesSelectItems={commisionTypesSelectItems}
          commissionAgeTypesSelectItems={commissionAgeTypesSelectItems}
          totalDistribution={totalDistribution(commissionDistribution)}
        />
      </div>
      <div className="mt-8">
        <ConditionAndOption
          calculateFip={handleCalculate}
          premiumOption={premiumOption}
          selectedOption={selectedOptionNumber}
          successFipPremium={successFipPremium || false}
          handleSaveOption={handleShowAllOptions}
          filteredDatas={filterData}
          onFilterByIndex={handleFilterByIndex}
          principalOptionCondition={principalMemberCondition}
          coInsuredOptionCondition={coInsuredDependentCondition}
          isEdit={isEdit}
        />
      </div>
    </>
  );
};
export default FipSalesQuotation;
