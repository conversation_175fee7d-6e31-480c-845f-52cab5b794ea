import ActionDropdown from "@components/common/ActionDropdown";
import Table from "@components/common/Table";
import Typography from "@components/common/Typography";
import { capitalizeFirstLetterOnly } from "@helpers/text";
import { IActions } from "@interface/common.interface";
import { ICooperativeOfficer } from "@interface/product-proposal.interface";
import dayjs from "dayjs";
import { FC, Fragment } from "react";
import { TableColumn } from "react-data-table-component";
import { CiEdit, CiTrash } from "react-icons/ci";

type TProps = {
  officers: ICooperativeOfficer[];
  editCoopOfficer: (data: ICooperativeOfficer & { index: number }) => void;
  deleteOfficer: (id: number) => void;
};

const CoopOfficersTable: FC<TProps> = ({ officers = [], editCoopOfficer, deleteOfficer }) => {
  const commonSetting = {
    sortable: false,
    reorder: false,
  };

  const actions: IActions<ICooperativeOfficer>[] = [
    {
      name: "Edit",
      event: (data: ICooperativeOfficer, index: number) => {
        editCoopOfficer({ ...data, index: index });
      },
      icon: CiEdit,
      color: "primary",
    },
    {
      name: "Delete",
      event: (_, index: number) => {
        deleteOfficer(index);
      },
      icon: CiTrash,
      color: "danger",
    },
  ];

  const columns: TableColumn<ICooperativeOfficer>[] = [
    {
      name: "Name",
      cell: (row) => <span>{`${row.firstName} ${row.middleName ? `${row.middleName[0]}.` : ""} ${row.lastName} ${row.generation ?? ""}`}</span>,
      ...commonSetting,
    },
    {
      name: "Position",
      cell: (row) => <span>{row.positionName ?? row.position?.positionName ?? "Not Set"}</span>,
      ...commonSetting,
    },
    {
      name: "Gender",
      cell: (row) => capitalizeFirstLetterOnly(row.gender ?? "Not Set"),
      ...commonSetting,
    },
    {
      name: "Contact No.",
      cell: (row) => row.contactNumber ?? "Not Set",
      ...commonSetting,
    },
    {
      name: "Email",
      cell: (row) => <span className="text-nowrap truncate">{row.emailAddress ?? "Not Set"}</span>,
      ...commonSetting,
    },
    {
      name: "Status",
      cell: (row) => row.status ?? "Not Set",
      ...commonSetting,
    },
    {
      name: "Effective Date",
      cell: (row) => dayjs(row.effectivityDate).format("MMM DD, YYYY") ?? "",
      ...commonSetting,
    },
    {
      name: "Signatory",
      cell: (row) => (row.signatory ? "Yes" : "No"),
      ...commonSetting,
    },
    {
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => <ActionDropdown actions={actions} data={row} rowIndex={rowIndex} />,
    },
  ];

  return (
    <Fragment>
      <div className="flex mt-4">
        <Table className="!min-h-[200px] !max-h-[300px]" columns={columns} data={officers} searchable={false} selectable={false} />
      </div>
    </Fragment>
  );
};

export default CoopOfficersTable;
