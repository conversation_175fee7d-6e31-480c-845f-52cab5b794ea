import LoadingButton from "@components/common/LoadingButton";
import Typography from "@components/common/Typography";
import TextField from "@components/form/TextField";
import Loader from "@components/Loader";
import { CooperativeDataSourceType } from "@enums/enums";
import { ICooperative } from "@interface/product-proposal.interface";
import { checkCooperativeIfExist } from "@services/product-proposal/product-proposal.service";
import { useProductProposalActions } from "@state/reducer/product-proposal";
import { RootState } from "@state/store";
import { ChangeEvent, FC, Fragment, useEffect, useState } from "react";
import { FaSearch } from "react-icons/fa";
import { useSelector } from "react-redux";
import { toast } from "react-toastify";
import { useDebouncedCallback } from "use-debounce";
import { useNavigate } from "react-router-dom";

type TProps = {
  toggleModal: () => void;
};

const SelectCoopFromCdaForm: FC<TProps> = ({ toggleModal }) => {
  const navigate = useNavigate();
  const { getCdaCooperatives, setCooperative } = useProductProposalActions();
  const cooperative = useSelector((state: RootState) => state.productProposal.cooperative);
  const cdaCooperatives = useSelector((state: RootState) => state.productProposal.cdaCooperatives);
  const loading = useSelector((state: RootState) => state.productProposal.getCdaCooperatives.loading);

  const [isExist, setIsExist] = useState<boolean>(false);
  const [isChecking, setIsChecking] = useState<boolean>(false);

  const handleSearchCoop = useDebouncedCallback((e: ChangeEvent<HTMLInputElement>) => {
    getCdaCooperatives({ page: 1, pageSize: 10, filter: e.target.value });
  }, 500);

  const handleSelectedCoop = async (coop: ICooperative) => {
    try {
      setIsChecking(true);
      const { data } = await checkCooperativeIfExist(coop.name ?? "");

      if (data.length > 0) {
        setIsExist(true);
      } else {
        setIsExist(false);
      }

      const newCoop = {
        ...coop,
        coopName: coop.name,
        source: CooperativeDataSourceType.CDA,
      };
      setCooperative(newCoop);
    } catch (error: any) {
      toast.error(error?.response?.data?.message);
    } finally {
      setIsChecking(false);
    }
  };

  const handleCancelSelectedCoop = () => {
    setCooperative(undefined as unknown as ICooperative);
    toggleModal && toggleModal();
  };

  const handleSelected = () => {
    if (cooperative === undefined) {
      toast.error("Please select a cooperative");
      return;
    }

    toggleModal && toggleModal();
  };

  useEffect(() => {
    setCooperative(undefined as unknown as ICooperative);
    getCdaCooperatives({ pageSize: 20, page: 1 });
  }, []);

  useEffect(() => {
    return () => {
      if (isExist) {
        setCooperative(undefined as unknown as ICooperative);
      }
    };
  }, []);
  const noRecords = !loading && cdaCooperatives.length === 0;
  const handleAddCoopPage = () => {
    const pathRole = location.pathname.split("/")[1]; // e.g., 'admin'
    navigate(`/${pathRole}/utilities/cooperatives/create`, { replace: true });
  };
  return (
    <Fragment>
      <div className="flex flex-1 flex-col">
        <div className="flex flex-1">
          <TextField type="search" placeholder="Search" leftIcon={<FaSearch className="text-zinc-500" />} onChange={handleSearchCoop} />
        </div>
        {isExist && (
          <div className="flex justify-center my-4">
            <Typography className="!text-danger">Cooperative is already registered</Typography>
          </div>
        )}
      </div>
      {loading && (
        <div className="flex flex-1 w-full justify-center items-center h-[300px]">
          <Loader />
        </div>
      )}
      {!loading && cdaCooperatives.length === 0 && (
        <div className="flex flex-col w-full h-[300px] items-center justify-center">
          <Typography size="xl">No results found</Typography>
        </div>
      )}
      {!loading && cdaCooperatives.length > 0 && (
        <div className="flex flex-1 flex-col w-full h-[300px] overflow-y-auto">
          {cdaCooperatives.map((coop: ICooperative) => {
            return (
              <div
                key={`coop-${coop.id}`}
                onClick={() => handleSelectedCoop(coop)}
                className={`
                                    group
                                      flex flex-1 cursor-pointer px-2 items-center border-[1px] border-zinc-200 min-h-12 max-h-12 my-[1px]
                                    hover:bg-primary !hover:text-white
                                    ${cooperative?.id === coop.id ? "bg-primary" : ""}
                                `}
              >
                <span className={`text-zinc-500 group-hover:text-white ${cooperative?.id === coop.id ? "!text-white" : ""}`}>{coop.name}</span>
              </div>
            );
          })}
        </div>
      )}
      <div className="flex flex-1 flex-row justify-end p-2 space-x-4">
        <LoadingButton type="button" isLoading={isChecking} className="btn bg-white !text-primary !border-zinc-300 !w-32" onClick={handleCancelSelectedCoop}>
          Cancel
        </LoadingButton>
        {!noRecords ? (
          <LoadingButton type="button" disabled={isExist} isLoading={isChecking} className="btn bg-primary text-white !w-32" onClick={handleSelected}>
            Select
          </LoadingButton>
        ) : (
          <LoadingButton
            type="button"
            // disabled={isExist} isLoading={isChecking}
            className="btn bg-primary text-white !w-32"
            onClick={handleAddCoopPage}
          >
            Add New
          </LoadingButton>
        )}
        {/* <LoadingButton type="button" disabled={isExist} isLoading={isChecking} className="btn bg-primary text-white !w-32" onClick={handleSelected}>
           Select 
        </LoadingButton> */}
      </div>
    </Fragment>
  );
};

export default SelectCoopFromCdaForm;
