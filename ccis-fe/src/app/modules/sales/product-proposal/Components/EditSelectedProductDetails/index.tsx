import Typography from "@components/common/Typography";
import { RootState } from "@state/store";
import { useSelector } from "react-redux";
import { FC, Fragment, useState } from "react";
import { IGuideline, IGuidelineContent, IGuidelineContentTable } from "@interface/guidelines.interface";
import { FaDotCircle, FaMinusCircle, FaPen, FaPlusCircle, FaRedo } from "react-icons/fa";
import LoadingButton from "@components/common/LoadingButton";
import Tooltip from "@components/common/Tooltip";
import { ProposalStatus } from "@enums/proposal-status";
import TextField from "@components/form/TextField";
import Wysiwyg from "@components/common/Wysiwyg";
import SimpleTable from "@modules/admin/products/components/Common/SimpleTable";
import { contentType } from "@enums/product-guidelines";
import { cloneDeep, isEqual } from "lodash";
import { TbEdit, TbEditOff } from "react-icons/tb";
import Modal from "@components/common/Modal";
import { FaTag } from "react-icons/fa6";
import { toast } from "react-toastify";
import { ProductStatus } from "@enums/product-status";
import { removeProperties } from "@helpers/objects";
import { confirmSaveOrEdit } from "@helpers/prompt";
import { ProposableTypes, ProposalTypes } from "@enums/enums";
import GuidelineList from "../List/GuidelineList";
import SelectContentTypeForm from "@modules/admin/products/components/Forms/SelectContentTypeForm";
import { useProductActions } from "@state/reducer/products";
import { IProductProposal } from "@interface/product-proposal.interface";
import { validateGuidelineContents, validateGuidelines } from "@helpers/products";
import { useProductProposalActions } from "@state/reducer/product-proposal";

type TProps = {
  setStep: () => void;
  handleChange: () => void;
  toggleManagementFee: () => void;
  toggleIsEditProvisions: () => void;
  submitting?: boolean;
};

const EditSelectProductDetails: FC<TProps> = ({ handleChange, toggleManagementFee, toggleIsEditProvisions }) => {
  const proposedProduct = useSelector((state: RootState) => state.productProposal.proposedProduct);
  const revisionDetails = useSelector((state: RootState) => state.products.revisionDetails);
  const { setRevisionDetails } = useProductActions();
  const { setProposedProduct, setIsEditedGuidelines } = useProductProposalActions();
  const managementPercentFee = useSelector((state: RootState) => state.productProposal.managementPercentFee);
  const [productGuidelines, setProductGuidelines] = useState<IGuideline[]>(revisionDetails?.productGuidelines ?? []);
  const [onEdit, setOnEdit] = useState<number[]>([]);
  const [addTag, setAddTag] = useState<boolean>(false);
  const toggleTagModal = () => setAddTag((prev) => !prev);
  const [tag, setTag] = useState<string>("");
  const [tagType, setTagType] = useState<string>("");
  const [taggedListIndex, setTaggedListIndex] = useState<number>(0);
  const [taggedGuidelineIndex, setTaggedGuidelineIndex] = useState<number>(0);
  const [taggedContentIndex, setTaggedContentIndex] = useState<number>(0);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [addGuidelines, setAddGuidelines] = useState<boolean>(false);
  const toggleAddGuidelines = () => setAddGuidelines((prev) => !prev);
  const [addContent, setAddContent] = useState<boolean>(false);
  const toggleAddContent = () => setAddContent((prev) => !prev);
  const [selectedGuidelineIndex, setSelectedGuidelineIndex] = useState<number | undefined>(undefined);

  const standard = revisionDetails?.commission?.commissionDetails?.filter((rowValue) => rowValue.commissionAgeType?.name?.toLowerCase() !== "standard") ?? [];

  const preSelectedGuidelines = productGuidelines.map((guideline) => {
    return guideline.label;
  });

  const handleSave = async () => {
    const currentPG = revisionDetails?.productGuidelines;
    const newPG = productGuidelines;

    const isValidGuidelines = validateGuidelines(newPG);
    if (!isValidGuidelines) {
      toast.error("Please fill out the product guidelines");
      return;
    }

    const isValidContent = validateGuidelineContents(newPG);
    if (!isValidContent) {
      toast.error("Please use corrent contents for the product guidelines");
      return;
    }

    if (isEqual(currentPG, newPG)) {
      toast.info("Product guidelines remain uncustomized");
      return;
    }

    const isConfirm = await confirmSaveOrEdit("Do you want to save changes to this product?", "if yes, then changes will be submitted for approval.");
    if (!isConfirm) return;

    saveProposedProductProvision();
    toggleIsEditProvisions();
  };

  const handleCancel = async () => {
    toggleIsEditProvisions();
  };

  const saveProposedProductProvision = async () => {
    try {
      const revisionCopy = cloneDeep(revisionDetails);
      const productRevision = removeProperties(revisionCopy);
      productRevision.productGuidelines = productGuidelines;

      const productDetails: IProductProposal = {
        cooperativeId: undefined,
        productId: productRevision?.productId,
        managementPercentFee: managementPercentFee,
        proposableId: revisionDetails?.id,
        proposableType: ProposableTypes.PRODUCT_REVISION,
        proposalType: ProposalTypes.CUSTOMIZED,
        productStatus: ProductStatus.FOR_APPROVAL,
        productRevision: { ...productRevision, type: "SALES", attachments: undefined },
        id: "",
        productType: "",
        status: "",
      };

      setProposedProduct(productDetails);
      setRevisionDetails(productRevision);
      setIsEditedGuidelines(true);
    } catch (error: any) {
      toast.error(error?.response?.data?.message);
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (index: number) => {
    if (onEdit.includes(index)) {
      setOnEdit((prev) => prev.filter((value) => value !== index));
    } else {
      setOnEdit((prev) => [...prev, index]);
    }
  };

  const handleUpdateContent = (guidelineIndex: number, contentIndex: number, value: string) => {
    const pg = cloneDeep(productGuidelines);
    pg[guidelineIndex].productGuideline[contentIndex].value = value;
    setProductGuidelines(pg);
  };

  const handleRemove = (guidelineIndex: number, contentIndex: number) => {
    const pg = cloneDeep(productGuidelines);
    pg[guidelineIndex].productGuideline.splice(contentIndex, 1);
    setProductGuidelines(pg);
  };

  const handleAddListItem = (guidelineIndex: number, contentIndex: number) => {
    const pg = cloneDeep(productGuidelines);
    const oldContent = [...(pg[guidelineIndex].productGuideline[contentIndex].value as IGuidelineContent[])];
    const newListItem: IGuidelineContent = {
      value: "",
      type: contentType.LIST,
      tag: "",
    };

    pg[guidelineIndex].productGuideline[contentIndex].value = [...oldContent, newListItem];
    setProductGuidelines(pg);
  };

  const handleUpdateListItemHeader = (guidelineIndex: number, contentIndex: number, value: string) => {
    const pg = cloneDeep(productGuidelines);
    pg[guidelineIndex].productGuideline[contentIndex].label = value;
    setProductGuidelines(pg);
  };

  const handleUpdateListItem = (guidelineIndex: number, contentIndex: number, listIndex: number, value: string) => {
    const pg = cloneDeep(productGuidelines);
    const oldContent = [...(pg[guidelineIndex].productGuideline[contentIndex].value as IGuidelineContent[])];
    oldContent[listIndex].value = value;
    pg[guidelineIndex].productGuideline[contentIndex].value = [...oldContent];
    setProductGuidelines(pg);
  };

  const handleRemoveListItem = (guidelineIndex: number, contentIndex: number, listIndex: number) => {
    const pg = cloneDeep(productGuidelines);
    const oldContent = [...(pg[guidelineIndex].productGuideline[contentIndex].value as IGuidelineContent[])];
    oldContent.splice(listIndex, 1);
    pg[guidelineIndex].productGuideline[contentIndex].value = [...oldContent];
    setProductGuidelines(pg);
  };

  const handleUpdateTable = (value: IGuidelineContentTable, guidelineIndex: number, contentIndex: number) => {
    const pg = cloneDeep(productGuidelines);
    pg[guidelineIndex].productGuideline[contentIndex].value = value;
    setProductGuidelines(() => pg);
  };

  const handleTagContent = (guidelineIndex: number, contentIndex: number) => {
    setTagType(contentType.TEXTFIELD);
    setTaggedGuidelineIndex(guidelineIndex);
    setTaggedContentIndex(contentIndex);
    toggleTagModal();
  };

  const handleTagListItem = (guidelineIndex: number, contentIndex: number, listIndex: number) => {
    setTagType(contentType.LIST);
    setTaggedGuidelineIndex(guidelineIndex);
    setTaggedContentIndex(contentIndex);
    setTaggedListIndex(listIndex);
    toggleTagModal();
  };

  const handleUpdateTagContent = (tag: string) => {
    const pg = cloneDeep(productGuidelines);
    pg[taggedGuidelineIndex].productGuideline[taggedContentIndex].tag = tag;
    setProductGuidelines(pg);
  };

  const handleUpdateTagListItem = (tag: string) => {
    const pg = cloneDeep(productGuidelines);
    const oldList = [...(pg[taggedGuidelineIndex].productGuideline[taggedContentIndex].value as IGuidelineContent[])];
    oldList[taggedListIndex].tag = tag;
    pg[taggedGuidelineIndex].productGuideline[taggedContentIndex].value = [...oldList];
    setProductGuidelines(pg);
  };

  const handleSaveTag = () => {
    if (tagType === contentType.LIST) {
      handleUpdateTagListItem(tag);
    } else if (tagType === contentType.TEXTFIELD) {
      handleUpdateTagContent(tag);
    }
    toggleTagModal();
  };

  const handleAppliedGuidelines = (guidelines: string[]) => {
    const pg = cloneDeep(productGuidelines);

    const newGuidelines: IGuideline[] = guidelines.map((guidelines) => {
      let guidelineLength = pg.length;

      return {
        label: guidelines,
        sequence: guidelineLength++,
        productGuideline: [],
        editable: true, // default
      };
    });

    pg.push(...newGuidelines);

    setProductGuidelines(pg);
  };

  const handleAddContent = (selectedContentType: string) => {
    if (selectedGuidelineIndex === undefined) {
      toast.info("Please select a guideline to add content");
      return;
    }

    const defaultTableContentValue: IGuidelineContentTable = {
      columns: [
        {
          id: "col-0",
          value: "",
          type: "string",
          tag: "",
        },
        {
          id: "col-1",
          value: "",
          type: "string",
          tag: "",
        },
      ],
      rows: [
        [
          {
            id: "row-0",
            value: "",
            type: "string",
            tag: "",
          },
          {
            id: "row-1",
            value: "",
            type: "string",
            tag: "",
          },
        ],
      ],
    };

    let content: IGuidelineContent = {
      type: selectedContentType,
      value: "",
      tag: "",
    };

    if (selectedContentType === "list") {
      content = {
        type: selectedContentType,
        label: "",
        value: [],
        tag: "",
      };
    }

    if (selectedContentType === "table") {
      content = {
        type: selectedContentType,
        value: defaultTableContentValue,
        tag: "",
      };
    }

    // Add the new content to the selected guideline
    const pg = cloneDeep(productGuidelines);
    pg[selectedGuidelineIndex].productGuideline.push(content);
    setProductGuidelines(pg);
    setSelectedGuidelineIndex(undefined);
  };

  const handleGuidelineContentIndex = (index: number) => {
    setSelectedGuidelineIndex(index);
    toggleAddContent();
  };

  return (
    <Fragment>
      {addTag && (
        <Modal title="Manage Tag" titleClass="flex flex-1 text-lg font-semibold" isOpen={addTag} onClose={toggleTagModal} modalContainerClassName="!max-w-2xl">
          <TextField value={""} onChange={(e) => setTag(e.target.value)} placeholder="Enter Tag" size="sm" />
          <div className="flex flex-1 flex-row mt-2 justify-end">
            <button type="button" className="btn btn-sm btn-primary min-w-32" onClick={handleSaveTag}>
              Save Tag
              <FaTag />
            </button>
          </div>
        </Modal>
      )}
      {addGuidelines && (
        <Modal title="Add Content" titleClass="flex flex-1 text-lg font-semibold" isOpen={addGuidelines} onClose={toggleAddGuidelines} modalContainerClassName="!max-w-6xl">
          <div className="flex flex-1 flex-col">
            <GuidelineList handleModal={toggleAddGuidelines} handleAppliedGuidelines={handleAppliedGuidelines} preSelectedGuidelines={preSelectedGuidelines} />
          </div>
        </Modal>
      )}
      {addContent && (
        <Modal title="Select Content Type" titleClass="flex flex-1 text-lg font-semibold" isOpen={addContent} onClose={toggleAddContent} modalContainerClassName="!max-w-3xl">
          <SelectContentTypeForm handleModal={toggleAddContent} handleSelectContent={handleAddContent} />
        </Modal>
      )}
      <div className="flex flex-1 flex-col p-2 space-y-4">
        {revisionDetails && (
          <Fragment>
            {proposedProduct?.status !== ProposalStatus.approved && (
              <Fragment>
                <div className="flex flex-1 flex-col justify-between mt-2">
                  <Typography className="text-accent !text-xl">EDIT SELECTED PROVISION</Typography>
                  <Typography>Edit selected provision's editable sections and save</Typography>
                </div>
                <div className="divider"></div>
              </Fragment>
            )}

            <div className="flex flex-1 flex-row justify-between mt-2">
              <Typography className="font-poppins-semibold" size="xl">
                {revisionDetails.product?.name ?? "No Product Name Set"}
              </Typography>
              {proposedProduct?.status !== ProposalStatus.approved && (
                <button className="btn bg-ghost text-accent btn-sm px-6" onClick={handleChange}>
                  <FaRedo />
                  Change Product
                </button>
              )}
            </div>
            <div className={`flex flex-1 flex-col  overflow-y-auto max-h-[400px] min-h-[${proposedProduct?.status !== ProposalStatus.approved ? "400px" : "320px"}]`}>
              <div className="flex flex-1 flex-col justify-start">
                <Typography className="font-poppins-semibold">Product Description</Typography>

                {revisionDetails.product?.description === null || revisionDetails.product?.description === "" ? (
                  <Typography className="ml-4 mt-4">No Product Description Set</Typography>
                ) : (
                  <div className="ml-4 mt-4" dangerouslySetInnerHTML={{ __html: revisionDetails.product?.description ?? "" }} />
                )}
              </div>
              <div className="flex flex-1 flex-col justify-start mt-10">
                {productGuidelines?.map((value, gIndex) => {
                  const isEditable = value.editable == 1 ? true : false;
                  return (
                    <div key={`guideline-${gIndex}`} className="flex flex-1 flex-col mb-10">
                      <div className="flex flex-row items-center gap-2">
                        <Typography className="text-[18px] mb-1 font-poppins-semibold">{value.label}</Typography>
                        {isEditable && !onEdit.includes(gIndex) && (
                          <Tooltip text="Toggle edit mode on">
                            <button className="btn btn-sm btn-ghost -mt-1" onClick={() => handleEdit(gIndex)}>
                              <TbEdit className="text-zinc-500" size={18} />
                            </button>
                          </Tooltip>
                        )}
                        {isEditable && onEdit.includes(gIndex) && (
                          <Tooltip text="Toggle edit mode off">
                            <button className="btn btn-sm btn-ghost -mt-1" onClick={() => handleEdit(gIndex)}>
                              <TbEditOff className="text-zinc-500" size={18} />
                            </button>
                          </Tooltip>
                        )}
                      </div>
                      {value.productGuideline.map((pgValue, pgIndex) => {
                        let listValue;
                        let tableValue;
                        if (pgValue.type === contentType.LIST) {
                          listValue = pgValue.value as IGuidelineContent[];
                        }

                        if (pgValue.type === contentType.TABLE) {
                          tableValue = pgValue.value as IGuidelineContentTable;
                        }

                        return (
                          <div key={`pg-${pgIndex}`}>
                            {pgValue.type === contentType.TEXTFIELD && (
                              <Fragment>
                                {!onEdit.includes(gIndex) && <Typography className="ml-4 mt-4 text-justify">{pgValue.value as string}</Typography>}
                                {isEditable && onEdit.includes(gIndex) && (
                                  <div className="ml-4 mt-4 mx-6 px-2 flex flex-row group">
                                    <TextField
                                      onChange={(e) => handleUpdateContent(gIndex, pgIndex, e.target.value)}
                                      value={pgValue.value as string}
                                      size="sm"
                                      className="focus-within:border-none focus-within:!w-full  outline-none border-t-0 border-x-0 border-b-2 rounded-none border-zinc-400"
                                      rightIcon={
                                        <Tooltip text="Add tag">
                                          <button onClick={() => handleTagContent(gIndex, pgIndex)} className={`btn btn-xs btn-circle ${pgValue.tag !== "" ? "text-success" : ""}`}>
                                            <FaTag />
                                          </button>
                                        </Tooltip>
                                      }
                                    />
                                    <Tooltip text="Remove?">
                                      <button onClick={() => handleRemove(gIndex, pgIndex)} className="btn btn-sm btn-ghost hover:bg-white">
                                        <FaMinusCircle className="text-zinc-500 hover:text-danger" />
                                      </button>
                                    </Tooltip>
                                  </div>
                                )}
                              </Fragment>
                            )}
                            {pgValue.type === contentType.LIST && (
                              <Fragment>
                                {!onEdit.includes(gIndex) && <Typography className="ml-4 mt-4 text-justify">{pgValue.label}</Typography>}
                                {isEditable && onEdit.includes(gIndex) && (
                                  <div className="ml-4 mt-4 mx-6 px-2 flex flex-row group">
                                    <TextField
                                      onChange={(e) => handleUpdateListItemHeader(gIndex, pgIndex, e.target.value)}
                                      value={pgValue.label as string}
                                      size="sm"
                                      className="focus-within:border-none focus-within:!w-full  outline-none border-t-0 border-x-0 border-b-2 rounded-none border-zinc-400"
                                    />
                                    <Tooltip text="Remove?">
                                      <button onClick={() => handleRemove(gIndex, pgIndex)} className="btn btn-sm btn-ghost hover:bg-white">
                                        <FaMinusCircle className="text-zinc-500 hover:text-danger" />
                                      </button>
                                    </Tooltip>
                                  </div>
                                )}
                                <ul className={`${isEditable ? "pb-2" : ""} list-disc ml-12`}>
                                  {listValue &&
                                    listValue.map((listValue, listIndex) => {
                                      return isEditable && onEdit.includes(gIndex) ? (
                                        <li key={`listItem-${listIndex}`} className="mt-4 mx-6 px-2 -ml-2 flex flex-row items-center group">
                                          <FaDotCircle className="mr-2" size={12} />
                                          <TextField
                                            onChange={(e) => handleUpdateListItem(gIndex, pgIndex, listIndex, e.target.value)}
                                            value={listValue.value as string}
                                            size="sm"
                                            className="focus-within:border-none focus-within:!w-full outline-none border-t-0 border-x-0 border-b-2 rounded-none border-zinc-400"
                                            rightIcon={
                                              <Tooltip text={`${listValue.tag !== "" ? "Tag: " + listValue.tag : "Add Tag"}`}>
                                                <button
                                                  type="button"
                                                  className={`btn btn-xs btn-circle ${listValue.tag !== "" ? "text-success" : ""}`}
                                                  onClick={() => handleTagListItem(gIndex, pgIndex, listIndex)}
                                                >
                                                  <FaTag />
                                                </button>
                                              </Tooltip>
                                            }
                                          />
                                          <Tooltip text="Remove?">
                                            <button onClick={() => handleRemoveListItem(gIndex, pgIndex, listIndex)} className="btn btn-sm btn-ghost hover:bg-white">
                                              <FaMinusCircle className="text-zinc-500 hover:text-danger" />
                                            </button>
                                          </Tooltip>
                                        </li>
                                      ) : (
                                        <li key={`listItem-${listIndex}`} className="mt-4">
                                          <Typography className="text-justify">{listValue.value as string}</Typography>
                                        </li>
                                      );
                                    })}
                                </ul>
                                {isEditable && onEdit.includes(gIndex) && (
                                  <div className="flex flex-1 items-center justify-center mb-4">
                                    <Tooltip text="Add new list item?">
                                      <button onClick={() => handleAddListItem(gIndex, pgIndex)} className="btn btn-sm btn-ghost hover:bg-white">
                                        <FaPlusCircle className="text-zinc-500 hover:text-success" />
                                      </button>
                                    </Tooltip>
                                  </div>
                                )}
                              </Fragment>
                            )}
                            {pgValue.type === contentType.TEXTEDITOR && (
                              <Fragment>
                                {!onEdit.includes(gIndex) && (
                                  <div className="text-justify text-wrap px-8">
                                    <div
                                      className="ml-2 mt-2"
                                      dangerouslySetInnerHTML={{
                                        __html: (pgValue as any).value ?? "",
                                      }}
                                    ></div>
                                  </div>
                                )}
                                {isEditable && onEdit.includes(gIndex) && (
                                  <div className="text-justify text-wrap px-8 flex flex-row group">
                                    <Wysiwyg onChange={(content) => handleUpdateContent(gIndex, pgIndex, content)} className="my-4 mb-8 group-focus:border-2" stateValue={pgValue.value as string} />
                                    <Tooltip text="Remove?">
                                      <button onClick={() => handleRemove(gIndex, pgIndex)} className="btn btn-sm btn-ghost hover:bg-white mt-2">
                                        <FaMinusCircle className="text-zinc-500 hover:text-danger" />
                                      </button>
                                    </Tooltip>
                                  </div>
                                )}
                              </Fragment>
                            )}
                            {pgValue.type === contentType.TABLE && (
                              <Fragment>
                                {!onEdit.includes(gIndex) && (
                                  <div className="flex flex-1 mt-10 mx-6 overflow-x-scroll">
                                    <table className="table border-[1px]">
                                      <thead className="table-header-group">
                                        <tr>
                                          {tableValue?.columns?.map((cValue, cIndex) => {
                                            return (
                                              <td key={`col-${cIndex}`} className="table-cell border-[1px]">
                                                <Typography className="font-semibold text-xs">{cValue.value as string}</Typography>
                                              </td>
                                            );
                                          })}
                                        </tr>
                                      </thead>
                                      <tbody>
                                        {tableValue?.rows?.map((rValue, rIndex) => {
                                          return (
                                            <tr key={`row-${rIndex}`}>
                                              {rValue.map((cell, cellIndex) => {
                                                return (
                                                  <td className="border-[1px] text-xs" key={`cell-${cellIndex}`}>
                                                    <Typography>{cell.value as string}</Typography>
                                                  </td>
                                                );
                                              })}
                                            </tr>
                                          );
                                        })}
                                      </tbody>
                                    </table>
                                  </div>
                                )}
                                {isEditable && onEdit.includes(gIndex) && (
                                  <div className="flex flex-1 flex-row mt-10 mx-6 pl-2">
                                    <SimpleTable pgIndex={pgIndex} guidelineIndex={gIndex} tableState={pgValue.value as IGuidelineContentTable} handleTable={handleUpdateTable} />
                                    <Tooltip text="Remove?">
                                      <button onClick={() => handleRemove(gIndex, pgIndex)} className="btn btn-sm btn-ghost hover:bg-white group-focus-within:hidden">
                                        <FaMinusCircle className="text-zinc-500 hover:text-danger" />
                                      </button>
                                    </Tooltip>
                                  </div>
                                )}
                              </Fragment>
                            )}
                          </div>
                        );
                      })}
                      {isEditable && onEdit.includes(gIndex) && (
                        <div className="flex flex-1 items-end justify-end mb-8 -mt-4 mr-4">
                          <button className="btn btn-ghost btn-sm mt-12 border-primary" onClick={() => handleGuidelineContentIndex(gIndex)}>
                            Add Content
                            <FaPlusCircle />
                          </button>
                        </div>
                      )}
                    </div>
                  );
                })}
                <div className="flex flex-1 items-center justify-center mb-8 -mt-8">
                  <button className="btn btn-ghost mt-12 border-primary" onClick={toggleAddGuidelines}>
                    Add Guidelines
                    <FaPlusCircle />
                  </button>
                </div>
              </div>
              <div className="flex flex-1 flex-col justify-start">
                {revisionDetails?.commission && (
                  <Fragment>
                    <Typography className="font-poppins-semibold" size="md">
                      Commission Structure
                    </Typography>
                    <Fragment>
                      <Typography size="sm" className="ml-4 mt-4">
                        {parseFloat(revisionDetails?.commission?.maximumDisposableRate ?? "").toFixed(2)}% Maximum Disposable Commission - Standard Rate
                      </Typography>
                      <div className="flex-flex-1 mt-6 mx-6 overflow-x-scroll">
                        <table className="table overflow-scroll">
                          <thead>
                            <tr>
                              <td className="table-cell border-[1px] text-center text-xs">Type</td>
                              <td className="table-cell border-[1px] text-center text-xs">Age Type</td>
                              {standard.length > 0 && (
                                <Fragment>
                                  <td className="table-cell border-[1px] text-center text-xs">Age From</td>
                                  <td className="table-cell border-[1px] text-center text-xs">Age To</td>
                                </Fragment>
                              )}
                              <td className="table-cell border-[1px] text-center text-xs">Rate</td>
                            </tr>
                          </thead>
                          <tbody>
                            {revisionDetails?.commission.commissionDetails?.map((rowValue, rowIndex) => {
                              return (
                                <tr key={`commissionDetailsRow-${rowIndex}`}>
                                  <td className="table-cell border-[1px] text-xs">{rowValue?.commissionType?.commissionName}</td>
                                  <td className="table-cell border-[1px] text-xs">{rowValue?.commissionAgeType?.name}</td>
                                  {standard.length > 0 && (
                                    <Fragment>
                                      <td className="table-cell border-[1px] text-center text-xs">{rowValue.ageFrom}</td>
                                      <td className="table-cell border-[1px] text-center text-xs">{rowValue.ageTo}</td>
                                    </Fragment>
                                  )}
                                  <td className="table-cell border-[1px] text-center text-xs">{rowValue.rate ? parseFloat(rowValue.rate.toString()).toFixed(0) : ""}%</td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    </Fragment>
                  </Fragment>
                )}
              </div>
              <div className="flex flex-1 flex-col justify-start">
                <Typography className="font-poppins-semibold mt-4">Added Benefits for the Cooperative</Typography>
                <div className="flex flex-1 flex-row items-center">
                  <Typography className="mt-4 ml-4">Management Fee - {managementPercentFee}%</Typography>
                  {proposedProduct?.status !== ProposalStatus.approved && (
                    <Tooltip text="Edit Management Fee">
                      <button className="btn btn-sm rounded-full mt-4 ml-4" onClick={toggleManagementFee}>
                        <FaPen size={15} />
                      </button>
                    </Tooltip>
                  )}
                </div>
              </div>
            </div>

            <div className="flex flex-1 flex-row justify-end">
              {proposedProduct?.status !== ProposalStatus.approved && (
                <LoadingButton isLoading={submitting} onClick={handleCancel} className="btn rounded-lg hover:bg-slate-200 bg-white !text-primary !w-32 mr-4 mt-4" type="submit">
                  Cancel
                </LoadingButton>
              )}
              <LoadingButton isLoading={submitting} onClick={handleSave} className="btn rounded-lg bg-accent text-white !w-32 mr-4 mt-4" type="button">
                Save
              </LoadingButton>
            </div>
          </Fragment>
        )}
      </div>
    </Fragment>
  );
};

export default EditSelectProductDetails;
