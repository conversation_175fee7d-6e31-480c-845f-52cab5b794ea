import Modal from "@components/common/Modal";
import Typography from "@components/common/Typography";
import { FC, Fragment, useEffect, useState } from "react";
import { FaFileAlt, FaPercent } from "react-icons/fa";
import { FaFilePen } from "react-icons/fa6";
import SelectStandardGuidelinesForm from "../Forms/SelectStandardGuidelinesForm";
import SelectedProductDetails from "../SelectedProductDetails";
import { RootState } from "@state/store";
import { useSelector } from "react-redux";
import TextField from "@components/form/TextField";
import { toast } from "react-toastify";
import { useProductProposalActions } from "@state/reducer/product-proposal";
import { ProposalStatus } from "@enums/proposal-status";
import { IoIosPaper } from "react-icons/io";
import { IoNewspaper } from "react-icons/io5";
import EditSelectProductDetails from "../EditSelectedProductDetails";
import SelectAERDetailsForm from "@modules/admin/product-proposal/Components/Forms/SelectAERDetailsForm";
import { useQuotationActions } from "@state/reducer/quotations";
import { useLocation, useParams } from "react-router-dom";
import { getQuotationByIdService } from "@services/quotation/quotation.service";
import SelectedAERDetails from "@modules/admin/product-proposal/Components/SelectedAERDetails";
import { ProposableTypes, ProposalTypes } from "@enums/enums";
import { useProductActions } from "@state/reducer/products";
import Switch from "@components/switch";
import Select from "@components/form/Select";
import { ProductCode } from "@enums/product-code";

enum CustomizeType {
  PROVISIONS = "provisions",
  AER = "AER",
}

type TStep1Props = {
  handleSaveAsDraft?: () => void;
};

const Step1: FC<TStep1Props> = ({ handleSaveAsDraft }) => {
  const [standardModal, setStandardModal] = useState<boolean>(false);
  const toggleStandardModal = () => setStandardModal(!standardModal);
  const [customizeModal, setCustomizeModal] = useState<boolean>(false);
  const toggleCustomizeModal = () => setCustomizeModal(!customizeModal);
  const [feeModal, setFeeModal] = useState<boolean>(false);
  const toggleFeeModal = () => setFeeModal(!feeModal);
  const proposedProduct = useSelector((state: RootState) => state.productProposal?.proposedProduct);
  const revisionDetails = useSelector((state: RootState) => state.products?.revisionDetails);
  const managementPercentFee = useSelector((state: RootState) => state.productProposal?.managementPercentFee);
  const withRebates = useSelector((state: RootState) => state.productProposal?.withRebates);
  const paymentMethod = useSelector((state: RootState) => state.productProposal?.paymentMethod);
  const { setStep, setManagementFee, setCustomType, setWithRebates, setPaymentMethod } = useProductProposalActions();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [selectedCustomization, setSelectedCustomization] = useState<string>("");
  const [isEditProvisions, setIsEditProvisions] = useState<boolean>(false);
  const toggleIsEditProvisions = () => setIsEditProvisions(!isEditProvisions);
  const [aerModal, setAerModal] = useState<boolean>(false);
  const toggleAerModal = () => setAerModal(!aerModal);
  const { setApprovedAER } = useQuotationActions();
  const approvedAER = useSelector((state: RootState) => state.quotation?.quotations);
  const [proposalId, setProposalId] = useState<number | undefined>(undefined);
  const [hasSelectedAER, setHasSelectedAER] = useState(false);
  const { id: aerIdFromParams } = useParams();
  const location = useLocation();
  const [mode, setMode] = useState<"create" | "edit">("create");
  const { resetRevisionDetails } = useProductActions();

  const isEditMode = !!(location.state as any)?.proposal;

  const paymentMethodOptions = [
    { text: "Select Payment Method", value: "", disabled: true },
    { text: "Annually", value: "annually" },
    { text: "Monthly", value: "monthly" },
    { text: "Semi-Annually", value: "semi-annually" },
  ];

  const handleToggleStandardModal = () => {
    setCustomType(undefined);
    toggleStandardModal();
    if (revisionDetails !== undefined) {
      toggleFeeModal();
    }
  };

  const handleStandardModalClose = () => {
    setCustomType(undefined);
    resetRevisionDetails();
    toggleStandardModal();
  };

  const handleToggleCustomizeModal = () => {
    toggleCustomizeModal();
    if (selectedCustomization !== "") {
      setSelectedCustomization("");
    }
  };

  const handleAERSelected = (aerData: any) => {
    setCustomType(CustomizeType.AER);
    setApprovedAER(aerData);
    setHasSelectedAER(true);
    setAerModal(false); // Close the modal
  };

  const handleContinueCustomize = () => {
    setCustomType(selectedCustomization);
    if (selectedCustomization === CustomizeType.PROVISIONS) {
      toggleCustomizeModal();
      toggleStandardModal();
    } else if (selectedCustomization === CustomizeType.AER) {
      toggleCustomizeModal();
      toggleAerModal();
    }
  };

  const handleStepAER = () => {
    if (approvedAER === null) {
      toast.error("Please select an AER");
      return;
    }
    setStep(2);
  };

  const handleChangeAER = () => {
    toggleAerModal();
  };

  const handleChangeProduct = () => {
    // setCustomType(undefined);
    resetRevisionDetails();
    setIsEditProvisions(false);
    toggleStandardModal();
  };

  const handleChange = () => {
    // toggleStandardModal();
    // handleToggleStandardModal();
    handleChangeProduct();
  };

  const handleCheckManagementPercentFee = () => {
    if (proposedProduct?.status === ProposalStatus.approved) {
      return true;
    }
    const fee = parseFloat((managementPercentFee ?? 0).toString());

    if (fee < 0) {
      toast.error("Please enter a valid management fee");
      return false;
    }

    return true;
  };

  const handleSetManagementFee = (fee: number) => {
    const managementFee = parseFloat((fee ?? 0).toString());

    if (fee < 0) {
      toast.error("Please enter a valid management fee");
      return;
    }

    setManagementFee(managementFee);
  };

  const handleStep = () => {
    if (!handleCheckManagementPercentFee()) {
      return;
    }

    setStep(1);
  };

  const handleSaveManagementFee = () => {
    const fee = parseFloat((managementPercentFee ?? 0).toString());
    if (isNaN(fee) || fee < 0) {
      toast.error("Please enter a valid management fee");
      setManagementFee(0);
      return;
    }

    setManagementFee(fee);
    toggleFeeModal();
  };

  const handleCancelManagementFee = () => {
    toggleFeeModal();
    setManagementFee(0);
    setWithRebates(false);
    setPaymentMethod("");
  };

  const handleWithRebates = (value: boolean) => {
    setWithRebates(value);
  };
  const handleSetPaymentMethod = (value: string) => {
    setPaymentMethod(value);
  };
  //senior's code
  // const handleSave = async () => {
  //   try {
  //     setSubmitting(true);

  //     if (managementPercentFee === undefined || managementPercentFee < 0) {
  //       toast.error("Please enter a valid management fee");
  //       return;
  //     }

  //     handleSaveAsDraft && handleSaveAsDraft();
  //   } catch (error: any) {
  //     toast.error(error?.response?.data?.message);
  //   } finally {
  //     setSubmitting(false);
  //   }
  // };
  const handleSave = async () => {
    try {
      if (managementPercentFee === undefined || managementPercentFee < 0) {
        toast.error("Please enter a valid management fee");
        return;
      }
      if (handleSaveAsDraft) {
        await handleSaveAsDraft();
      }
    } catch (error: any) {
      toast.error(error?.response?.data?.message);
    }
  };

  useEffect(() => {
    const locationState = location.state as {
      proposal?: {
        proposalType?: string;
        proposableType?: string;
        proposable?: { quotationId?: number };
      };
      proposalId?: number;
      cameFromStep3WithAER?: boolean;
    };

    const proposal = locationState?.proposal;
    const isEditRoute = Boolean(aerIdFromParams || locationState?.proposalId);

    // Check if it's a customized proposal
    const isCustomized = proposal?.proposalType === ProposalTypes.CUSTOMIZED;

    // Check if it's specifically an AER (not PRODUCT_REVISION)
    const isAER = proposal?.proposableType === ProposableTypes.AER;

    // Only proceed with AER fetch if it's CUSTOMIZED + AER
    if (!isEditRoute || !isCustomized || !isAER) {
      setMode("create");
      setHasSelectedAER(false);
      return;
    }

    // This code only runs for CUSTOMIZED AER proposals
    const fetchAERById = async () => {
      let lookupId = aerIdFromParams;
      const quotationIdFromState = proposal?.proposable?.quotationId;
      if (!lookupId && quotationIdFromState) {
        lookupId = String(quotationIdFromState);
      }

      if (!lookupId) {
        toast.error("No AER/Quotation ID found");
        return;
      }

      try {
        const { data } = await getQuotationByIdService(lookupId);

        if (Array.isArray(data) && data.length > 0) {
          const aer = data[0];
          setApprovedAER(aer);
          setCustomType("AER");
          setHasSelectedAER(true);
          setMode("edit");
          setProposalId(locationState?.proposalId);
        } else {
          toast.error("No AER found for this ID");
        }
      } catch (error) {
        toast.error("Failed to fetch AER by ID");
      }
    };

    fetchAERById();
  }, [aerIdFromParams, location.state]);
  useEffect(() => {
    const cameFromStep3 = sessionStorage.getItem("cameFromStep3WithAER");
    if (cameFromStep3 === "true" && approvedAER) {
      setHasSelectedAER(true);
      sessionStorage.removeItem("cameFromStep3WithAER");
    }
  }, []);

  return (
    <Fragment>
      {standardModal && (
        <Modal isOpen={standardModal} onClose={handleStandardModalClose} modalContainerClassName="!max-w-4xl" title="Select Product Guidelines">
          <SelectStandardGuidelinesForm toggleModal={toggleStandardModal} toggleFeeModal={toggleFeeModal} />
        </Modal>
      )}
      {customizeModal && (
        <Modal isOpen={customizeModal} onClose={handleToggleCustomizeModal} modalContainerClassName="!max-w-2xl" justifyContent="justify-end">
          <div className="flex flex-col items-center justify-center">
            <Typography size="3xl" className="mb-10">
              Customize Type
            </Typography>
            <Typography>How would like to customize this proposal?</Typography>
            <div className="flex flex-row justify-between gap-2 mt-8">
              <div
                onClick={() => setSelectedCustomization(CustomizeType.PROVISIONS)}
                className={`                                   
                                    group
                                    card flex flex-col items-center gap-2 
                                    border-2 min-w-[250px] 
                                    hover:cursor-pointer hover:border-primary  
                                     ${selectedCustomization === "provisions" ? "border-primary" : "border-zinc-100"}
                                `}
              >
                <IoIosPaper className="text-primary" size={50} />
                <Typography className="text-primary">Product Provisions</Typography>
              </div>
              <div
                onClick={() => setSelectedCustomization(CustomizeType.AER)}
                className={`                                   
                                    group
                                    card flex flex-col items-center gap-2 
                                    border-2 min-w-[250px] 
                                    hover:cursor-pointer hover:border-primary  
                                     ${selectedCustomization === "AER" ? "border-primary" : "border-zinc-100"}
                                `}
              >
                <IoNewspaper className="text-primary" size={50} />
                <Typography className="text-primary">AER</Typography>
              </div>
            </div>
            <div className="mt-8 flex flex-row w-full items-center justify-center px-10">
              <button onClick={handleContinueCustomize} className="btn bg-primary-dark text-white w-full hover:bg-primary" disabled={selectedCustomization === ""}>
                Continue
              </button>
            </div>
          </div>
        </Modal>
      )}
      {aerModal && (
        <Modal isOpen={aerModal} onClose={toggleAerModal} modalContainerClassName="!max-w-4xl" title="Select Approved AER">
          <SelectAERDetailsForm toggleModal={toggleAerModal} onAERSelected={handleAERSelected} />
        </Modal>
      )}
      {feeModal && (
        <Modal isOpen={feeModal} onClose={toggleFeeModal} modalContainerClassName="!max-w-xl" title="Coop Management Fee">
          <div>
            <Typography>Enter Coop Management Fee</Typography>
            <TextField value={managementPercentFee} onChange={(e) => handleSetManagementFee(parseFloat(e.target.value))} type="number" size="md" className="mt-2" rightIcon={<FaPercent />} />

            <Typography className="mt-4">Select Payment Method</Typography>
            <Select
              variant="primary"
              className="mt-2"
              options={paymentMethodOptions}
              value={paymentMethod}
              onChange={(e) => handleSetPaymentMethod(e.target.value)}
              placeholder="Select Payment Method"
            />

            {revisionDetails?.product?.productCode === ProductCode.CLPP && (
              <div className="mt-4">
                <p>With Rebates</p>
                <div className="mt-2">
                  {" "}
                  <Switch checked={withRebates} onChange={handleWithRebates} />
                </div>
              </div>
            )}
            <div className="flex flex-1 flex-row mt-8 justify-end space-x-2">
              <button onClick={handleCancelManagementFee} className="btn bg-outline bg-zinc-300 min-w-32">
                Cancel
              </button>
              <button onClick={handleSaveManagementFee} className=" btn rounded-lg  bg-info hover:bg-info/75 text-white min-w-32">
                Save
              </button>
            </div>
          </div>
        </Modal>
      )}
      {!isEditMode && revisionDetails === undefined && !hasSelectedAER && (
        <Fragment>
          <div className="flex flex-1 justify-center mt-72">
            <div className="flex flex-row justify-center justify-items-center w-3/4">
              <div className="flex flex-1 flex-col items-center">
                <div className="flex flex-1 flex-col shadow-md shadow-accent border-[1px] border-zinc-500 w-3/4 rounded-lg p-4 pt-10 justify-center items-center">
                  <FaFileAlt className="text-5xl text-accent" />
                  <Typography className="mt-8" size="xl">
                    Standard
                  </Typography>
                  <Typography className="text-center mt-4">create a standardized process for your product proposal</Typography>
                  <div className="mt-5">
                    {/* <button onClick={toggleStandardModal} className="btn bg-accent text-white min-w-32"> */}
                    <button onClick={handleToggleStandardModal} className="btn bg-accent text-white min-w-32">
                      Select
                    </button>
                  </div>
                </div>
              </div>
              <div className="flex flex-1 flex-col items-center">
                <div className="flex flex-1 flex-col shadow-md shadow-accent border-[1px] border-zinc-500 w-3/4 rounded-lg p-4 pt-10 justify-center items-center">
                  <FaFilePen className="text-5xl text-accent" />
                  <Typography className="mt-8" size="xl">
                    Customize
                  </Typography>
                  <Typography className="text-center mt-4">create a product proposal with credible and comprehensive actuarial data</Typography>
                  <div className="mt-5">
                    <button onClick={() => toggleCustomizeModal()} className="btn bg-accent text-white min-w-32">
                      Select
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Fragment>
      )}
      {revisionDetails !== undefined && !standardModal && !feeModal && !isEditProvisions && (
        <div className="flex flex-1 flex-col p-2 mt-24">
          <SelectedProductDetails
            setStep={handleStep}
            handleChange={handleChange}
            handleSaveAsDraft={handleSave}
            toggleManagementFee={toggleFeeModal}
            submitting={submitting}
            setSubmitting={setSubmitting}
            toggleIsEditProvisions={toggleIsEditProvisions}
          />
        </div>
      )}
      {revisionDetails !== undefined && isEditProvisions && (
        <div className="flex flex-1 flex-col p-2 mt-24">
          <EditSelectProductDetails setStep={handleStep} handleChange={handleChange} toggleManagementFee={toggleFeeModal} submitting={submitting} toggleIsEditProvisions={toggleIsEditProvisions} />
        </div>
      )}
      {!aerModal && hasSelectedAER && approvedAER && mode && <SelectedAERDetails setStep={handleStepAER} handleChange={handleChangeAER} submitting={submitting} mode={mode} proposalId={proposalId} />}
    </Fragment>
  );
};

export default Step1;
