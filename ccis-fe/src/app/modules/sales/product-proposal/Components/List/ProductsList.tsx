import Typography from "@components/common/Typography";
import Loader from "@components/Loader";
import { RevisionStatus } from "@enums/revision-status";
import { IProduct, IProductRevisions } from "@interface/products.interface";
import { useProductActions } from "@state/reducer/products";
import { RootState } from "@state/store";
import { FC, MouseEvent, useState } from "react";
import { useSelector } from "react-redux";
import { useEffect } from "react";
type TProps = {
  loadingRevDetails?: (value: boolean) => void;
};
const ProductsList: FC<TProps> = ({ loadingRevDetails }) => {
  const { getRevisionDetails } = useProductActions();
  const [selectedProduct, setSelectedProduct] = useState<number | null>(null);
  const [selectedRevision, setSelectedRevision] = useState<number | null>(null);
  const { data, loading } = useSelector((state: RootState) => state.products.getProducts);
  const loadingRev = useSelector((state: RootState) => state.products.getRevisionDetails.loading);
  const products = data?.data;

  const handleSelectedProduct = (productIndex: number) => {
    if (selectedProduct === productIndex) {
      setSelectedProduct(null);
      return;
    }
    setSelectedRevision(null);
    setSelectedProduct(() => productIndex);
  };

  const handleSelectedProductRevision = (e: MouseEvent, revisionIndex: number, productRevision: IProductRevisions) => {
    e.stopPropagation();
    setSelectedRevision(revisionIndex);
    getRevisionDetails({ productid: productRevision.productId, revisionid: productRevision.id });
  };
  useEffect(() => {
    if (loadingRevDetails) {
      loadingRevDetails(loadingRev);
    }
  }, [loadingRev, loadingRevDetails]);
  return (
    <div className="flex flex-1 flex-col items-start justify-center pr-2">
      {loading && (
        <div className="flex flex-1 w-full items-center justify-center">
          <Loader />
        </div>
      )}
      {!loading && products?.length === 0 && (
        <div className="flex flex-1 w-full justify-center p-2 mt-4">
          <Typography>No products found</Typography>
        </div>
      )}
      {!loading &&
        products?.length > 0 &&
        products?.map((product: IProduct, index: number) => {
          return (
            <div key={`productList-${product.id}-${index}`} onClick={() => handleSelectedProduct(index)} className={`w-full items-start cursor-pointer ${index === 0 ? "mt-2" : ""}`}>
              <div className={`p-3 max-w-60 rounded-lg border-[1px] my-1 border-zinc-300 ${selectedProduct === index ? "bg-accent" : ""}`}>
                <Typography size="sm" className={`text-nowrap !truncate !text-x ${selectedProduct === index ? "!text-white" : ""}`}>
                  {product.name}
                </Typography>
              </div>
              {selectedProduct === index && (
                <div className="w-full cursor-pointer transition-all mb-2">
                  {product.productRevisions?.map((revision, rIndex) => {
                    if (revision.approvalStatus !== RevisionStatus.approved) return null;
                    if (revision.type === "SALES") return null;
                    return (
                      <div
                        key={`productRevisionItem-${revision.id}-${rIndex}`}
                        className={`p-2 mx-2 border-l-[1px] border-zinc-300 ${selectedRevision === rIndex ? "bg-zinc-300" : ""}`}
                        onClick={(e) => handleSelectedProductRevision(e, rIndex, revision)}
                      >
                        <Typography size="xs" className="!text-xs">
                          {revision.revisionNumber?.toUpperCase()}
                        </Typography>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          );
        })}
    </div>
  );
};

export default ProductsList;
