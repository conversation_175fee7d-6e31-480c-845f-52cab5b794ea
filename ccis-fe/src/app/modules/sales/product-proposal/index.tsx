import React, { ChangeEvent, useEffect, useState } from "react";
import ProductProposalTable from "./Components/Tables/ProductProposalTable";
import Filter from "@components/common/Filter";
import Select from "@components/form/Select";
import Typography from "@components/common/Typography";
import TextField from "@components/form/TextField";
import { useDebouncedCallback } from "use-debounce";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { useProductCategoryManagementActions } from "@state/reducer/utilities-product-category";
import { useProductTypesManagementActions } from "@state/reducer/utilities-product-type";
import { useTargetMarketsManagementActions } from "@state/reducer/utilities-target-market";
import { FaPlus, FaRedo, FaArrowLeft } from "react-icons/fa";
import { ROUTES } from "@constants/routes";
import { useNavigate } from "react-router-dom";
import SideDrawer from "@components/template/Drawer";
import Timeline from "@components/common/Timeline";
import { getProductProposalLogService } from "@services/product-proposal/product-proposal.service";
import { toast } from "react-toastify";
import { ITimelineItemProps } from "@interface/product-proposal.interface";
import { ProductProposalStatusFilter } from "@enums/proposal-status";
import { UserRoles } from "@interface/routes.interface";
import { handleRoleBasedNavigation } from "@helpers/navigatorHelper";
import { IUserRPermission } from "@interface/user.interface";
import { canCreateProductProposal } from "@helpers/product-proposal/product-proposal-permissions";

const ProductProposal: React.FC = () => {
  const navigate = useNavigate();
  const [searchText, setSearchText] = useState<string>("");
  const [dateFrom, setDateFrom] = useState<string>("");
  const [dateTo, setDateTo] = useState<string>("");
  const [statusFilter, setStatusFilter] = useState<string | undefined>("");
  const [typeFilter, setTypeFilter] = useState<number | undefined>(undefined);
  const [isShowingArchived, setIsShowingArchived] = useState<boolean>(false);
  const { getProductType } = useProductTypesManagementActions();
  const { getProductCategory } = useProductCategoryManagementActions();
  const { getTargetMarket } = useTargetMarketsManagementActions();
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
  const toggleLogDrawer = () => setDrawerOpen((prev) => !prev);
  const [timelineData, setTImelineData] = useState<ITimelineItemProps[]>([]);
  const { data: currentUser } = useSelector((state: RootState) => state.auth.user);
  const roleNavigationMap = {
    [UserRoles.admin]: ROUTES.ADMIN.createProductProposal.key,
    [UserRoles.sales]: ROUTES.SALES.createProductProposal.key,
  };
  const handleNavigation = () => {
    handleRoleBasedNavigation(currentUser?.roles ?? [], navigate, roleNavigationMap);
  };

  const user = (currentUser ?? { roles: [] }) as IUserRPermission;
  const canCreate = canCreateProductProposal(user);

  const handleSearch = useDebouncedCallback((event: ChangeEvent<HTMLInputElement>) => {
    // Extract the current input value from the event
    const value = event.target.value;
    setSearchText(value);
  }, 500);

  const [resetCounter, setResetCounter] = useState(0);

  const statusOptions = Object.values(ProductProposalStatusFilter).map((value) => ({
    value,
    label: value,
    text: value,
  }));

  const productType = useSelector((state: RootState) => state.utilitiesProductType.productType);
  const productTypeOptions = productType.map((item) => ({
    value: item.id,
    text: item.productType,
  }));

  const handleClearAll = () => {
    setSearchText("");
    setDateFrom("");
    setDateTo("");
    setStatusFilter(undefined);
    setTypeFilter(undefined);
    setResetCounter(resetCounter + 1);
  };

  const handleStatusChange = (event: ChangeEvent<HTMLSelectElement>) => {
    const value = event.target.value;
    setStatusFilter(value);
  };

  const handleTypeFilterChange = (event: ChangeEvent<HTMLSelectElement>) => {
    const value = parseInt(event.target.value);
    setTypeFilter(value);
  };

  const handleDateFromChange = (event: ChangeEvent<HTMLInputElement>) => {
    setDateFrom(event.target.value);
  };

  const handleDateToChange = (event: ChangeEvent<HTMLInputElement>) => {
    setDateTo(event.target.value);
  };

  const handleProductProposalLog = async (id: string) => {
    try {
      const { data } = await getProductProposalLogService(id);
      if (data) {
        const formattedTimeline =
          data?.map((activity: any) => {
            return { title: activity.description, date: activity.created_at };
          }) ?? [];
        setTImelineData(formattedTimeline);
      }
      toggleLogDrawer();
    } catch (error: any) {
      toast.error(error?.response?.data?.message);
    }
  };

  useEffect(() => {
    getProductType({ filter: "" });
    getProductCategory({ filter: "" });
    getTargetMarket({ filter: "" });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleArchiveToggle = () => {
    if (isShowingArchived) {
      // clear all filters
      setSearchText("");
      setDateFrom("");
      setDateTo("");
      setStatusFilter("");
      setTypeFilter(undefined);
      setIsShowingArchived(false);
      setResetCounter(resetCounter + 1);
    } else {
      // set archive mode
      setSearchText("");
      setDateFrom("");
      setDateTo("");
      setStatusFilter(undefined);
      setTypeFilter(undefined);
      setIsShowingArchived(true);
      setResetCounter(resetCounter + 1);
    }
  };
  return (
    <>
      <div className=" my-4 py-4 border-b border-zinc-300 w-full text-zinc-500">
        Dashboard / <span className="text-primary font-poppins-semibold ">Proposals</span>
      </div>
      <Typography className="!text-2xl">{isShowingArchived ? "Archived Product Proposals" : "Product Proposals"}</Typography>
      <div className="flex flex-col sm:flex-row justify-between mt-10">
        <Filter search={searchText} onChange={handleSearch}>
          <div className="flex justify-end">
            <button className="text-primary text-xs btn-sm" type="button" onClick={handleClearAll}>
              Clear All
            </button>
          </div>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <div className="text-xs">Date From</div>
              <TextField className="" type="date" size="sm" value={dateFrom} onChange={handleDateFromChange} />
            </div>
            <div>
              <div className="text-xs">Date To</div>
              <TextField className="" type="date" size="sm" value={dateTo} onChange={handleDateToChange} />
            </div>
            <div>
              <div className="text-xs">Status</div>
              <Select key={`status-${resetCounter}`} size="sm" placeholder="Status" options={statusOptions} value={statusFilter} onChange={handleStatusChange} />
            </div>
            <div>
              <div className="text-xs">Product Type</div>
              <Select key={`type-${resetCounter}`} size="sm" placeholder="Product Type" options={productTypeOptions} value={typeFilter} onChange={handleTypeFilterChange} />
            </div>
          </div>
        </Filter>
        <div className="w-full flex flex-col sm:flex-1 sm:flex-row items-center mt-4 sm:mt-0">
          <div className="flex flex-1 flex-row justify-end items-center gap-x-2">
            {canCreate && (
              <div className="flex gap-2">
                <button
                  className="btn btn-sm btn-success text-white min-w-44"
                  onClick={() => {
                    handleNavigation();
                  }}
                >
                  Create product proposal
                  <FaPlus />
                </button>
                <button className="btn btn-sm btn-zinc-400 text-primary" onClick={handleArchiveToggle}>
                  {isShowingArchived ? (
                    <>
                      <FaArrowLeft /> Back to Proposals
                    </>
                  ) : (
                    <>
                      <FaRedo /> Archived Proposals
                    </>
                  )}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
      <ProductProposalTable
        searchText={searchText}
        dateFrom={dateFrom}
        dateTo={dateTo}
        statusFilter={isShowingArchived ? undefined : statusFilter}
        typeFilter={typeFilter}
        handleProductProposalLog={(id) => handleProductProposalLog(id)}
        showArchived={isShowingArchived}
      />
      <SideDrawer direction="right" isOpen={drawerOpen} handleDrawer={toggleLogDrawer}>
        <div className="p-4 bg-white">
          <Typography className="absolute top-0 h-10 w-full pt-2 px-2 bg-white z-[100]">Product Proposal Log</Typography>
          <div className="flex flex-1 flex-col justify-start mt-10 pl-5">
            <Timeline data={timelineData} />
          </div>
        </div>
      </SideDrawer>
    </>
  );
};

export default ProductProposal;
