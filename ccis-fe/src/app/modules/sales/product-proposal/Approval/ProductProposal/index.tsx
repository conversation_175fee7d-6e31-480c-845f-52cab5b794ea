import { FC, useEffect, useState, Fragment, useRef } from "react";
import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import { FaCircleCheck } from "react-icons/fa6";
import { FaCircleXmark } from "react-icons/fa6";
import TextField from "@components/form/TextField";
import FileDropzone from "@components/common/FileDropzone";
import { FaCloudArrowUp } from "react-icons/fa6";
import Typography from "@components/common/Typography";
import TextArea from "@components/form/TextArea";
import CheckBox from "@components/form/CheckBox";
import { Form, FormikProvider, useFormik } from "formik";
import dayjs from "dayjs";
import { IGuidelineContent, IGuidelineContentTable } from "@interface/guidelines.interface";
import { postProductProposalApproval } from "@services/proposal/proposal.service";
import { showError, showSuccess } from "@helpers/prompt";
import { AxiosResponse } from "axios";
import { ApprovalRejectSchema, ApprovalSchema } from "@services/product-proposal/product-proposal.schema";
import { Status } from "@constants/global-constant-value";
import { ProposalApprovalStatus } from "@enums/proposal-status";
import { ProposalTypes } from "@enums/enums";
import Select1 from "@components/form/Combo-box";
import { FormatStatus } from "@enums/proposal-status";
import { toast } from "react-toastify";
import { PiPrinter } from "react-icons/pi";
import httpClient from "@clients/httpClient";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { IUserRPermission } from "@interface/user.interface";
import { canCreateProductProposal } from "@helpers/product-proposal/product-proposal-permissions";
import { ImSpinner6 } from "react-icons/im";

interface ProductProposalProps {
  data: any;
}

enum UpdateStatus {
  Approved = "Approved",
  Reject = "Reject",
}

const ProductProposal: FC<ProductProposalProps> = ({ data }) => {
  const [updateStatusModal, setUpdateStatusModal] = useState<boolean>();
  const [updateStatus, setUpdateStatus] = useState<string>("");
  const cooperativeOfficers = data?.cooperative?.cooperativeOfficers;
  const [viewCoopDetailsModal, setCoopDetailsModal] = useState<boolean>();
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const standard =
    data?.proposable?.commission?.commissionDetails?.filter((row: any) => {
      return row.commissionAgeType?.name?.toLocaleLowerCase() !== "standard";
    }) ?? [];

  const sectionRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [titleCurrIndex, setTitleCurrIndex] = useState<number>(0);
  const [lastIndex, setLastIndex] = useState<number>(0);
  const [commissionView, setCommissionStructureView] = useState<boolean>(false);

  //check permission
  const currentUser = useSelector((state: RootState) => state?.auth?.user?.data);
  const user = {
    ...currentUser,
    roles: currentUser?.roles ?? [],
  } as IUserRPermission;

  const canCreateProposal = canCreateProductProposal(user);

  const formik = useFormik({
    initialValues: data?.proposalApproval ?? {
      approveRejectDate: "",
      approveRejectRemarks: "",
      hasPricingRemarks: false,
      hasUnderwritingRemarks: false,
      status: "",
      attachments: [],
    },
    validationSchema: updateStatus === UpdateStatus.Approved ? ApprovalSchema : ApprovalRejectSchema,
    onSubmit: async (values) => {
      if (files.length === 0 && updateStatus === UpdateStatus.Approved) {
        showError("Error", "Please upload signed document.");
        return;
      }
      setIsSubmitting(true);
      const { createdAt, updatedAt, ...updatedData } = values;

      if (typeof updatedData.hasPricingRemarks === "boolean") {
        updatedData.hasPricingRemarks = updatedData.hasPricingRemarks ? 1 : 0;
      }

      if (typeof updatedData.hasUnderwritingRemarks === "boolean") {
        updatedData.hasUnderwritingRemarks = updatedData.hasUnderwritingRemarks ? 1 : 0;
      }

      try {
        const status: AxiosResponse = await postProductProposalApproval(data?.id as number, updatedData);

        if (status) {
          handleUpdateStatusModal();
          showSuccess("Success", "Product Proposal Approval has been updated!").then((result) => {
            if (result.isConfirmed) {
              setIsSubmitting(false);
              window.location.reload();
            }
          });
        }
      } catch (error) {
        console.error(error);
        setIsSubmitting(false);
      }
    },
  });

  const handleScrollToSection = (index: number) => {
    sectionRefs.current[index]?.scrollIntoView({ behavior: "smooth" });
    setLastIndex(data?.proposable?.productGuidelines.length);
    setTitleCurrIndex(index);
    setCommissionStructureView(false);
  };

  const handleSetCommissionStructureView = () => {
    handleScrollToSection(lastIndex - 1);
    setTitleCurrIndex(data?.proposable?.productGuidelines.length + 1);
    setCommissionStructureView(true);
  };

  const handleUpdateStatusModal = () => {
    setUpdateStatusModal((prev) => !prev);
    if (updateStatus === UpdateStatus.Approved) {
      formik.setFieldValue("status", ProposalApprovalStatus.approved);
    } else if (updateStatus === UpdateStatus.Reject) {
      formik.setFieldValue("status", ProposalApprovalStatus.rejected);
    }
  };

  const handleCoopDetailsModal = () => {
    setCoopDetailsModal((prev) => !prev);
  };

  const handlePrint = async (apiUrl: string, queryParams?: string) => {
    try {
      const endpoint = queryParams ? `${apiUrl}?${queryParams}` : apiUrl;
      const response: any = await httpClient.get(endpoint, {
        responseType: "blob",
      });

      const blob = new Blob([response], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);
      window.open(url, "_blank");
    } catch (error) {
      toast.error(`PDF export failed: ${String(error)}`);
    }
  };
  const [files, setFiles] = useState<Array<File>>([]);
  const [imagePreview, setImagePreview] = useState<string | ArrayBuffer | null>(null);

  const handleFile = (acceptedFiles: Array<File>) => {
    setFiles(acceptedFiles);

    const file = new FileReader();
    file.onload = () => {
      setImagePreview(file.result);
    };

    file.readAsDataURL(acceptedFiles[0]);
  };

  useEffect(() => {
    if (files) {
      const fileArray = Array.from(files).map((file) => ({
        file: file,
        label: file.name,
        description: "Description here",
      }));

      formik.setFieldValue("attachments", fileArray);
    }
  }, [files]);

  return (
    <div>
      {viewCoopDetailsModal && (
        <Modal isOpen={viewCoopDetailsModal} onClose={handleCoopDetailsModal} modalContainerClassName="max-w-5xl  ">
          <div className="w-full h-[48rem] overflow-y-auto ">
            <div className="h-max  flex flex-col gap-4 pt-4">
              <div className="mb-2 text-xl font-semibold">BASIC INFORMATION</div>
              <div className="text-zinc-500">Cooperative Name (In Full)</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.coopName}</div>
              <div className="text-zinc-500">Cooperative Acronym</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.coopAcronym}</div>
              <div className="text-zinc-500">Cooperative Category</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.cooperativeCategory?.coopCategoryName}</div>
              <div className="text-zinc-500">Cooperative Type</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.cooperativeType?.coopTypeName}</div>
              <div className="text-zinc-500">No. of Branches</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.coopBranchesCount}</div>
              <div className="mb-2 text-xl font-semibold border-t-2 pt-8 mt-8 border-zinc-400"></div>
              <div className="text-zinc-500">Province</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.province}</div>
              <div className="text-zinc-500">Municipality/City</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.city}</div>
              <div className="text-zinc-500">Baranggay</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.barangay}</div>
              <div className="text-zinc-500">Street Address</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.streetAddress}</div>
              <div className="text-zinc-500">Zipcode</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.zipCode}</div>
              <div className="mb-2 text-xl font-semibold border-t-2 pt-8 mt-8 border-zinc-400">CONTACT INFORMATION</div>
              <div className="text-zinc-500">Email Address</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.emailAddress}</div>
              <div className="text-zinc-500">Website</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.websiteAddress}</div>
              <div className="text-zinc-500">Telephone No. Fax No.</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.telephoneNumber}</div>
              <div className="mb-2 text-xl font-semibold border-t-2 pt-8 mt-8 border-zinc-400">REGISTRATION AND COMPLIANCE</div>
              <div className="flex mb-4">
                <div className="w-1/2 flex flex-col gap-4">
                  {" "}
                  <div className="text-zinc-500">CDA Reg No.</div>
                  <div className=" w-5/6 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.cdaRegistrationNumber}</div>
                </div>
                <div className="w-1/2 flex flex-col gap-4">
                  {" "}
                  <div className="text-zinc-500">Date Registered</div>
                  <div className=" w-5/6 pb-2 border-b border-zinc-300 font-semibold">
                    {data?.cooperative?.cdaRegistrationDate !== null && <span>{dayjs(data?.cooperative?.cdaRegistrationDate).format("MMMM DD, YYYY")}</span>}
                  </div>
                </div>
              </div>
              <div className="flex ">
                <div className="w-1/2 flex flex-col gap-4">
                  {" "}
                  <div className="text-zinc-500">CDA Certificate of Compliance No. (COC No.)</div>
                  <div className=" w-5/6 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.cdaCocNumber}</div>
                </div>
                <div className="w-1/2 flex flex-col gap-4">
                  {" "}
                  <div className="text-zinc-500">Date Registered</div>
                  <div className=" w-5/6 pb-2 border-b border-zinc-300 font-semibold">
                    {data?.cooperative?.cdaCocDate !== null && <span>{dayjs(data?.cooperative?.cdaCocDate).format("MMMM DD, YYYY")}</span>}
                  </div>
                </div>
              </div>
              <div className="mb-2 text-xl font-semibold border-t-2 pt-8 mt-8 border-zinc-400">TAX INFORMATION</div>
              <div className="flex mb-4">
                <div className="w-1/2 flex flex-col gap-4">
                  {" "}
                  <div className="text-zinc-500">Tax Identification No. (TIN)</div>
                  <div className=" w-5/6 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.taxIdNumber}</div>
                </div>
                <div className="w-1/2 flex flex-col gap-4">
                  {" "}
                  <div className="text-zinc-500">Certificate of Tax Exemption (CTE) No.</div>
                  <div className=" w-5/6 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.taxCteNumber}</div>
                </div>
              </div>
              <div className="flex mb-4">
                <div className="w-1/2 flex flex-col gap-4">
                  {" "}
                  <div className="text-zinc-500">Tax Identification No. Issue Date</div>
                  <div className=" w-5/6 pb-2 border-b border-zinc-300 font-semibold">
                    <span>{dayjs(data?.cooperative?.taxIdDate).format("MMMM DD, YYYY") ?? ""}</span>
                  </div>
                </div>
                <div className="w-1/2 flex flex-col gap-4">
                  {" "}
                  <div className="text-zinc-500">Certificate of Tax Exemption Expiration Date</div>
                  <div className=" w-5/6 pb-2 border-b border-zinc-300 font-semibold">
                    <span>{dayjs(data?.cooperative?.taxCteExpiryDate).format("MMMM DD, YYYY") ?? ""}</span>
                  </div>
                </div>
              </div>
              <div className="mb-2 text-xl font-semibold border-t-2 pt-8 mt-8 border-zinc-400">AFFILIATIONS</div>

              <div className="flex flex-col mb-4 w-full">
                <table className="table">
                  <thead>
                    <tr>
                      <th>AFFILIATIONS</th>
                      <th>STATUS</th>
                      <th>EFFECTIVE DATE</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data?.cooperative?.cooperativeAffiliations?.map((affiliation: any, index: number) => (
                      <tr key={index} className="border-b border-zinc-300">
                        <td>{affiliation?.affiliation?.affiliationName ?? "N/A"}</td>
                        <td>{affiliation?.status ?? "N/A"}</td>
                        <td>{affiliation?.effectivityDate ? dayjs(affiliation.effectivityDate).format("MMMM DD, YYYY") : "N/A"}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <div className="mb-2 text-xl font-semibold border-t-2 pt-8 mt-8 border-zinc-400">COOP OFFICERS</div>

              <div className="flex flex-col mb-4 w-full">
                <table className="table">
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Position</th>
                      <th>Title</th>
                      <th>Gender</th>
                      <th>Contact No.</th>
                      <th>Email Address</th>
                      <th>Status</th>
                      <th>Effective Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    {cooperativeOfficers?.map((officer: any, index: number) => (
                      <tr key={index} className="border-b border-zinc-300">
                        <td>
                          {officer?.firstName} {officer?.lastName}
                        </td>
                        <td>{officer?.position?.positionName ?? "N/A"}</td>
                        <td>{officer?.title ?? "N/A"}</td>
                        <td>{officer?.gender ?? "N/A"}</td>
                        <td>{officer?.contactNumber ?? "N/A"}</td>
                        <td>{officer?.emailAddress ?? "N/A"}</td>
                        <td>{officer?.status ?? "N/A"}</td>
                        <td>{officer?.effectivityDate ? dayjs(officer.effectivityDate).format("MMMM DD, YYYY") : "N/A"}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </Modal>
      )}

      {updateStatusModal && (
        <Modal isOpen={updateStatusModal} onClose={handleUpdateStatusModal} modalContainerClassName="max-w-3xl ">
          <FormikProvider value={formik}>
            <Form onSubmit={formik.handleSubmit}>
              <div className="w-full flex flex-col  ">
                <div className=" flex items-center justify-center text-center mb-10">
                  {" "}
                  {updateStatus === UpdateStatus.Approved && <FaCircleCheck size={50} className="text-success" />}
                  {updateStatus === UpdateStatus.Reject && <FaCircleXmark size={50} className="text-danger" />}
                </div>

                <div className="text-3xl text-center font-poppins-semibold mb-10"> {updateStatus} Proposal</div>

                <div>
                  <label className="text-zinc-400">Please enter the date it was {updateStatus === "Approved" ? "approved" : "rejected"}.</label>
                  <TextField
                    name="approveRejectDate"
                    type="date"
                    value={formik.values.approveRejectDate}
                    error={formik.touched.approveRejectDate && !!formik.errors.approveRejectDate}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {formik.touched.approveRejectDate && formik.errors.approveRejectDate ? <div className="text-red-500 mb-4 text-sm">{formik.errors.approveRejectDate as string}</div> : null}
                </div>

                {updateStatus === FormatStatus.Approved && (
                  <div className="mt-3">
                    <label className="text-zinc-400">Please select rate/option.</label>
                    <Select1
                      name="selectedOption"
                      options={
                        data?.proposable?.productGuidelines
                          ?.find((guideline: any) => guideline.label === "SCHEDULE OF PREMIUMS")
                          ?.productGuideline?.[0]?.value?.rows?.[0]?.slice(1)
                          .map((row: any) => ({
                            value: row.value,
                            text: row.value,
                          })) ?? []
                      }
                      value={formik.values.selectedOption}
                      error={formik.touched.selectedOption && !!formik.errors.selectedOption}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    />
                    {formik.touched.selectedOption && formik.errors.selectedOption ? <div className="text-red-500 mb-4 text-sm">{formik.errors.selectedOption as string}</div> : null}
                  </div>
                )}

                {updateStatus === "Approved" && (
                  <div className="mt-4">
                    <label>Upload Signed Document</label>
                    <div className="border border-zinc-400 rounded-xl mt-2 w-full h-60 flex items-center justify-center">
                      <FileDropzone setFiles={handleFile} height={200}>
                        {files.length === 0 && (
                          <div className="flex flex-1 flex-col items-center">
                            <FaCloudArrowUp size={30} className="mb-4" />
                            <Typography>Click or drag and drop to upload your profile</Typography>
                            <Typography className="text-slate-400">PNG, JPG (Max 20MB)</Typography>
                          </div>
                        )}
                        {imagePreview && (
                          <div className="flex flex-1 flex-col items-center  p-4 mb-4 rounded-md h-full w-full  ">
                            <img src={imagePreview as string} alt="Image Preview" className="w-full h-full object-contain" />
                          </div>
                        )}
                      </FileDropzone>
                    </div>
                  </div>
                )}

                {updateStatus === UpdateStatus.Reject && (
                  <>
                    <div className="mt-4 text-xl w-full text-center font-poppins-semibold">Remarks</div>{" "}
                    <div className="flex items-center  gap-4">
                      {" "}
                      <CheckBox name="hasPricingRemarks" checked={formik.values.hasPricingRemarks} onChange={formik.handleChange} /> Pricing
                    </div>
                    <div className="flex items-center  gap-4 mb-4">
                      {" "}
                      <CheckBox name="hasUnderwritingRemarks" checked={formik.values.hasUnderwritingRemarks} onChange={formik.handleChange} /> Underwriting Provisions
                    </div>
                    <TextArea
                      name="approveRejectRemarks"
                      placeholder="Enter Remarks"
                      value={formik.values.approveRejectRemarks}
                      error={formik.touched.approveRejectRemarks && !!formik.errors.approveRejectRemarks}
                      errorText={formik.errors.approveRejectRemarks as string}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    />
                  </>
                )}

                <div className="flex items-center justify-center gap-4 w-full mt-4">
                  <Button classNames="w-40 rounded-lg bg-zinc-500 flex items-center justify-center" onClick={handleUpdateStatusModal}>
                    {isSubmitting && <ImSpinner6 size={30} className="text-white animate-spin text-center" />}

                    {!isSubmitting && (
                      <>
                        <div>Cancel</div>
                      </>
                    )}
                  </Button>
                  <Button type="submit" classNames={`w-40 rounded-lg flex items-center justify-center ${updateStatus === "Approved" ? "bg-success" : "bg-danger"}`}>
                    {isSubmitting && <ImSpinner6 size={30} className="text-white animate-spin text-center" />}

                    {!isSubmitting && (
                      <>
                        <div> {updateStatus === UpdateStatus.Approved ? UpdateStatus.Approved : UpdateStatus.Reject}</div>
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </Form>
          </FormikProvider>
        </Modal>
      )}

      <div className="p-4">
        {" "}
        <div className="w-full flex flex-col gap-4 mb-4">
          <div className="flex items-center justify-center">
            {" "}
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Product Proposed</div>
              <div className="w-2/3 text-black"> {data?.product?.name ?? ""}</div>
            </div>
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400"> Creation Date</div>
              <div className="w-2/3 text-black">{data?.createdAt !== null && <span>{dayjs(data?.createdAt).format("MMMM DD, YYYY - h:m A")}</span>}</div>
            </div>
          </div>
          <div className="flex items-center justify-start">
            {" "}
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Cooperative</div>
              <div className="w-2/3 text-black">{data?.cooperative?.coopName}</div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center justify-between mb-2">
        <div>
          {" "}
          <Button type="button" variant="primary" classNames="text-xs" onClick={handleCoopDetailsModal}>
            View Coop Details
          </Button>
        </div>
        {canCreateProposal && (
          <div className="flex gap-2">
            {" "}
            <Button type="button" classNames="bg-primary/[.5] w-24 text-xs" disabled>
              Edit
            </Button>{" "}
            <Button
              variant="primary"
              classNames="w-full md:w-auto flex gap-2"
              onClick={() => {
                const proposalId = data?.id; // Assuming proposalId is the same as data.id
                if (proposalId) {
                  const exportUrl = `/product-proposals/${proposalId}/pdf`;
                  handlePrint(exportUrl);
                } else {
                  toast.error("Missing AER ID.");
                }
              }}
            >
              <PiPrinter className="mt-1" />
              Print
            </Button>
          </div>
        )}
      </div>

      <div className="min-h-[70rem] w-full flex  justify-center gap-4   ">
        {/* COL1 */}
        <div className="w-80 px-4 min-h-screen hidden  xl:flex flex-col text-start text-sm relative">
          <div className="w-full text-xl font-poppins-semibold pt-4 mb-4 ">Table of Contents</div>

          {data?.proposable?.productGuidelines?.map((item: any, gIndex?: any) => (
            <div
              className={`w-full  font-poppins-semibold hover:bg-zinc-100 p-4 rounded-md cursor-pointer ${titleCurrIndex === gIndex ? "text-primary bg-sky-50" : "text-zinc-500"}`}
              onClick={() => handleScrollToSection(gIndex)}
            >
              {item.label}
            </div>
          ))}

          {data?.proposable && (
            <div
              onClick={handleSetCommissionStructureView}
              className={`w-full font-poppins-semibold hover:bg-zinc-100 p-4 rounded-md cursor-pointer ${commissionView ? "text-primary bg-sky-50" : "text-zinc-500"} `}
            >
              Commission Structure
            </div>
          )}
        </div>
        {/* COL2 */}
        <div className="w-1/2 min-h-[70rem] h-96 border border-zinc-200 p-6 pt-6 text-justify overflow-y-auto scroll-mt-96 ">
          <div className="p-6">
            <p>
              {" "}
              <span>{dayjs(data?.createdAt).format("MMMM DD, YYYY") ?? ""}</span>{" "}
            </p>
            <br />
            <p> Dear Cooperators,</p>
            <br /> <br />
            <p> Greetings from CLIMBS!</p>
            <br />
            <strong className="font-poppins-semibold">{data?.cooperative?.coopName}</strong> has come a long way from where it started in 1971. From our humble beginnings, we have firmly established a
            strong financial position which make us a P3.68 billion pesos with a net worth of P 1.97 billion insurance cooperative today.
            <br /> <br /> <br />
            As a group of companies with insurance as our core business, we grew into one of the country's leading players in the insurance industry and the top Cooperative Insurer being licensed both
            by the Cooperative Development Authority and insurance Commission. It is owned by more than 4,000 primary cooperatives in the Philippines.
            <br /> <br /> <br />
            with an array of insurance products both Life and Non-life, we propose to offer you the <strong className="font-poppins-semibold">{data?.product?.name}</strong> insurance plan for all
            Cooperative members' protection with an affordable premium. Please find in the following pages the salient features and schedule of benefits of this insurance plan.
            <br /> <br /> For further inquiries or clarification regarding this proposal, please do not hesitate to get in touch with me thru my mobile number *********** or <NAME_EMAIL> We
            would be happy to do a product presentation on one your board/management meetings. Thank you very much.
            <br /> <br /> <br />
            <p> Ray Doe </p>
            <p> Regional Sales Manager </p>
          </div>
          <div className=" w-full min-h-screen  ">
            {" "}
            <div className="w-full font-poppins-semibold xl:text-xl text-base text-center ">{data?.product?.name}</div>
            {/* PRODUCT GUIDELINES CONTENT START */}
            <div className="xl:p-8 p-4">
              <div className="flex flex-col">
                {/* FIRST PRODUCT GUIDELINE*/}
                <div className="flex flex-col xl:text-base text-xs ">
                  {data?.proposable?.productGuidelines?.map((value: any, gIndex: any) => {
                    return (
                      <div key={`guideline-${gIndex}`} className="scroll-mt-16 flex flex-1 flex-col mb-2 xl:mb-10" ref={(el) => (sectionRefs.current[gIndex] = el)}>
                        <Typography className="xl:text-xl text-sm font-poppins-semibold text-primary">{value.label}</Typography>
                        {value.productGuideline.map((pgValue: any, pgIndex: any) => {
                          let listValue;
                          let tableValue;
                          if (pgValue.type === "list") {
                            listValue = pgValue.value as IGuidelineContent[];
                          }

                          if (pgValue.type === "table") {
                            tableValue = pgValue.value as IGuidelineContentTable;
                          }

                          return (
                            <div key={`pg-${pgIndex}`} className="p-2">
                              {pgValue.type === "textfield" && (
                                <Fragment>
                                  <Typography className="ml-4 xl:mt-4  mt-0 text-justify xl:text-base text-xs">{pgValue.value as string}</Typography>
                                </Fragment>
                              )}
                              {pgValue.type === "list" && (
                                <Fragment>
                                  <Typography className="ml-4 xl:mt-4  mt-0 xl:text-justify text-start xl:text-base text-xs">{pgValue.label}</Typography>
                                  <ul className="list-disc xl:ml-12 ml-6">
                                    {listValue &&
                                      listValue.map((listValue, listIndex) => {
                                        return (
                                          <li key={`listItem-${listIndex}`} className="xl:mt-4 mt-0">
                                            <Typography className="text-justify xl:text-base text-xs">{listValue.value as string}</Typography>
                                          </li>
                                        );
                                      })}
                                  </ul>
                                </Fragment>
                              )}
                              {pgValue.type === "texteditor" && (
                                <Fragment>
                                  <div
                                    className="ml-4 mt-2 xl:text-base text-xs "
                                    dangerouslySetInnerHTML={{
                                      __html: (pgValue as any).value ?? "",
                                    }}
                                  ></div>
                                </Fragment>
                              )}
                              {pgValue.type === "table" && (
                                <Fragment>
                                  <div className="flex flex-1 mt-2 mx-6 overflow-x-scroll xl:text-base text-xs">
                                    <table className="table border-[1px]">
                                      <thead className="table-header-group">
                                        <tr>
                                          {tableValue?.columns?.map((cValue, cIndex) => {
                                            return (
                                              <td key={`col-${cIndex}`} className="table-cell border-[1px] xl:text-base text-xs">
                                                <Typography className="font-semibold text-xs">{cValue.value as string}</Typography>
                                              </td>
                                            );
                                          })}
                                        </tr>
                                      </thead>
                                      <tbody>
                                        {tableValue?.rows?.map((rValue, rIndex) => {
                                          return (
                                            <tr key={`row-${rIndex}`}>
                                              {rValue.map((cell, cellIndex) => {
                                                return (
                                                  <td className="border-[1px] xl:text-base text-xs" key={`cell-${cellIndex}`}>
                                                    <Typography size="xs">{cell.value as string}</Typography>
                                                  </td>
                                                );
                                              })}
                                            </tr>
                                          );
                                        })}
                                      </tbody>
                                    </table>
                                  </div>
                                </Fragment>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    );
                  })}
                </div>
                {/* LAST PRODUCT GUIDELINE*/}

                {data?.proposable?.commission && (
                  <Fragment>
                    <Typography size="md" className="font-poppins-semibold text-primary">
                      Commission Structure
                    </Typography>
                    <Fragment>
                      <Typography className="ml-4 mt-4 xl:text-base text-xs">
                        {parseFloat(data?.proposable?.commission.maximumDisposableRate ?? "").toFixed(2)}% Maximum Disposable Commission - Standard Rate
                      </Typography>

                      <div className="flex-flex-1 mt-6 mx-6 overflow-x-scroll">
                        <table className="table overflow-scroll">
                          <thead>
                            <tr>
                              <td className="table-cell border-1 text-center text-xs">Type</td>
                              <td className="table-cell border-1 text-center text-xs">Age Type</td>
                              {standard.length > 0 && (
                                <Fragment>
                                  <td className="table-cell border-1 text-center text-xs">Age From</td>
                                  <td className="table-cell border-1 text-center text-xs">Age To</td>
                                </Fragment>
                              )}
                              <td className="table-cell border-1 text-center text-xs">Rate</td>
                            </tr>
                          </thead>
                          <tbody>
                            {data?.proposable?.commission.commissionDetails?.map((rowValue: any, rowIndex: any) => {
                              return (
                                <tr key={`commissionDetailsRow-${rowIndex}`}>
                                  <td className="table-cell border-1 text-xs text-center  font-poppins-semibold">{rowValue?.commissionType?.commissionName}</td>
                                  <td className="table-cell border-1 text-xs text-center">{rowValue?.commissionAgeType?.name}</td>
                                  {standard.length > 0 && (
                                    <Fragment>
                                      <td className="table-cell border-1 text-center text-xs">{rowValue.ageFrom}</td>
                                      <td className="table-cell border-1 text-center text-xs">{rowValue.ageTo}</td>
                                    </Fragment>
                                  )}
                                  <td className="table-cell border-1 text-center text-xs">{rowValue.rate ? parseFloat(rowValue.rate.toString()).toFixed(0) : ""}%</td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    </Fragment>
                  </Fragment>
                )}
              </div>
            </div>
            {/* PRODUCT GUIDELINES CONTENT END */}
          </div>
        </div>
        {/* COL3 */}
        <div className="w-1/4 flex flex-col items-center justify-center h-[70rem] border border-zinc-200 p-4">
          <div className="h-1/2 w-full">
            <div className="w-full text-xl text-zinc-500  font-poppins-semibold py-4"> APPROVAL STATUS</div>

            {data?.proposalType === ProposalTypes.CUSTOMIZED &&
              data?.proposalApproval?.status === Status.for_review &&
              data?.claimStatus === Status.for_approval &&
              data?.underwritingStatus === Status.for_approval && <div className="bg-amber-50 text-amber-400 p-4 text-xs mt-4 rounded-md">Awaiting approval from claims and underwriting.</div>}

            {((data?.proposalType === ProposalTypes.STANDARD && data?.proposalApproval?.status === Status.for_review) ||
              (data?.proposalApproval?.status === Status.for_review &&
                data?.claimStatus === Status.approved &&
                data?.underwritingStatus === Status.approved &&
                data?.proposalType === ProposalTypes.CUSTOMIZED)) && (
              <div>
                {canCreateProposal && (
                  <div className="w-full  text-zinc-500 text-center flex gap-4">
                    <Button
                      type="button"
                      classNames="w-1/2 text-xs text-success bg-gradient-to-l flex items-center justify-center from-sky-50 to-white border border-zinc-200"
                      outline
                      disabled={isSubmitting}
                      onMouseEnter={() => setUpdateStatus(UpdateStatus.Approved)}
                      onClick={handleUpdateStatusModal}
                    >
                      Approve
                    </Button>
                    <Button
                      type="button"
                      classNames="w-1/2 text-xs text-red-500 bg-gradient-to-l from-sky-50 to-white border border-zinc-200"
                      outline
                      onMouseEnter={() => setUpdateStatus(UpdateStatus.Reject)}
                      onClick={handleUpdateStatusModal}
                    >
                      Reject
                    </Button>
                  </div>
                )}
                <div className="bg-amber-50 text-amber-400 p-4 text-xs mt-4 rounded-md">
                  <strong className="font-poppins-semibold">PENDING</strong> - awaiting approval from the cooperative.
                </div>
              </div>
            )}
            {data?.proposalApproval?.status !== ProposalApprovalStatus.forReview && (
              <div>
                <div className="w-full">
                  <div className="flex justify-between text-sm mb-4 border-b pb-4 border-zinc-300">
                    <div>Status</div>
                    <div>
                      {data?.proposalApproval?.status === ProposalApprovalStatus.approved
                        ? ProposalApprovalStatus.approved
                        : data?.proposalApproval?.status === ProposalApprovalStatus.rejected
                          ? ProposalApprovalStatus.rejected
                          : data?.proposalApproval?.status?.toUpperCase()}
                    </div>
                  </div>

                  <div className="flex justify-between text-sm mb-4 border-b pb-4 border-zinc-300">
                    <div>Date {data?.status === ProposalApprovalStatus.approved ? "Approved" : data?.status === ProposalApprovalStatus.rejected ? "Rejected" : ""}</div>
                    <div>
                      {" "}
                      <span>{dayjs(formik?.values?.approveRejectDate).format("MMMM DD, YYYY") ?? ""}</span>
                    </div>
                  </div>

                  <div className="flex flex-col gap-4 mb-4  text-sm w-full overflow-hidden">
                    <div>Attachment</div>

                    <div className="underline text-accent hover:cursor-pointer">
                      <a
                        href={
                          (data?.proposalApproval?.attachments?.[0] as any)?.filepath ? `${import.meta.env.VITE_AWS_S3_ENDPOINT}/${(data.proposalApproval?.attachments?.[0] as any)?.filepath}` : "#"
                        }
                        target="_blank"
                        rel="noopener noreferrer" // This is important for security reasons
                      >
                        {data?.proposalApproval?.attachments[0]?.label ? data?.proposalApproval?.attachments[0]?.label : formik.values?.attachments[0]?.name}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="h-1/2 w-full">
            {" "}
            <div className="w-full text-xl text-zinc-500  font-poppins-semibold py-4 border-t border-zinc-200"> REMARKS</div>
            {data?.status === ProposalApprovalStatus.rejected && (
              <div>
                {" "}
                <div>Product Proposal</div>
                <div className="flex items-center  gap-4">
                  {" "}
                  <CheckBox disabled name="hasPricingRemarks" value={formik.values.hasPricingRemarks ? 1 : 0} checked={formik.values.hasPricingRemarks} onChange={formik.handleChange} /> Pricing
                </div>
                <div className="flex items-center  gap-4 mb-4">
                  {" "}
                  <CheckBox
                    disabled
                    name="hasUnderwritingRemarks"
                    value={formik.values.hasUnderwritingRemarks ? 1 : 0}
                    checked={formik.values.hasUnderwritingRemarks}
                    onChange={formik.handleChange}
                  />{" "}
                  Underwriting Provisions
                </div>
                <TextArea className="w-full" name="approveRejectRemarks" disabled placeholder="Enter Remarks" value={formik.values.approveRejectRemarks} />
              </div>
            )}
            {data?.proposalType === ProposalTypes.CUSTOMIZED && (
              <div className="w-full flex flex-col gap-4">
                {data?.underwritingStatus && (
                  <div>
                    <p className="text-zinc-400"> Underwriting Approval</p>
                    <p className="font-poppins-semibold">{data?.underwritingStatus?.replace(/_/g, " ")}</p>
                  </div>
                )}

                {data?.claimStatus && (
                  <div>
                    <p className="text-zinc-400">Claims Approval </p>
                    <p className="font-poppins-semibold"> {data?.claimStatus?.replace(/_/g, " ")}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductProposal;
