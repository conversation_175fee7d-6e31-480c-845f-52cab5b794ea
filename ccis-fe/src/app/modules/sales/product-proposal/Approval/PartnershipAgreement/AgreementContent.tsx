// src/components/proposal/AgreementSectionsContent.tsx
import { FC, Fragment, useRef } from "react";

import { formatDate } from "@helpers/date";
import Typography from "@components/common/Typography";
import { IGuidelineContent, IGuidelineContentTable } from "@interface/guidelines.interface";

type SectionRef = React.RefObject<HTMLDivElement>;
export type SectionMap = Record<
  string,
  {
    label: string;
    ref: SectionRef;
  }
>;

interface AgreementSectionsContentProps {
  data: any;
  sections: SectionMap; // parent owns the refs used by the left menu
  className?: string; // optional styling hook
  productGuidelines?: any[]; // data?.proposable?.productGuidelines
  guidelineRefs?: React.MutableRefObject<(HTMLDivElement | null)[]>;
  showCommission?: boolean;
}

const AgreementSectionsContent: FC<AgreementSectionsContentProps> = ({ data, sections, className = "", productGuidelines = [], guidelineRefs, showCommission = true }) => {
  const contentRef = useRef<HTMLDivElement>(null);

  // ---- local helpers kept self-contained for reusability ----
  const cooperativeOfficers = data?.cooperative?.cooperativeOfficers;
  const primarySignatory = cooperativeOfficers?.[0];
  const secondarySignatory = cooperativeOfficers?.find((o: any) => o !== primarySignatory);

  const formatOfficerName = (officer: any) => {
    if (!officer) return "___________________________";
    const title = officer.title ? `${officer.title} ` : "";
    const firstName = officer.firstName || "";
    const middleName = officer.middleName ? `${officer.middleName} ` : "";
    const lastName = officer.lastName || "";
    const generation = officer.generation ? ` ${officer.generation}` : "";
    const name = `${title}${firstName} ${middleName}${lastName}${generation}`.trim();
    return name || "___________________________";
  };

  const getPositionName = (officer: any) => officer?.position?.positionName || "";

  // commission helpers
  const standard =
    data?.proposable?.commission?.commissionDetails?.filter((row: any) => {
      return row?.commissionAgeType?.name?.toLowerCase() !== "standard";
    }) ?? [];

  return (
    <div ref={contentRef} className={`w-full min-h-[50rem] h-96 border border-zinc-200 p-6 pt-6 text-justify overflow-y-auto scroll-mt-96 ${className}`}>
      <p className="text-center font-poppins-semibold text-xl"> SALES PARTNERSHIP AGREEMENT </p>
      <br />
      <p className="font-poppins-semibold"> KNOW ALL MEN BY THESE PRESENTS </p>
      <br />
      <p>
        This Sales Partnership Agreement is made and executed in
        <span className="font-poppins-semibold">
          {" "}
          {data?.proposalNotarization?.agreementNotarizationDate ? formatDate(data.proposalNotarization.agreementNotarizationDate, "d") : "______"},{" "}
          {data?.proposalNotarization?.agreementNotarizationDate ? formatDate(data.proposalNotarization.agreementNotarizationDate, "yyyy") : "______"} at{" "}
          {data?.proposalNotarization?.agreementNotarizationDate ? formatDate(data.proposalNotarization.agreementNotarizationDate, "MMMM") : "______"}
        </span>{" "}
        by and between:
      </p>
      <br />
      <p>
        <span className="font-poppins-semibold">CLIMBS LIFE AND GENERAL INSURANCE COOPERATIVE</span> , a cooperative duly organized and existing under and by virtue of the laws of the Republic of the
        Philippines with principal office located at CLIMBS Compound, Upper Zone 5, National Highway, Brgy. Bulua, 9000 Cagayan de Oro City, and duly represented in this act by its President &amp;
        Chief Executive Officer, <span className="font-poppins-semibold">MR. NOEL D. RABOY</span> , and hereinafter referred to as <span className="font-poppins-semibold">CLIMBS</span> ;
        <span className="flex justify-center p-4 font-poppins-semibold"> AND</span>
        <span className="font-poppins-semibold">{data?.cooperative?.coopName} </span> , a primary cooperative duly organized and existing under and by virtue of the laws of the Republic of the
        Philippines with principal office located at{" "}
        <span className="font-poppins-semibold">
          {data?.cooperative?.streetAddress}, {data?.cooperative?.province}, {data?.cooperative?.city}
        </span>{" "}
        , represented herein by its Chairperson, {data?.Chairperson || "________________"} of legal age, Filipino, _________ and a resident of ____________________________________________ hereinafter
        referred to as <span className="font-poppins-semibold">COOP PARTNER</span>;
      </p>
      <br />
      <p className="text-center mb-4 font-poppins-semibold"> WITNESSETH:</p>
      <p>
        <b className="font-poppins-semibold">WHEREAS,</b> <span className="font-poppins-semibold">CLIMBS</span> is a composite insurance company duly authorized by the Philippine Insurance Commission
        to offer life insurance, non-life insurance and health care products.
        <br />
        <br />
        <b className="font-poppins-semibold">WHEREAS,</b> the <span className="font-poppins-semibold">COOP PARTNER </span>is a multipurpose cooperative duly licensed by the Cooperative Development
        Authority to offer various mutual benefit products, which includes financial aid and lending services, to its members and is officially recognized as a cooperative member-investor of CLIMBS.
        <br />
        <br />
        <b className="font-poppins-semibold">WHEREAS,</b> the <span className="font-poppins-semibold">COOP PARTNER </span> agreed to sell and promote designated products and services of{" "}
        <span className="font-poppins-semibold">CLIMBS</span> as listed in ANNEX “A” hereof, which is considered an integral part of this Agreement, for the period and on the terms and conditions set
        forth herein.
        <br />
        <br />
        <b className="font-poppins-semibold">NOW, THEREFORE,</b> for and in consideration of the foregoing premises, the Parties have agreed and by these presents do hereby agree as follows:
      </p>
      {/* Sections */}
      <br /> <br />
      <div>
        <div ref={sections.appointment.ref}>
          1. <span className="font-poppins-semibold underline">Appointment</span>
          <p className="mx-8">
            <br />
            <span className="font-poppins-semibold">CLIMBS</span> hereby appoints <span className="font-poppins-semibold">COOP PARTNER</span> as an authorized distributor of{" "}
            <span className="font-poppins-semibold">CLIMBS Products</span> (hereinafter referred to as <span className="font-poppins-semibold">PRODUCT</span> ) with the non-assignable, non-exclusive
            right to promote and sell the same and the <span className="font-poppins-semibold">COOP PARTNER </span> hereby accepts such designation and appointment.
          </p>
        </div>
        <br /> <br />
        <div ref={sections.responsibility.ref}>
          2. <span className="font-poppins-semibold underline">COOP PARTNER’s RESPONSIBILITY</span>
          <p className="mx-8">
            <br />
            2.1 The <span className="font-poppins-semibold">COOP PARTNER</span> is responsible in designating a specific person/s within its organization to take charge of its Insurance Program. This
            designated person/s shall be duly authorized and/or licensed to sell life and non-life insurance products in accordance with the prevailing requirements adopted by the Insurance Commission
            and <span className="font-poppins-semibold">CLIMBS</span>.
            <br /> <br />
            2.2 The <span className="font-poppins-semibold">COOP PARTNER</span> agrees to sell all Life Insurance <span className="font-poppins-semibold">PRODUCT</span> of{" "}
            <span className="font-poppins-semibold">CLIMBS</span> exclusively, to the exclusion of Life Insurance PRODUCTS of all other Life Insurance Companies and/or Cooperatives, and in accordance
            with the Insurance Code of the Philippines including all the existing rules and regulations of the Insurance Commission pertaining to Life Insurance Selling.
            <br /> <br />
            2.3 The <span className="font-poppins-semibold">COOP PARTNER</span> agrees to sell all other <span className="font-poppins-semibold">PRODUCT</span> of{" "}
            <span className="font-poppins-semibold">CLIMBS</span> and its subsidiary/ies in accordance to its established sales guidelines, and to fully cooperate with{" "}
            <span className="font-poppins-semibold">CLIMBS</span> in promoting and marketing the said PRODUCTS among its members and other identified markets of which the cooperative intends to
            penetrate.
            <br /> <br />
            2.4 The <span className="font-poppins-semibold">COOP PARTNER</span> shall sell all the <span className="font-poppins-semibold">PRODUCTS</span> strictly in accordance to the prescribed
            provisions of the insurance policy proposed and shall not in any manner promise, propose, promote, or represent any product feature or benefit not covered under the said insurance policy.
            Master Policies issued by <span className="font-poppins-semibold">CLIMBS</span>, if any.
            <br /> <br />
            2.5 The <span className="font-poppins-semibold">COOP PARTNER</span> shall obtain from <span className="font-poppins-semibold">CLIMBS</span> all sales and underwriting training to
            effectively market, promote and sell the <span className="font-poppins-semibold">PRODUCTS</span>
            .
            <br />
            2.6 The <span className="font-poppins-semibold">COOP PARTNER</span> agrees to:
            <br /> <br />
            2.6.1 Attend sales meetings and training seminars as may be required and scheduled by <span className="font-poppins-semibold">CLIMBS </span> from time to time; and
            <br /> <br />
            2.6.2 Provide sales forecasts, and sales reports as may be requested by <span className="font-poppins-semibold">CLIMBS </span> periodically.
            <br /> <br />
            2.7 The <span className="font-poppins-semibold">COOP PARTNER</span> assumes all sales and marketing expenses in selling the PRODUCTS to its prospects.
            <br />
            2.8 The <span className="font-poppins-semibold">COOP PARTNER</span> shall be responsible in transmitting to <span className="font-poppins-semibold">CLIMBS </span> all individual insurance
            applications for policy issuance. Likewise, for group accounts, the <span className="font-poppins-semibolf">COOP PARTNER</span> shall be responsible in encoding the list of insured to the
            data base system provided by <span className="font-poppins-semibold">CLIMBS </span> , or in case no such data base system is provided by{" "}
            <span className="font-poppins-semibold">CLIMBS </span> , to encode in its own data base system the list of insured and accordingly transmit the same to{" "}
            <span className="font-poppins-semibold">CLIMBS </span> within a reasonable period of time from its completion.
            <br /> <br />
            2.9 The <span className="font-poppins-semibold">COOP PARTNER</span> shall be responsible in collecting all insurance premiums from its own clients and to remit the same to CLIMBS within 15
            days of the following month. Premium remittance to <span className="font-poppins-semibold">CLIMBS </span> may be done via: 1. deposit to{" "}
            <span className="font-poppins-semibold">CLIMBS </span> account; 2. Pera Padala; or other payment method, online or otherwise, available to PARTNERSHIP AGREEMENT 3 the _____________ PARTNER
            taking into special account its own peculiar situation and location. The corresponding deposit slip shall be scanned, or photographed and transmitted to CLIMBS within the same day the
            premium is deposited for immediate receipting.
            <br /> <br />
            2.10 That both parties agree the schedule of reporting and remittance of all transaction and collection shall not be later than 15th day of the succeeding month together with the
            corresponding remittance report with list of enrollees, date of birth, gender,loan amount, date of loan release, term of coverage, maturity of coverage, premium and list of beneficiaries.
            In event that the report and remittance is not remitted within the prescribed period, <span className="font-poppins-semibold">CLIMBS </span> shall not be liable of any claim. The
            submission of remittance listing can be uploaded through the online remittance system of <span className="font-poppins-semibold">CLIMBS </span> .
            <br /> <br />
            2.11 That in case of delayed premium refund due to disqualifications or adjustment, the provisions outlined in this contract shall prevail over any conflicting terms regarding claims
            arising from such delay. That <span className="font-poppins-semibold">CLIMBS </span> shall process claims based on terms and conditions of this contract, even if refund of premium has been
            delayed.
            <br /> <br />
            2.12 The <span className="font-poppins-semibold">COOP PARTNER</span> shall collect and collate all claim documents coming from its clients and shall only submit to{" "}
            <span className="font-poppins-semibold">CLIMBS </span> the complete set. Incomplete set of claim documents submitted to <span className="font-poppins-semibold">CLIMBS </span> shall not be
            processed and shall be returned to the <span className="font-poppins-semibold">COOP PARTNER</span> for completion.
            <br /> <br />
            2.13 The <span className="font-poppins-semibold">COOP PARTNER</span> agrees to turn over all claim proceeds to the beneficiaries of the insured and shall correspondingly have the receipt
            of the proceeds duly acknowledged by the beneficiaries and, at the same time, facilitate the procurement of the quit-claim documents from the same.
            <br /> <br />
            2.14 The <span className="font-poppins-semibold">COOP PARTNER</span> agrees to return all properties provided by <span className="font-poppins-semibold">CLIMBS </span> including software,
            supplies, collateral materials, price schedules etc. which have been provided to the
            <span className="font-poppins-semibold">COOP PARTNER</span> without charge, upon termination of this Agreement, or upon written request by{" "}
            <span className="font-poppins-semibold">CLIMBS </span> for whatever valid reasons.
            <br />
            2.15 For Life &amp; Non-life Products of <span className="font-poppins-semibold">CLIMBS </span> as stated in ANNEX “A”, once <span className="font-poppins-semibolf">COOP PARTNER</span>{" "}
            avail the <span className="font-poppins-semibolf">COOP PARTNER</span> said products for its members and/or borrowers, agrees to enroll 100% their qualified members with{" "}
            <span className="font-poppins-semibold">CLIMBS </span> exclusively.
          </p>
        </div>
        <br />
        <br />
        <div ref={sections.climbs.ref}>
          3. <span className="font-poppins-semibold underline">CLIMBS’s RESPONSIBILITIES</span>
          <br />
          <p className="mx-8">
            3.1 <span className="font-poppins-semibold">CLIMBS </span> agrees to provide the PRODUCTS as may needed by the <span className="font-poppins-semibolf">COOP PARTNER</span> to distribute,
            the product collateral, and product trainings.
            <br /> <br />
            3.2 <span className="font-poppins-semibold">CLIMBS </span> agrees to provide sales, marketing, administrative and underwriting training and seminars to the{" "}
            <span className="font-poppins-semibolf">COOP PARTNER</span>’s officers and staff for empowerment purposes. PARTNERSHIP AGREEMENT 4
            <br /> <br />
            3.3 <span className="font-poppins-semibold">CLIMBS </span> agrees to include <span className="font-poppins-semibolf">COOP PARTNER</span> in all of its sales contests and promotions and
            shall provide COOP PARTNER with updated information on how to qualify in the same.
            <br /> <br />
            3.4 <span className="font-poppins-semibold">CLIMBS </span> agrees to provide a certificate of coverage to each group client that COOP PARTNER acquired indicating therein that the
            particular group and its members are covered under the Master Policy issued for a specific product availed of through the COOP PARTNER.
            <br /> <br />
            3.5 <span className="font-poppins-semibold">CLIMBS </span> agrees to provide a claim settling facility to COOP PARTNER which will allow the settlement of claims within ten (10) to fifteen
            (15) working days from <span className="font-poppins-semibold">CLIMBS </span> receipt of the complete set of claim documents as specified under Section 2.12 hereof.
            <br /> <br />
            3.6 <span className="font-poppins-semibold">CLIMBS </span> agrees to provide a claims check, if required by the COOP PARTNER. The system of deposit and withdrawal of said fund shall be
            subject to a subsequent agreement between the COOP PARTNER and <span className="font-poppins-semibold">CLIMBS </span>.
            <br /> <br />
            3.7 <span className="font-poppins-semibold">CLIMBS </span> agrees to draw the claim check in the name of the beneficiary of the member of the COOP PARTNER.
            <br /> <br />
            3.8 <span className="font-poppins-semibold">CLIMBS </span> shall provide the COOP PARTNER with the complete management fee or each product. The management fee shall be the sole basis of
            the payment of <span className="font-poppins-semibold">CLIMBS </span> to the COOP PARTNER for its sales and marketing efforts. This Agreement confers no other benefit or compensation
            payments to the COOP PARTNER from <span className="font-poppins-semibold">CLIMBS </span> , unless the latter, from time to time, provides additional benefits or incentives to the former
            during special events like marketing contests and promotions. The management fees may be amended by <span className="font-poppins-semibold">CLIMBS </span> anytime subject to at least
            thirty (30) days prior written notice to the COOP PARTNER.
          </p>
        </div>
        <br /> <br />
        <div ref={sections.mutual.ref}>
          4. <span className="font-poppins-semibold underline">MUTUAL OBLIGATIONS</span>
          <p className="mx-8">
            4.1 The parties have agreed that all names of qualified persons submitted by COOP PARTNER for coverage shall be underwritten by <span className="font-poppins-semibold">CLIMBS </span> based
            on its standard underwriting practices and procedures.
            <br /> <br />
            4.2 <span className="font-poppins-semibold">CLIMBS </span> may decline to accept individual and/or group applications submitted by COOP PARTNER, for whatever cause which in CLIMBS’ sole
            opinion renders such application unacceptable, including, but not limited to location hazards, political hazards, and moral hazards.
            <br /> <br />
            4.3 Each Group Master Policy issued under this Agreement shall be a contract between <span className="font-poppins-semibold">CLIMBS </span> and the COOP PARTNER. Third party group enrolees
            are deemed covered and bound by the governing provisions of the aforementioned Master Policy. Only a certificate of inclusion shall be issued to each third party group indicating therein
            that all its enrollees are covered under the applicable Master Policy.PARTNERSHIP AGREEMENT 5
            <br /> <br />
            4.4 The parties have agreed that for a third party group to be officially accepted as included in the CLIMBS - COOP PARTNER group insurance program, a minimum number of enrolees from the
            third party group is required. The minimum number enrollees from each new third party group shall be one hundred percent (100%) participation of all eligible members to maintain the
            insurance policy. The third group may then enrol subsequent members on a weekly basis with no minimum number required.
            <br /> <br />
            4.5 The COOP PARTNER shall make no representations and warranties to third party groups except those specifically indicated in the issued Master Policy.
            <br /> <br />
            4.6 Individual policies issued under this Agreement is a private contract between the <span className="font-poppins-semibold">CLIMBS </span> and the individual insured. The COOPPARTNER’s
            role in soliciting individual account is purely limited to sales, marketing and after sales services.
            <br /> <br />
            4.7 The parties agree and consent that <span className="font-poppins-semibold">CLIMBS </span> may collect, use and disclose personal data of insured members, as provided in this agreement,
            or obtained by <span className="font-poppins-semibold">CLIMBS </span> as a result of this agreement, and that COOP PARTNER and its insured members consent to do the same, for the following
            purposes in accordance with the Data Privacy Act of 2012: The processing and/or enrollment of policy of insured individual, and The processing of insurance claims of insured individual.
            <br /> <br />
            4.8 One (1) year contestability for pre-existing illness only: Any member-borrower renew his/her loan but has increased its loan amount, the excess amount will be contestable for one (1)
            year. Member-borrower insured whose last coverage have lapsed for more than twelve (12) months shall be considered as “new application” and shall be subject for contestability period of
            one (1) year and may be required to submit medical requirements as needed.
          </p>
        </div>
        <br /> <br />
        <div ref={sections.relationship.ref}>
          5. <span className="font-poppins-semibold underline">RELATIONSHIP OF THE PARTIES</span>
          <p className="mx-8">
            5.1 The COOP PARTNER and <span className="font-poppins-semibold">CLIMBS </span> agrees that the COOP PARTNER serves under this Agreement as an independent sales partner without power to
            bind, act for, or obligate <span className="font-poppins-semibold">CLIMBS </span> , whether by expression or implication except as specifically provided in this Agreement.
            <br /> <br />
            5.2 Each party hereto hereby agrees to indemnify and hold the other harmless against any negligent or intentional acts by the offending party, its representatives, employees, or
            contractees that cause damage or injury to third parties.
          </p>
          <br />
        </div>
        <br /> <br />
        <div ref={sections.confidentiality.ref}>
          6. <span className="font-poppins-semibold underline">CONFIDENTIALITY AGREEMENT</span>
          <p className="mx-8">
            <br />
            Each party agrees that it shall, at all times, regardless of termination of this Agreement, keep in strict confidence any and all information (proprietary or otherwise, written or verbal)
            relating to the other party and those of its affiliates, subsidiaries, directors, officers, employees and/or customers. Each party use utmost efforts to prevent any unauthorized disclosure
            or use of confidential information, applying the degree of care which applies to its own confidential information.
          </p>
        </div>
        <br /> <br />
        <div ref={sections.indemnification.ref}>
          7. <span className="font-poppins-semibold underline">INDEMNIFICATION AGREEMENT</span>
          <p className="mx-8">
            <br />
            Each party shall be liable and shall indemnify the other party, its affiliates, subsidiaries, officers, directors, shareholders, employees and agents for any and all claims, demands,
            losses, expenses, costs or damages of whatever nature, whether accrued or absolute, contingent or otherwise, arising out of or in connection with the defaulting party’s breach of this
            Agreement.
          </p>
        </div>
        <br /> <br />
        <div ref={sections.termination.ref}>
          8. <span className="font-poppins-semibold underline">TERMINATION OF CONTRACT</span>
          <p className="mx-8">
            8.1 This agreement shall remain in full force and effect unless amended, modified, revoked or terminated by the parties. The modification or termination of this agreement may be made by
            both parties for valid and/or legal reasons that may not undermine any provisions of this agreement, provided that a written notice will be given to the other party at least thirty (30)
            days prior hereto.
            <br /> <br />
            8.2 The revocation, cancellation or termination of this agreement shall not in any way prejudice, diminish, or abate any cause of action already accruing to{" "}
            <span className="font-poppins-semibold">CLIMBS </span> prior to or at the time of the effective date of the revocation, cancellation, or termination. In case of Termination of this policy,
            any unused premium shall be returned to COOP PARTNER, less 10% operational cost, within 30 days from date of termination. Thereafter, <span className="font-poppins-semibold">CLIMBS </span>{" "}
            shall have no further liability or obligation for any and all subsequent claims arising from events occurring after the date of termination. Claims arising from events occurring prior to
            termination shall still be considered valid;
            <br /> <br />
            8.3 This agreement shall be binding upon and shall inure to the benefit of the successors and assigns of the parties herein.
          </p>
          <br />
        </div>
        <br /> <br />
        <div ref={sections.amendments.ref}>
          9. <span className="font-poppins-semibold underline">AMENDMENTS AND VENUE OF SUIT</span>
          <p className="mx-8">
            Any amendments to this Agreement shall be in writing and only upon the mutual agreement of both parties. The Parties hereby agree that any suit, action or proceeding arising from or in
            relation to this Agreement shall be brought to the proper court with competent jurisdiction in Cagayan de Oro City to the exclusion of all other courts.
          </p>
        </div>
        <br /> <br />
        <div ref={sections.separability.ref}>
          <p className="font-poppins-semibold underline">10. SEPARABILITY CLAUSE</p>
          <p className="mx-8">
            If any term or condition of this Agreement is declared contrary to the law, the other terms and conditions hereof shall not be affected thereby and shall remain fully valid, subsisting and
            enforceable.
          </p>
        </div>
        <br />
        <span className="font-poppins-semibold">IN WITNESS WHEREOF</span>, the Parties have hereunto set their hands on the date and place first written above.
        <br /> <br />
        <div className="flex justify-center gap-4">
          <div>
            <div className="w-60">
              <span className="font-poppins-semibold flex justify-center text-center">CLIMBS LIFE AND GENERAL INSURANCE COOPERATIVE</span>
            </div>
            <br />
            By:
            <br /> <br />
            <span className="font-poppins-semibold underline">NOEL D. RABOY</span>
            <br />
            President and CEO
            <br />
            <br />
            Attested by:
            <br />
            <span className="font-poppins-semibold underline"> RENAN P. DIAZ </span>
            <br />
            Vice President - Sales General Manager
          </div>
          <div>
            <div className="w-60">
              <span className="font-poppins-semibold flex justify-center text-center">{data?.cooperative?.coopName}</span>
            </div>
            <br />
            By:
            <br /> <br />
            <span className="font-poppins-semibold underline">{formatOfficerName(primarySignatory)}</span>
            <br />
            <span>{getPositionName(primarySignatory)}</span>
            <br />
            <br />
            Attested by: <br />
            <br />
            <span className="font-poppins-semibold underline">{formatOfficerName(secondarySignatory)}</span>
            <br />
            <span>{getPositionName(secondarySignatory)}</span>
          </div>
        </div>
        <br />
        <br />
        {productGuidelines?.length > 0 && (
          <>
            <div className="h-6" />
            <Typography className="text-center font-poppins-semibold text-xl">PRODUCT GUIDELINES</Typography>
            <div className="xl:p-8 p-4">
              <div className="flex flex-col">
                <div className="flex flex-col xl:text-base text-xs">
                  {productGuidelines.map((g: any, gIndex: number) => {
                    return (
                      <div key={`guideline-${gIndex}`} className="scroll-mt-16 flex flex-1 flex-col mb-2 xl:mb-10" ref={(el) => guidelineRefs && (guidelineRefs.current[gIndex] = el)}>
                        <Typography className="xl:text-xl text-sm font-poppins-semibold text-primary">{g.label}</Typography>

                        {g.productGuideline?.map((pg: any, pgIndex: number) => {
                          let listValue: IGuidelineContent[] | undefined;
                          let tableValue: IGuidelineContentTable | undefined;

                          if (pg.type === "list") listValue = pg.value as IGuidelineContent[];
                          if (pg.type === "table") tableValue = pg.value as IGuidelineContentTable;

                          return (
                            <div key={`pg-${pgIndex}`} className="p-2">
                              {pg.type === "textfield" && <Typography className="ml-4 xl:mt-4 mt-0 text-justify xl:text-base text-xs">{pg.value as string}</Typography>}

                              {pg.type === "list" && (
                                <Fragment>
                                  {!!pg.label && <Typography className="ml-4 xl:mt-4 mt-0 xl:text-justify text-start xl:text-base text-xs">{pg.label}</Typography>}
                                  <ul className="list-disc xl:ml-12 ml-6">
                                    {listValue?.map((li, liIdx) => (
                                      <li key={`listItem-${liIdx}`} className="xl:mt-4 mt-0">
                                        <Typography className="text-justify xl:text-base text-xs">{li.value as string}</Typography>
                                      </li>
                                    ))}
                                  </ul>
                                </Fragment>
                              )}

                              {pg.type === "texteditor" && <div className="ml-4 mt-2 xl:text-base text-xs" dangerouslySetInnerHTML={{ __html: pg.value ?? "" }} />}

                              {pg.type === "table" && (
                                <div className="flex flex-1 mt-2 mx-6 overflow-x-auto xl:text-base text-xs">
                                  <table className="table border-[1px]">
                                    <thead>
                                      <tr>
                                        {tableValue?.columns?.map((c, cIdx) => (
                                          <td key={`col-${cIdx}`} className="table-cell border-[1px] xl:text-base text-xs">
                                            <Typography className="font-semibold text-xs">{c.value as string}</Typography>
                                          </td>
                                        ))}
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {tableValue?.rows?.map((r, rIdx) => (
                                        <tr key={`row-${rIdx}`}>
                                          {r.map((cell, cellIdx) => (
                                            <td key={`cell-${cellIdx}`} className="border-[1px] xl:text-base text-xs">
                                              <Typography size="xs">{cell.value as string}</Typography>
                                            </td>
                                          ))}
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    );
                  })}
                </div>
                <div>
                  <p className="font-poppins-semibold text-center mb-10"> ACKNOWLEDGEMENT </p>
                  <p className="mb-8">Republic of the Philippines City of Cagayan de Oro S.S </p>
                  <p className="mt-2">
                    Before Me, Notary Public, this {data?.proposalNotarization?.agreementNotarizationDate ? formatDate(data.proposalNotarization.agreementNotarizationDate, "d") : "______"} day of{" "}
                    {data?.proposalNotarization?.agreementNotarizationDate ? formatDate(data.proposalNotarization.agreementNotarizationDate, "MMMM") : "______"} ,
                    {data?.proposalNotarization?.agreementNotarizationDate ? formatDate(data.proposalNotarization.agreementNotarizationDate, "yyyy") : "______"}, personally appeared:{" "}
                  </p>
                  <div className="flex gap-2 mt-8">
                    <div>
                      <p className="font-poppins-semibold">NAME</p>
                      <p className="underline font-poppins-semibold"> NOEL D. RABOY</p>
                      <p className="underline font-poppins-semibold"> RENAN P. DIAZ, MBM </p>
                      <p>_______________</p>
                      <p>_______________</p>
                    </div>
                    <div>
                      <p className="font-poppins-semibold">VALID ID</p>
                      <p>_______________</p>
                      <p>_______________</p>
                      <p>_______________</p>
                      <p>_______________</p>
                    </div>
                    <div>
                      <p className="font-poppins-semibold">EXPIRY DATE</p>
                      <p>_______________</p>
                      <p>_______________</p>
                      <p>_______________</p>
                      <p>_______________</p>
                    </div>
                  </div>
                  <p className="mt-10">
                    Known to me and to me known as the same persons who signed and executed the foregoing Memorandum of Agreement and acknowledged before me that the same is of their free act and
                    deed, as well as the Cooperative represented therein.{" "}
                  </p>
                  <p className="mt-4">
                    This instrument consisting of eleven (11) pages including this page wherein this acknowledgement is written have been signed by the parties together with their instrumental
                    witnesses on each and every page hereof.{" "}
                  </p>

                  <p className="mt-6">In Witness Whereof, I have unto set my hand and affix my notarial seal at the place and on the date above written. </p>
                  <p className="mt-10">Doc. No.</p>
                  <p> Page No. </p>
                  <p>Book No. </p>
                  <p>Series of {new Date().getFullYear()}</p>
                </div>

                {/* Commission Structure (optional) */}
                {showCommission && data?.proposable?.commission && (
                  <Fragment>
                    <Typography size="md" className="font-poppins-semibold text-primary">
                      Commission Structure
                    </Typography>

                    <Typography className="ml-4 mt-4 xl:text-base text-xs">
                      {parseFloat(data?.proposable?.commission?.maximumDisposableRate ?? "").toFixed(2)}% Maximum Disposable Commission - Standard Rate
                    </Typography>

                    <div className="flex-flex-1 mt-6 mx-6 overflow-x-auto">
                      <table className="table">
                        <thead>
                          <tr>
                            <td className="table-cell border-1 text-center text-xs">Type</td>
                            <td className="table-cell border-1 text-center text-xs">Age Type</td>
                            {standard.length > 0 && (
                              <>
                                <td className="table-cell border-1 text-center text-xs">Age From</td>
                                <td className="table-cell border-1 text-center text-xs">Age To</td>
                              </>
                            )}
                            <td className="table-cell border-1 text-center text-xs">Rate</td>
                          </tr>
                        </thead>
                        <tbody>
                          {data?.proposable?.commission?.commissionDetails?.map((row: any, idx: number) => (
                            <tr key={`commissionDetailsRow-${idx}`}>
                              <td className="table-cell border-1 text-xs text-center font-poppins-semibold">{row?.commissionType?.commissionName}</td>
                              <td className="table-cell border-1 text-xs text-center">{row?.commissionAgeType?.name}</td>
                              {standard.length > 0 && (
                                <>
                                  <td className="table-cell border-1 text-center text-xs">{row?.ageFrom}</td>
                                  <td className="table-cell border-1 text-center text-xs">{row?.ageTo}</td>
                                </>
                              )}
                              <td className="table-cell border-1 text-center text-xs">{row?.rate ? parseFloat(row.rate.toString()).toFixed(0) : ""}%</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </Fragment>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default AgreementSectionsContent;
