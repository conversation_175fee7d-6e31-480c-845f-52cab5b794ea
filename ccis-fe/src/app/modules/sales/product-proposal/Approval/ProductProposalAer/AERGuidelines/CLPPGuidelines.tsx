import { FC, useEffect } from "react";
import Typography from "@components/common/Typography";
import { useContestabilityActions } from "@state/reducer/contestability";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { ProductGuidelineType, ProposalAerGuidelines } from "@enums/proposalAerGuidelines";

const CLPPGuidelines: FC<{
  approvedAERDetails: any;
  productGuidelines: any[];
}> = ({ approvedAERDetails, productGuidelines }) => {
  const { getContestability } = useContestabilityActions();
  const contestability = useSelector((state: RootState) => state.contestability.getContestability);

  const quotation = approvedAERDetails?.quotation ?? approvedAERDetails?.proposable?.quotation;
  const clppBenefits = quotation?.clppBenefits ?? [];
  const hasRider = clppBenefits?.[0]?.hasRider !== 0;

  useEffect(() => {
    getContestability({ filter: "" });
  }, []);
  return (
    <div className="flex flex-col">
      {productGuidelines?.map((value: any, gIndex: number) => {
        if ([ProposalAerGuidelines.SalientFeatures, ProposalAerGuidelines.ClaimProcessFlow, ProposalAerGuidelines.UnderwritingProcedure].includes(value.label)) {
          return null;
        }

        if ([ProposalAerGuidelines.ScheduleOfBenefits].includes(value.label)) {
          const nonTableGuidelines = value.productGuideline.filter((pg: any) => pg.type !== ProductGuidelineType.Table);
          const htmlContent = nonTableGuidelines
            .map((pg: any) => (pg.type === ProductGuidelineType.Texteditor ? pg.value : pg.type === ProductGuidelineType.TextField ? `<p>${pg.value}</p>` : ""))
            .join("");

          return (
            <div key={`guideline-${gIndex}`} className="flex flex-col mb-6">
              <Typography className="text-[18px] mb-1 font-poppins-semibold text-primary">{value.label}</Typography>

              <div className="overflow-x-auto mb-6">
                <table className="table border-[1px] w-full">
                  <thead className="bg-amber-300">
                    <tr>
                      <th className="border-[1px] text-xs font-poppins-semibold">Benefits</th>
                      <th className="border-[1px] text-xs font-poppins-semibold">Coverage</th>
                      <th className="border-[1px] text-xs font-poppins-semibold">Type of Coverage</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="border-[1px] text-sm">Credit life insurance</td>
                      <td className="border-[1px] text-sm">100% equal to the Loan Insure</td>
                      <td className="border-[1px] text-sm text-center" rowSpan={hasRider ? 3 : 1}>
                        {quotation?.coverageType?.coverageType ?? "N/A"}
                      </td>
                    </tr>

                    {hasRider && (
                      <>
                        <tr>
                          <td className="border-[1px] text-sm">Riders: (Optional) Accidental Death</td>
                          <td className="border-[1px] text-sm">200% of the Loan Insured Amount</td>
                        </tr>
                        <tr>
                          <td className="border-[1px] text-sm">Dismemberment and Disability</td>
                          <td className="border-[1px] text-sm">According to schedule</td>
                        </tr>
                      </>
                    )}
                  </tbody>
                </table>
              </div>

              {htmlContent && <div className="text-justify text-wrap px-4" dangerouslySetInnerHTML={{ __html: htmlContent }} />}
            </div>
          );
        }

        if ([ProposalAerGuidelines.BenefitDescription].includes(value.label)) {
          return (
            <div key={`guideline-${gIndex}`} className="flex flex-col mb-6">
              <Typography className="text-[18px] mb-2 mt-2 font-poppins-semibold text-primary">{value.label}</Typography>

              {value.productGuideline?.map((pg: any, pgIndex: number) => {
                if (pg.type === ProductGuidelineType.Texteditor) {
                  let modifiedValue = pg.value ?? "";

                  if (!hasRider) {
                    modifiedValue = modifiedValue
                      .replace(/<p>2\.\s*<strong>Accidental Death<\/strong>[\s\S]*?<\/p>/gi, "")
                      .replace(/<p>3\.\s*<strong>Dismemberment &amp; Disability<\/strong>[\s\S]*?<\/p>/gi, "");
                  }

                  return <div key={pgIndex} className="text-justify text-wrap px-4" dangerouslySetInnerHTML={{ __html: modifiedValue }} />;
                }

                return null;
              })}
            </div>
          );
        }

        if ([ProposalAerGuidelines.ScheduleOfBenefitsForAccidentalDismemberment].includes(value.label) && !hasRider) {
          return null;
        }

        if ([ProposalAerGuidelines.ScheduleOfPremiums].includes(value.label)) {
          const hasTable = value.productGuideline?.some((pg: any) => pg.type === ProductGuidelineType.Table);

          return (
            <div key={`guideline-${gIndex}`} className="flex flex-col mb-6">
              <Typography className="text-[18px] mt-4 font-poppins-semibold text-primary">SCHEDULE OF PREMIUMS:</Typography>

              {hasTable && (
                <div className="overflow-x-auto mb-6">
                  <table className="table border-[1px] mt-2 w-full">
                    <thead className="bg-amber-300">
                      <tr>
                        <th className="border-[1px] text-xs font-poppins-semibold">Age Limit</th>
                        <th className="border-[1px] text-xs font-poppins-semibold">Gross Premium Rate (No Rider)</th>
                        <th className="border-[1px] text-xs font-poppins-semibold">Gross Premium Rate (With Rider)</th>
                        <th className="border-[1px] text-xs font-poppins-semibold">Maximum Term</th>
                      </tr>
                    </thead>
                    <tbody>
                      {clppBenefits.map((benefit: any, index: number) => (
                        <tr key={index}>
                          <td className="border-[1px] text-sm text-center">
                            {benefit.ageFrom} to {benefit.ageTo} years old
                          </td>
                          <td className="border-[1px] text-sm text-center">{benefit?.rate ?? "N/A"}/1000 months</td>
                          <td className="border-[1px] text-sm text-center">{benefit?.rate != null ? `${(Number(benefit?.rate) + 0.1).toFixed(2)}/1000 months` : "N/A"}</td>
                          <td className="border-[1px] text-sm text-center">{benefit.maximumTerm} Months</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}

              {value.productGuideline
                ?.filter((pg: any) => pg.type !== ProductGuidelineType.Table)
                .map((pg: any, pgIndex: number) => {
                  if (pg.type === ProductGuidelineType.Texteditor) {
                    return <div key={pgIndex} className="text-justify text-wrap px-4 mt-4" dangerouslySetInnerHTML={{ __html: pg.value ?? "" }} />;
                  }

                  if (pg.type === ProductGuidelineType.TextField) {
                    return (
                      <Typography key={pgIndex} className="text-justify mt-4">
                        {pg.value}
                      </Typography>
                    );
                  }

                  if (pg.type === "list") {
                    return (
                      <div key={pgIndex} className="mt-4">
                        <Typography className="font-poppins-semibold">{pg.label}</Typography>
                        <ul className="list-disc ml-6">
                          {pg.value?.map((item: any, idx: number) => (
                            <li key={idx}>
                              <Typography>{item.value}</Typography>
                            </li>
                          ))}
                        </ul>
                      </div>
                    );
                  }

                  return null;
                })}
            </div>
          );
        }

        if ([ProposalAerGuidelines.UnderwritingApproval].includes(value.label)) {
          return (
            <div key={`guideline-${gIndex}`} className="flex flex-col mb-6">
              <Typography className="text-[18px] mb-2 mt-2 font-poppins-semibold text-primary">{value.label}</Typography>

              {value.productGuideline?.map((pg: any, pgIndex: number) => {
                if (pg.type === ProductGuidelineType.Texteditor) {
                  let modifiedValue = pg.value;

                  let ageFrom = 66;
                  let ageTo = 69;

                  if (clppBenefits.length > 1) {
                    ageFrom = clppBenefits[0]?.ageFrom ?? 66;
                    ageTo = clppBenefits[1]?.ageTo ?? 69;
                  } else if (clppBenefits.length === 1) {
                    ageFrom = clppBenefits[0]?.ageFrom ?? 66;
                    ageTo = clppBenefits[0]?.ageTo ?? 69;
                  }

                  const pattern =
                    /<p class="ql-align-justify"><span style="color: black;">7\.\&nbsp;\&nbsp;\&nbsp;\&nbsp;For ages \d+ to \d+ years old, the maximum aggregate active coverage amount limit of Php250,000\.00\.<\/span><\/p>/;

                  const replacement = `<p class="ql-align-justify"><span style="color: black;">7.&nbsp;&nbsp;&nbsp;&nbsp;For ages ${ageFrom} to ${ageTo} years old, the maximum aggregate active coverage amount limit of Php250,000.00.</span></p>`;

                  if (pattern.test(modifiedValue)) {
                    modifiedValue = modifiedValue.replace(pattern, replacement);
                  }

                  return <div key={pgIndex} className="text-justify text-wrap px-4" dangerouslySetInnerHTML={{ __html: modifiedValue }} />;
                }

                return null;
              })}
            </div>
          );
        }

        if ([ProposalAerGuidelines.Contestability].includes(value.label)) {
          const contestabilityId = quotation?.contestability;
          const contestabilityObject = contestability?.data?.find((item: any) => item.id === contestabilityId);
          const selectedContestability = contestabilityObject?.value || contestabilityObject?.label || "";

          return (
            <div key={`guideline-${gIndex}`} className="flex flex-col mb-6">
              <Typography className="text-[18px] mb-2 mt-2 font-poppins-semibold text-primary">{value.label}</Typography>

              {value.productGuideline?.map((pgValue: any, pgIndex: number) => {
                if (pgValue.type === ProductGuidelineType.Texteditor) {
                  const isWaivedWithMasterlist = selectedContestability === "Waived with masterlist";

                  if (isWaivedWithMasterlist) {
                    const customContestabilityHTML = `
                      <ul style="list-style-type: disc; margin-left: 20px;">
                        <li>One (1) year contestability period for new & incoming members. However, waive contestability period for all members included in the Masterlist submitted and enrolled at the same time on the FIRST REMITTANCE.</li>
                      </ul>
                      <p>Members who fail to renew within the thirty (30) day grace period shall be considered as "new" and will automatically be subject to a one (1) year contestability period.</p>
                    `;
                    return <div key={`pg-${pgIndex}`} className="text-justify text-wrap px-4" dangerouslySetInnerHTML={{ __html: customContestabilityHTML }} />;
                  }

                  return <div key={`pg-${pgIndex}`} className="text-justify text-wrap px-4" dangerouslySetInnerHTML={{ __html: pgValue.value ?? "" }} />;
                }

                return null;
              })}
            </div>
          );
        }

        if ([ProposalAerGuidelines.Termination].includes(value.label)) {
          return (
            <div key={`guideline-${gIndex}`} className="flex flex-col mb-6">
              <Typography className="text-[18px] mb-2 mt-2 font-poppins-semibold text-primary">{value.label}</Typography>

              {value.productGuideline?.map((pg: any, pgIndex: number) => {
                if (pg.type === "list") {
                  const listLabel = pg.label;
                  const originalList = pg.value ?? [];

                  const latestAgeTo = clppBenefits.reduce((max: number, curr: any) => {
                    const age = curr?.ageTo ?? 0;
                    return age > max ? age : max;
                  }, 0);
                  const replacementAge = latestAgeTo > 0 ? latestAgeTo + 1 : 70;

                  const updatedList = originalList.map((item: any) => {
                    if (typeof item.value === "string" && item.value.includes("policyholder attends 70th birthday")) {
                      return {
                        ...item,
                        value: `The date on which the policyholder attends ${replacementAge}th birthday.`,
                      };
                    }
                    return item;
                  });

                  return (
                    <div key={pgIndex} className="mt-4 px-4">
                      {listLabel && <Typography className="font-poppins-semibold mb-2">{listLabel}</Typography>}
                      <ul className="list-disc ml-6">
                        {updatedList.map((item: any, idx: number) => (
                          <li key={idx}>
                            <Typography>{item.value}</Typography>
                          </li>
                        ))}
                      </ul>
                    </div>
                  );
                }

                return null;
              })}
            </div>
          );
        }

        return (
          <div key={`guideline-${gIndex}`} className="flex flex-col mb-6">
            <Typography className="text-[18px] mb-2 mt-2 font-poppins-semibold text-primary">{value.label}</Typography>

            {value.productGuideline?.map((pg: any, pgIndex: number) => {
              if (pg.type === ProductGuidelineType.Texteditor) {
                return <div key={pgIndex} className="text-justify text-wrap px-4" dangerouslySetInnerHTML={{ __html: pg.value ?? "" }} />;
              }

              if (pg.type === ProductGuidelineType.TextField) {
                return (
                  <Typography key={pgIndex} className="text-justify mt-4">
                    {pg.value}
                  </Typography>
                );
              }

              if (pg.type === "list") {
                return (
                  <div key={pgIndex} className="mt-4">
                    <Typography className="font-poppins-semibold">{pg.label}</Typography>
                    <ul className="list-disc ml-6">
                      {pg.value?.map((item: any, idx: number) => (
                        <li key={idx}>
                          <Typography>{item.value}</Typography>
                        </li>
                      ))}
                    </ul>
                  </div>
                );
              }

              return null;
            })}
          </div>
        );
      })}
    </div>
  );
};

export default CLPPGuidelines;
