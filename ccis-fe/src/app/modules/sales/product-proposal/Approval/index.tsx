import { FC, ReactNode, useEffect, useState } from "react";
import Tabs from "@components/common/Tabs";
import ProductProposal from "./ProductProposal";
import PartnershipAgreement from "./PartnershipAgreement";
import CommissionStructure from "./CommissionStructure";
import Requirements from "./Requirements";
import Button from "@components/common/Button";
import { IoChevronBack } from "react-icons/io5";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { getProductProposalAERByIDService, getProductProposalByIDService } from "@services/proposal/proposal.service";
import Loader from "@components/Loader";
import { IProductProposal } from "@interface/product-proposal.interface";
import { ProposalStatus } from "@enums/proposal-status";
import { toast } from "react-toastify";
import { ROUTES } from "@constants/routes";
import { UserRoles } from "@interface/routes.interface";
import ProductProposalAER from "./ProductProposalAer";
import { CustomizeType, ProposableTypes } from "@enums/enums";
import { Statuses } from "@constants/global-constant-value";

const Approval: FC = () => {
  const navigate = useNavigate();
  const params = useParams();
  const location = useLocation();

  const [data, setData] = useState<IProductProposal>();

  const [loading, setLoading] = useState<boolean>(true);

  const isRequirementsValid = (data?.requirementable?.status ?? "").toUpperCase() === Statuses.VALID;

  const isCommissionStructureApproved = (data?.commissionStructure?.status ?? "").toUpperCase() === Statuses.APPROVED;

  const fetchData = async () => {
    try {
      if (location.state?.proposableType === CustomizeType.AER) {
        const { data } = await getProductProposalAERByIDService(params.id ?? "", location.state?.type);
        setData(data);
      } else {
        const { data } = await getProductProposalByIDService(params.id ?? "", location.state?.type);
        setData(data);
      }
      setLoading(false);
    } catch (error) {
      toast.error("Error fetching product proposal");
    }
  };
  useEffect(() => {
    fetchData();
  }, []);

  const headers = [
    { label: "Product Proposal", disabled: false },

    {
      label: "Requirements",
      disabled: data?.status === ProposalStatus.rejected,
    },
    {
      label: "Commission Structure",
      disabled: data?.status === ProposalStatus.rejected || !isRequirementsValid,
    },
    {
      label: "Partnership Agreement",
      disabled: data?.status === ProposalStatus.rejected || !isCommissionStructureApproved,
    },
  ];
  const contents: ReactNode[] = [
    location.state?.proposal?.proposableType === ProposableTypes.AER ? <ProductProposalAER data={data} /> : <ProductProposal data={data} />,
    <Requirements data={data} onSuccess={fetchData} />,
    <CommissionStructure data={data} onSuccess={fetchData} />,
    <PartnershipAgreement data={data} />,
  ];

  const handleBack = () => {
    // navigate(ROUTES.ADMIN.productProposal.key);
    if (location.pathname.includes(UserRoles.sales)) {
      navigate(ROUTES.SALES.productProposal.key);
      return;
    }
    if (location.pathname.includes(UserRoles.rnd)) {
      navigate(ROUTES.RESEARCHANDDEVELOPMENT.productProposal.key);
      return;
    }
    if (location.pathname.includes(UserRoles.actuary)) {
      navigate(ROUTES.ACTUARY.productProposal.key);
      return;
    }
    if (location.pathname.includes(UserRoles.marketing)) {
      navigate(ROUTES.MARKETING.productProposal.key);
      return;
    } else {
      navigate(ROUTES.ADMIN.productProposal.key);
    }
  };

  return (
    <div className="w-full">
      {" "}
      {loading && (
        <div>
          <Loader />
        </div>
      )}
      {!loading && (
        <>
          <div className=" w-full mb-2">
            <Button classNames="flex items-center justify-center border-none" type="button" variant="primary" outline onClick={handleBack}>
              <IoChevronBack />
              Back
            </Button>
          </div>
          <div className="w-full">
            <Tabs
              headers={headers}
              contents={contents}
              size="sm"
              headerClass="min-h-14"
              activeTabClassName="bg-primary text-white"
              inActiveTabClassName="bg-sky-50"
              contentClass="min-h-[48rem] "
              className="w-full"
            />
          </div>
        </>
      )}
    </div>
  );
};

export default Approval;
