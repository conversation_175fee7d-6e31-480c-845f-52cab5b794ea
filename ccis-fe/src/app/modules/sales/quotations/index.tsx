import { ChangeEvent, use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { useQuotationActions } from "@state/reducer/quotations";
import { useModalController } from "@modules/sales/controller";
import { Breadcrumbs } from "@components/common/Breadcrumbs";
import Button from "@components/common/Button";
import Dropdown from "@modules/sales/components/dropdown";
import QuotationModal from "@modules/sales/quotations/modals/QuotationModal";
import Table, { TableHeader } from "@modules/sales/components/table";
import { getTextStatusColor } from "@helpers/text";
import { formatWordDateDDMMYYY } from "@helpers/date";
import { ROUTES } from "@constants/routes";
import { useDebounce } from "use-debounce";
import { toast } from "react-toastify";
import Filter from "@components/common/Filter";
import TextField from "@components/form/TextField";
import Select from "@components/form/Select";
import { ProductProposalStatusFilter } from "@enums/proposal-status";
import { useProductTypesManagementActions } from "@state/reducer/utilities-product-type";
import { FaEye, FaPlus } from "react-icons/fa";
import { FaRegCircleCheck } from "react-icons/fa6";
import { FiAlertTriangle } from "react-icons/fi";
import { LuFileClock } from "react-icons/lu";
import { Status } from "@constants/global-constant-value";
// import { capitalizeFirstLetterOnly } from "@helpers/text";
import { FaRegPenToSquare } from "react-icons/fa6";
import { ProductCode } from "@enums/product-code";

export default function Quotations() {
  const navigate = useNavigate();
  const [page, setPage] = useState<number>(1);
  const [pageSize, _setPageSize] = useState<number>(10);
  const { getProductType } = useProductTypesManagementActions();
  const { setQuotation, getQuotations } = useQuotationActions();
  const loading = useSelector((state: RootState) => state.quotation.getQuotations.loading);
  const { data: currentUser } = useSelector((state: RootState) => state.auth.user);
  const breadcrumbItems = [
    {
      label: "Dashboard",
      link: "/",
    },
    {
      label: "Quotations",
      link: "#",
    },
  ];

  // TODO: Replace keyw with the actual key selector
  const tableColumns: TableHeader[] = [
    {
      label: "Cooperative",
      key: "quotation.cooperative.coopName",
      sortable: true,
    },
    {
      label: "Product",
      key: "product.name",
    },
    {
      label: "Creation Date",
      key: "createdAt",
      render(data) {
        try {
          return formatWordDateDDMMYYY(new Date(data.createdAt).toISOString());
        } catch (error) {
          return formatWordDateDDMMYYY(new Date().toISOString());
        }
      },
    },
    {
      label: "Status",
      key: "status",
      className: "text-center w-fit lg:w-[100px]",
      render(data) {
        return (
          <span className={`font-semibold flex items-center gap-1 justify-center ${getTextStatusColor(data.status)}`}>
            {data.status === Status.approved && <FaRegCircleCheck size={15} className="text-green-600 " />}
            {data.status === Status.for_approval && <FiAlertTriangle size={15} className="text-yellow-600 " />}
            {data.status === Status.draft && <LuFileClock size={15} className="text-sky-400" />}

            {data.status
              .split("_")
              .map((word: any) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
              .join(" ")}
          </span>
        );
      },
    },
    {
      label: "Action",
      key: "id",
      className: "text-center ",
      tdClassName: "flex items-center justify-center h-14",
      render(object) {
        return (
          <Dropdown
            items={[
              {
                label: (
                  <span className="text-primary flex flex-row items-center gap-2">
                    <FaEye /> View
                  </span>
                ),
                onClick() {
                  setQuotation(object);
                  localStorage.setItem("productCode", object?.product?.productCode ?? "");
                  switch (object.product?.productCode?.toLowerCase()) {
                    case "gyrt":
                      navigate(ROUTES.SALES.quotationGyrtQuotationView.parse(object?.quotation?.id), { state: { isShowSignatureStatusLocal: false } });
                      break;
                    case "clpp":
                      navigate(ROUTES.SALES.quotationClppQuotationView.parse(object?.quotation?.id));
                      break;
                    case "clsp":
                      navigate(ROUTES.SALES.quotationClspQuotationView.parse(object?.quotation?.id), { state: { isShowSignatureStatusLocal: false } });
                      break;
                    case "fip":
                      navigate(ROUTES.SALES.quotationFipAerView.parse(object?.quotation?.id), { state: { isShowStatus: false } });
                      break;

                    default:
                      toast.error("invalid product type");
                      break;
                  }
                },
              },

              {
                hidden: object.status !== Status.draft || ![ProductCode.FIP, ProductCode.GYRT].includes(object?.product?.productCode),
                label: (
                  <span className="text-primary flex flex-row items-center gap-2">
                    <FaRegPenToSquare /> Edit
                  </span>
                ),
                onClick() {
                  setQuotation(object);
                  localStorage.setItem("productCode", object?.product?.productCode ?? "");
                  if (object?.product?.productCode === "CLPP") {
                    navigate(ROUTES.SALES.clppQuotation.parse(String(object?.product?.id)));
                  } else if (object?.product?.productCode === "FIP") {
                    navigate(ROUTES.SALES.fipQuotation.parse(String(object?.product?.id)), { state: { isShowStatus: false, isEdit: true, quotationData: { ...object } } });
                  } else if (object?.product?.productCode === "CLSP") {
                    navigate(ROUTES.SALES.clspQuotation.parse(String(object?.product?.id)));
                  } else {
                    navigate(ROUTES.SALES.gyrtQuotation.parse(String(object?.product?.id)), { state: { data: object, action: "edit" } });
                  }
                },
              },
            ]}
          />
        );
      },
    },
  ];

  const [search, setSearch] = useState<string>("");
  const [debouncedSearch] = useDebounce(search, 1000);
  const [dateFrom, setDateFrom] = useState<string>("");
  const [dateTo, setDateTo] = useState<string>("");
  const [statusFilter, setStatusFilter] = useState<string | undefined>("");
  const [typeFilter, setTypeFilter] = useState<number | undefined>(undefined);

  const productType = useSelector((state: RootState) => state.utilitiesProductType.productType);

  const quotations = useSelector((state: RootState) => state.quotation.getQuotations.data?.data);

  const maxPage = useSelector((state: RootState) => state.quotation.getQuotations.data?.meta?.total);

  const localFilteredQuotations = useMemo(() => {
    return quotations ?? [];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [quotations, search]);

  const modalController = useModalController();

  const statusOptions = Object.values(ProductProposalStatusFilter).map((value) => ({
    value,
    label: value,
    text: value,
  }));

  const productTypeOptions = productType.map((item) => ({
    value: item.id,
    text: item.productType,
  }));

  const handleChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  }, []);

  const handleAddQuotation = useCallback(() => {
    modalController.openFn();
  }, [modalController]);

  const handleDateFromChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    setDateFrom(e.target.value);
  }, []);
  const handleDateToChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    setDateTo(e.target.value);
  }, []);

  const handleStatusChange = useCallback((e: ChangeEvent<HTMLSelectElement>) => {
    setStatusFilter(e.target.value);
  }, []);

  const handleTypeFilterChange = useCallback((e: ChangeEvent<HTMLSelectElement>) => {
    setTypeFilter(Number(e.target.value));
  }, []);

  const handleClearAll = useCallback(() => {
    setSearch("");
    setDateFrom("");
    setDateTo("");
    setStatusFilter(undefined);
    setTypeFilter(undefined);
  }, []);

  useEffect(() => {
    getQuotations({
      params: {
        page: page,
        pageSize: pageSize,
        dateFrom: dateFrom,
        dateTo: dateTo,
        statusFilter: statusFilter,
        productTypeFilter: typeFilter,
        user: currentUser.id,
        filter: debouncedSearch,
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, pageSize, debouncedSearch, dateFrom, dateTo, statusFilter, typeFilter]);

  useEffect(() => {
    getProductType({ filter: "" });
    localStorage.removeItem("productCode");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <section className="p-3">
      <Breadcrumbs items={breadcrumbItems} />
      <h3 className="text-2xl font-[600] mb-8">Quotations</h3>
      {/* Table container */}
      <div id="quotation-table-container" className="top-[103px]">
        <div className="flex flex-row flex-wrap items-center justify-between w-full gap-2">
          <div className="flex flex-row flex-nowrap gap-3 justify-between w-full">
            <div className="w-fit">
              <Filter search={search} onChange={handleChange}>
                <div className="flex justify-end">
                  <button className="text-primary text-xs btn-sm" type="button" onClick={handleClearAll}>
                    Clear All
                  </button>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <div className="text-xs">Date From</div>
                    <TextField className="" type="date" size="sm" value={dateFrom} onChange={handleDateFromChange} />
                  </div>
                  <div>
                    <div className="text-xs">Date To</div>
                    <TextField className="" type="date" size="sm" value={dateTo} onChange={handleDateToChange} />
                  </div>
                  <div>
                    <div className="text-xs">Status</div>
                    <Select key={`status-${statusFilter}`} size="sm" placeholder="Status" options={statusOptions} value={statusFilter} onChange={handleStatusChange} />
                  </div>
                  <div>
                    <div className="text-xs">Product Type</div>
                    <Select key={`type-${typeFilter}`} size="sm" placeholder="Product Type" options={productTypeOptions} value={typeFilter} onChange={handleTypeFilterChange} />
                  </div>
                </div>
              </Filter>
            </div>
            <Button classNames="w-fit h-[40px] ms-auto" variant="success" onClick={handleAddQuotation}>
              <span className="text-nowrap">
                <FaPlus className="inline mr-2" />
                Add Quotation
              </span>
            </Button>
          </div>
        </div>

        {/* Table component */}
        <div className="mt-4">
          <Table columns={tableColumns} data={localFilteredQuotations ?? []} currentPage={page} rowsPerPage={pageSize} maxItems={maxPage} paginate={true} onPageChange={setPage} loading={loading} />
        </div>
      </div>
      {/*  */}
      <QuotationModal controller={modalController} />
    </section>
  );
}
