type Feature = {
  id: number;
  name: string;
};

type SalientFeature = {
  id: number;
  name: string;
  features: Feature[];
};

type ScheduleOfBenefit = {
  id: number;
  benefit: string;
  coverage: string;
};

type Requirement = {
  id: number;
  name: string;
};

type BasicClaimRequirement = {
  id: number;
  name: string;
  requirements: Requirement[];
};

type Guideline = {
  id: number;
  name: string;
  description: string;
};

type GroupYearlyRenewableTerm = {
  id: number;
  title: string;
  product_description: string;
  salient_features: SalientFeature[];
  schedule_of_benefits: ScheduleOfBenefit[];
  basic_claim_requirements: BasicClaimRequirement[];
  guidelines_on_clpp: Guideline[];
};
