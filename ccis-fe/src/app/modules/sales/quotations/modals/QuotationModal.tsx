/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import { ROUTES } from "@constants/routes";
import { ModalController } from "@modules/sales/controller";
import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import Table, { TableHeader } from "@modules/sales/components/table";
import colorMode from "@modules/sales/utility/color";
import { useProductActions } from "@state/reducer/products";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { IProduct } from "@interface/products.interface";
import { toast } from "react-toastify";
import { PiEmptyDuotone } from "react-icons/pi";
import { IGuideline, IGuidelineContent } from "@interface/guidelines.interface";
import Input from "@modules/sales/components/input";
import { FaSearch } from "react-icons/fa";

interface QuotationModalProps {
  controller: ModalController;
}

const QuotationModal = ({ controller }: QuotationModalProps) => {
  const [search, setSearch] = useState<string>("");
  const [selected, setSelected] = useState<number>(0);

  const { getMainProducts, setSelectedProduct } = useProductActions();
  const products = useSelector((state: RootState) => state.products.getMainProducts);

  const navigate = useNavigate();

  useEffect(() => {
    if (!controller.isOpen) return;
    getMainProducts();
  }, [controller.isOpen]);

  const samples = useMemo<IProduct[]>(() => (products.data?.data ?? []).filter((data: IProduct) => data.name.toLowerCase().includes(search.toLowerCase())), [products, search]);

  const selectedData = useMemo<IProduct | undefined>(() => {
    return samples.find((data: IProduct) => Number(data.id) == selected);
  }, [samples, selected]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  };

  const handleCancel = () => {
    controller.closeFn();
  };

  const handleSelect = () => {
    if (!selectedData) return toast.warn("Please select a product");
    localStorage.setItem("productCode", selectedData?.productCode ?? "");
    setSelectedProduct(selectedData);
    if (selectedData?.productCode == "CLPP") {
      navigate(ROUTES.SALES.clppQuotation.parse(String(selectedData?.id)));
    } else if (selectedData?.productCode === "FIP") {
      navigate(ROUTES.SALES.fipQuotation.parse(String(selectedData?.id)));
    } else if (selectedData?.productCode == "CLSP") {
      navigate(ROUTES.SALES.clspQuotation.parse(String(selectedData?.id)));
    } else {
      navigate(ROUTES.SALES.gyrtQuotation.parse(String(selectedData?.id)));
    }
  };

  useEffect(() => {
    /**
     * If loaded products are empty, set the first product as selected
     */
    if (samples.length > 0) {
      setSelected(Number(samples[0].id));
    } else {
      setSelected(0);
    }
  }, [samples]);

  return (
    <Modal
      isOpen={controller.isOpen}
      onClose={() => controller.closeFn()}
      modalBgColor={colorMode({
        classLight: "!bg-gradient-to-b !from-[#FFFFFF] !to-[#E8EEFF] !via-[#FDFDFD] !rotate-160",
        classDark: "!bg-[#1D232A]",
      })}
      title="Select Product for Quotation"
      showHeader
      modalContainerClassName="lg:max-w-[1290px] rounded-3xl !p-0"
    >
      <div>
        <div className="grid grid-cols-12 gap-[4px] h-[590px]">
          <div className="col-span-4 border border-black/10 w-100 h-100 overflow-y-auto">
            <div className="p-4 block space-y-2 items-center h-full">
              <div className="relative w-full">
                <Input placeholder="Search Product" onChange={handleSearch} icon={<FaSearch />} />
              </div>
              {/* Loader */}
              {products.loading &&
                Array.from({ length: 15 }).map((_, index) => (
                  <div
                    key={index}
                    className={colorMode({
                      classLight: "animate-pulse !bg-light-info shrink-0 w-full h-10 rounded-md",
                      classDark: "animate-pulse !bg-black/10 shrink-0 w-full h-10 rounded-md",
                    })}
                  >
                    <div className="h-full w-full bg-gray-200 rounded-md"></div>
                  </div>
                ))}
              {/* Empty State */}
              {!products.loading && samples.length <= 0 && (
                <div className="flex flex-col items-center justify-center !h-full">
                  <div className="flex flex-col items-center">
                    <PiEmptyDuotone className="text-4xl text-gray-400 mb-4" />
                    <h2 className="text-lg font-semibold text-gray-500">No Products Found</h2>
                    <p className="text-sm text-gray-400">Try adjusting your search criteria.</p>
                  </div>
                </div>
              )}
              {/* Actual Productss */}
              {samples.map((data: IProduct) => (
                <Button
                  key={data.id}
                  classNames={colorMode({
                    className: `
                      text-start
                      ${
                        Number(data.id) == selected
                          ? "bg-primary !text-white !rounded-md"
                          : colorMode({
                              classLight: "bg-light-info !text-gray !rounded-md",
                              classDark: "bg-black/10 !text-white/60 !rounded-md",
                            })
                      }
                  `,
                  })}
                  onClick={() => setSelected(Number(data.id))}
                  block
                >
                  <span className="text-sm font-[400] break-words whitespace-break-spaces text-wrap">{data.name}</span>
                </Button>
              ))}
            </div>
          </div>
          {/*  */}
          <div className="col-span-8 border border-black/10 h-[100%] overflow-y-auto">
            <div className="p-8">
              {/* Header */}
              <h4
                className={colorMode({
                  classLight: "text-2xl text-center mb-8 text-gray",
                  classDark: "text-2xl text-center mb-8 text-white/60",
                })}
              >
                {selectedData?.name}
              </h4>
              {/* Product */}
              <span
                className={colorMode({
                  classLight: "block text-lg font-[500] font-poppins-semibold mb-6 text-gray",
                  classDark: "block text-lg font-[500] font-poppins-semibold mb-6 text-white/60",
                })}
              >
                Product Description
              </span>
              {/* Revisions */}
              {selectedData?.productRevisions.map((data) => (
                <div className="block mt-2" key={data.id}>
                  <p
                    className={colorMode({
                      classLight: "block text-sm mb-5 text-gray",
                      classDark: "block text-sm mb-5 text-white/60",
                    })}
                  >
                    {data.description}
                  </p>
                  <div>
                    {data.productGuidelines?.map((guideline: IGuideline, index: number) => (
                      <div className="block mb-20" key={`${guideline.label}-${index}`}>
                        <span
                          className={colorMode({
                            classLight: "block text-lg font-[500] font-poppins-semibold text-gray",
                            classDark: "block text-lg font-[500] font-poppins-semibold text-white/60",
                          })}
                        >
                          {guideline.label}
                        </span>
                        {(guideline as any).description && (
                          <p
                            className={colorMode({
                              classLight: "block text-[12px] font-[400] text-gray",
                              classDark: "block text-[12px] font-[400] text-white/60",
                            })}
                          >
                            {" "}
                            @@ {(guideline as any).description}
                          </p>
                        )}
                        <div className="block space-y-3 mt-5">
                          {guideline.productGuideline.map((content: IGuidelineContent, index: number) => (
                            <RenderGuidelineContent key={`${content.id}-${index}`} content={content} />
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      <div className="grid grid-cols-12 gap-4">
        <div className="col-span-12 lg:col-span-3">
          <Button variant="danger" classNames="min-w-[195px] rounded-lg" onClick={handleCancel} block>
            Cancel
          </Button>
        </div>
        {/*  */}
        <div className="col-span-12 lg:col-span-3 col-spart-1 lg:col-start-10">
          <Button variant="primary" classNames="min-w-[240px] rounded-lg" onClick={handleSelect} block disabled={!selectedData}>
            Select
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default QuotationModal;

const RenderGuidelineContent = ({ content }: { content: IGuidelineContent }) => {
  switch (content.type) {
    case "textfield":
      return (
        <div className="block space-y-2">
          <label
            htmlFor=""
            className={colorMode({
              classLight: "text-[14px] font-[500] text-gray",
              classDark: "text-[14px] font-[500] text-white/60",
            })}
          >
            {content.label}
          </label>
          <div
            className={colorMode({
              className: "text-[14px] font-[400] text-gray",
              classDark: "text-[14px] font-[400] text-white/60",
            })}
            dangerouslySetInnerHTML={{
              __html: content.value?.toString() || "",
            }}
          />
        </div>
      );
    case "texteditor":
      return (
        <div className="block space-y-2">
          <label
            htmlFor=""
            className={colorMode({
              classLight: "text-[14px] font-[500] text-gray",
              classDark: "text-[14px] font-[500] text-white/60",
            })}
          >
            {content.label}
          </label>
          {/* <Wysiwyg stateValue={content.value?.toString()} /> */}
          <div
            dangerouslySetInnerHTML={{
              __html: content.value?.toString() || "",
            }}
          />
        </div>
      );
    case "list":
      return (
        <div className="block space-y-2">
          <label
            htmlFor=""
            className={colorMode({
              classLight: "text-[14px] font-[500] text-gray",
              classDark: "text-[14px] font-[500] text-white/60",
            })}
          >
            {content.label}
          </label>
          <ul
            className={colorMode({
              classLight: "pl-5",
              classDark: "pl-5",
            })}
          >
            {(content.value as IGuidelineContent[]).map((inner, index) => (
              <li
                key={`${inner.id}-${index}`}
                className={colorMode({
                  classLight: "text-[12px] font-[400] text-gray",
                  classDark: "text-[12px] font-[400] text-white/60",
                })}
              >
                {inner.value?.toString()}
              </li>
            ))}
          </ul>
        </div>
      );
    case "table": {
      const cols: TableHeader[] = (content.value as any).columns.map(
        (inner: any) =>
          ({
            label: inner.value,
            key: inner.id.toString().replace("col-", "row-"),
            className: "text-center",
            tdClassName: inner.id == "col-0" ? "text-left" : "text-right",
          }) as TableHeader
      );
      const rows: any[] = (content.value as any).rows.map((inner: any[], index: number) => ({
        id: `row-${index}`,
        [inner[0]?.id]: inner[0]?.value,
        [inner[1]?.id]: inner[1]?.value,
      }));
      return (
        <div className="block space-y-2" key={content.id}>
          <label
            htmlFor=""
            className={colorMode({
              classLight: "text-[14px] font-[500] text-gray",
              classDark: "text-[14px] font-[500] text-white/60",
            })}
          >
            {content.label}
          </label>
          <Table columns={cols} data={rows} />
        </div>
      );
    }
    case "image":
      return (
        <div className="elevation-1 shadow-md">
          <img src={content.value?.toString()} alt={content.label} />
          <div
            className={colorMode({
              className: "p-4",
              classLight: "text-[14px] font-[500] text-gray",
              classDark: "text-[14px] font-[500] text-white/60",
            })}
          >
            {content.label}
          </div>
        </div>
      );
  }
};
