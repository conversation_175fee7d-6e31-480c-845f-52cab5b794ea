
export type ColorModeProps = {
    className?: string;
    classLight?: string;
    classDark?: string;
    textLightColor?: string;
    textDarkColor?: string;
    borderLightColor?: string;
    borderDarkColor?: string;
    colorLight?: string;
    colorDark?: string;
};

const colorMode = ({
    className='',
    classLight='',
    classDark='',
    textLightColor='',
    textDarkColor='',
    borderLightColor='',
    borderDarkColor='',
    colorLight='',
    colorDark='',
}: ColorModeProps): string => {
    const isDarkMode = () => window.matchMedia("(prefers-color-scheme: dark)").matches;

    // Watchers for color mode changes
    const handleColorModeChange = (event: MediaQueryListEvent) => {
        // You can add any additional logic here if needed when the color mode changes
        console.log("Color mode changed to:", event.matches ? "dark" : "light");
        // Trigger a re-render or state update if necessary
    };

    const mediaQueryList = window.matchMedia("(prefers-color-scheme: dark)");
    mediaQueryList.addEventListener('change', handleColorModeChange);

    const currentColor = isDarkMode() ? colorDark : colorLight;
    return colorLight?.length || colorDark?.length ? currentColor : 
           ` ${className} ${isDarkMode() ? classDark : classLight} ${isDarkMode() ? textDarkColor : textLightColor} ${isDarkMode() ? borderDarkColor : borderLightColor} `;
}

export default colorMode;
