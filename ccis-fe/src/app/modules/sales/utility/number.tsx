export function romanize(num: number): string {
    if (isNaN(num))
        return NaN.toString();
    const digits = String(+num).split("");
    const key = ["","C","CC","CCC","CD","D","DC","DCC","DCCC","CM",
               "","X","XX","XXX","XL","L","LX","LXX","LXXX","XC",
               "","I","II","III","IV","V","VI","VII","VIII","IX"];
    let roman = "";
    let i = 3;
    while (i--) {
        const digit = digits.pop();
        roman = (key[+(digit || "0") + (i * 10)] || "") + roman;
    }
    return Array(+digits.join("") + 1).join("M") + roman;
}

export function parseNumber(num: string|number): number {
    try {
        if (num?.toString().trim() == "") return 0;
        const parsed = parseFloat(num.toString().replace(/,/g, ""));
        return isNaN(parsed) ? 0 : parsed;
    } catch (error) {
        return 0;
    }
}