import { useEffect, useState } from "react";
// import { useDispatch } from "react-redux";
import { STATUS_OPTIONS, Statuses } from "@constants/global-constant-value";
import { IoChevronBack } from "react-icons/io5";
import Button from "@components/common/Button";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import EditableTable from "@modules/sales/components/editable-table";
// import { PiPrinter } from "react-icons/pi";
import { useModalController } from "@modules/sales/controller"; // basig gamiton pani sa senior
import SubmitModal from "@modules/sales/view-aer/modals/submit.modal"; // basig gamiton pani sa senior
import { useQuotationActions } from "@state/reducer/quotations";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { useProductBenefitsManagementActions } from "@state/reducer/utilities-product-benefits";
// import { IUtilitiesProductBenefits } from "@interface/utilities.interface";
import { formatWordDateDDMMYYY } from "@helpers/date";
import TextArea from "@components/form/TextArea";
import { getGyrtAer } from "@services/quotation/quotation.service";
import { ICooperative } from "@interface/product-proposal.interface";
import { IContestability } from "@interface/contestability.interface";
import { IProduct, ISignatories } from "@interface/products.interface";
import { navigateBack } from "@helpers/navigatorHelper";
import SummaryHeaderQuotation from "@modules/admin/approval-aer/components/SummaryHeader";
import { FaUsers } from "react-icons/fa";
import Typography from "@components/common/Typography";
import Tabs from "@components/common/Tabs";
import Signatories2 from "@modules/users/approvals/review-revisions/Tabs/Signatories2";
import ApprovalHistory from "@modules/admin/products/components/Common/ApprovalHistory";
import { useUserId } from "@helpers/data";
import Modal from "@components/common/Modal";
import ChangeStatusForm from "@modules/admin/approval-aer/components/forms/ChangeStatus";
import { TApprovalPayload } from "@state/types/users-product-approval";
import { toast } from "react-toastify";
import ApproverRemarksDynamic from "@modules/admin/approval-aer/components/ApproverRemarksDynamic";
import Table from "@modules/actuary/AER/components/Table";
import { columns4 } from "@modules/actuary/AER/GYRT/Forms/data2";
import { HIDDEN_COMMISSION_ID } from "../gyrt-quotations/components/constants";
import { ROUTES } from "@constants/routes";
import { AERStatus } from "@enums/aer-status";

type GyrtQuotationProduct = {
  id: number;
  productId: number;
  quotationId: number;
  options: string; // e.g., "[1,2]"
  remarks: string | null;
  status: string;
  contestabilityId: number;
  contestability: IContestability;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;

  product: IProduct;

  quotation: {
    id: number;
    coopId: number;
    previousProvider: string;
    branch: string;
    contestability: number;
    totalNumberOfMembers: number;
    productId: number;
    coverageTypeId: number | null;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;

    quotationClaimsExperienceAge: {
      id: number;
      ageFrom: number;
      ageTo: number;
      numberOfDeaths: number;
      totalClaimAmount: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];

    quotationClaimsExperienceYear: {
      id: number;
      year: string;
      numberOfDeaths: number;
      totalClaimAmount: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];

    quotationCommissionDistribution: {
      id: number;
      commissionTypeId: number;
      rate: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
      isAgentInput: number;
      commissionAgeType: string | null;
      ageFrom: number | null;
      ageTo: number | null;
    }[];

    gyrtQuotations: {
      id: number;
      premiumBudget: string;
      averageAge: number;
      averageClaims: number;
      maxDependents: number;
      quotationId: number;
      status: string;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    };

    quotationPremium: {
      id: number;
      quotationId: number;
      grossPremium: string;
      netPremium: string;
      option: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];

    quotationCondition: {
      id: number;
      quotationId: number;
      condition: string;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    };
    cooperative: ICooperative;
  };
};

// TO BE USED LATER
// type GyrtBenefitOption = {
//   id: number;
//   quotationId: number;
//   benefitId: number;
//   option: number;
//   coverage: string; // e.g., "99999"
//   memberType: string | null;
//   createdAt: string; // ISO date string
//   updatedAt: string; // ISO date string
//   deletedAt: string | null;
// };

// function BaseQuotationGyrtAerView({ showActions = false, showStatus = false, isShowSignatureStatus = false }: { showActions?: boolean; showStatus: boolean; isShowSignatureStatus?: boolean }) {
function BaseQuotationGyrtAerView({ showActions = false, isShowSignatureStatus = false }: { showActions?: boolean; isShowSignatureStatus?: boolean }) {
  // const navigate = useNavigate();
  const { id } = useParams();
  const navigate = useNavigate();
  const { setQuotation, getAerSignatoriesLogs, putAerSignatoriesStatus, clearAerSignatoriesStatus } = useQuotationActions();
  const { getProductBenefits } = useProductBenefitsManagementActions();

  const quotationData: GyrtQuotationProduct | any | undefined = useSelector((state: RootState) => state.quotation.quotation);

  // const productBenefits: IUtilitiesProductBenefits[] = useSelector((state: RootState) => state.utilitiesProductBenefits.productBenefits);

  // const updateAerStatusState = useSelector((state: RootState) => state.quotation.updateAerStatus);
  const userId = useUserId();
  const selectedQuotationSignature: GyrtQuotationProduct | any | undefined = useSelector((state: RootState) =>
    (state.quotation?.quotation as any)?.approval?.signatories.find((items: any) => items?.userId === userId)
  );

  const putSignatoriesSucccess = useSelector((state: RootState) => state.quotation.putAerSignatoriesStatus?.success);
  const signatoryLogs = useSelector((state: RootState) => state?.quotation?.getAerSignatoriesLogs?.data?.data);
  const modalController = useModalController();

  // const mappedHeader = useMemo<EditableTableColumn[]>(() => {
  //   if (!quotationData) return [];
  //   const options: string[] = Array?.from(new Set(quotationData?.quotation?.gyrtBenefits?.map((gyrtBen: GyrtBenefitOption) => `OPTION ${gyrtBen?.option}`)));
  //   return [
  //     {
  //       header: "",
  //       key: "benefitName",
  //     },
  //     ...options.map((option) => ({
  //       className: "!text-center",
  //       align: "center" as const,
  //       header: option,
  //       key: option,
  //     })),
  //   ];
  // }, [quotationData]);
  const [statusModal, setStatusModal] = useState<boolean>(false);
  const toggleModal = () => setStatusModal((prev) => !prev);
  const [remarksModal, setRemarksModal] = useState<boolean>(false);
  const toggleRemarksModal = () => setRemarksModal((prev) => !prev);
  const [processing, setProcessing] = useState<boolean>(false);
  const [ishidebutton, setIsHideButton] = useState<boolean>(false);
  const [dataCurrentSignatory, setDataCurrentSignatory] = useState<ISignatories>({} as ISignatories);
  const [selectedOption, setSelectedOption] = useState<number>(0);

  const loadOptions = (option: number) => {
    setSelectedOption(option);
  };

  const statusOptions = STATUS_OPTIONS;

  // TO BE USED LATER
  // const mappedRows = useMemo<Record<string, any>[]>(() => {
  //   const gyrtBenefits = quotationData?.quotation?.gyrtBenefits as GyrtBenefitOption[];
  //   const options: number[] = Array.from(new Set(quotationData?.quotation?.gyrtBenefits?.map((gyrtBen: GyrtBenefitOption) => gyrtBen?.option)));

  //   const rowsData: Record<string, any>[] = [];

  //   for (const option of options) {
  //     const rowData: Record<string, any> = {};
  //     for (const gyrtBenefit of gyrtBenefits) {
  //       const benefit = productBenefits.find((item) => item.id === gyrtBenefit.benefitId);
  //       if (!benefit) continue;
  //       rowData["benefitName"] = benefit?.benefitName;
  //       if (gyrtBenefit.option === option) {
  //         rowData[`OPTION ${gyrtBenefit.option}`] = Intl.NumberFormat("en-US", {
  //           style: "currency",
  //           currency: "Php",
  //           minimumFractionDigits: 2,
  //           maximumFractionDigits: 2,
  //         }).format(Number(gyrtBenefit.coverage) ?? 0);
  //       } else {
  //         rowData[`OPTION ${gyrtBenefit.option}`] = "₱0.00";
  //       }
  //     }
  //     rowsData.push(rowData);
  //   }

  //   return rowsData;
  // }, [quotationData]);

  // const dispatch = useDispatch();

  const [_selectedRemarks, setSelectedRemarks] = useState<string[]>([]);
  const [otherRemarks, setOtherRemarks] = useState<string>("");

  useEffect(() => {
    const isAllIn = quotationData?.quotation?.gyrtAge?.some((item: any) => item?.ageType === "All");
    const isWavedContestability = String(quotationData?.quotation?.contestabilityPeriod?.label ?? "")
      .toLowerCase()
      .includes("waived");
    const isFivePercentMembers = "";

    setSelectedRemarks([
      ...(isAllIn ? ["All in"] : []),
      ...(isWavedContestability ? ["Waived contestability"] : []),
      ...(isFivePercentMembers ? ["5% of Members aged 65-69"] : []),
      ...(otherRemarks ? [otherRemarks] : []),
    ]);
  }, [quotationData]);

  // const handleSaveAsDraft = () => {
  //   updateAerStatus({
  //     id: quotationData?.id || "",
  //     status: "Draft",
  //   });

  //   toast.success("AER saved as draft successfully."); //temporary
  // };

  const handleSubmit = async () => {
    modalController.openFn();

    // const payload = {
    //   remarks: JSON.stringify([...selectedRemarks, otherRemarks].filter(Boolean)),
    // };
    // await dispatch(
    //   updateAerStatus({
    //     id: quotationData?.id || "",
    //     remarks: payload.remarks,
    //     status: Statuses.FOR_APPROVAL,
    //   })
    // );
    // toast.success("AER submitted for approval successfully."); //temporary
    // navigate(ROUTES.SALES.quotations.key);
  };

  useEffect(() => {
    getProductBenefits({ filter: "" });
  }, []);

  const fetchData = async () => {
    const { data }: { data: GyrtQuotationProduct[] } = await getGyrtAer(id!);
    if (!data) return;
    const model = data.find((item) => item.quotationId == parseInt(id!));
    if (!model) return;
    setQuotation(model);
    setSelectedOption(JSON.parse(model.options)[0] || 0);
  };

  useEffect(() => {
    if (id) fetchData();
  }, []);
  const handleSubmitStatus = async (data: TApprovalPayload) => {
    setProcessing(true); // Set processing before the try block
    try {
      const transformedData = {
        approvalSignatoryId: selectedQuotationSignature?.id,
        // status: data.approvalStatus,
        status: AERStatus.for_approval,
        remarks: data.remarks || " ",
      };

      // Await the asynchronous operation
      await putAerSignatoriesStatus(transformedData);
    } catch (error) {
      toast.error("An error occurred while submitting the status. Please try again.");
    }
  };
  useEffect(() => {
    if (selectedQuotationSignature) {
      getAerSignatoriesLogs({ id: Number(selectedQuotationSignature?.approvalId) || 0 });
    }
    if (selectedQuotationSignature?.status === Statuses.PENDING) {
      setIsHideButton(false);
    } else {
      setIsHideButton(true);
    }
  }, [selectedQuotationSignature]);

  useEffect(() => {
    if (id && putSignatoriesSucccess) {
      fetchData();
      setProcessing(false);
      toggleModal();
      clearAerSignatoriesStatus();
    }
  }, [putSignatoriesSucccess]);

  const handleDataCurrentSignatory = (data: ISignatories) => {
    setDataCurrentSignatory(data);
  };

  // TO BE USED LATER
  // const handlePrint = async (apiUrl: string, queryParams?: string) => {
  //   try {
  //     const endpoint = queryParams ? `${apiUrl}?${queryParams}` : apiUrl;
  //     const response: any = await httpClient.get(endpoint, {
  //       responseType: "blob",
  //     });

  //     const blob = new Blob([response], { type: "application/pdf" });
  //     const url = window.URL.createObjectURL(blob);
  //     window.open(url, "_blank");
  //   } catch (error) {
  //     toast.error(`PDF export failed: ${String(error)}`);
  //   }
  // };

  return (
    <section>
      <Button classNames="bg-white border-0 flex items-center justify-center" outline variant="primary" onClick={navigateBack}>
        <IoChevronBack />
        Back
      </Button>
      <div className="my-6">
        {isShowSignatureStatus && (
          <SummaryHeaderQuotation quotationInfo={quotationData as any} onSubmit={() => toggleModal()} hideButton={ishidebutton} currentUserStatus={selectedQuotationSignature?.status} />
        )}
      </div>
      <div className="grid grid-cols-12 gap-3">
        {/*  */}
        {/* <div className={`col-span-12 ${showStatus ? "md:col-span-9" : "md:col-span-12"}`}> */}
        <div className="col-span-12 md:col-span-9">
          <div>
            <div className="flex justify-between">
              <div>
                <h2 className="text-xl font-bold font-poppins-semibold text-primary mb-4">ACTUARY EVALUATION REPORT</h2>
              </div>
              {!showActions && (
                <div>
                  <Button
                    classNames=" bg-info text-xs font-poppins-semibold"
                    onClick={() => navigate(ROUTES.SALES.gyrtQuotation.parse(String(quotationData?.product?.id)), { state: { data: quotationData, action: "copy" } })}
                  >
                    Create Copy
                  </Button>
                </div>
              )}
            </div>
            <div className="grid grid-cols-1 gap-3 text-gray-700">
              <div className="space-y-2 text-gray-700">
                <div className="flex justify-between gap-2">
                  <p className="font-medium text-sm">AER No.</p> <p className="text-justify w-1/2 font-poppins-semibold text-2xl">{quotationData?.id}</p>
                </div>
                {/* <div className="flex justify-between gap-2">
                  <p className="font-medium text-sm">Version</p> <p className="text-justify w-1/2">Version 1</p>
                </div> */}
                <div className="flex justify-between gap-2">
                  <p className="font-medium text-sm">Date</p> <p className="text-justify w-1/2">{formatWordDateDDMMYYY(new Date((quotationData as any)?.createdAt ?? new Date()).toISOString())}</p>
                </div>
                <div className="flex justify-between gap-2">
                  <p className="font-medium text-sm">Type of Plan</p> <p className="text-justify w-1/2">{quotationData?.product?.name}</p>
                </div>
                <div className="flex justify-between gap-2">
                  <p className="font-medium text-sm">Cooperative</p> <p className="text-justify w-1/2">{quotationData?.quotation?.cooperative?.coopName}</p>
                </div>
                <div className="flex justify-between gap-2">
                  <p className="font-medium text-sm">Previous Provider</p> <p className="text-justify w-1/2">{quotationData?.quotation?.previousProvider}</p>
                </div>
                <div className="flex justify-between gap-2">
                  <p className="font-medium text-sm">Premium Budget</p>{" "}
                  <p className="text-justify w-1/2">
                    ₱ {(Number(quotationData?.quotation?.gyrtQuotations?.premiumBudget) || 0).toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </p>
                </div>
                <div className="flex justify-between gap-2">
                  <p className="font-medium text-sm">Contestability</p> <p className="text-justify w-1/2">{quotationData?.quotation.contestabilityPeriod?.value}</p>
                </div>
                <div className="flex justify-between gap-2">
                  <p className="font-medium text-sm">Total Members</p> <p className="text-justify w-1/2">{quotationData?.quotation.totalNumberOfMembers?.toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-4 space-y-2">
            <span className="text-[16px] font-poppins-medium">Recommendations</span>

            <div className="my-8">
              <p className="text-xl mt-4 font-poppins-semibold">Options</p>
              <p className="text-zinc-400">Current options added are display below.</p>

              {quotationData?.options && (
                <div className="w-full flex gap-2 mt-4 items-center">
                  <div className="flex gap-2">
                    {JSON.parse(quotationData?.options || "[]").map((option: number) => (
                      <div
                        className={`text-sm cursor-pointer transition duration-200 font-poppins-medium p-1 px-2 border rounded-md ${
                          selectedOption === option ? "bg-primary text-white" : "border-primary text-primary hover:bg-primary hover:text-white"
                        }`}
                        key={option}
                        onClick={() => loadOptions(option)}
                      >
                        Option {option}
                      </div>
                    ))}
                  </div>
                  {/* for future use */}
                  {/* {!showActions && !isShowSignatureStatus && (
                    <div className="ml-auto">
                      <Button
                        classNames="w-full md:w-auto bg-accent"
                        // onClick={handleCreateNewAER}
                      >
                        Copy AER
                      </Button>
                    </div>
                  )} */}
                </div>
              )}
            </div>
            <br />

            <div className="w-full text-sm bg-primary text-white p-3 font-poppins-semibold ">TOTAL GROSS AND NET PREMIUM</div>

            <div className="flex p-4 pt-0 ">
              <div className="w-1/2">
                <p className=" text-zinc-400">Gross Premium</p>
                <p className="text-3xl  font-poppins-semibold text-primary">
                  ₱{" "}
                  {parseFloat(quotationData?.quotation?.quotationPremium.find((quotationPremium: any) => quotationPremium.option === selectedOption)?.grossPremium)
                    .toFixed(2)
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}
                </p>
              </div>
              <div className="w-1/2">
                <p className=" text-zinc-400">Net Premium</p>
                <p className="text-3xl  font-poppins-semibold text-primary">
                  ₱{" "}
                  {parseFloat(quotationData?.quotation?.quotationPremium.find((quotationPremium: any) => quotationPremium.option === selectedOption)?.netPremium)
                    .toFixed(2)
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}
                </p>
              </div>
            </div>

            <br />

            <div className="flex flex-col mt-16 mb-6">
              <div className="min-h-40">
                <Table
                  data={quotationData?.quotation?.gyrtBenefits?.filter((benefit: any) => benefit.option === selectedOption)}
                  columns={columns4}
                  tableMaxHeight="max-h-96 text-xs"
                  headerClassNames={"bg-primary text-white text-xs"}
                />
              </div>
            </div>

            <div className="min-h-40">
              <EditableTable
                editable={false}
                rows={(quotationData?.quotation?.gyrtAge ?? []).filter((item: any) => item.option === selectedOption)}
                className="text-center"
                columns={[
                  {
                    key: "ageType",
                    header: "AGE TYPE",
                    align: "center",
                    className: "text-[14px] font-[500] text-nowrap",
                  },
                  {
                    key: "minimum",
                    header: "MINIMUM",
                    className: "text-[14px] font-[500] text-nowrap",
                    number: true,
                    formatInput: true,
                  },
                  {
                    key: "maximum",
                    header: "MAXIMUM",
                    className: "text-[14px] font-[500] text-nowrap",
                    number: true,
                    formatInput: true,
                  },
                  {
                    key: "exitAge",
                    header: "EXIT AGE",
                    className: "text-[14px] font-[500] text-nowrap",
                    number: true,
                    formatInput: true,
                    locked: true,
                  },
                ]}
              />
            </div>

            <div className="pb-10">
              <EditableTable
                className="border-b-0 text-center"
                editable={false}
                columns={[
                  {
                    key: "commissionType",
                    header: "COMMISSION TYPE",
                    className: "text-[14px] font-[500]",
                  },
                  {
                    key: "rate",
                    header: "PERCENTAGE RATE",
                    className: "text-[14px] font-[500]",
                    number: true,
                  },
                ]}
                rows={
                  quotationData?.quotation?.quotationCommissionDistribution
                    .filter(
                      (commission: any) => !HIDDEN_COMMISSION_ID.includes(commission.commissionTypeId) && Number(commission.option) === selectedOption // Only show commissions for the selected option
                    )
                    .map((commission: any) => ({
                      commissionType: commission.commissionType?.commissionName ?? "N/A",
                      rate: `${commission.rate * 100}%`,
                    })) || []
                }
              />
            </div>

            {showActions && (
              <div className="mt-4">
                <div className="flex flex-col md:flex-row justify-between gap-4 md:gap-0">
                  {/* Print button */}
                  {/* TO BE USED LATER */}
                  {/* <Button
                    disabled={true}
                    onClick={() => {
                      if (quotationData?.id) {
                        const exportUrl = `/actuary-evaluation-report/${quotationData.id}/export`;
                        handlePrint(exportUrl);
                      } else {
                        toast.error("Missing AER ID.");
                      }
                    }}
                    variant="primary"
                  >
                    <div className="flex flex-row w-full gap-3 justify-center items-center !text-center">
                      <PiPrinter scale={4.5} size={25} />
                      Print
                    </div>
                  </Button> */}

                  <div className="flex justify-end w-full">
                    {/* <Button variant="default" onClick={handleSaveAsDraft} classNames="w-full md:w-auto bg-accent" disabled={updateAerStatusState.loading}>
                      Save as Draft
                    </Button> */}
                    <Button variant="primary" onClick={handleSubmit} classNames="w-full md:w-auto">
                      Finish Request
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        {/*  */}

        {/* {showStatus && !isShowSignatureStatus && ( */}
        {!isShowSignatureStatus && (
          <div className="col-span-12 md:col-span-3">
            <div className="block shadow-md p-5">
              <div className="flex flex-row gap-3 justify-between items-center my-2">
                <div className="text-xl font-medium">Status</div>
                <span className="text-sm">
                  {quotationData?.status
                    ? quotationData.status
                        .toLowerCase()
                        .split("_")
                        .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
                        .join(" ")
                    : ""}
                </span>
              </div>
              {/* <div className="flex flex-row gap-3 justify-between items-center my-2">
                <div className="text-xs font-medium">Date Approved</div>
                <span className="text-sm">{formatWordDateDDMMYYY(new Date(quotationData?.updatedAt ?? new Date()).toISOString())}</span>
              </div> */}
              <hr className="my-14 border-gray/20" />
              <span className="block text-xl mb-5">REMARKS</span>
              <div className="flex flex-col gap-3">
                <span className="flex flex-row gap-2 items-center">
                  <input type="checkbox" size={12} checked={(JSON.parse(quotationData?.remarks ?? "[]") ?? []).includes("All in")} onChange={() => void null} />
                  <p className="text-xs font-[12]">Age All in</p>
                </span>
                <span className="flex flex-row gap-2 items-center">
                  <input
                    type="checkbox"
                    size={12}
                    checked={(JSON.parse(quotationData?.remarks ?? "[]") ?? []).some((remark: string) => remark?.toLowerCase().includes("waived"))}
                    onChange={() => void null}
                  />
                  <p className="text-xs font-[12]">Waived contestability</p>
                </span>
                <span className="flex flex-row gap-2 items-center">
                  <input type="checkbox" size={12} checked={(JSON.parse(quotationData?.remarks ?? "[]") ?? []).includes("5% of Members aged 66-69")} onChange={() => void null} />
                  <p className="text-xs font-[12]">5% Members aged 65-69</p>
                </span>
                <span className="flex flex-row gap-2 items-center">
                  <TextArea disabled value={(JSON.parse(quotationData?.remarks ?? "[]") ?? [])[3] ?? ""} onChange={(e) => setOtherRemarks(e.target.value)} />
                </span>
              </div>
            </div>
          </div>
        )}
        {/* {showStatus && isShowSignatureStatus && ( */}
        {!showActions && isShowSignatureStatus && (
          <>
            <div className="col-span-12 md:col-span-3 justify-center mt-10">
              <div className="flex flex-row  w-full justify-center items-center ">
                <FaUsers size={20} className="mr-2" />
                <Typography size="lg">SIGNEES</Typography>
              </div>
              <Tabs
                className="mt-4"
                contentClass="p-6 border-0 bg-sky-50"
                headerClass="text-xs rounded-t-lg h-10"
                activeTabClassName="bg-primary !text-white text-xs"
                headers={["Ongoing Approvals", "Approval History"]}
                contents={[
                  <Signatories2 signatories={quotationData?.approval?.signatories} toggleRemarksModal={toggleRemarksModal} selectedSignatory={handleDataCurrentSignatory} />,
                  <ApprovalHistory historyData={signatoryLogs} />,
                ]}
              />
            </div>
          </>
        )}
      </div>
      {statusModal && (
        <Modal title="Change AER Status" titleClass="text-md md:text-lg" modalContainerClassName="!max-w-2xl" isOpen={statusModal} onClose={toggleModal}>
          <ChangeStatusForm handleSubmit={handleSubmitStatus} isSubmitting={processing} options={statusOptions} />
        </Modal>
      )}
      {remarksModal && (
        <Modal title="Remarks" titleClass="text-md md:text-lg" modalContainerClassName="!max-w-2xl" isOpen={remarksModal} onClose={toggleRemarksModal}>
          <ApproverRemarksDynamic currentSignatory={dataCurrentSignatory} />
        </Modal>
      )}
      <SubmitModal controller={modalController} />
    </section>
  );
}

export function QuotationGyrtAerView() {
  // const location = useLocation();
  // const { isShowSignatureStatusLocal, showActions, showStatus } = location.state || {}; // Access the passed stat
  // return <BaseQuotationGyrtAerView showActions={showActions} showStatus={showStatus} isShowSignatureStatus={isShowSignatureStatusLocal} />;
  return <BaseQuotationGyrtAerView showActions={true} isShowSignatureStatus={false} />;
}

export function QuotationGyrtQuotationView() {
  // const location = useLocation();
  // const { isShowSignatureStatusLocal, showActions, showStatus } = location.state || {}; // Access the passed stat
  // return <BaseQuotationGyrtAerView showActions={showActions} showStatus={showStatus} isShowSignatureStatus={isShowSignatureStatusLocal} />;
  const location = useLocation();
  const { isShowSignatureStatusLocal, showActions } = location.state || {}; // Access the passed state
  return <BaseQuotationGyrtAerView showActions={showActions} isShowSignatureStatus={isShowSignatureStatusLocal} />;
}
