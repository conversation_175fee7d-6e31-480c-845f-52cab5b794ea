import { useEffect, useMemo, useState } from "react";
import { IoChevronBack } from "react-icons/io5";
import Button from "@components/common/Button";
import { ROUTES } from "@constants/routes";
import { useLocation, useNavigate, useParams } from "react-router-dom";

import { <PERSON><PERSON><PERSON><PERSON> } from "react-icons/pi";
import { useModalController } from "@modules/sales/controller";
import SubmitModal from "@modules/sales/view-aer/modals/submit.modal";
import { useQuotationActions } from "@state/reducer/quotations";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { useProductBenefitsManagementActions } from "@state/reducer/utilities-product-benefits";
import { formatWordDateDDMMYYY } from "@helpers/date";
import colorMode from "@modules/sales/utility/color";
import { getFipAer } from "@services/quotation/quotation.service";
import { ICooperative } from "@interface/product-proposal.interface";
import { IContestability } from "@interface/contestability.interface";
import { IProduct, ISignatories } from "@interface/products.interface";
import { eAgeType, eInsuredType, quotationRemarks, Statuses } from "@constants/global-constant-value";
import { ISelectOptions } from "@interface/common.interface";
import { IFilteredDataCondition } from "@interface/quotation.interface";
import ConditionOptionTab from "@modules/sales/fip-quotations/components/Tabs/ConditionOptionTab";
import Tabs from "@components/common/Tabs";
import { useSalesFipQuotationManagementActions } from "@state/reducer/quotation-sales-fip";
import { useContestabilityActions } from "@state/reducer/contestability";
import SummaryHeaderQuotation from "@modules/admin/approval-aer/components/SummaryHeader";
import { navigateBack } from "@helpers/navigatorHelper";
import { FaUsers } from "react-icons/fa";
import Typography from "@components/common/Typography";
import Signatories2 from "@modules/users/approvals/review-revisions/Tabs/Signatories2";
import ApprovalHistory from "@modules/admin/products/components/Common/ApprovalHistory";
import Modal from "@components/common/Modal";
import ChangeStatusForm from "@modules/admin/approval-aer/components/forms/ChangeStatus";
import { TApprovalPayload } from "@state/types/users-product-approval";
import { useUserId } from "@helpers/data";
import { toast } from "react-toastify";
import ApproverRemarksDynamic from "@modules/admin/approval-aer/components/ApproverRemarksDynamic";
import { ProposalStatus } from "@enums/proposal-status";
import { STATUS_OPTIONS } from "@constants/global-constant-value";
import { ProductCode } from "@enums/product-code";
import { isPrecedingSignatoryApproved } from "@helpers/sequentialSignatory";

type FipQuotationProduct = {
  id: number;
  productId: number;
  quotationId: number;
  options: string;
  remarks: string | null;
  status: string;
  contestabilityId: number;
  contestability: IContestability;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;

  product: IProduct;

  quotation: {
    id: number;
    coopId: number;
    previousProvider: string;
    branch: string;
    contestability: number;
    totalNumberOfMembers: number;
    productId: number;
    coverageTypeId: number | null;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;

    quotationClaimsExperienceAge: {
      id: number;
      ageFrom: number;
      ageTo: number;
      numberOfDeaths: number;
      totalClaimAmount: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];

    quotationClaimsExperienceYear: {
      id: number;
      year: string;
      numberOfDeaths: number;
      totalClaimAmount: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];

    quotationCommissionDistribution: {
      id: number;
      commissionTypeId: number;
      rate: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
      isAgentInput: number;
      commissionAgeType: string | null;
      ageFrom: number | null;
      ageTo: number | null;
    }[];

    gyrtQuotations: {
      id: number;
      premiumBudget: string;
      averageAge: number;
      averageClaims: number;
      maxDependents: number;
      quotationId: number;
      status: string;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    };

    quotationPremium: {
      id: number;
      quotationId: number;
      grossPremium: string;
      netPremium: string;
      option: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];

    quotationCondition: {
      id: number;
      quotationId: number;
      condition: string;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    };
    cooperative: ICooperative;
  };
};
type Accumulator = { option: number }[];

export default function QuotationFipAerView() {
  const navigate = useNavigate();
  const { id } = useParams();
  const location = useLocation();
  const { isShowStatus, isShowSignaturStatus, demographicData } = location.state || {}; // Access the passed state
  const { setQuotation, putAerSignatoriesStatus, clearAerSignatoriesStatus, getAerSignatoriesLogs } = useQuotationActions();
  const { getProductBenefits } = useProductBenefitsManagementActions();
  const { getContestability } = useContestabilityActions();
  const { setSelectedSalesFipQuotation, getFipAerPrint, clearFipAerPrint, clearSelectedSalesFipQuotation, clearSalesFipQuotation } = useSalesFipQuotationManagementActions();
  const userId = useUserId();
  const quotationDataFip: FipQuotationProduct | any | undefined = useSelector((state: RootState) => state.salesFipQuotation.selectedSalesFipQuotation?.data);
  const selectedQuotationSignature: FipQuotationProduct | any | undefined = useSelector((state: RootState) =>
    (state.quotation?.quotation as any)?.approval?.signatories.find((items: any) => items?.userId === userId)
  );
  const quotation = quotationDataFip[0]?.quotation?.cooperative.coopName;
  const contestabilityData = useSelector((state: RootState) => state.contestability.contestabilities);
  const pdfBlob = useSelector((state: RootState) => state?.salesFipQuotation?.getFipAerPrint?.pdfUrl);
  const successPdfBlob = useSelector((state: RootState) => state?.salesFipQuotation?.getFipAerPrint?.success);
  const [optionTabNumber, setOptionTabNumber] = useState<number>();
  const modalController = useModalController();
  const fipPrincipalMember = quotationDataFip[0]?.quotation?.fipPrincipalMember;
  const optionTabs = fipPrincipalMember?.reduce((acc: Accumulator, item: any) => {
    if (!acc.some((entry) => entry.option === item.option)) {
      acc.push({ option: item.option });
    }
    return acc;
  }, [] as Accumulator);
  const putSignatoriesSucccess = useSelector((state: RootState) => state.quotation.putAerSignatoriesStatus?.success);
  const signatoryLogs = useSelector((state: RootState) => state?.quotation?.getAerSignatoriesLogs?.data?.data);
  const contestabilityLabel = useMemo(() => {
    const contestabilityId = quotationDataFip[0]?.quotation?.fipPrincipalMember[0]?.contestabilityId;
    return contestabilityData?.find((item: IContestability) => item.id === contestabilityId)?.label;
  }, [quotationDataFip[0]?.quotation?.fipPrincipalMember[0]?.contestabilityId, contestabilityData]);
  const allIn = useMemo(() => {
    const fipAges = quotationDataFip[0]?.quotation?.fipAges ?? [];
    const hasAllIn =
      Array.isArray(fipAges) &&
      fipAges.some((item: any) => String(item.ageType).toLowerCase() === eAgeType.ALL_IN.toLowerCase() && String(item.memberType).toUpperCase() === eInsuredType.principalMember);
    return hasAllIn ? "All in" : "";
  }, [quotationDataFip[0]?.quotation?.fipAges, contestabilityData]);
  const membersAged66To69 = useMemo(() => {
    const ageArray = demographicData?.ageAndYearCounts?.age;
    const total = demographicData?.totalNumberOfMembers ?? 0;
    if (!Array.isArray(ageArray) || total === 0) return false;
    const age66to69 = ageArray.reduce((sum: number, item: any) => {
      const age = Number(item.age);
      const count = Number(item.totalCount ?? 0);
      return age >= 66 && age <= 69 ? sum + count : sum;
    }, 0);

    const percentage = (age66to69 / total) * 100;

    return percentage >= 5;
  }, [demographicData]);

  const handleSubmit = () => {
    modalController.openFn();
  };
  const handleCreateNewAER = (edit: boolean) => {
    navigate(`${ROUTES.SALES.fipQuotation.parse(quotationDataFip[0]?.productId)}`, {
      state: {
        quotationData: { ...quotationDataFip[0] },
        productCodeName: quotationDataFip[0]?.product?.productCode,
        isEdit: edit,
      },
    });
  };
  const [remarksModal, setRemarksModal] = useState<boolean>(false);
  const toggleRemarksModal = () => setRemarksModal((prev) => !prev);
  const [statusModal, setStatusModal] = useState<boolean>(false);
  const toggleModal = () => setStatusModal((prev) => !prev);
  const statusOptions = STATUS_OPTIONS;
  const [ishidebutton, setIsHideButton] = useState<boolean>(false);
  const [processing, setProcessing] = useState<boolean>(false);
  const [dataCurrentSignatory, setDataCurrentSignatory] = useState<ISignatories>({} as ISignatories);
  const handleDataCurrentSignatory = (data: ISignatories) => {
    setDataCurrentSignatory(data);
  };
  const signatoryList = quotationDataFip[0]?.approval?.signatories;
  const isUserSign = isPrecedingSignatoryApproved(signatoryList, Number(userId));
  useEffect(() => {
    getProductBenefits({ filter: "" });
    getContestability({ filter: ProductCode.FIP });
  }, []);
  useEffect(() => {
    if (selectedQuotationSignature) {
      getAerSignatoriesLogs({ id: Number(selectedQuotationSignature?.approvalId) || 0 });
    }
    if (selectedQuotationSignature?.status === Statuses.PENDING && isUserSign) {
      setIsHideButton(false);
    } else {
      setIsHideButton(true);
    }
  }, [selectedQuotationSignature]);

  const fetchData = async () => {
    const { data }: { data: FipQuotationProduct[] } = await getFipAer(id!);
    if (!data) return;
    const result = data?.reduce((acc, item: FipQuotationProduct) => {
      acc = item;
      return acc;
    }, {});

    setQuotation(result);
    setSelectedSalesFipQuotation({
      data: data as any,
      index: 0,
    });
  };

  useEffect(() => {
    if (id) fetchData();
  }, []);

  useEffect(() => {
    if (id && putSignatoriesSucccess) {
      fetchData();
      setProcessing(false);
      toggleModal();
      clearAerSignatoriesStatus();
    }
  }, [putSignatoriesSucccess]);

  const getTextFromValue = (value: string | number, options: { value: string; text: string }[]): string => {
    const option = options.find((opt) => opt.value === value);
    return option ? option.text : "";
  };
  const contestabilitySelectItems = useMemo<ISelectOptions[]>(() => {
    if (!contestabilityData) return [];
    return contestabilityData.map((item: IContestability) => ({
      text: item.label,
      value: item.id?.toString() ?? "",
    }));
  }, [contestabilityData]);

  const relationships = Array?.from(
    new Set(
      quotationDataFip[0]?.quotation?.fipCoInsuredDependentBenefit?.map((item: IFilteredDataCondition) => ({
        relationship: item.relationship,
        type: item.type,
        benefitId: item.benefitId,
        minimumAge: item.minimumAge,
        maximumAge: item.maximumAge,
        exitAge: item.exitAge,
        coverage: item.coverage,
        netPremium: quotationDataFip[0]?.quotation?.fipCoInsuredDependent
          .filter((optNum: IFilteredDataCondition) => optNum.option === item.option)
          .map((item: IFilteredDataCondition) => Number(item.netPremium))[0],
        option: item.option,
      }))
    )
  ) as Array<IFilteredDataCondition>;

  const handlePrint = async (pdfUrl: Blob | string | null | undefined) => {
    try {
      // Fetch the PDF file from the server
      const response = await fetch(pdfUrl as string);
      if (!response.ok) {
        throw new Error("Failed to fetch the PDF file.");
      }

      // Convert the response to a Blob
      const pdfBlob = await response.blob();

      // Create a Blob URL for the PDF
      const blobUrl = URL.createObjectURL(pdfBlob);

      // Open the Blob URL in a new tab
      const newWindow = window.open(blobUrl, "_blank");
      if (newWindow) {
        newWindow.focus();
        newWindow.print(); // Trigger the print dialog
      } else {
        toast.error("Failed to open the PDF in a new tab.");
      }

      // Clean up the Blob URL after use
      URL.revokeObjectURL(blobUrl);
    } catch (error) {
      toast.error("Failed to print the PDF. Please try again.");
    }
  };
  useEffect(() => {
    let printed = false;
    if (successPdfBlob) {
      handlePrint(pdfBlob);
      printed = true;
    }
    if (printed) {
      setTimeout(() => {
        clearFipAerPrint();
      }, 2000); // 2 seconds delay
    }
  }, [successPdfBlob]);
  const principalDataFind = quotationDataFip[0]?.quotation?.fipPrincipalMember?.find((opt: any) => opt?.option === optionTabNumber);
  const clearData = () => {
    clearSelectedSalesFipQuotation();
    clearSalesFipQuotation();
    clearFipAerPrint();
  };
  if (quotation) {
    const headers = relationships?.reduce((acc: string[], curr) => {
      if (!acc.some((item) => item === `OPTION ${curr.option}`)) {
        acc.push(`OPTION ${curr.option}`);
      }

      return acc;
    }, []);
    const handleTabChange = (activeTab: number) => {
      const selectedOptionNumber = optionTabs[activeTab]?.option;
      setOptionTabNumber(selectedOptionNumber);
    };
    const contentsClaims = optionTabs.map((opt: any) => {
      const filteredRelationships = relationships?.filter((item) => item.option === Number(opt.option));

      return <ConditionOptionTab relationships={filteredRelationships} optionNumber={opt.option} quotationData={quotationDataFip} />;
    });

    const handleSubmitStatus = async (data: TApprovalPayload) => {
      setProcessing(true); // Set processing before the try block
      try {
        const transformedData = {
          approvalSignatoryId: selectedQuotationSignature?.id,
          status: data.approvalStatus,
          remarks: data.remarks || " ",
        };
        // Await the asynchronous operation
        await putAerSignatoriesStatus(transformedData);
      } catch (error) {
        toast.error("Failed to update status."); // Optional: Add user feedback
      }
    };

    let remarksArray: string[] = [];

    try {
      const raw = quotationDataFip[0]?.remarks;
      remarksArray = Array.isArray(raw) ? raw : typeof raw === "string" ? JSON.parse(raw) : [];
    } catch (e) {
      remarksArray = [];
    }

    const parsedRemarks = Object.values(quotationRemarks).map((remark) => ({
      label: remark,
      checked: remarksArray.some((r) => r.toLowerCase().includes(remark.toLowerCase())),
    }));
    const note = {
      notes: remarksArray.filter((r) => !Object.values(quotationRemarks).some((predef) => r.toLowerCase().includes(predef.toLowerCase()))),
    };

    return (
      <section>
        <Button
          classNames="bg-white border-0 flex items-center justify-center"
          outline
          variant="primary"
          onClick={() => {
            if (isShowStatus) {
              clearData();
              navigateBack();
            } else if (isShowSignaturStatus || !isShowStatus) {
              clearData();
              navigateBack();
              localStorage.removeItem("productCode");
              clearAerSignatoriesStatus();
            }
          }}
        >
          <IoChevronBack />
          Back
        </Button>
        {isShowSignaturStatus && (
          <SummaryHeaderQuotation quotationInfo={quotationDataFip[0] as any} onSubmit={() => toggleModal()} hideButton={ishidebutton} currentUserStatus={selectedQuotationSignature?.status} />
        )}
        <div className={`gap-3 ${!isShowSignaturStatus ? "grid grid-cols-12" : !isShowStatus ? "grid grid-cols-12" : "grid w-full"}`}>
          {/*  */}
          <div className="col-span-12 md:col-span-9">
            <div>
              <h2 className="text-xl font-bold font-poppins-semibold text-primary mb-4">ACTUARY EVALUATION REPORT</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-gray-700">
                <div className="space-y-2 text-gray-700">
                  <div className="flex justify-between gap-2">
                    <p className="font-medium text-sm w-1/2">AER No.</p>
                    <p className="text-justify w-1/2">{quotationDataFip[0]?.quotationId}</p>
                  </div>
                  <div className="flex justify-between gap-2">
                    <p className="font-medium text-sm w-1/2">Version</p>
                    <p className="text-start w-1/2">{quotationDataFip[0]?.id}</p>
                  </div>
                  <div className="flex justify-between gap-2">
                    <p className="font-medium text-sm w-1/2">Date</p>{" "}
                    <p className="text-justify w-1/2">{formatWordDateDDMMYYY(new Date((quotationDataFip as any)?.createdAt ?? new Date()).toISOString())}</p>
                  </div>
                  <div className="flex justify-between gap-2">
                    <p className="font-medium text-sm w-1/2">Type of Plan</p> <p className="text-justify w-1/2">{quotationDataFip[0]?.product?.name}</p>
                  </div>
                  <div className="flex justify-between gap-2">
                    <p className="font-medium text-sm w-1/2">Cooperative</p>{" "}
                    <p className="text-justify w-1/2">{quotationDataFip?.cooperative?.coopName || quotationDataFip[0]?.quotation?.cooperative?.coopName}</p>
                  </div>
                  <div className="flex justify-between gap-2">
                    <p className="font-medium text-sm w-1/2">Previous Provider</p>{" "}
                    <p className="text-justify w-1/2">{quotationDataFip[0]?.quotation?.previousProvider || quotationDataFip?.previousProvider}</p>
                  </div>
                  <div className="flex justify-between gap-2">
                    <p className="font-medium text-sm w-1/2">Premium Budgets</p>{" "}
                    <p className="text-justify w-1/2">
                      {
                        // premiumBudget
                        quotationDataFip[0]?.quotation?.fipPrincipalMember?.find((opt: any) => opt?.option === optionTabNumber)?.premiumBudget
                          ? `₱${Number(quotationDataFip[0]?.quotation?.fipPrincipalMember?.find((opt: any) => opt?.option === optionTabNumber)?.premiumBudget).toLocaleString()}`
                          : "No Premium Budget Available"
                      }
                    </p>
                  </div>
                  <div className="flex justify-between gap-2">
                    <p className="font-medium text-sm w-1/2">Contestability</p>{" "}
                    <p className="text-justify w-1/2">{principalDataFind?.contestabilityId !== undefined && getTextFromValue(Number(principalDataFind.contestabilityId), contestabilitySelectItems)}</p>
                  </div>
                  <div className="flex justify-between gap-2">
                    <p className="font-medium text-sm w-1/2">Total Members</p>{" "}
                    <p className="text-justify w-1/2">{principalDataFind?.totalNumberOfMembers !== undefined ? principalDataFind?.totalNumberOfMembers?.toLocaleString() : "0.00"}</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-8 space-y-2">
              {!isShowStatus && !isShowSignaturStatus && quotationDataFip[0]?.status === ProposalStatus.for_approval && (
                <div className="col-span-12 flex justify-end gap-4">
                  <div className="flex justify-start">
                    <Button classNames="w-full md:w-auto bg-accent" onClick={() => handleCreateNewAER(false)}>
                      Copy AER
                    </Button>
                  </div>
                </div>
              )}

              <span className="text-[20px] font-poppins-medium uppercase">Recommendations</span>

              <div>
                <Tabs
                  headers={headers}
                  contents={contentsClaims}
                  size="md"
                  headerClass="w-1/2 mx-auto flex flex-row justify-center text-lg
                  font-poppins-medium"
                  fullWidthHeader={false}
                  onTabChange={handleTabChange}
                />
              </div>
              {/* for table */}

              <pre className="font-poppins-medium text-sm">
                <div
                  className="mt-4 text-sm text-wrap whitespace-break-spaces"
                  dangerouslySetInnerHTML={{
                    __html: quotationDataFip?.quotation?.quotationCondition?.condition ?? "",
                  }}
                />
              </pre>
              <div className="mt-4">
                <div className="flex flex-col md:flex-row justify-between gap-4 md:gap-0">
                  {/* Print button */}
                  <Button
                    variant="primary"
                    onClick={() => {
                      getFipAerPrint({
                        actuaryEvaluationReportId: quotationDataFip[0]?.id,
                      });
                    }}
                    classNames={colorMode({
                      classLight: "w-full md:w-auto text-white bg-primary  ",
                      classDark: "w-full md:w-auto text-gray/20 bg-slate-50 border-gray/20 !text-gray shadow-sm",
                    })}
                  >
                    <div className="flex flex-row w-full gap-3 justify-center items-center !text-center">
                      <PiPrinter scale={4.5} size={25} />
                      Print
                    </div>
                  </Button>
                  {isShowStatus || quotationDataFip[0]?.status === ProposalStatus.draft ? (
                    <div className="flex flex-col md:flex-row gap-4 md:gap-2 w-full md:w-auto">
                      <Button variant="default" onClick={() => handleCreateNewAER(true)} classNames="w-full md:w-auto bg-accent">
                        {/* Save as Draft */}
                        Edit Request
                      </Button>
                      <Button variant="primary" onClick={handleSubmit} classNames="w-full md:w-auto">
                        Submit
                      </Button>
                    </div>
                  ) : null}
                </div>
              </div>
            </div>
          </div>

          {/* {isShowStatus && ( */}
          {!isShowSignaturStatus && (
            <div className="col-span-12 md:col-span-3">
              <div className="block shadow-md p-5">
                <span className="text-xl">STATUS</span>
                <div className="flex flex-row gap-3 justify-between items-center my-2">
                  <div className="text-xs font-medium">Status</div>
                  <span className="text-sm">{quotationDataFip?.[0]?.status}</span>
                </div>
                <div className="flex flex-row gap-3 justify-between items-center my-2">
                  <div className="text-xs font-medium">Date Approved</div>
                  <span className="text-sm">{formatWordDateDDMMYYY(new Date(quotationDataFip?.[0]?.updatedAt ?? new Date()).toISOString())}</span>
                </div>
                <hr className="my-14 border-gray/20" />
                <span className="block text-xl mb-5">REMARKS</span>
                <div className="flex flex-col gap-3">
                  <span className="flex flex-row gap-2 items-center">
                    <input type="checkbox" size={12} checked={parsedRemarks.find((r) => r.label === quotationRemarks.ALLIN)?.checked} readOnly />
                    <p className="text-xs font-[12]">Age All in</p>
                  </span>

                  <span className="flex flex-row gap-2 items-center">
                    <input type="checkbox" size={12} checked={parsedRemarks.find((r) => r.label === quotationRemarks.CONTESTABILITY)?.checked} readOnly />
                    <p className="text-xs font-[12]">Waived contestability</p>
                  </span>
                  <span className="flex flex-row gap-2 items-center">
                    <input type="checkbox" size={12} checked={parsedRemarks.find((r) => r.label === quotationRemarks.FIVEPERCENT66TO69)?.checked} readOnly />
                    <p className="text-xs font-[12]">5% of Members aged 66-69</p>
                  </span>
                  {note.notes.length > 0 && (
                    <span className="flex flex-row gap-2 items-center min-h-[50px] border border-zinc-300 bg-zinc-100 rounded p-2">
                      <p className="text-xs">{note?.notes}</p>
                    </span>
                  )}
                </div>
              </div>
            </div>
          )}
          {isShowSignaturStatus && !isShowStatus && (
            <>
              <div className="col-span-12 md:col-span-3 justify-center mt-10">
                <div className="flex flex-row  w-full justify-center items-center ">
                  <FaUsers size={20} className="mr-2" />
                  <Typography size="lg">SIGNEES</Typography>
                </div>
                <Tabs
                  className="mt-4"
                  contentClass="p-6 border-0 bg-sky-50"
                  headerClass="text-xs rounded-t-lg h-10"
                  activeTabClassName="bg-primary !text-white text-xs"
                  headers={["Ongoing Approvals", "Approval History"]}
                  contents={[
                    <Signatories2 signatories={quotationDataFip[0]?.approval?.signatories} toggleRemarksModal={toggleRemarksModal} selectedSignatory={handleDataCurrentSignatory} />,
                    <ApprovalHistory historyData={signatoryLogs} />,
                  ]}
                />
              </div>
            </>
          )}
          {statusModal && (
            <Modal title="Change AER Status" titleClass="text-md md:text-lg" modalContainerClassName="!max-w-2xl" isOpen={statusModal} onClose={toggleModal}>
              <ChangeStatusForm handleSubmit={handleSubmitStatus} isSubmitting={processing} options={statusOptions} />
            </Modal>
          )}
          {remarksModal && (
            <Modal title="Remarks" titleClass="text-md md:text-lg" modalContainerClassName="!max-w-2xl" isOpen={remarksModal} onClose={toggleRemarksModal}>
              <ApproverRemarksDynamic currentSignatory={dataCurrentSignatory} />
            </Modal>
          )}
        </div>
        <SubmitModal controller={modalController} contestabilityLabel={contestabilityLabel} allIn={allIn} membersAged66To69={membersAged66To69} />
      </section>
    );
  }
}
