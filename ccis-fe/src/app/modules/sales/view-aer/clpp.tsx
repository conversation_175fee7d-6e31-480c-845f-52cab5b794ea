import { useEffect, useMemo, useState } from "react";
import { quotationRemarks, Status, STATUS_OPTIONS, Statuses } from "@constants/global-constant-value";

import Button from "@components/common/Button";
import { ROUTES } from "@constants/routes";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import EditableTable from "@modules/sales/components/editable-table";
import { PiPrinter } from "react-icons/pi";
import { useModalController } from "@modules/sales/controller"; // basig gamiton pa sa sEÑIOR
import SubmitModal from "@modules/sales/view-aer/modals/submit.modal"; // basig gamiton pa sa sEÑIOR
import { useQuotationActions } from "@state/reducer/quotations";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { useProductBenefitsManagementActions } from "@state/reducer/utilities-product-benefits";
import { formatWordDateDDMMYYY } from "@helpers/date";
import colorMode from "@modules/sales/utility/color";
import { exportAer, getClppAer } from "@services/quotation/quotation.service";
import { ICooperative } from "@interface/product-proposal.interface";
import { IContestability } from "@interface/contestability.interface";
import { IAttachmentResponse, IProduct, ISignatories } from "@interface/products.interface";
import { toast } from "react-toastify";

import SummaryHeaderQuotation from "@modules/admin/approval-aer/components/SummaryHeader";
import { FaDownload, FaUsers } from "react-icons/fa";
import Typography from "@components/common/Typography";
import Tabs from "@components/common/Tabs";
import Signatories2 from "@modules/users/approvals/review-revisions/Tabs/Signatories2";
import ApprovalHistory from "@modules/admin/products/components/Common/ApprovalHistory";
import Modal from "@components/common/Modal";
import ChangeStatusForm from "@modules/admin/approval-aer/components/forms/ChangeStatus";
import { TApprovalPayload } from "@state/types/users-product-approval";
import { useUserId } from "@helpers/data";
import ApproverRemarksDynamic from "@modules/admin/approval-aer/components/ApproverRemarksDynamic";
import { ICommissionType } from "@interface/commission-structure.interface";
import { ProductCode } from "@enums/product-code";

import { useCoverageTypeActions } from "@state/reducer/coverage-type";
import httpClient from "@clients/httpClient";
import { useCommissionTypeActions } from "@state/reducer/commision-type";
import { useContestabilityActions } from "@state/reducer/contestability";

type ClppQuotationProduct = {
  id: number;
  productId: number;
  quotationId: number;
  options: string; // e.g., "[1,2]"
  remarks: string | null;
  status: string;
  contestabilityId: number;
  contestability: IContestability;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;

  product: IProduct;

  quotation: {
    id: number;
    coopId: number;
    previousProvider: string;
    // branch: string;
    contestability: number;
    totalNumberOfMembers: number;
    productId: number;
    coverageTypeId: number | null;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;

    quotationClaimsExperienceAge: {
      id: number;
      ageFrom: number;
      ageTo: number;
      numberOfDeaths: number;
      totalClaimAmount: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];

    quotationClaimsExperienceYear: {
      id: number;
      year: string;
      numberOfDeaths: number;
      totalClaimAmount: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];

    quotationCommissionDistribution: {
      id: number;
      commissionTypeId: number;
      rate: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
      isAgentInput: number;
      commissionAgeType: string | null;
      ageFrom: number | null;
      ageTo: number | null;
    }[];

    clppQuotations: {
      id: number;
      premiumBudget: string;
      averageAge: number;
      averageClaims: number;
      maxDependents: number;
      quotationId: number;
      status: string;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    };

    quotationPremium: {
      id: number;
      quotationId: number;
      grossPremium: string;
      netPremium: string;
      option: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];

    quotationCondition: {
      id: number;
      quotationId: number;
      condition: string;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    };
    cooperative: ICooperative;
  };
};

function BaseQuotationClppAerView({ showActions = false, isShowSignatureStatus = false }: { showActions?: boolean; isShowSignatureStatus?: boolean }) {
  const navigate = useNavigate();
  const { id } = useParams();
  const contestability = useSelector((state: RootState) => state.contestability.contestabilities);
  const { setQuotation, getAerSignatoriesLogs, putAerSignatoriesStatus, clearAerSignatoriesStatus } = useQuotationActions();
  const { getProductBenefits } = useProductBenefitsManagementActions();
  const { getCommissionTypes } = useCommissionTypeActions();
  const quotationData: ClppQuotationProduct | any | undefined = useSelector((state: RootState) => state.quotation.quotation);

  const commissionDistribution = quotationData?.quotation?.quotationCommissionDistribution || [];

  // Handle any processing or formatting if needed
  const commissionRows = commissionDistribution.map((item: { ageFrom: string | number; ageTo: string | number; commissionTypeId: string | number; rate: string }) => ({
    ageFrom: item.ageFrom ?? "N/A",
    ageTo: item.ageTo ?? "N/A",
    commissionTypeId: item.commissionTypeId ?? "N/A",
    rate: item.rate ?? "0.00",
  }));
  const { getContestability } = useContestabilityActions();
  const { getCoverageTypes } = useCoverageTypeActions();
  const coverageTypesData = useSelector((state: RootState) => state.coverageType.getCoverageTypes);
  //const productBenefits: IUtilitiesProductBenefits[] = useSelector((state: RootState) => state.utilitiesProductBenefits.productBenefits);

  // const updateAerStatusState = useSelector((state: RootState) => state.quotation.updateAerStatus);
  const userId = useUserId();
  const selectedQuotationSignature: ClppQuotationProduct | any | undefined = useSelector((state: RootState) =>
    (state.quotation?.quotation as any)?.approval?.signatories.find((items: any) => items?.userId === userId)
  );
  const commissionTypesData = useSelector((state: RootState) => state.commissionType.getCommissionTypes);
  const putSignatoriesSucccess = useSelector((state: RootState) => state.quotation.putAerSignatoriesStatus?.success);
  const signatoryLogs = useSelector((state: RootState) => state?.quotation?.getAerSignatoriesLogs?.data?.data);
  const modalController = useModalController(); // basig gamiton pa sa sEÑIOR
  // const staticRemarks = ["Waved contestability", "5% of Members aged 65-69", "NEL"];  // basig gamiton pa sa sEÑIOR
  // const dispatch = useDispatch();
  const location = useLocation();

  // const [selectedRemarks, setSelectedRemarks] = useState<string[]>([]);
  // const [otherRemarks, setOtherRemarks] = useState<string>("");
  const [statusModal, setStatusModal] = useState<boolean>(false);
  const toggleModal = () => setStatusModal((prev) => !prev);
  const [ishidebutton, setIsHideButton] = useState<boolean>(false);
  const [remarksModal, setRemarksModal] = useState<boolean>(false);
  const toggleRemarksModal = () => setRemarksModal((prev) => !prev);
  const [processing, setProcessing] = useState<boolean>(false);
  const [dataCurrentSignatory, setDataCurrentSignatory] = useState<ISignatories>({} as ISignatories);
  const demographicData = location.state?.demographicData;
  const aerAttachments: IAttachmentResponse[] = useMemo(() => quotationData?.attachments ?? [], [quotationData?.attachments]);
  const statusOptions = STATUS_OPTIONS;

  const commissionTypesList = useMemo(
    () =>
      (commissionTypesData?.data ?? []).map((item: ICommissionType) => ({
        id: item.id,
        name: item.commissionName,
      })),
    [commissionTypesData]
  );

  const handleExport = async () => {
    if (!id || !quotationData) return;
    try {
      // Get blob data from API
      const response = await exportAer(quotationData.id!);
      const blob = new Blob([response]);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `aer-${quotationData?.product?.name}-${quotationData?.quotation?.cooperative?.coopName}-${id}.pdf`.toLowerCase();

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Cleanup
      window.URL.revokeObjectURL(url);
    } catch (error) {
      toast.error("Failed to export AER. Please try again.");
    }
  };
  const contestabilityData = useMemo(() => {
    return (contestability ?? []).map((item: IContestability) => ({
      id: item.id,
      label: item.label,
    }));
  }, [contestability]);

  const contestabilityLabel = useMemo(() => {
    const contestabilityId = quotationData?.quotation?.contestability;
    return contestabilityData.find((item: { id: number | undefined; label: string }) => item.id === contestabilityId)?.label;
  }, [quotationData?.quotation?.contestability, contestabilityData]);

  const membersAged66To69 = useMemo(() => {
    const ageArray = demographicData?.ageAndYearCounts?.age;
    const total = demographicData?.totalNumberOfMembers ?? 0;
    if (!Array.isArray(ageArray) || total === 0) return false;
    const age66to69 = ageArray.reduce((sum: number, item: any) => {
      const age = Number(item.age);
      const count = Number(item.totalCount ?? 0);
      return age >= 66 && age <= 69 ? sum + count : sum;
    }, 0);

    const percentage = (age66to69 / total) * 100;
    return percentage >= 5;
  }, [demographicData]);

  // All in = at least one benefit has ageTo == 99
  const allIn = useMemo(() => {
    const benefits: { ageTo: string | number }[] = quotationData?.quotation?.clppBenefits ?? [];
    if (!Array.isArray(benefits) || benefits.length === 0) return "";

    const hasAllIn = benefits.some((b) => {
      const ageToNum = typeof b.ageTo === "string" ? parseInt(b.ageTo as any, 10) : Number(b.ageTo);
      return Number.isFinite(ageToNum) && ageToNum === 99;
    });

    return hasAllIn ? "all in" : "";
  }, [quotationData?.quotation?.clppBenefits]);

  const handleCreateNewAER = () => {
    if (!quotationData) return;

    const copiedQuotation = {
      ...quotationData,
      status: undefined,
      quotation: {
        ...quotationData.quotation,
        status: undefined,
      },
    };

    navigate(`${ROUTES.SALES.clppQuotation.parse(quotationData.productId)}`, {
      state: {
        quotationData: copiedQuotation,
      },
    });
  };

  const handlePrint = async (apiUrl: string, queryParams?: string) => {
    try {
      const endpoint = queryParams ? `${apiUrl}?${queryParams}` : apiUrl;
      const response: any = await httpClient.get(endpoint, {
        responseType: "blob",
      });

      const blob = new Blob([response], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);
      window.open(url, "_blank");
    } catch (error) {
      toast.error(`PDF export failed: ${String(error)}`);
    }
  };

  const handleEditRequest = () => {
    navigate(`${ROUTES.SALES.clppQuotation.parse(quotationData.productId)}`, {
      state: {
        quotationData,
      },
    });
  };

  // const handleSubmit = async () => {
  //   const payload = {
  //     remarks: JSON.stringify([...selectedRemarks, otherRemarks].filter(Boolean)),
  //   };
  //   await dispatch(
  //     updateAerStatus({
  //       id: quotationData?.id || "",
  //       remarks: payload.remarks,
  //       status: Status.for_approval,
  //     })
  //   );
  //   fetchData();
  // };

  const handleSubmit = () => {
    modalController.openFn();
  };

  useEffect(() => {
    getProductBenefits({ filter: "" });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchData = async () => {
    const { data }: { data: ClppQuotationProduct[] } = await getClppAer(id!);
    if (!data) return;
    const model = data.find((item) => item.quotationId == parseInt(id!));
    if (!model) return;
    setQuotation(model);
  };

  useEffect(() => {
    if (id) fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    if (id && putSignatoriesSucccess) {
      fetchData();
      setProcessing(false);
      toggleModal();
      clearAerSignatoriesStatus();
    }
  }, [putSignatoriesSucccess]);

  useEffect(() => {
    if (selectedQuotationSignature) {
      getAerSignatoriesLogs({ id: Number(selectedQuotationSignature?.approvalId) || 0 });
    }
    if (selectedQuotationSignature?.status === Statuses.PENDING) {
      setIsHideButton(false);
    } else {
      setIsHideButton(true);
    }
  }, [selectedQuotationSignature]);
  const handleSubmitStatus = async (data: TApprovalPayload) => {
    setProcessing(true); // Set processing before the try block
    try {
      const transformedData = {
        approvalSignatoryId: selectedQuotationSignature?.id,
        status: data.approvalStatus,
        remarks: data.remarks || " ",
      };

      // Await the asynchronous operation
      await putAerSignatoriesStatus(transformedData);
    } catch (error) {
      toast.error("Failed to update status."); // Optional: Add user feedback
    }
  };
  const handleDataCurrentSignatory = (data: ISignatories) => {
    setDataCurrentSignatory(data);
  };

  let remarksArray: string[] = [];
  try {
    const raw = quotationData?.remarks;
    remarksArray = Array.isArray(raw) ? raw : typeof raw === "string" ? JSON.parse(raw) : [];
  } catch (e) {
    remarksArray = [];
  }

  const parsedRemarks = {
    checkboxes: [
      {
        label: quotationRemarks.ALLIN,
        checked: remarksArray.some((r) => r.toLowerCase().includes(quotationRemarks.ALLIN.toLowerCase())),
      },
      {
        label: quotationRemarks.CONTESTABILITY,
        checked: remarksArray.some((r) => r.toLowerCase().includes(quotationRemarks.CONTESTABILITY.toLowerCase())),
      },
      {
        label: quotationRemarks.FIVEPERCENT66TO69,
        checked: remarksArray.some((r) => r.toLowerCase().includes(quotationRemarks.FIVEPERCENT66TO69.toLowerCase())),
      },
    ],
    notes: remarksArray.filter((r) => !Object.values(quotationRemarks).some((predef) => r.toLowerCase().includes(predef.toLowerCase()))),
  };

  // const contestabilityData = useMemo(() => {
  //   return (contestability ?? []).map((item: IContestability) => ({
  //     id: item.id,
  //     label: item.label,
  //   }));
  // }, [contestability]);

  const typeOfCoverageItem = (coverageTypesData?.data || []).map((item: { id: number; coverageType: string }) => ({
    value: item.id,
    text: item.coverageType,
  }));

  useEffect(() => {
    getProductBenefits({ filter: "" });
    getCommissionTypes();
    getCoverageTypes();
    getContestability({ filter: ProductCode.CLPP });
  }, []);
  return (
    <section>
      {/* <Button classNames="bg-white border-0 flex items-center justify-center" outline variant="primary" onClick={navigateBack}>
        <IoChevronBack />
        Back
      </Button> */}
      <div className="my-6">
        {isShowSignatureStatus && (
          <SummaryHeaderQuotation quotationInfo={quotationData as any} onSubmit={() => toggleModal()} hideButton={ishidebutton} currentUserStatus={selectedQuotationSignature?.status} />
        )}
      </div>
      <div className="grid grid-cols-12 gap-3">
        <div className="col-span-12 flex justify-end gap-4">
          {!showActions && !isShowSignatureStatus && (
            <div className="flex justify-start">
              <Button classNames="w-full md:w-auto bg-accent" onClick={handleCreateNewAER}>
                Copy AER
              </Button>
            </div>
          )}
          {quotationData?.status === Status.approved && (
            <Button variant="default" classNames="!text-gray shadow-sm flex gap-2" onClick={handleExport}>
              <FaDownload className="mt-1" /> Export
            </Button>
          )}
        </div>
        {/*  */}
        <div className="col-span-12 md:col-span-9">
          <div>
            <h2 className="text-xl font-bold font-poppins-semibold text-primary mb-4">ACTUARY EVALUATION REPORT</h2>

            <div className="space-y-2 text-gray-700 w-full">
              <div className="flex justify-between gap-4 w-full">
                <p className="font-medium text-sm w-2/5">AER No.</p> <p className="w-full">{quotationData?.id}</p>
              </div>
              <div className="flex justify-between gap-4 w-full">
                <p className="font-medium text-sm w-2/5">Date</p> <p className="w-full">{formatWordDateDDMMYYY(new Date((quotationData as any)?.createdAt ?? new Date()).toISOString())}</p>
              </div>
              <div className="flex justify-between gap-4 w-full">
                <p className="font-medium text-sm w-2/5">Type of Plan</p> <p className="w-full">{quotationData?.product?.name}</p>
              </div>
              <div className="flex justify-between gap-4 w-full">
                <p className="font-medium text-sm w-2/5">Cooperative</p> <p className="w-full uppercase">{quotationData?.quotation?.cooperative?.coopName}</p>
              </div>
              <div className="flex justify-between gap-4 w-full">
                <p className="font-medium text-sm w-2/5">Previous Provider</p> <p className="w-full">{quotationData?.quotation?.previousProvider}</p>
              </div>
              <div className="flex justify-between gap-4 w-full">
                <p className="font-medium text-sm w-2/5">Contestability</p>{" "}
                <p className="w-full">{contestabilityData?.find((c: { id: number | undefined; label: string }) => c.id === quotationData?.quotation?.contestability)?.label || "N/A"}</p>{" "}
              </div>
              <div className="flex justify-between gap-4 w-full">
                <p className="font-medium text-sm w-2/5">Type of Coverage</p>
                <p className="w-full">{typeOfCoverageItem.find((c: { value: number; text: string }) => String(c.value) === String(quotationData?.quotation?.coverageTypeId))?.text || "N/A"}</p>
              </div>
              <div className="flex justify-between gap-4 w-full">
                <p className="font-medium text-sm w-2/5">Total Members</p> <p className="w-full">{quotationData?.quotation?.totalNumberOfMembers?.toLocaleString()}</p>
              </div>
            </div>
          </div>
          <div className="mt-8 space-y-2">
            <span className="text-[16px] font-poppins-medium">Recommendations</span>
            <div className="grid grid-cols-12">
              {/* <div className="col-span-6 p-2 bg-primary text-white">Benefit</div>
              <div className="col-span-6 text-end p-2 bg-primary text-white">Maximum Coverage</div> */}

              {Array.from(new Set((quotationData?.quotation?.clppBenefits || []).map((benefit: { option: string | number }) => benefit.option))).map((optionNumber) => (
                <div key={optionNumber as string | number} className="col-span-12 mt-10">
                  {/* <div className="font-poppins-semibold text-2xl my-2">OPTION {String(optionNumber)}</div> */}
                  <div className="col-span-12">
                    <EditableTable
                      headerClassName={colorMode({
                        className: "!bg-primary !text-white font-poppins-semibold tracking-wide",
                      })}
                      columns={[
                        {
                          header: "AGE",
                          key: "age",
                          align: "center",
                          render(row) {
                            return `${row?.ageFrom || "N/A"} - ${row?.ageTo || "N/A"}`;
                          },
                        },
                        {
                          header: "BENEFIT",
                          key: "benefit",
                          align: "center",
                          render(row) {
                            return row?.benefit?.benefitName || "N/A";
                          },
                        },
                        {
                          header: "MAXIMUM COVERAGE",
                          key: "coverage",
                          align: "center",
                          formatInput: true,
                          render(row) {
                            return Intl.NumberFormat("en-US", {
                              style: "currency",
                              currency: "PHP",
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            }).format(parseFloat(row?.maximumCoverage) || 0);
                          },
                        },
                        {
                          header: "MAXIMUM TERM",
                          key: "maximumTerm",
                          align: "center",
                          render(row) {
                            return row?.maximumTerm ? parseFloat(row?.maximumTerm).toLocaleString() : "N/A";
                          },
                        },
                        {
                          header: "NML",
                          key: "nml",
                          align: "center",
                          render(row) {
                            return row?.nml ? parseFloat(row?.nml).toLocaleString() : "N/A";
                          },
                        },
                        {
                          header: "REQUESTED RATE",
                          key: "rate",
                          align: "center",
                          render(row) {
                            const isGraduated = row?.isGraduated === true || row?.isGraduated === 1 || row?.isGraduated === "1";

                            if (isGraduated) return "GRADUATED";

                            const num = Number(row?.rate ?? 0);
                            return Number.isFinite(num) ? num.toLocaleString() : "0";
                          },
                        },
                      ]}
                      rows={(quotationData?.quotation?.clppBenefits || []).filter((benefit: { option: string | number }) => benefit.option === optionNumber)}
                      editable={false}
                    />
                  </div>
                  <div>
                    <h3 className="text-[16px] font-poppins-medium mt-2">Commission Distribution</h3>
                    <EditableTable
                      columns={[
                        {
                          header: "Commission Type",
                          key: "commissionTypeId",
                          render: (row) => {
                            const commissionType = commissionTypesList.find((type: { id: number; name: string }) => type.id === row.commissionTypeId);
                            return commissionType ? commissionType.name : "N/A";
                          },
                          rowSpan: (rows, rowIndex) => {
                            const currentCommissionTypeId = rows[rowIndex]?.commissionTypeId;
                            if (currentCommissionTypeId === undefined) return 1;

                            // Check if this cell should be skipped (merged into previous row)
                            if (rowIndex > 0 && rows[rowIndex - 1]?.commissionTypeId === currentCommissionTypeId) {
                              return 0;
                            }

                            // Count consecutive rows with same commissionTypeId
                            let span = 1;
                            for (let i = rowIndex + 1; i < rows.length; i++) {
                              if (rows[i]?.commissionTypeId === currentCommissionTypeId) {
                                span++;
                              } else {
                                break;
                              }
                            }
                            return span;
                          },
                        },
                        {
                          key: "ageFrom",
                          header: "AGE FROM",
                          className: "text-[14px] font-[500]",
                          number: true,
                        },
                        {
                          key: "ageTo",
                          header: "AGE TO",
                          className: "text-[14px] font-[500]",
                          number: true,
                        },
                        {
                          header: "Rate",
                          key: "rate",
                          render: (row) => row.rate,
                        },
                      ]}
                      rows={commissionRows}
                      editable={false}
                      headerClassName={colorMode({
                        className: "!bg-primary !text-white font-poppins-semibold tracking-wide",
                      })}
                    />
                  </div>
                </div>
              ))}
            </div>
            {/* {showActions && ( */}
            <div className="mt-4">
              <div className="flex flex-col md:flex-row justify-between gap-4 md:gap-0">
                {/* Print button */}
                <Button
                  variant="default"
                  isSubmitting
                  onClick={() => {
                    if (quotationData?.id) {
                      const exportUrl = `/actuary-evaluation-report/${quotationData.id}/export`;
                      handlePrint(exportUrl);
                    } else {
                      toast.error("Missing AER ID.");
                    }
                  }}
                  classNames={colorMode({
                    classLight: "w-full md:w-auto text-gray/20 bg-slate-50 border-gray/20 !text-gray/20 shadow-sm",
                    classDark: "w-full md:w-auto text-gray/20 bg-slate-50 border-gray/20 !text-gray shadow-sm",
                  })}
                >
                  <div className="flex flex-row w-full gap-3 justify-center items-center !text-center">
                    <PiPrinter scale={4.5} size={25} />
                    Print
                  </div>
                </Button>

                <div className="flex flex-col md:flex-row mt-4 gap-4 md:gap-2 w-full md:w-auto">
                  {/* <Button variant="default" onClick={handleSaveAsDraft} classNames="w-full md:w-auto bg-accent" disabled={updateAerStatusState.loading}>
                      Save as Draft
                    </Button> */}
                  {quotationData?.status === Status.draft && (
                    <Button variant="default" classNames="w-full md:w-auto bg-accent" onClick={handleEditRequest}>
                      Edit Request
                    </Button>
                  )}
                  {quotationData?.status !== Status.for_approval && quotationData?.status !== Status.for_signatory && (
                    <Button variant="primary" onClick={handleSubmit} classNames="w-full md:w-auto">
                      Submit
                    </Button>
                  )}
                </div>
              </div>
            </div>
            {/* )} */}
          </div>
        </div>
        {/*  */}

        {!isShowSignatureStatus && (
          <div className="col-span-12 md:col-span-3">
            <div className="block shadow-md p-5">
              <span className="text-xl">STATUS</span>
              <div className="flex flex-row gap-3 justify-between items-center my-2">
                <div className="text-xs font-medium">Status</div>
                <span className="text-sm">{quotationData?.status}</span>
              </div>
              <div className="flex flex-row gap-3 justify-between items-center my-2">
                <div className="text-xs font-medium">Date Submitted</div>
                <span className="text-sm">{formatWordDateDDMMYYY(new Date(quotationData?.updatedAt ?? new Date()).toISOString())}</span>
              </div>
              <hr className="my-14 border-gray/20" />
              <span className="block text-xl mb-5">REMARKS</span>
              <div className="flex flex-col gap-3">
                <span className="flex flex-row gap-2 items-center">
                  <input
                    type="checkbox"
                    size={12}
                    checked={(JSON.parse(quotationData?.remarks ?? "[]") ?? []).some((remark: string) => remark.toLowerCase().includes("waived"))}
                    onChange={() => void null}
                  />
                  <p className="text-xs font-[12]">Waived (Transferred Data) contestability</p>
                </span>
                <span className="flex flex-row gap-2 items-center">
                  {/* <input type="checkbox" size={12} checked={(JSON.parse(quotationData?.remarks ?? "[]") ?? []).includes("5% of Members aged 65-69")} onChange={() => void null} /> */}
                  <input type="checkbox" size={12} checked={false} onChange={() => void null} />
                  <p className="text-xs font-[12]">5% Members aged 65-69</p>
                </span>

                <span className="flex flex-row gap-2 items-center">
                  {/* <TextArea value={(JSON.parse(quotationData?.remarks ?? "[]") ?? [])[3] ?? ""} onChange={(e) => setOtherRemarks(e.target.value)} /> */}
                  {parsedRemarks.notes.map((note: string, index: number) => (
                    <span key={index} className="flex flex-row gap-2 items-center min-h-[50px] border border-zinc-300 bg-zinc-100 rounded p-2 w-full">
                      <p className="text-xs">{note}</p>
                    </span>
                  ))}
                </span>
              </div>
              <div className="my-4">
                <div className="text-xs font-medium mb-1">Attachments</div>
                {aerAttachments.length > 0 ? (
                  <ul className="space-y-1">
                    {aerAttachments.map((att) => (
                      <li key={att.id} className="flex justify-between items-center gap-2">
                        <a
                          className="text-xs underline text-accent truncate"
                          href={`${import.meta.env.VITE_AWS_S3_ENDPOINT}/${att.filepath}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          title={att.label || att.filename}
                        >
                          {att.label || att.filename}
                        </a>
                        <span className="text-[11px] text-gray-500 shrink-0">{typeof att.size === "number" ? (att.size / 1024).toFixed(1) : "0.0"} KB</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div className="text-xs text-gray-500">No attachment</div>
                )}
              </div>
            </div>
          </div>
        )}
        {!showActions && isShowSignatureStatus && (
          <>
            <div className="col-span-12 md:col-span-3 justify-center mt-10">
              <div className="flex flex-row  w-full justify-center items-center ">
                <FaUsers size={20} className="mr-2" />
                <Typography size="lg">SIGNEES</Typography>
              </div>
              <Tabs
                className="mt-4"
                contentClass="p-6 border-0 bg-sky-50"
                headerClass="text-xs rounded-t-lg h-10"
                activeTabClassName="bg-primary !text-white text-xs"
                headers={["Ongoing Approvals", "Approval History"]}
                contents={[
                  <Signatories2 signatories={quotationData?.approval?.signatories} toggleRemarksModal={toggleRemarksModal} selectedSignatory={handleDataCurrentSignatory} />,
                  <ApprovalHistory historyData={signatoryLogs} />,
                ]}
              />
            </div>
          </>
        )}
      </div>
      {statusModal && (
        <Modal title="Change AER Status" titleClass="text-md md:text-lg" modalContainerClassName="!max-w-2xl" isOpen={statusModal} onClose={toggleModal}>
          <ChangeStatusForm handleSubmit={handleSubmitStatus} isSubmitting={processing} options={statusOptions} />
        </Modal>
      )}
      {remarksModal && (
        <Modal title="Remarks" titleClass="text-md md:text-lg" modalContainerClassName="!max-w-2xl" isOpen={remarksModal} onClose={toggleRemarksModal}>
          <ApproverRemarksDynamic currentSignatory={dataCurrentSignatory} />
        </Modal>
      )}
      <SubmitModal controller={modalController} contestabilityLabel={contestabilityLabel} membersAged66To69={membersAged66To69} allIn={allIn} />
    </section>
  );
}

export function QuotationClppQuotationView() {
  const location = useLocation();
  const { isShowSignatureStatusLocal, showActions } = location.state || {}; // Access the passed state
  return <BaseQuotationClppAerView showActions={showActions} isShowSignatureStatus={isShowSignatureStatusLocal} />;
}

export function QuotationClppAerView() {
  return <BaseQuotationClppAerView showActions={true} />;
}
