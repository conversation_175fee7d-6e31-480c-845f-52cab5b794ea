/* eslint-disable react-hooks/exhaustive-deps */
import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import { ModalController } from "@modules/sales/controller";
import Checkmark from "@assets/checkmark.png";
import colorMode from "@modules/sales/utility/color";
import { modalColorMode } from "@constants/modal-color-mode";
import { useNavigate } from "react-router-dom";
import { ROUTES } from "@constants/routes";

const SuccessModal = ({ controller }: { controller: ModalController }) => {
  const navigate = useNavigate();

  const handleOk = () => {
    controller.closeFn();
    navigate(ROUTES.SALES.quotations.key);
    localStorage.removeItem("productCode");
  };
  return (
    <Modal
      isOpen={controller.isOpen}
      onClose={() => controller.closeFn()}
      modalContainerClassName="!w-[890px] !rounded-3xl overflow-hidden flex flex-col items-center justify-center"
      modalBgColor={colorMode({
        classLight: modalColorMode.classLight,
        classDark: modalColorMode.classDark,
      })}
      showCloseButton={false}
    >
      <div className="flex flex-col items-center justify-center min-w-[400px] min-h-fit">
        <div className="text-center w-full mb-12">
          <img src={Checkmark} alt="success" sizes="140" className="w-20 h-20 mx-auto" />
          <span className="block mt-2 text-2xl font-bold font-poppins-semibold !mb-16">Success</span>
          <span className="block text-[16] font-[500]">New AER has been successfully saved.</span>
        </div>
        <div className="flex flex-col items-center justify-center w-full">
          <Button classNames="elevation-sm shadow-md rounded-[5px] !px-20" variant="success" onClick={handleOk}>
            Ok
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default SuccessModal;
