/* eslint-disable react-hooks/exhaustive-deps */
import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import { ModalController, useModalController } from "@modules/sales/controller";
import { useState, useEffect } from "react";
import { RootState } from "@state/reducer";
import { useDispatch, useSelector } from "react-redux";
import { ICda } from "@interface/cooperatives-cda";
import TextArea from "@components/form/TextArea";
import { useQuotationActions } from "@state/reducer/quotations";
import { toast } from "react-toastify";
import { PiXCircle } from "react-icons/pi";
import SuccessModal from "./success.modal";
import colorMode from "@modules/sales/utility/color";
import { quotationRemarks, Status } from "@constants/global-constant-value";
import { modalColorMode } from "@constants/modal-color-mode";
import FileDropzone from "@components/common/FileDropzone";
import { FaCloudArrowUp } from "react-icons/fa6";

import { AttachableTypes } from "@enums/attachable-types";
import { uploadAttachments } from "@services/common/attachment.service";
import { IAttachment } from "@interface/products.interface";

type TAerAttachment = { label: string; filepath: string };

const SubmitModal = ({
  controller,
  contestabilityLabel,
  membersAged66To69,
  allIn,
}: {
  controller: ModalController;
  onSelect?: (coop: ICda) => void;
  contestabilityLabel?: string;
  membersAged66To69?: boolean;
  allIn?: string;
  onUploaded?: (a: TAerAttachment) => void;
}) => {
  const staticRemarks = [quotationRemarks.ALLIN, quotationRemarks.CONTESTABILITY, quotationRemarks.FIVEPERCENT66TO69];
  const dispatch = useDispatch();
  const [selectedRemarks, setSelectedRemarks] = useState<string[]>([...staticRemarks]);
  const [otherRemarks, setOtherRemarks] = useState<string>("");

  // Optional attachment state
  const [files, setFiles] = useState<File[]>([]);
  const [fileError, setFileError] = useState<string>("");
  const [uploading, setUploading] = useState<boolean>(false);
  const [uploadedAttachment, setUploadedAttachment] = useState<TAerAttachment | null>(null);

  const { updateAerStatus, resetUpdateAerStatus } = useQuotationActions();
  const updateAerStatusState = useSelector((state: RootState) => state.quotation.updateAerStatus);
  const quotationData = useSelector((state: RootState) => state.quotation.quotation);
  const successController = useModalController();

  const handleFile = (accepted: File[]) => {
    setFiles(accepted?.[0] ? [accepted[0]] : []);
    setFileError("");
  };

  const uploadAerAttachment = async (file: File, attachableId: number | string): Promise<TAerAttachment> => {
    const attachments: IAttachment[] = [
      {
        file,
        label: file.name,
        description: "AER submission attachment",
        tag: "AER_SUBMISSION",
      },
    ];

    const res = await uploadAttachments(AttachableTypes.ACTUARY_EVALUATION_REPORT, String(attachableId), attachments);
    const payload = res?.data?.data?.[0] ?? res?.data?.attachments?.[0] ?? res?.data?.[0] ?? res?.data;

    return {
      label: payload?.label ?? file.name,
      filepath: payload?.filepath ?? payload?.path ?? "",
    };
  };

  const handleSubmit = async () => {
    try {
      setUploading(true);

      // Only validate and upload file if one is selected
      if (files[0]) {
        // Validate type/size for selected file
        const f = files[0];
        const maxBytes = 20 * 1024 * 1024;
        if (f.size > maxBytes) {
          setFileError("File too large (max 20MB).");
          setUploading(false);
          return;
        }
        const allowed = ["application/pdf", "image/png", "image/jpeg"];
        if (!allowed.includes(f.type)) {
          setFileError("Invalid file type. Allowed: PDF, PNG, JPG.");
          setUploading(false);
          return;
        }

        const attachment = await uploadAerAttachment(f, quotationData?.id || "");
        setUploadedAttachment(attachment);
      }

      const remarksPayload = JSON.stringify([...selectedRemarks, otherRemarks].filter(Boolean));

      await dispatch(
        updateAerStatus({
          id: quotationData?.id || "",
          remarks: remarksPayload,
          status: Status.for_approval,
        })
      );
    } catch (e) {
      toast.error("Failed to submit. Please try again.");
    } finally {
      setUploading(false);
    }
  };

  useEffect(() => {
    if (updateAerStatusState.success) {
      toast.success("Aer status updated successfully");
      controller.closeFn();
      successController.openFn();
      dispatch(resetUpdateAerStatus());
    } else if (updateAerStatusState.error) {
      toast.error("Failed to update aer status");
    }
  }, [updateAerStatusState.success, updateAerStatusState.error, controller, successController]);

  useEffect(() => {
    if (quotationData) {
      const remarks = JSON.parse((quotationData as any)?.remarks ?? "[]");
      const next = [...remarks];

      if (contestabilityLabel?.toLowerCase().includes("waived") && !next.includes(quotationRemarks.CONTESTABILITY)) {
        next.push(quotationRemarks.CONTESTABILITY);
      }
      if (membersAged66To69 && !next.includes(quotationRemarks.FIVEPERCENT66TO69)) {
        next.push(quotationRemarks.FIVEPERCENT66TO69);
      }
      if (allIn?.toLowerCase().includes("all") && !next.includes(quotationRemarks.ALLIN)) {
        next.push(quotationRemarks.ALLIN);
      }
      setSelectedRemarks(next);

      if (remarks.length >= 4) {
        setOtherRemarks(remarks[3]);
      }
    }
  }, [quotationData, contestabilityLabel, membersAged66To69, allIn]);

  return (
    <>
      <Modal
        isOpen={controller.isOpen}
        onClose={controller.closeFn}
        modalContainerClassName="!w-[890px] !rounded-2xl overflow-hidden"
        modalBgColor={colorMode({
          classLight: modalColorMode.classLight,
          classDark: modalColorMode.classDark,
        })}
        showCloseButton={false}
      >
        <div className="block w-full">
          <div className="flex flex-row flex-nowrap justify-between items-center w-full">
            <h2 className="block text-[24px] font-[500] text-gray !text-center w-full">AER Customization Remarks</h2>
            <button className="absolute top-10 right-4 w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors" onClick={controller.closeFn}>
              <PiXCircle size={24} />
            </button>
          </div>

          <div className="flex flex-col flex-nowrap justify-between items-center w-full gap-4">
            <p className="text-md">Please ensure that you provide your remarks and upload the attachment before submitting the Actuary Evaluation Report (AER).</p>

            {staticRemarks.map((remark, index) => (
              <div key={index} className="flex flex-row flex-nowrap items-center gap-2 w-full">
                <input
                  type="checkbox"
                  id={`remark-${index}`}
                  className="w-4 h-4"
                  disabled
                  checked={selectedRemarks.includes(remark)}
                  onChange={(e) => {
                    const remarks = [...selectedRemarks];
                    remarks[index] = e.target.checked ? remark : "";
                    setSelectedRemarks(remarks);
                  }}
                />
                <label htmlFor={`remark-${index}`} className="text-[14px] font-[400] cursor-pointer">
                  {remark}
                </label>
              </div>
            ))}

            <div className="w-full space-y-2">
              <span className="text-16 font-[500] font-poppins-semibold">Other Remarks:</span>
              <TextArea placeholder="Enter your remarks here" className="w-full" value={otherRemarks} onChange={(e) => setOtherRemarks(e.target.value)} />
            </div>

            {/* Attachment */}
            <div className="w-full space-y-2">
              <div className="flex items-center gap-2">
                <span className="text-16 font-[500] font-poppins-semibold">Attachment</span>
                <span className="text-slate-500 text-xs">(optional)</span>
              </div>
              <div className={`border rounded-xl ${fileError ? "border-red-400" : "border-zinc-300"}`}>
                <FileDropzone setFiles={handleFile} height={140}>
                  {files.length === 0 ? (
                    <div className="flex flex-1 flex-col items-center justify-center p-6 gap-2 text-center">
                      <FaCloudArrowUp size={28} />
                      <div className="text-sm">Click or drag & drop to upload</div>
                      <div className="text-xs text-slate-400">PDF, JPG, PNG (max 20MB)</div>
                    </div>
                  ) : (
                    <div className="p-4 text-sm">{files[0].name}</div>
                  )}
                </FileDropzone>
              </div>
              {fileError && <div className="text-red-500 text-xs mt-1">{fileError}</div>}

              {/* Show uploaded link (post-upload) */}
              {uploadedAttachment?.filepath && (
                <div className="text-sm">
                  <span className="mr-2">Uploaded:</span>
                  <a className="underline text-accent" href={`${import.meta.env.VITE_AWS_S3_ENDPOINT}/${uploadedAttachment.filepath}`} target="_blank" rel="noopener noreferrer">
                    {uploadedAttachment.label}
                  </a>
                </div>
              )}
            </div>
          </div>

          <hr className="my-4 border-gray/10" />

          <div className="grid grid-cols-12 gap-2 mt-4">
            <div className="col-span-12 md:col-span-4">
              <Button variant="danger" classNames="w-full rounded-lg" onClick={controller.closeFn} block>
                Cancel
              </Button>
            </div>
            <div className="col-span-12 md:col-span-4 md:col-start-9 text-end">
              <Button variant="default" classNames="w-full rounded-lg bg-accent" onClick={handleSubmit} block disabled={uploading}>
                {uploading ? "Submitting..." : "Submit"}
              </Button>
            </div>
          </div>
        </div>
      </Modal>

      <SuccessModal controller={successController} />
    </>
  );
};

export default SubmitModal;
