import { useEffect, useMemo, useState } from "react";
import { IoChevronBack } from "react-icons/io5";
import Button from "@components/common/Button";
import { ROUTES } from "@constants/routes";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import EditableTable from "@modules/sales/components/editable-table";
import { PiPrinter } from "react-icons/pi";
import { useModalController } from "@modules/sales/controller";
import SubmitModal from "@modules/sales/view-aer/modals/submit.modal";
import { useQuotationActions } from "@state/reducer/quotations";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { useProductBenefitsManagementActions } from "@state/reducer/utilities-product-benefits";
import { IUtilitiesProductBenefits } from "@interface/utilities.interface";
import { formatWordDateDDMMYYY } from "@helpers/date";
import { export<PERSON>er, getClspAer } from "@services/quotation/quotation.service";
import { ICooperative } from "@interface/product-proposal.interface";
import { IContestability } from "@interface/contestability.interface";
import { IAttachmentResponse, IProduct, ISignatories } from "@interface/products.interface";
import { useCommissionTypeActions } from "@state/reducer/commision-type";
import { ICommissionType } from "@interface/commission-structure.interface";
import { toast } from "react-toastify";
import SummaryHeaderQuotation from "@modules/admin/approval-aer/components/SummaryHeader";
import { useUserId } from "@helpers/data";
import { quotationRemarks, STATUS_OPTIONS, Statuses } from "@constants/global-constant-value";
import Modal from "@components/common/Modal";
import ChangeStatusForm from "@modules/admin/approval-aer/components/forms/ChangeStatus";
import { TApprovalPayload } from "@state/types/users-product-approval";
import { FaDownload, FaUsers } from "react-icons/fa";
import Typography from "@components/common/Typography";
import Tabs from "@components/common/Tabs";
import Signatories2 from "@modules/users/approvals/review-revisions/Tabs/Signatories2";
import ApprovalHistory from "@modules/admin/products/components/Common/ApprovalHistory";
import ApproverRemarksDynamic from "@modules/admin/approval-aer/components/ApproverRemarksDynamic";
import { ProposalStatus } from "@enums/proposal-status";
import { useContestabilityActions } from "@state/reducer/contestability";
import httpClient from "@clients/httpClient";
import { ProductCode } from "@enums/product-code";
import { navigateBack } from "@helpers/navigatorHelper";

type ClspQuotationProduct = {
  id: number;
  productId: number;
  quotationId: number;
  options: string;
  remarks: string | null;
  status: string;
  contestabilityId: number;
  contestability: IContestability;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  product: IProduct;
  quotation: {
    id: number;
    coopId: number;
    previousProvider: string;
    branch: string;
    contestability: number;
    totalNumberOfMembers: number;
    productId: number;
    coverageTypeId: number | null;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;

    quotationClaimsExperienceAge: {
      id: number;
      ageFrom: number;
      ageTo: number;
      numberOfDeaths: number;
      totalClaimAmount: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];

    quotationClaimsExperienceYear: {
      id: number;
      year: string;
      numberOfDeaths: number;
      totalClaimAmount: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];

    quotationCommissionDistribution: {
      id: number;
      commissionTypeId: number;
      rate: string;
      quotationId: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
      isAgentInput: number;
      commissionAgeType: string | null;
      ageFrom: number | null;
      ageTo: number | null;
      option?: number | null;
    }[];

    clspQuotations: {
      id: number;
      premiumBudget: string;
      averageAge: number;
      averageClaims: number;
      maxDependents: number;
      quotationId: number;
      status: string;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    };

    quotationPremium: {
      id: number;
      quotationId: number;
      grossPremium: string;
      netPremium: string;
      option: number;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    }[];

    quotationCondition: {
      id: number;
      quotationId: number;
      condition: string;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    };
    cooperative: ICooperative;
  };
};

type QuotationBenefit = {
  id: number;
  quotationId: number;
  benefitId: number;
  ageFrom: number;
  ageTo: number;
  maximumCoverageAmount: string;
  rate: string;
  option: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
};

type QuotationCommission = {
  id: number;
  quotationId: number;
  commissionTypeId: number;
  commissionAgeType: string | null;
  ageFrom: number | null;
  ageTo: number | null;
  rate: string;
  isAgentInput: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  option?: number | null;
};

export default function BaseQuotationClspAerView({ showActions = false, isShowSignatureStatus = false }: { showActions?: boolean; isShowSignatureStatus?: boolean }) {
  const navigate = useNavigate();
  const { id } = useParams();
  const { setQuotation, getAerSignatoriesLogs, putAerSignatoriesStatus, clearAerSignatoriesStatus } = useQuotationActions();
  const { getCommissionTypes } = useCommissionTypeActions();
  const { getProductBenefits } = useProductBenefitsManagementActions();

  const quotationData: ClspQuotationProduct | any | undefined = useSelector((state: RootState) => state.quotation.quotation);
  const commissionTypesData = useSelector((state: RootState) => state.commissionType.getCommissionTypes);
  const { getContestability } = useContestabilityActions();
  const productBenefits: IUtilitiesProductBenefits[] = useSelector((state: RootState) => state.utilitiesProductBenefits.productBenefits);
  const location = useLocation();
  const updateAerStatusState = useSelector((state: RootState) => state.quotation.updateAerStatus);
  const [statusModal, setStatusModal] = useState<boolean>(false);
  const toggleModal = () => setStatusModal((prev) => !prev);
  const [remarksModal, setRemarksModal] = useState<boolean>(false);
  const toggleRemarksModal = () => setRemarksModal((prev) => !prev);
  const [ishidebutton, setIsHideButton] = useState<boolean>(false);
  const [processing, setProcessing] = useState<boolean>(false);
  const [dataCurrentSignatory, setDataCurrentSignatory] = useState<ISignatories>({} as ISignatories);
  const userId = useUserId();
  const selectedQuotationSignature: ClspQuotationProduct | any | undefined = useSelector((state: RootState) =>
    (state.quotation?.quotation as any)?.approval?.signatories.find((items: any) => items?.userId === userId)
  );
  const putSignatoriesSucccess = useSelector((state: RootState) => state.quotation.putAerSignatoriesStatus?.success);
  const signatoryLogs = useSelector((state: RootState) => state?.quotation?.getAerSignatoriesLogs?.data?.data);
  const modalController = useModalController();
  const contestability = useSelector((state: RootState) => state.contestability.contestabilities);
  const demographicData = location.state?.demographicData;
  const aerAttachments: IAttachmentResponse[] = useMemo(() => quotationData?.attachments ?? [], [quotationData?.attachments]);
  const contestabilityData = useMemo(() => {
    return (contestability ?? []).map((item: IContestability) => ({
      id: item.id,
      label: item.label,
    }));
  }, [contestability]);

  const contestabilityLabel = useMemo(() => {
    const contestabilityId = quotationData?.quotation?.contestability;
    return contestabilityData.find((item: { id: number | undefined; label: string }) => item.id === contestabilityId)?.label;
  }, [quotationData?.quotation?.contestability, contestabilityData]);

  const membersAged66To69 = useMemo(() => {
    const ageArray = demographicData?.ageAndYearCounts?.age;
    const total = demographicData?.totalNumberOfMembers ?? 0;
    if (!Array.isArray(ageArray) || total === 0) return false;
    const age66to69 = ageArray.reduce((sum: number, item: any) => {
      const age = Number(item.age);
      const count = Number(item.totalCount ?? 0);
      return age >= 66 && age <= 69 ? sum + count : sum;
    }, 0);

    const percentage = (age66to69 / total) * 100;
    return percentage >= 5;
  }, [demographicData]);

  // All in = at least one benefit has ageTo == 99
  const allIn = useMemo(() => {
    const benefits: QuotationBenefit[] = quotationData?.quotation?.clspBenefits ?? [];
    if (!Array.isArray(benefits) || benefits.length === 0) return "";

    const hasAllIn = benefits.some((b) => {
      const ageToNum = typeof b.ageTo === "string" ? parseInt(b.ageTo as any, 10) : Number(b.ageTo);
      return Number.isFinite(ageToNum) && ageToNum === 99;
    });

    return hasAllIn ? "all in" : "";
  }, [quotationData?.quotation?.clspBenefits]);

  const productBenefitsList = useMemo(
    () =>
      (productBenefits ?? []).map((item: IUtilitiesProductBenefits) => ({
        id: item.id,
        name: item.benefitName,
      })),
    [productBenefits]
  );

  const commissionTypesList = useMemo(
    () =>
      (commissionTypesData?.data ?? []).map((item: ICommissionType) => ({
        id: item.id,
        name: item.commissionName,
      })),
    [commissionTypesData]
  );

  // Group benefits by option for rendering
  const benefitsByOption = useMemo(() => {
    const groups: Record<number, QuotationBenefit[]> = {};
    const list = (quotationData?.quotation?.clspBenefits ?? []) as QuotationBenefit[];
    for (const b of list) {
      const opt = Number(b.option ?? 0);
      if (!groups[opt]) groups[opt] = [];
      groups[opt].push(b);
    }
    // sort options numerically for consistent UI
    return Object.fromEntries(Object.entries(groups).sort(([a], [b]) => Number(a) - Number(b)));
  }, [quotationData?.quotation?.clspBenefits]);

  const allCommissions: QuotationCommission[] = useMemo(() => {
    const raw = (quotationData?.quotation?.quotationCommissionDistribution ?? []) as any[];

    return raw.map((r) => ({
      ...r,
      option: r.option == null || r.option === "" ? null : typeof r.option === "string" ? Number(r.option) : (r.option as number),
    })) as QuotationCommission[];
  }, [quotationData?.quotation?.quotationCommissionDistribution]);

  const { commissionsByOption, legacyCommissions, hasAnyOption } = useMemo(() => {
    const rows = allCommissions ?? [];
    const hasOpt = rows.some((r) => r.option != null);

    if (!hasOpt) {
      return {
        commissionsByOption: {} as Record<number, QuotationCommission[]>,
        legacyCommissions: rows,
        hasAnyOption: false,
      };
    }

    const map: Record<number, QuotationCommission[]> = {};
    for (const r of rows) {
      const opt = Number(r.option);
      if (!map[opt]) map[opt] = [];
      map[opt].push(r);
    }
    return { commissionsByOption: map, legacyCommissions: [] as QuotationCommission[], hasAnyOption: true };
  }, [allCommissions]);

  // helpers
  const fmtRate = (v: string | number) => Number(v ?? 0).toFixed(2);
  const uniqueBy = <T, K>(arr: T[], key: (x: T) => K) => {
    const seen = new Set<K>();
    return arr.filter((x) => {
      const k = key(x);
      if (seen.has(k)) return false;
      seen.add(k);
      return true;
    });
  };

  const statusOptions = STATUS_OPTIONS;

  const handleEditAer = () => {
    navigate(`${ROUTES.SALES.clspQuotation.parse(quotationData.productId)}`, {
      state: {
        quotationData,
        editMode: true,
      },
    });
  };

  const handleCreateNewAER = () => {
    if (!quotationData) return;

    const copiedQuotation = {
      ...quotationData,
      status: undefined,
      quotation: {
        ...quotationData.quotation,
        status: undefined,
      },
    };

    navigate(`${ROUTES.SALES.clspQuotation.parse(quotationData.productId)}`, {
      state: {
        quotationData: copiedQuotation,
      },
    });
  };

  const handlePrint = async (apiUrl: string, queryParams?: string) => {
    try {
      const endpoint = queryParams ? `${apiUrl}?${queryParams}` : apiUrl;
      const response: any = await httpClient.get(endpoint, {
        responseType: "blob",
      });

      const blob = new Blob([response], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);
      window.open(url, "_blank");
    } catch (error) {
      toast.error(`PDF export failed: ${String(error)}`);
    }
  };

  const handleSubmit = () => {
    modalController.openFn();
  };

  const handleExport = async () => {
    if (!id || !quotationData) return;
    try {
      const response = await exportAer(quotationData.id!);
      const blob = new Blob([response]);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `aer-${quotationData?.product?.name}-${quotationData?.quotation?.cooperative?.coopName}-${id}.pdf`.toLowerCase();
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      toast.error("Failed to export AER. Please try again.");
    }
  };

  useEffect(() => {
    getProductBenefits({ filter: "" });
    getCommissionTypes();
    getContestability({ filter: ProductCode.CLSP });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  let remarksArray: string[] = [];
  try {
    const raw = quotationData?.remarks;
    remarksArray = Array.isArray(raw) ? raw : typeof raw === "string" ? JSON.parse(raw) : [];
  } catch (e) {
    remarksArray = [];
  }

  const parsedRemarks = {
    checkboxes: [
      {
        label: quotationRemarks.ALLIN,
        checked: remarksArray.some((r) => r.toLowerCase().includes(quotationRemarks.ALLIN.toLowerCase())),
      },
      {
        label: quotationRemarks.CONTESTABILITY,
        checked: remarksArray.some((r) => r.toLowerCase().includes(quotationRemarks.CONTESTABILITY.toLowerCase())),
      },
      {
        label: quotationRemarks.FIVEPERCENT66TO69,
        checked: remarksArray.some((r) => r.toLowerCase().includes(quotationRemarks.FIVEPERCENT66TO69.toLowerCase())),
      },
    ],
    notes: remarksArray.filter((r) => !Object.values(quotationRemarks).some((predef) => r.toLowerCase().includes(predef.toLowerCase()))),
  };

  const fetchData = async () => {
    const { data }: { data: ClspQuotationProduct[] } = await getClspAer(id!);
    if (!data) return;
    const model = data?.reduce((acc, item: ClspQuotationProduct) => {
      acc = item;
      return acc;
    }, {} as any);
    if (!model) return;
    setQuotation(model);
  };

  useEffect(() => {
    const fetchDataWithErrorHandling = async () => {
      try {
        if (id) {
          await fetchData();
        }
      } catch (error) {
        toast.error("Failed to fetch quotation data. Please try again later.");
      }
    };
    fetchDataWithErrorHandling();
  }, []);

  useEffect(() => {
    if (selectedQuotationSignature) {
      getAerSignatoriesLogs({ id: Number(selectedQuotationSignature?.approvalId) || 0 });
    }
    if (selectedQuotationSignature?.status === Statuses.PENDING) {
      setIsHideButton(false);
    } else {
      setIsHideButton(true);
    }
  }, [selectedQuotationSignature]);

  useEffect(() => {
    if (id && putSignatoriesSucccess) {
      fetchData();
      setProcessing(false);
      toggleModal();
      clearAerSignatoriesStatus();
    }
  }, [putSignatoriesSucccess]);

  const handleSubmitStatus = async (data: TApprovalPayload) => {
    setProcessing(true);
    try {
      const transformedData = {
        approvalSignatoryId: selectedQuotationSignature?.id,
        status: data.approvalStatus,
        remarks: data.remarks || " ",
      };
      await putAerSignatoriesStatus(transformedData);
    } catch (error) {
      toast.error("Failed to update status.");
    }
  };

  const handleDataCurrentSignatory = (data: ISignatories) => {
    setDataCurrentSignatory(data);
  };

  return (
    <section>
      <Button classNames="bg-white border-0 flex items-center justify-center" outline variant="primary" onClick={navigateBack}>
        <IoChevronBack />
        Back
      </Button>

      <div>
        {isShowSignatureStatus && (
          <SummaryHeaderQuotation quotationInfo={quotationData as any} onSubmit={() => toggleModal()} hideButton={ishidebutton} currentUserStatus={selectedQuotationSignature?.status} />
        )}
        {statusModal && (
          <Modal title="Change AER Status" titleClass="text-md md:text-lg" modalContainerClassName="!max-w-2xl" isOpen={statusModal} onClose={toggleModal}>
            <ChangeStatusForm handleSubmit={handleSubmitStatus} isSubmitting={processing} options={statusOptions} />
          </Modal>
        )}
      </div>

      <div className="grid grid-cols-12 gap-3">
        <div className="col-span-12 flex justify-end gap-4">
          {!showActions && !isShowSignatureStatus && (
            <div className="flex justify-start">
              <Button classNames="w-full md:w-auto bg-accent" onClick={handleCreateNewAER}>
                Copy AER
              </Button>
            </div>
          )}
          {quotationData?.status === Statuses.APPROVED && (
            <Button variant="default" classNames="!text-gray shadow-sm flex gap-2" onClick={handleExport}>
              <FaDownload className="mt-1" /> Export
            </Button>
          )}
        </div>

        <div className="col-span-12 md:col-span-9">
          <div>
            <h2 className="text-xl font-bold font-poppins-semibold text-primary mb-4">ACTUARY EVALUATION REPORT</h2>

            <div className="space-y-2 text-gray-700 w-full">
              <div className="flex justify-between gap-4 w-full">
                <p className="font-medium text-sm w-2/5">AER No.</p> <p className="w-full">{quotationData?.id}</p>
              </div>
              <div className="flex justify-between gap-4 w-full">
                <p className="font-medium text-sm w-2/5">Date</p> <p className="w-full">{formatWordDateDDMMYYY(new Date((quotationData as any)?.createdAt ?? new Date()).toISOString())}</p>
              </div>
              <div className="flex justify-between gap-4 w-full">
                <p className="font-medium text-sm w-2/5">Type of Plan</p> <p className="w-full">{quotationData?.product?.name}</p>
              </div>
              <div className="flex justify-between gap-4 w-full">
                <p className="font-medium text-sm w-2/5">Cooperative</p> <p className="w-full uppercase">{quotationData?.quotation?.cooperative?.coopName}</p>
              </div>
              <div className="flex justify-between gap-4 w-full">
                <p className="font-medium text-sm w-2/5">Previous Provider</p> <p className="w-full">{quotationData?.quotation?.previousProvider}</p>
              </div>
              <div className="flex justify-between gap-4 w-full">
                <p className="font-medium text-sm w-2/5">Contestability</p>{" "}
                <p className="w-full">{contestabilityData?.find((c: { id: number | undefined; label: string }) => c.id === quotationData?.quotation?.contestability)?.label || "N/A"}</p>{" "}
              </div>
              <div className="flex justify-between gap-4 w-full">
                <p className="font-medium text-sm w-2/5">Coverage Basis</p>
                <p className="w-full">{quotationData?.quotation?.clspQuotation?.coverageBasis}</p>
              </div>
              <div className="flex justify-between gap-4 w-full">
                <p className="font-medium text-sm w-2/5">Total Members</p> <p className="w-full">{quotationData?.quotation?.totalNumberOfMembers?.toLocaleString()}</p>
              </div>
            </div>
          </div>

          <div className="mt-4 space-y-2">
            <span className="text-[16px] font-poppins-medium">Recommendations</span>
            <div className="grid grid-cols-12">
              <div className="col-span-12">
                {Object.entries(benefitsByOption).map(([option, benefits]) => {
                  const currentOption = Number(option);

                  const columns = [
                    {
                      header: "BENEFITS",
                      key: "benefitId",
                      render(data: any, rowIndex: number) {
                        const castedData = data as QuotationBenefit;
                        const benefit = productBenefitsList.find((item) => item.id === castedData.benefitId);
                        const benefitName = benefit?.name;

                        const isFirstOccurrence = benefits.findIndex((b) => b.benefitId === castedData.benefitId) === rowIndex;

                        if (isFirstOccurrence) {
                          const rowSpan = benefits.filter((b) => b.benefitId === castedData.benefitId).length;

                          return (
                            <td rowSpan={rowSpan} className="text-center align-middle">
                              {benefitName}
                            </td>
                          );
                        }

                        return null;
                      },
                    },
                    {
                      header: "COVERAGE",
                      key: "coverage",
                      className: "!text-center !justify-center !items-center",
                      align: "center",
                      render(data: any) {
                        const castedData = data as QuotationBenefit;
                        let yearValue = "";
                        if (castedData.ageFrom && castedData.ageTo) {
                          yearValue = `${castedData.ageFrom} - ${castedData.ageTo} Years Old`;
                        } else if (castedData.ageFrom && !castedData.ageTo) {
                          yearValue = `${castedData.ageFrom} Years Old and above`;
                        } else {
                          yearValue = "All Ages";
                        }
                        return (
                          <div className="flex flex-row flex-nowrap w-full">
                            <td className="w-[50%]">
                              {Intl.NumberFormat("en-US", {
                                style: "currency",
                                currency: "Php",
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }).format(Number(castedData.maximumCoverageAmount ?? 0))}
                            </td>
                            <td className="w-[50%]">{yearValue}</td>
                          </div>
                        );
                      },
                    },
                    {
                      header: "ANNUAL PREMIUM RATES",
                      key: "annualPremiumRate",
                      className: "!text-center !justify-center !items-center",
                      align: "center",
                      render(data: any) {
                        const castedData = data as QuotationBenefit;
                        const percentage = `${Number(castedData.rate ?? 0)}%`;
                        return `${percentage} of the total share cooperative per year`;
                      },
                    },
                    {
                      header: "TOTAL COMMISSION",
                      key: "totalCommission",
                      className: "!text-center !justify-center !items-center",
                      align: "center",
                      render(rowData: any, rowIndex: number) {
                        const castedBenefit = rowData as QuotationBenefit;

                        const sourceList: QuotationCommission[] = hasAnyOption ? (commissionsByOption[currentOption] ?? []) : legacyCommissions;

                        const rowsForDisplay = sourceList.filter((c) => {
                          const agesMatch = (c.ageFrom === castedBenefit.ageFrom && c.ageTo === castedBenefit.ageTo) || (c.ageFrom == null && c.ageTo == null);
                          return agesMatch;
                        });

                        const compact = uniqueBy(rowsForDisplay, (c) => `${c.commissionTypeId}-${fmtRate(c.rate)}`).map((c) => {
                          const typeName = commissionTypesList.find((t: ICommissionType) => t.id === c.commissionTypeId)?.name ?? `#${c.commissionTypeId}`;
                          return `${typeName} - ${fmtRate(c.rate)}%`;
                        });

                        if (compact.length === 0) return rowIndex === 0 ? "N/A" : null;

                        return rowIndex === 0 ? (
                          <td rowSpan={benefits.length}>
                            <pre>{compact.join("\n")}</pre>
                          </td>
                        ) : null;
                      },
                    },
                  ] as const;

                  return (
                    <div key={option} className="mt-6">
                      <h3 className="text-lg font-semibold text-primary mb-4">Option {option}</h3>
                      <EditableTable columns={columns as any} rows={benefits} editable={false} />
                    </div>
                  );
                })}
              </div>
            </div>

            {quotationData?.status === ProposalStatus.draft && (
              <div className="mt-4">
                <div className="flex justify-between ">
                  <div className="flex justify-start">
                    {quotationData?.status === Statuses.APPROVED && (
                      <Button
                        variant="primary"
                        classNames="w-full md:w-auto flex gap-2"
                        onClick={() => {
                          if (quotationData?.id) {
                            const exportUrl = `/actuary-evaluation-report/${quotationData.id}/export`;
                            handlePrint(exportUrl);
                          } else {
                            toast.error("Missing AER ID.");
                          }
                        }}
                      >
                        <PiPrinter className="mt-1" />
                        Print
                      </Button>
                    )}
                  </div>

                  <div className="flex md:flex-row gap-4 md:gap-2 w-full md:w-auto justify-end">
                    <Button variant="default" onClick={handleEditAer} classNames="w-full md:w-auto bg-accent" disabled={updateAerStatusState.loading}>
                      Edit Request
                    </Button>
                    <Button variant="primary" onClick={handleSubmit} classNames="w-full md:w-auto">
                      Submit
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {!isShowSignatureStatus && (
          <div className="col-span-12 md:col-span-3">
            <div className="block shadow-md p-5">
              <span className="text-xl">STATUS</span>
              <div className="flex flex-row gap-3 justify-between items-center my-2">
                <div className="text-xs font-medium">Status</div>
                <span className="text-sm">{quotationData?.status?.replace(/_/g, " ")}</span>
              </div>
              <div className="flex flex-row gap-3 justify-between items-center my-2">
                <div className="text-xs font-medium">Date Submitted</div>
                <span className="text-sm">{formatWordDateDDMMYYY(new Date(quotationData?.updatedAt ?? new Date()).toISOString())}</span>
              </div>
              <hr className="my-14 border-gray/20" />
              <span className="block text-xl mb-5">REMARKS</span>
              <span className="flex flex-row gap-2 items-center">
                <input type="checkbox" size={12} checked={parsedRemarks.checkboxes.find((r) => r.label === quotationRemarks.ALLIN)?.checked} readOnly />
                <p className="text-xs font-[12]">Age All in</p>
              </span>
              <span className="flex flex-row gap-2 items-center">
                <input type="checkbox" size={12} checked={parsedRemarks.checkboxes.find((r) => r.label === quotationRemarks.CONTESTABILITY)?.checked} readOnly />
                <p className="text-xs font-[12]">Waived contestability</p>
              </span>
              <span className="flex flex-row gap-2 items-center">
                <input type="checkbox" size={12} checked={parsedRemarks.checkboxes.find((r) => r.label === quotationRemarks.FIVEPERCENT66TO69)?.checked} readOnly />
                <p className="text-xs font-[12]">5% Members aged 66-69</p>
              </span>
              {parsedRemarks.notes.map((note: string, index: number) => (
                <span key={index} className="flex flex-row gap-2 items-center min-h-[50px] border border-zinc-300 bg-zinc-100 rounded p-2">
                  <p className="text-xs">{note}</p>
                </span>
              ))}
              <div className="my-4">
                <div className="text-xs font-medium mb-1">Attachments</div>
                {aerAttachments.length > 0 ? (
                  <ul className="space-y-1">
                    {aerAttachments.map((att) => (
                      <li key={att.id} className="flex justify-between items-center gap-2">
                        <a
                          className="text-xs underline text-accent truncate"
                          href={`${import.meta.env.VITE_AWS_S3_ENDPOINT}/${att.filepath}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          title={att.label || att.filename}
                        >
                          {att.label || att.filename}
                        </a>
                        <span className="text-[11px] text-gray-500 shrink-0">{typeof att.size === "number" ? (att.size / 1024).toFixed(1) : "0.0"} KB</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div className="text-xs text-gray-500">No attachment</div>
                )}
              </div>
            </div>
          </div>
        )}

        {!showActions && isShowSignatureStatus && (
          <>
            <div className="col-span-12 md:col-span-3 justify-center mt-10">
              <div className="flex flex-row  w-full justify-center items-center ">
                <FaUsers size={20} className="mr-2" />
                <Typography size="lg">SIGNEES</Typography>
              </div>
              <Tabs
                className="mt-4"
                contentClass="p-6 border-0 bg-sky-50"
                headerClass="text-xs rounded-t-lg h-10"
                activeTabClassName="bg-primary !text-white text-xs"
                headers={["Ongoing Approvals", "Approval History"]}
                contents={[
                  <Signatories2 signatories={quotationData?.approval?.signatories} toggleRemarksModal={toggleRemarksModal} selectedSignatory={handleDataCurrentSignatory} />,
                  <ApprovalHistory historyData={signatoryLogs} />,
                ]}
              />
            </div>
          </>
        )}
      </div>

      {statusModal && (
        <Modal title="Change AER Status" titleClass="text-md md:text-lg" modalContainerClassName="!max-w-2xl" isOpen={statusModal} onClose={toggleModal}>
          <ChangeStatusForm handleSubmit={handleSubmitStatus} isSubmitting={processing} options={statusOptions} />
        </Modal>
      )}

      {remarksModal && (
        <Modal title="Remarks" titleClass="text-md md:text-lg" modalContainerClassName="!max-w-2xl" isOpen={remarksModal} onClose={toggleRemarksModal}>
          <ApproverRemarksDynamic currentSignatory={dataCurrentSignatory} />
        </Modal>
      )}

      <SubmitModal controller={modalController} contestabilityLabel={contestabilityLabel} membersAged66To69={membersAged66To69} allIn={allIn} />
    </section>
  );
}

export function QuotationClspAerView() {
  return <BaseQuotationClspAerView showActions={true} isShowSignatureStatus={false} />;
}

export function QuotationClspQuotationView() {
  const location = useLocation();
  const { isShowSignatureStatusLocal, showActions } = location.state || {};
  return <BaseQuotationClspAerView showActions={showActions} isShowSignatureStatus={isShowSignatureStatusLocal} />;
}
