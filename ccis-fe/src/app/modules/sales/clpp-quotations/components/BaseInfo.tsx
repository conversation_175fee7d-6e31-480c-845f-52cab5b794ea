import { ProductCode } from "@enums/product-code";
import { IContestability } from "@interface/contestability.interface";
import { ICda } from "@interface/cooperatives-cda";
import { ICoverageType } from "@interface/coverage-type.interface";
import { ISharesCoopInformation } from "@interface/shares.interface";
import Input from "@modules/sales/components/input";
import Select, { TOptions } from "@modules/sales/components/select";
import { useModalController } from "@modules/sales/controller";
import CooperativeModal from "@modules/sales/modals/CooperativeModal";
import colorMode from "@modules/sales/utility/color";
import { parseNumber } from "@modules/sales/utility/number";
import { useContestabilityActions } from "@state/reducer/contestability";
import { useCooperativesManagementActions } from "@state/reducer/cooperatives";
import { useCoverageTypeActions } from "@state/reducer/coverage-type";
import { RootState } from "@state/store";
import { useEffect, useMemo, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { toast } from "react-toastify";

export type TClppBaseData = {
  cooperativeId: number;
  previousProvider: string;
  typeOfCoverage: string;
  contestabilityId: number;
  productId?: number;
  fileName?: string;
};

type TClppBaseInfoProps = {
  value?: TClppBaseData;
  onChange?: (value: TClppBaseData) => void;
};

export const ClppBaseInfo = ({ value, onChange }: TClppBaseInfoProps) => {
  const { getCooperatives, getCooperativeById } = useCooperativesManagementActions();
  const { getContestability } = useContestabilityActions();
  const { getCoverageTypes } = useCoverageTypeActions();
  const getCooperativeByIdState = useSelector((state: RootState) => state.cooperatives?.getCooperativeById);
  const cooperativesData = useSelector((state: RootState) => state.cooperatives?.cooperatives);

  const contestabilityData = useSelector((state: RootState) => state.contestability.contestabilities);

  const coverageTypesData = useSelector((state: RootState) => state.coverageType.getCoverageTypes);

  const cooperativeModalController = useModalController();

  const [fieldState, setFieldState] = useState<TClppBaseData>(
    value ?? {
      cooperativeId: 0,
      previousProvider: "",
      typeOfCoverage: "",
      contestabilityId: 0,
    }
  );
  useEffect(() => {
    setFieldState(
      value ?? {
        cooperativeId: 0,
        previousProvider: "",
        typeOfCoverage: "",
        contestabilityId: 0,
      }
    );
  }, [value]);

  const [dynamicCooperatives, setDynamicCooperatives] = useState<ICda[]>([]);
  const [fetchingCoopIds, setFetchingCoopIds] = useState<Set<number>>(new Set());
  const processedCoopRef = useRef<Set<number>>(new Set());

  const cooperativesSelectItems = useMemo<TOptions[]>(() => {
    const items = [
      ...cooperativesData.map((c: ISharesCoopInformation) => ({
        text: c.coopName,
        value: c.id?.toString() ?? "",
      })),
      ...dynamicCooperatives.map((c) => ({
        text: c.coopName,
        value: c.coopCdaId.toString(),
      })),
    ];

    // dedupe by value
    const seen = new Set<string>();
    return items.filter((it) => {
      if (seen.has(it.value)) return false;
      seen.add(it.value);
      return true;
    });
  }, [cooperativesData, dynamicCooperatives]);

  const typeOfCoverageSelectItems = useMemo<TOptions[]>(() => {
    return (coverageTypesData.data ?? []).map((data: ICoverageType) => ({
      text: data.coverageType,
      value: data.id!.toString(),
    }));
  }, [coverageTypesData]);

  const contestabilitySelectItems = useMemo<TOptions[]>(() => {
    if (!contestabilityData) return [];
    return contestabilityData.map((item: IContestability) => ({
      text: item.label,
      value: item.id?.toString() ?? "",
    }));
  }, [contestabilityData]);

  const handleChange = (key: keyof TClppBaseData, value: string | number) => {
    const data = { ...fieldState, [key]: value };
    setFieldState(data);
    onChange?.(data);
  };

  useEffect(() => {
    getCooperatives({ payload: { filter: "" } });
    getContestability({ filter: ProductCode.CLPP });
    getCoverageTypes();
  }, []);

  const shallowEqualBaseInfo = (a?: TClppBaseData, b?: TClppBaseData) => {
    if (!a || !b) return a === b;
    return a.cooperativeId === b.cooperativeId && a.previousProvider === b.previousProvider && a.contestabilityId === b.contestabilityId && a.productId === b.productId;
  };
  // Debounce ref for search
  const searchTimeoutRef = useRef<number | null>(null);

  // ---- Handle getCooperativeById success/error (once per id) ----
  useEffect(() => {
    const state = getCooperativeByIdState;
    if (!state) return;

    if (state.success && state.data) {
      const data = state.data;
      const coopId = data.id!;
      if (processedCoopRef.current.has(coopId)) return;
      processedCoopRef.current.add(coopId);

      const normalized: ICda = {
        coopCdaId: coopId,
        coopName: data.coopName,
      } as ICda;

      setDynamicCooperatives((prev) => {
        if (prev.some((c) => c.coopCdaId === normalized.coopCdaId)) return prev;
        return [...prev, normalized];
      });

      setFetchingCoopIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(coopId);
        return newSet;
      });
    }

    if (state.error) {
      setFetchingCoopIds(new Set());
      toast.warn("Failed to fetch cooperative by ID");
    }
  }, [getCooperativeByIdState?.success, getCooperativeByIdState?.error, getCooperativeByIdState?.data?.id]);

  // ---- Fetch-by-id ONLY when cooperativeId actually changes ----
  const prevCoopIdRef = useRef<number | null>(null);
  useEffect(() => {
    const coopId = fieldState.cooperativeId;
    if (!coopId) return;

    // Only when the id changes
    if (prevCoopIdRef.current === coopId) return;
    prevCoopIdRef.current = coopId;

    const inRedux = cooperativesData.some((c: ISharesCoopInformation) => c.id === coopId);
    const inDynamic = dynamicCooperatives.some((c: ICda) => c.coopCdaId === coopId);
    const isAlreadyFetching = fetchingCoopIds.has(coopId);

    if (inRedux || inDynamic || isAlreadyFetching) return;

    setFetchingCoopIds((prev) => {
      const s = new Set(prev);
      s.add(coopId);
      return s;
    });

    getCooperativeById({ id: coopId });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fieldState.cooperativeId]);

  useEffect(() => {
    if (value && !shallowEqualBaseInfo(value, fieldState)) {
      setFieldState(value); // Only update if value exists
    }
  }, [value]);

  const handleCooperativeSearch = (searchTerm?: string) => {
    if (searchTimeoutRef.current) {
      window.clearTimeout(searchTimeoutRef.current);
    }
    searchTimeoutRef.current = window.setTimeout(() => {
      getCooperatives({ payload: { filter: searchTerm ?? "" } });
    }, 300);
  };

  const coopValueStr = fieldState.cooperativeId ? String(fieldState.cooperativeId) : "";

  return (
    <>
      <CooperativeModal
        controller={cooperativeModalController}
        onSelect={(coop: ICda) => {
          setDynamicCooperatives((prev) => {
            if (prev.some((c) => c.coopCdaId === coop.coopCdaId)) return prev;
            return [...prev, coop];
          });
          handleChange("cooperativeId", coop.coopCdaId);
        }}
      />
      <div className="grid grid-cols-12 gap-2 md:gap-y-7 py-6">
        {/* Cooperative */}
        <div className="col-span-12 md:col-span-2 flex flex-col">
          <span
            className={colorMode({
              classLight: "mt-[8px] font-[500] text-[14px] text-gray",
              classDark: "mt-[8px] font-[500] text-[14px] text-white/60",
            })}
          >
            Cooperative
          </span>
        </div>
        <div className="col-span-12 md:col-span-10 px-4">
          <Select
            name="cooperativeId"
            placeholder="Select Coop"
            options={cooperativesSelectItems}
            // onChange={(e) => {
            //   handleChange("cooperativeId", e);
            // }}
            // value={fieldState.cooperativeId.toString()}
            value={coopValueStr}
            onChange={(v) => {
              if (v) handleChange("cooperativeId", parseNumber(v));
            }}
            allowSearch={true}
            placeHolderCenter={false}
            onSearch={handleCooperativeSearch}
          >
            <span
              onClick={() => {
                cooperativeModalController.openFn();
              }}
            >
              Add New Coop
            </span>
          </Select>
        </div>
        {/* Branch */}
        {/* <div className="col-span-12 md:col-span-2 flex flex-col">
          <span
            className={colorMode({
              classLight: "mt-[8px] font-[500] text-[14px] text-gray",
              classDark: "mt-[8px] font-[500] text-[14px] text-white/60",
            })}
          >
            Branch
          </span>
        </div>
        <div className="col-span-12 md:col-span-10">
          <Input
            name="branch"
            placeholder="Enter branch"
            value={fieldState.branch}
            onChange={(e) => {
              handleChange("branch", e.target.value);
            }}
          />
        </div> */}
        {/* Previous Provider */}
        <div className="col-span-12 md:col-span-2 flex flex-col">
          <span
            className={colorMode({
              classLight: "mt-[8px] font-[500] text-[14px] text-gray",
              classDark: "mt-[8px] font-[500] text-[14px] text-white/60",
            })}
          >
            Previous Provider
          </span>
        </div>
        <div className="col-span-12 md:col-span-10">
          <Input
            name="previousProvider"
            placeholder="Enter previous provider"
            value={fieldState.previousProvider ?? ""}
            onChange={(e) => {
              handleChange("previousProvider", e.target.value);
            }}
          />
        </div>
        {/* Type of Coverage */}
        <div className="col-span-12 md:col-span-2 flex flex-col">
          <span
            className={colorMode({
              classLight: "mt-[8px] font-[500] text-[14px] text-gray",
              classDark: "mt-[8px] font-[500] text-[14px] text-white/60",
            })}
          >
            Type of Coverage
          </span>
        </div>
        <div className="col-span-12 md:col-span-10 lg:col-span-3 px-4">
          <Select
            name="typeOfCoverage"
            placeholder="Select"
            options={typeOfCoverageSelectItems}
            value={fieldState.typeOfCoverage}
            onChange={(e) => {
              handleChange("typeOfCoverage", e);
            }}
          />
        </div>
        {/* Contestability */}
        <div className="col-span-12 md:col-span-2 lg:col-span-2 xl:col-span-1 col-start-1 lg:col-start-8 xl:col-start-9 flex flex-col">
          <span
            className={colorMode({
              classLight: "mt-[8px] font-[500] text-[14px] text-gray",
              classDark: "mt-[8px] font-[500] text-[14px] text-white/60",
            })}
          >
            Contestabilty
          </span>
        </div>
        <div className="col-span-12 md:col-span-10 lg:col-span-3 px-4">
          <Select
            name="contestabilityId"
            placeholder="Select"
            options={contestabilitySelectItems}
            value={fieldState.contestabilityId.toString()}
            onChange={(e) => {
              handleChange("contestabilityId", e);
            }}
          />
        </div>
      </div>
    </>
  );
};
