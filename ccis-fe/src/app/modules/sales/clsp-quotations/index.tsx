/* eslint-disable react-hooks/exhaustive-deps */
import _ from "lodash";
import { useEffect, useRef, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { toast } from "react-toastify";
import { PiCloudArrowUp } from "react-icons/pi";

import Button from "@components/common/Button";
import { ROUTES } from "@constants/routes";
import { RootState } from "@state/reducer";
import { useAppDispatch } from "@state/store";
import { TCreateClspQuotationWithAerPayload } from "@state/types/quotation";
import { useQuotationActions } from "@state/reducer/quotations";

import colorMode from "../utility/color";
import { parseNumber } from "../utility/number";
import { AlertError } from "../components/alert-error";
import { ClspActionButton } from "./components/ActionButton";
import { ClspBaseInfo, TClspBaseData } from "./components/BaseInfo";
import { ClspBenefitInfo, TClspBenefitInfo } from "./components/BenefitInfo";
import { ClspClaimsExperienceInfo, TClspClaimsExperienceInfo } from "./components/ClaimsExperience";
import { ClspCommissionDistributionInfo, TClspCommissionDistribution } from "./components/CommisionDistribution";
import { COVERAGE_BASIS } from "./components/constants";
import { ClspMemberAndAverageInfo, TClspMemberAndAverageInfo } from "./components/MemberAndAverageInfo";
import { ClspOptionsInfo, TClspOptions } from "./components/OptionsInfo";
import { ClspPortfolioInfo, TClspPortfolioInfo } from "./components/PortfolioInfo";
import { useModalController } from "../controller";
import UploadMasterListModal from "../modals/UploadMasterListModal";
import DemographicModal from "../modals/DemographicModal";
import httpClient from "@clients/httpClient";
import { ProductCode } from "@enums/product-code";
import { useProductActions } from "@state/reducer/products";
import { getProductNameService } from "@services/products/products.service";
import { postDemographicDataService } from "@services/quotation/quotation.service";
import Modal from "@components/common/Modal";
import { FaChevronLeft, FaInfoCircle } from "react-icons/fa";
import { Statuses } from "@constants/global-constant-value";
import { ProductStatus } from "@enums/product-status";
import { confirmSaveOrEdit, showError } from "@helpers/prompt";
import { putClspAER } from "@state/reducer/actuary-clsp-aer";

type TClspFormState = {
  baseInfo: TClspBaseData;
  memberAndAverageInfo: TClspMemberAndAverageInfo;
  portfolioInfo: TClspPortfolioInfo;
  claimsExperience: TClspClaimsExperienceInfo;
  benefits: TClspBenefitInfo[];
  commissionDistribution: TClspCommissionDistribution[];
};

const initialState = (): TClspFormState => ({
  baseInfo: {
    cooperativeId: 0,
    previousProvider: "",
    contestabilityId: 0,
    productId: 0,
  },
  memberAndAverageInfo: {
    coverageBasis: COVERAGE_BASIS.SHARE_CAPITAL,
    totalNumberOfMembers: "",
    averageAge: "",
  },
  portfolioInfo: {
    portfolioYears: [],
    shareCapitalAges: [],
    savingsAges: [],
    timeDepositAges: [],
  },
  claimsExperience: {
    years: [
      {
        year: new Date().getFullYear(),
        numberOfDeaths: "",
        totalClaimAmount: "",
      },
    ],
    ages: [
      {
        ageFrom: "",
        ageTo: "",
        numberOfDeaths: "",
        totalClaimAmount: "",
      },
    ],
  },
  benefits: [],
  commissionDistribution: [],
});

export default function CLSPQuotation() {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [showStandardProposalModal, setShowStandardProposalModal] = useState(false);
  const { createClspQuotationWithAer, clearClspQuotation } = useQuotationActions();
  const { setSelectedProduct, clearSelectedProduct } = useProductActions();
  const aerData = useSelector((state: RootState) => state.quotation.createClspQuotationWithAer);
  const putClspAer = useSelector((state: RootState) => state.clspAER.putClspAER);
  const uploadMasterListModalController = useModalController();
  const demographicModalController = useModalController();
  const [formState, setFormState] = useState<TClspFormState>(() => initialState());
  const location = useLocation();

  const quotationData = location.state?.quotationData;
  const productCode = localStorage.getItem("productCode") || ProductCode.CLSP;
  const [conditions, setConditions] = useState<string>("");
  const [savedOptions, setSavedOptions] = useState<TClspOptions[]>([]);
  const [selectedOptions, setSelectedOptions] = useState<number[]>([]);
  const [alertError, setAlertError] = useState<string | undefined>(undefined);
  // const [calculatedRate, setCalculatedRate] = useState<number | null>(null);
  const didRequestEditRef = useRef(false);

  const [claimsHasErrors, setClaimsHasErrors] = useState<boolean>(false);
  //Demographics
  const [fileName, setFileName] = useState<string>("");
  const [hasDemographics, setHasDemographics] = useState<boolean>(false);
  const [demographicData, setDemographicData] = useState<any>(null);
  const productData = useSelector((state: RootState) => state.products?.product);
  // Reset fields
  const reset = () => {
    dispatch(clearClspQuotation());
    setFormState(initialState());
    setSavedOptions([]);
    setSelectedOptions([]);
    setConditions("");
    setAlertError(undefined);
  };

  useEffect(() => {
    if (!aerData?.data) return;
    /**
     * save options to local storage
     */
    navigate(ROUTES.SALES.quotationClspAerView.parse(aerData?.data?.quotationId ?? aerData?.data.id), {
      state: {
        demographicData, // Pass demographic data here
      },
    });
    toast.success("Quotation created successfully!");
    reset();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [aerData]);
  const fetchProduct = async () => {
    const { data } = await getProductNameService(productCode || "");
    if (!data) return;
    setSelectedProduct(data[0]);
  };

  useEffect(() => {
    if (productData) return;
    fetchProduct();
  }, []);
  const fetchData = async () => {
    try {
      const { data } = await postDemographicDataService({
        fileName,
        productType: productCode as string,
        ageFrom: 0,
        ageTo: 200,
      });
      if (data) {
        setDemographicData(data);
      }
    } catch (error) {
      toast.error("Error fetching product proposal");
    }
  };
  useEffect(() => {
    if (fileName) {
      fetchData();
    }
  }, [fileName]);

  const WORKING_MIN = 18;
  const WORKING_MAX = 65;
  const SENIOR_MIN = 66;

  const overlaps = (aFrom: number, aTo: number, bFrom: number, bTo: number) => aFrom <= bTo && aTo >= bFrom;

  const isWorkingOverlap = (ageFrom: number, ageTo: number) => overlaps(ageFrom, ageTo, WORKING_MIN, WORKING_MAX);

  const isSeniorOnly = (ageFrom: number) => ageFrom >= SENIOR_MIN; // strictly 66+ (no overlap with working range)

  const isOnePercent = (rate: number) => rate === 1;

  const isExact = (value: number, target: number) => Number(value) === target;

  const shouldShowStandardProposalWarning = () => {
    let hasStandardWorking = false;
    let hasStandardSenior = false;
    let hasNonStandardBenefit = false;

    for (const b of formState.benefits) {
      const maxCoverage = parseNumber(b.maximumCoverageAmount);
      const ageFrom = parseNumber(b.ageFrom);
      const ageTo = parseNumber(b.ageTo);
      const rate = parseNumber(b.rate);

      if (!Number.isFinite(maxCoverage) || !Number.isFinite(ageFrom) || !Number.isFinite(ageTo) || !Number.isFinite(rate)) continue;

      // Working-age candidate: any overlap with 18–65
      if (isWorkingOverlap(ageFrom, ageTo)) {
        if (isExact(maxCoverage, 300_000) && isOnePercent(rate)) {
          hasStandardWorking = true;
        } else {
          // Non-standard working age benefit found
          hasNonStandardBenefit = true;
        }
      }

      // Senior-only candidate: 66+
      if (isSeniorOnly(ageFrom)) {
        if (isExact(maxCoverage, 100_000) && isOnePercent(rate)) {
          hasStandardSenior = true;
        } else {
          // Non-standard senior benefit found
          hasNonStandardBenefit = true;
        }
      }
    }

    // Only show standard proposal warning if:
    // 1. We have both standard working and senior benefits, AND
    // 2. We don't have any non-standard benefits
    return hasStandardWorking && hasStandardSenior && !hasNonStandardBenefit;
  };

  const shouldShowStandardProposalForCoverage = () => {
    if (formState.benefits.length !== 2) {
      return false; // Only apply this rule when there are exactly 2 entries
    }

    const entry1 = formState.benefits[0];
    const entry2 = formState.benefits[1];

    const coverage1 = parseNumber(entry1.maximumCoverageAmount);
    const coverage2 = parseNumber(entry2.maximumCoverageAmount);

    // Check if both coverages are below their respective thresholds
    const entry1Below300k = coverage1 < 300000;
    const entry2Below100k = coverage2 < 100000;

    // If entry 1 is below 300k AND entry 2 is below 100k, show standard modal
    return entry1Below300k && entry2Below100k;
  };

  const getActivePortfolioAges = () => {
    switch (formState.memberAndAverageInfo.coverageBasis) {
      case COVERAGE_BASIS.SHARE_CAPITAL:
        return formState.portfolioInfo.shareCapitalAges;
      case COVERAGE_BASIS.SAVINGS:
        return formState.portfolioInfo.savingsAges;
      case COVERAGE_BASIS.TIME_DEPOSIT:
        return formState.portfolioInfo.timeDepositAges;
      default:
        return [];
    }
  };

  const createAerPayload = (): TCreateClspQuotationWithAerPayload => {
    const coverageBasis = formState.memberAndAverageInfo.coverageBasis;

    let portfolioAges: Record<string, any>[] = [];
    let portfolioYears: Record<string, any>[] = [];

    switch (coverageBasis) {
      case COVERAGE_BASIS.SHARE_CAPITAL:
        portfolioAges = formState.portfolioInfo.shareCapitalAges.map((item) => ({
          ageFrom: parseNumber(item.ageFrom),
          ageTo: parseNumber(item.ageTo),
          totalNumberOfMembers: parseNumber(item.totalNumberOfMembers),
          totalCapital: parseNumber(item.totalShareCapital),
          portfolioType: "Total " + coverageBasis,
          averageCoverage: item.averageCoverage ?? 0,
        }));
        portfolioYears = formState.portfolioInfo.portfolioYears.map((item) => ({
          year: item.year.toString(),
          minimumAmount: parseNumber(item.minimumAmount),
          maximumAmount: parseNumber(item.maximumAmount),
          totalPortfolio: parseNumber(item.totalAmount),
          portfolioType: "Total " + coverageBasis,
          averageCoverage: item.averageCoverage ?? 0,
        }));

        break;
      case COVERAGE_BASIS.SAVINGS:
        portfolioAges = formState.portfolioInfo.savingsAges.map((item) => ({
          ageFrom: parseNumber(item.ageFrom),
          ageTo: parseNumber(item.ageTo),
          totalNumberOfMembers: parseNumber(item.totalNumberOfMembers),
          totalCapital: parseNumber(item.totalSavingsCapital),
          portfolioType: "Total " + coverageBasis,
          averageCoverage: item.averageCoverage ?? 0,
        }));
        portfolioYears = formState.portfolioInfo.portfolioYears.map((item) => ({
          year: item.year.toString(),
          minimumAmount: parseNumber(item.minimumAmount),
          maximumAmount: parseNumber(item.maximumAmount),
          totalPortfolio: parseNumber(item.totalAmount),
          portfolioType: "Total " + coverageBasis,
          averageCoverage: item.averageCoverage ?? 0,
        }));

        break;
      case COVERAGE_BASIS.TIME_DEPOSIT:
        portfolioAges = formState.portfolioInfo.timeDepositAges.map((item) => ({
          ageFrom: parseNumber(item.ageFrom),
          ageTo: parseNumber(item.ageTo),
          totalNumberOfMembers: parseNumber(item.totalNumberOfMembers),
          totalCapital: parseNumber(item.totalTimeDepositCapital),
          portfolioType: "Total " + coverageBasis,
          averageCoverage: item.averageCoverage ?? 0,
        }));
        portfolioYears = formState.portfolioInfo.portfolioYears.map((item) => ({
          year: item.year.toString(),
          minimumAmount: parseNumber(item.minimumAmount),
          maximumAmount: parseNumber(item.maximumAmount),
          totalPortfolio: parseNumber(item.totalAmount),
          portfolioType: "Total " + coverageBasis,
          averageCoverage: item.averageCoverage ?? 0,
        }));

        break;
    }

    return {
      coverageBasis: formState.memberAndAverageInfo.coverageBasis,
      averageAge: parseNumber(formState.memberAndAverageInfo.averageAge),
      quotations: {
        coopId: formState.baseInfo.cooperativeId,
        previousProvider: formState.baseInfo.previousProvider,
        contestability: formState.baseInfo.contestabilityId,
        totalNumberOfMembers: parseNumber(formState.memberAndAverageInfo.totalNumberOfMembers),
        fileName: fileName,
      },
      quotationCondition: {
        condition: conditions,
      },
      claimsExperienceYears: formState.claimsExperience.years.map((item) => ({
        year: item.year.toString(),
        numberOfDeaths: parseNumber(item.numberOfDeaths),
        totalClaimAmount: parseNumber(item.totalClaimAmount),
      })),
      claimsExperienceAges: formState.claimsExperience.ages.map((item) => ({
        ageFrom: parseNumber(item.ageFrom),
        ageTo: parseNumber(item.ageTo),
        numberOfDeaths: parseNumber(item.numberOfDeaths),
        totalClaimAmount: parseNumber(item.totalClaimAmount),
      })),
      commissionDistributions: savedOptions
        .filter((opt) => selectedOptions.includes(opt.option))
        .flatMap((opt) =>
          (opt.commissionDistribution ?? []).map((cd) => ({
            commissionTypeId: cd.commissionTypeId,
            rate: parseNumber(cd.rate),
            option: String(cd.option ?? opt.option),
          }))
        ),

      clspPortfolioAges: portfolioAges as any,
      clspPortfolioYears: portfolioYears as any,
      status: Statuses.DRAFT,
      options: {
        clspBenefits: savedOptions
          .filter((item) => selectedOptions.includes(item.option))
          .flatMap((item) =>
            item.benefits.map((benefit) => ({
              ...benefit,
              option: item.option,
            }))
          ),
        aerOptions: "[" + selectedOptions.join(",") + "]",
      },
    };
  };

  const handleUploadMasterList = () => {
    uploadMasterListModalController.openFn();
  };

  const handleShowDemographic = () => {
    demographicModalController.openFn();
  };

  const handleSetFileName = (fileName: string) => {
    setFileName(fileName);
  };

  useEffect(() => {
    if (fileName) {
      fetchDemographicData();
    }
  }, [fileName]);

  const fetchDemographicData = async () => {
    try {
      const response = await httpClient.post("/quotations/demographicData", {
        fileName: fileName,
        productType: localStorage.getItem("productCode") || ProductCode.CLSP,
      });
      if (response) {
        setDemographicData(response.data);
      }
    } catch (error) {
      toast.error("An unexpected error occurred. Please try again.");
    }
  };

  const handleLoadOption = (optionToLoad: TClspOptions) => {
    // Load the option's benefits into the form state
    setFormState((prev) => ({
      ...prev,
      benefits: optionToLoad.benefits.map((benefit) => ({
        ...benefit,
        // Convert numbers to strings if your form expects string inputs
        ageFrom: benefit.ageFrom.toString(),
        ageTo: benefit.ageTo.toString(),
        maximumCoverageAmount: benefit.maximumCoverageAmount.toString(),
        rate: benefit.rate.toString(),
        benefitId: benefit.benefitId,
      })),
      // Also load the commission distribution if needed
      commissionDistribution: optionToLoad.commissionDistribution
        ? optionToLoad.commissionDistribution.map((cd) => ({
            commissionTypeId: cd.commissionTypeId,
            rate: cd.rate,
            option: cd.option,
          }))
        : [],
    }));

    // Load conditions if they exist
    if (optionToLoad.conditions) {
      setConditions(optionToLoad.conditions);
    }

    // Show success message
    toast.success(`Option ${optionToLoad.option} loaded successfully!`);
  };

  const checkBenefits = () => {
    const errors: string[] = [];

    if (formState.benefits.length < 1) {
      errors.push("At least 1 benefit is required");
      return errors.reverse();
    }

    for (let i = 0; i < formState.benefits.length; i++) {
      const item = formState.benefits[i];

      // Validate required fields
      if (!item.ageFrom) {
        errors.push(`Age From is required for benefit #${i + 1}`);
      }
      if (!item.ageTo) {
        errors.push(`Age To is required for benefit #${i + 1}`);
      }
      if (!item.benefitId) {
        errors.push(`Benefit Type is required for benefit #${i + 1}`);
      }
      if (!item.maximumCoverageAmount) {
        errors.push(`Maximum Coverage Amount is required for benefit #${i + 1}`);
      }
      if (!item.rate) {
        errors.push(`Rate is required for benefit #${i + 1}`);
      }
    }

    return errors.reverse();
  };

  const checkForm = () => {
    const errors: string[] = [];

    // Base information validation
    if (!formState.baseInfo.cooperativeId) {
      errors.push("Cooperative is required");
    }
    if (!formState.memberAndAverageInfo.totalNumberOfMembers) {
      errors.push("Total Number of Members is required");
    }

    if (!formState.memberAndAverageInfo.averageAge) {
      errors.push("Average Age is required");
    }
    // Validate Claims Experience
    const hasNaturalDeathInYear = formState.claimsExperience.years.some((item) => parseNumber(item.numberOfDeaths) > 0);

    if (hasNaturalDeathInYear) {
      const hasFilledClaimsExperienceAge = formState.claimsExperience.ages.some(
        (item) => parseNumber(item.ageFrom) > 0 && parseNumber(item.ageTo) > 0 && parseNumber(item.numberOfDeaths) > 0 && parseNumber(item.totalClaimAmount) > 0
      );

      if (!hasFilledClaimsExperienceAge) {
        errors.push("Claims Experience Age is required when there is a natural death in year.");
      }
    }

    if (!formState.baseInfo.contestabilityId) {
      errors.push("Contestability is required");
    }
    if (!formState.memberAndAverageInfo.coverageBasis) {
      errors.push("Coverage Basis is required");
    }
    const portfolioYears: any[] = [];
    const portfolioAges: any[] = [];
    let property = "";

    switch (formState.memberAndAverageInfo.coverageBasis) {
      case COVERAGE_BASIS.SHARE_CAPITAL: {
        portfolioAges.push(...formState.portfolioInfo.shareCapitalAges);
        portfolioYears.push(...formState.portfolioInfo.portfolioYears);

        property = "Shares";
        break;
      }
      case COVERAGE_BASIS.SAVINGS: {
        portfolioAges.push(...formState.portfolioInfo.savingsAges);
        portfolioYears.push(...formState.portfolioInfo.portfolioYears);
        property = "Savings";
        break;
      }
      case COVERAGE_BASIS.TIME_DEPOSIT: {
        portfolioAges.push(...formState.portfolioInfo.timeDepositAges);
        portfolioYears.push(...formState.portfolioInfo.portfolioYears);

        property = "TimeDeposit";
        break;
      }
    }

    // Portfolio years validation
    if (portfolioYears.length < 1) {
      errors.push("At least 1 portfolio year is required");
    } else {
      for (let i = 0; i < portfolioYears.length; i++) {
        const item = portfolioYears[i];
        if (!item.year) {
          errors.push(`Years is required for portfolio year #${i + 1}`);
        }
        if (!item.minimumAmount) {
          errors.push(`Minimum Amount is required for portfolio year #${i + 1}`);
        }
        if (!item.maximumAmount) {
          errors.push(`Maximum Amount is required for portfolio year #${i + 1}`);
        }
        if (!item.totalAmount) {
          errors.push(`Total Amount is required for portfolio year #${i + 1}`);
        }
      }
    }
    // Portfolio ages validation
    if (portfolioAges.length < 1) {
      errors.push(`At least 1 portfolio age is required for ${property}`);
    } else {
      for (let i = 0; i < portfolioAges.length; i++) {
        const item = portfolioAges[i];
        if (!item.ageFrom) {
          errors.push(`Age From is required for portfolio age #${i + 1}`);
        }
        if (!item.ageTo) {
          errors.push(`Age To is required for portfolio age #${i + 1}`);
        }
        if (!item.totalNumberOfMembers) {
          errors.push(`Total Number of Members is required for portfolio age #${i + 1}`);
        }
      }
    }

    // Benefits validation
    errors.push(...checkBenefits());

    return errors.reverse();
  };

  useEffect(() => {
    if (!demographicData) return;

    const { totalNumberOfMembers, averageAge } = demographicData;

    setFormState((prev) => ({
      ...prev,
      memberAndAverageInfo: {
        ...prev.memberAndAverageInfo,
        ...(totalNumberOfMembers && {
          totalNumberOfMembers: totalNumberOfMembers.toString(),
        }),
        ...(averageAge && {
          averageAge: averageAge.toString(),
        }),
      },
    }));
  }, [demographicData]);

  useEffect(() => {
    if (!quotationData) return;
    const quotation = quotationData?.quotation ?? {};
    const benefits = Array.isArray(quotation.clspBenefits) ? quotation.clspBenefits : [];

    // group benefits by option
    const groupedBenefits = _.groupBy(benefits, "option");
    const firstOptionKey = Object.keys(groupedBenefits)[0];
    const firstOptionBenefits = firstOptionKey ? groupedBenefits[firstOptionKey] : [];
    const firstOptionId = firstOptionKey ? Number(firstOptionKey) : undefined;

    // build savedOptions
    const groupedOptions: Record<number, TClspOptions> = {};
    benefits.forEach((benefit: any) => {
      const optionId = benefit.option ?? 1;
      if (!groupedOptions[optionId]) {
        groupedOptions[optionId] = {
          option: optionId,
          benefits: [],
          conditions: quotation.quotationCondition?.condition ?? "",
        };
      }
      groupedOptions[optionId].benefits.push({
        benefitId: benefit.benefitId,
        ageFrom: benefit.ageFrom,
        ageTo: benefit.ageTo,
        maximumCoverageAmount: benefit.maximumCoverageAmount,
        rate: benefit.rate,
      });
    });

    const commissionsRaw = Array.isArray(quotation.quotationCommissionDistribution) ? quotation.quotationCommissionDistribution : [];
    const commissionsByOption = _.groupBy(commissionsRaw, "option");

    Object.values(groupedOptions).forEach((opt) => {
      const cds = commissionsByOption[opt.option] ?? [];
      opt.commissionDistribution = cds.map((cd: any) => ({
        commissionTypeId: cd.commissionTypeId,
        rate: cd.rate,
        option: String(opt.option),
      }));
    });
    // Object.values(groupedOptions).forEach((opt) => {
    //   const cds = commissionsByOption[opt.option] ?? [];
    //   opt.commissionDistribution = cds.map((cd: any) => ({
    //     commissionTypeId: cd.commissionTypeId,
    //     rate: cd.rate,
    //     option: String(opt.option),
    //   }));
    // });

    const loadedOptions = Object.values(groupedOptions);
    const hasAnyOption = commissionsRaw.some((cd: any) => cd.option != null);

    const editorCommissionDistribution = commissionsRaw
      .filter(
        (cd: any) => (hasAnyOption ? String(cd.option) === String(firstOptionId) : true) // include all when legacy data has null options
      )
      .map((cd: any) => ({
        commissionTypeId: cd.commissionTypeId,
        rate: cd.rate,
        option: String(cd.option ?? firstOptionId),
      }));
    Object.values(groupedOptions).forEach((opt) => {
      const cds = commissionsByOption[opt.option] ?? [];
      // Deep clone the commission distribution
      opt.commissionDistribution = cds.map((cd: any) => ({
        commissionTypeId: cd.commissionTypeId,
        rate: cd.rate,
        option: String(opt.option),
      }));
    });
    // --- helpers to dedup ---
    const uniqYears = (arr: any[]) => _.uniqBy(arr, (x) => `${x.year}-${x.id ?? ""}-${x.type ?? ""}`);
    const uniqAges = (arr: any[]) => _.uniqBy(arr, (x) => `${x.ageFrom}-${x.ageTo}-${x.id ?? ""}-${x.type ?? ""}`);
    const uniqPortfolioYears = (arr: any[]) => _.uniqBy(arr, (x) => `${x.year}-${x.minimumAmount}-${x.maximumAmount}-${x.totalPortfolio}-${x.id ?? ""}`);
    const uniqPortfolioAges = (arr: any[]) => _.uniqBy(arr, (x) => `${x.ageFrom}-${x.ageTo}-${x.totalNumberOfMembers}-${x.totalCapital}-${x.id ?? ""}`);

    // normalize portfolio + claims
    const portfolioYearsRaw = Array.isArray(quotation.clspPortfolioYears) ? quotation.clspPortfolioYears : [];
    const portfolioAgesRaw = Array.isArray(quotation.clspPortfolioAges) ? quotation.clspPortfolioAges : [];
    const claimsYearsRaw = Array.isArray(quotation?.quotationClaimsExperienceYear) ? quotation?.quotationClaimsExperienceYear : [];
    const claimsAgesRaw = Array.isArray(quotation?.quotationClaimsExperienceAge) ? quotation?.quotationClaimsExperienceAge : [];

    const portfolioYears = uniqPortfolioYears(portfolioYearsRaw).map((item: any) => ({
      id: item.id,
      quotationId: item.quotationId,
      year: item.year,
      minimumAmount: item.minimumAmount,
      maximumAmount: item.maximumAmount,
      totalAmount: item.totalPortfolio,
      portfolioType: item.portfolioType,
      averageCoverage: item.averageCoverage ?? 0,
    }));

    const portfolioAges = uniqPortfolioAges(portfolioAgesRaw);

    setSavedOptions(loadedOptions);
    setSelectedOptions(loadedOptions.map((opt) => opt.option));

    setFormState({
      baseInfo: {
        cooperativeId: quotation.coopId ?? 0,
        previousProvider: quotation.previousProvider ?? "",
        contestabilityId: quotation.contestability ?? 0,
        productId: quotation.productId ?? Number(productData?.id) ?? 0,
        fileName: quotation.fileName || "",
      },
      memberAndAverageInfo: {
        coverageBasis: quotation?.clspQuotation?.coverageBasis ?? COVERAGE_BASIS.SHARE_CAPITAL,
        totalNumberOfMembers: quotation.totalNumberOfMembers ?? "",
        averageAge: quotation.clspQuotation?.averageAge ?? "",
      },
      portfolioInfo: {
        shareCapitalAges: portfolioAges.map((item: any) => ({
          id: item.id,
          quotationId: item.quotationId,
          ageFrom: item.ageFrom,
          ageTo: item.ageTo,
          totalNumberOfMembers: item.totalNumberOfMembers,
          totalShareCapital: item.totalCapital,
          portfolioType: item.portfolioType,
          averageCoverage: item.averageCoverage ?? 0,
        })),
        savingsAges: portfolioAges.map((item: any) => ({
          id: item.id,
          quotationId: item.quotationId,
          ageFrom: item.ageFrom,
          ageTo: item.ageTo,
          totalNumberOfMembers: item.totalNumberOfMembers,
          totalSavingsCapital: item.totalCapital,
          portfolioType: item.portfolioType,
          averageCoverage: item.averageCoverage ?? 0,
        })),
        timeDepositAges: portfolioAges.map((item: any) => ({
          id: item.id,
          quotationId: item.quotationId,
          ageFrom: item.ageFrom,
          ageTo: item.ageTo,
          totalNumberOfMembers: item.totalNumberOfMembers,
          totalTimeDepositCapital: item.totalCapital,
          portfolioType: item.portfolioType,
          averageCoverage: item.averageCoverage ?? 0,
        })),
        portfolioYears,
      },
      claimsExperience: {
        years: uniqYears(claimsYearsRaw).map((item: any) => ({
          id: item.id,
          quotationId: item.quotationId,
          year: item.year,
          numberOfDeaths: item.numberOfDeaths,
          totalClaimAmount: item.totalClaimAmount,
          type: item.type,
        })),
        ages: uniqAges(claimsAgesRaw).map((item: any) => ({
          id: item.id,
          quotationId: item.quotationId,
          ageFrom: item.ageFrom,
          ageTo: item.ageTo,
          numberOfDeaths: item.numberOfDeaths,
          totalClaimAmount: item.totalClaimAmount,
          type: item.type,
        })),
      },
      benefits: firstOptionBenefits,
      commissionDistribution: editorCommissionDistribution,
    });
    if (quotation.fileName) {
      setFileName(quotation.fileName);
      setHasDemographics(true);
    }
    setConditions(quotation.quotationCondition?.condition ?? "");
  }, [quotationData, productData?.id]);

  // Action::selectOption
  const handleSelectOption = (optionId: number) => {
    setSelectedOptions((prev) => (prev.includes(optionId) ? prev.filter((x) => x !== optionId) : [...prev, optionId]));
    const opt = savedOptions.find((o) => o.option === optionId);

    // Deep clone the commission distribution to prevent reference sharing
    const clonedCommissionDistribution = opt?.commissionDistribution
      ? opt.commissionDistribution.map((cd) => ({
          commissionTypeId: cd.commissionTypeId,
          rate: cd.rate,
          option: cd.option,
        }))
      : [];

    setFormState((prev) => ({
      ...prev,
      commissionDistribution: clonedCommissionDistribution,
    }));
  };

  // Put this near your other utils
  const normalizeOption = (opt: TClspOptions) => {
    const normBenefits = (opt.benefits ?? [])
      .map((b) => ({
        benefitId: Number(b.benefitId),
        ageFrom: Number(b.ageFrom),
        ageTo: Number(b.ageTo),
        maximumCoverageAmount: Number(b.maximumCoverageAmount),
        rate: Number(b.rate),
      }))
      // sort so order doesn’t affect equality
      .sort((a, b) => a.benefitId - b.benefitId || a.ageFrom - b.ageFrom || a.ageTo - b.ageTo || a.maximumCoverageAmount - b.maximumCoverageAmount || a.rate - b.rate);

    const normCommissions = (opt.commissionDistribution ?? [])
      .map((cd) => ({
        commissionTypeId: Number(cd.commissionTypeId),
        rate: Number(cd.rate),
      }))
      .sort((a, b) => a.commissionTypeId - b.commissionTypeId || a.rate - b.rate);

    return {
      // ignore `option` id when comparing
      conditions: (opt.conditions ?? "").trim(),
      benefits: normBenefits,
      commissionDistribution: normCommissions,
    };
  };

  const validateCommissionDistributionPairs = (rows: TClspCommissionDistribution[]) => {
    const issues: string[] = [];

    rows.forEach((row, i) => {
      const hasType = Number(row.commissionTypeId) > 0;
      const parsedRate = Number(row.rate);
      const hasRate = Number.isFinite(parsedRate) && parsedRate > 0; // treat 0 or NaN/empty as missing

      if (hasType && !hasRate) issues.push(`Commission row #${i + 1}: enter a rate.`);
      if (!hasType && hasRate) issues.push(`Commission row #${i + 1}: select a commission type.`);
    });

    return issues;
  };
  const presentError = async (title: string, errors: string[]) => {
    if (errors.length === 0) return;
    const singleError = errors[errors.length - 1];
    await showError(title, singleError);
  };

  // Action::saveOption
  const handleSaveOption = () => {
    // Commission Distribution Validation
    const totalCommissionRate = formState.commissionDistribution.reduce((sum, item) => sum + parseNumber(item.rate), 0);

    // If commission distribution exceeds 35%, show error and stop further processing
    if (totalCommissionRate > 35) {
      const msg = "Total Commission Distribution rate must not exceed 35%";
      setAlertError(msg); // Show error alert
      toast.error(msg); // Toast error
      return; // Prevent saving the option
    }

    if (shouldShowStandardProposalForCoverage()) {
      setShowStandardProposalModal(true);
      return;
    }

    if (shouldShowStandardProposalWarning()) {
      setShowStandardProposalModal(true);
      return;
    }

    const errors = checkBenefits();
    if (errors.length > 0) {
      presentError("Benefit Validation Error", errors);
      return;
    } else {
      setAlertError(undefined);
    }
    const cd = formState.commissionDistribution ?? [];
    const cdIssues = validateCommissionDistributionPairs(cd);
    if (cdIssues.length > 0) {
      const first = cdIssues[0];
      presentError("Commission Distribution Error", [first]);
      toast.error(first);
      return;
    }

    const maxOptionId = savedOptions.reduce((max, opt) => Math.max(max, opt.option), 0);
    const optionId = maxOptionId + 1;
    //check duplicate
    const duplicateBenefits = formState.benefits.some((newBenefit) => {
      return savedOptions.some((option) =>
        option.benefits.some(
          (existingBenefit) =>
            String(existingBenefit.benefitId) === String(newBenefit.benefitId) &&
            Number(existingBenefit.maximumCoverageAmount) === Number(newBenefit.ageFrom) &&
            Number(existingBenefit.rate) === Number(newBenefit.ageTo)
        )
      );
    });

    if (duplicateBenefits) {
      toast.error("Benefit already exists.");
      return;
    }

    const option: TClspOptions = {
      option: optionId,
      benefits: formState.benefits.map((b) => ({
        benefitId: b.benefitId,
        ageFrom: parseNumber(b.ageFrom),
        ageTo: parseNumber(b.ageTo),
        maximumCoverageAmount: parseNumber(b.maximumCoverageAmount),
        rate: parseNumber(b.rate),
      })),
      conditions,

      commissionDistribution: (formState.commissionDistribution ?? []).map((cd) => ({
        commissionTypeId: cd.commissionTypeId,
        rate: parseNumber(cd.rate),
        option: optionId.toString(),
      })),
    };

    const optionExists = savedOptions.some((existing) => _.isEqual(normalizeOption(existing), normalizeOption(option)));

    if (optionExists) {
      toast.error("Option already exists");
      return;
    }

    // --- Commission Distribution total must not exceed 35% ---
    const totalRatePct = (formState.commissionDistribution ?? []).reduce((sum, row) => sum + (Number.isFinite(Number(row.rate)) ? Number(row.rate) : 0), 0);

    if (totalRatePct > 35) {
      const msg = "Total Commission Distribution must not exceed 35%";
      setAlertError(msg);
      toast.error(msg);
      return;
    }

    setSavedOptions((prev) => [...prev, option]);
    toast.success(`Option ${optionId} created successfully`);
  };

  const handleCreateAer = () => {
    if (claimsHasErrors) {
      toast.error("Please fix the Claims Experience validation errors before creating an AER.");
      return;
    }

    if (selectedOptions.length <= 0) {
      toast.error("Please select at least one option before creating an AER.");
      return;
    }

    const errors = checkForm();
    if (errors.length > 0) {
      presentError("Form Validation Error", errors);
      return;
    } else {
      setAlertError(undefined);
    }

    try {
      createClspQuotationWithAer(createAerPayload());
      clearSelectedProduct();
    } catch (err) {
      toast.error("Failed to create AER");
    }
  };

  const handleSaveEditAer = async () => {
    if (claimsHasErrors) {
      toast.error("Please fix the Claims Experience validation errors before saving.");
      return;
    }
    if (selectedOptions.length <= 0) {
      toast.error("Please select at least one option before saving.");
      return;
    }

    const errors = checkForm();
    if (errors.length > 0) {
      presentError("Form Validation Error", errors);
      return;
    } else {
      setAlertError(undefined);
    }

    const payload = { ...createAerPayload(), status: ProductStatus.DRAFT };
    const isConfirmed = await confirmSaveOrEdit("Do you want to save the AER?");
    if (!isConfirmed) return;
    didRequestEditRef.current = true;
    try {
      dispatch(putClspAER({ ...payload, id: quotationData?.id as any }));
    } catch {
      toast.error("Failed to save AER");
    }
  };

  useEffect(() => {
    if (!didRequestEditRef.current || !putClspAer?.data) return;
    didRequestEditRef.current = false;

    if (putClspAer.success) {
      navigate(ROUTES.SALES.quotationClspAerView.parse(putClspAer.data.quotationId ?? putClspAer.data.id ?? quotationData?.id), { state: { demographicData } });
    }

    toast.success("AER saved successfully!");
  }, [putClspAer]);

  return (
    <section>
      <UploadMasterListModal
        controller={uploadMasterListModalController}
        onUpload={(files: any[], _: string) => {
          setHasDemographics(files.length > 0);
          return Promise.resolve();
        }}
        data={formState}
        setFileName={handleSetFileName}
      />
      {demographicData && <DemographicModal controller={demographicModalController} hasDemographics={hasDemographics} data={demographicData} />}

      {demographicData && <DemographicModal controller={demographicModalController} hasDemographics={hasDemographics} data={demographicData} />}
      <div className="grid grid-cols-12 gap-2 px-4">
        <Button variant="primary" onClick={() => navigate(ROUTES.SALES.quotations.key)}>
          <div className="flex flex-row items-center gap-2">
            <FaChevronLeft size={20} />
            <span className="text-[14px] font-[400] font-poppins-medium">Back</span>
          </div>
        </Button>
        <div className="block col-span-12">
          <h3
            className={colorMode({
              classLight: "text-2xl font-[600] mb-3 text-primary",
              classDark: "text-2xl font-[600] mb-3 text-white/60",
            })}
          >
            CLSP QUOTATION
          </h3>
          <p
            className={colorMode({
              classLight: "text-gray text-base text-[12px]",
              classDark: "text-white/60 text-base text-[12px]",
            })}
          >
            Create a customized Coop Life Savings Plan quotation based on cooperative-specific data.
          </p>
        </div>
        {/* Main Content */}
        <div className="col-span-12 space-y-10">
          {/* Alert Section */}
          {alertError != undefined && <AlertError message={alertError} />}

          {/* Base Info */}
          <ClspBaseInfo
            value={formState.baseInfo}
            onChange={(value) => {
              setFormState((prev) => ({
                ...prev,
                baseInfo: {
                  ...value,
                  productId: Number(productData?.id) || 0,
                },
              }));
            }}
          />

          <div className="mt-6 flex flex-wrap gap-4">
            {/* Masterlist */}
            <Button classNames="bg-info elevation-sm shadow-md rounded-[5px]" variant="default" onClick={handleUploadMasterList}>
              <div className="flex flex-row items-center gap-2">
                <PiCloudArrowUp size={25} />
                <span className="text-[14px] font-[400] font-poppins-medium">Upload Masterlist</span>
              </div>
            </Button>
            {/* Demographic */}
            <Button classNames="!bg-[#99999926] rounded-[5px]" variant="default" onClick={handleShowDemographic}>
              <div className="flex flex-row items-center gap-2">
                <span
                  className={colorMode({
                    classLight: "text-gray/50 text-[14px] font-[400] font-poppins-medium",
                    classDark: "text-white/60 text-[14px] font-[400] font-poppins-medium",
                  })}
                >
                  Show Demographic
                </span>
              </div>
            </Button>
          </div>
          <span className="text-slate-400 text-xs">You are required to upload Masterlist to autogenerate data</span>
          <hr className="my-10 border-gray/10" />

          <ClspMemberAndAverageInfo
            value={formState.memberAndAverageInfo}
            onChange={(value) => {
              setFormState((prev) => ({
                ...prev,
                memberAndAverageInfo: value,
              }));
            }}
          />

          <ClspPortfolioInfo
            coverageBasis={formState.memberAndAverageInfo.coverageBasis}
            value={formState.portfolioInfo}
            onChange={(value) => {
              setFormState((prev) => ({
                ...prev,
                portfolioInfo: value,
              }));
            }}
            demographicData={demographicData}
          />

          <ClspClaimsExperienceInfo
            value={formState.claimsExperience}
            onChange={(value) => {
              setFormState((prev) => ({
                ...prev,
                claimsExperience: value,
              }));
            }}
            onValidationChange={(hasErrors) => setClaimsHasErrors(hasErrors)}
          />

          <ClspBenefitInfo
            value={formState.benefits}
            ageBands={getActivePortfolioAges().map((r: any) => ({
              ageFrom: r.ageFrom,
              ageTo: r.ageTo,
            }))}
            lockAgeBands
            onChange={(value) => {
              setFormState((prev) => ({ ...prev, benefits: value }));
            }}
          />

          <ClspCommissionDistributionInfo
            value={formState.commissionDistribution}
            onChange={(value) => {
              setFormState((prev) => ({
                ...prev,
                commissionDistribution: value,
              }));
            }}
          />

          <hr className="my-4 border-gray/10" />

          {/* Action Buttons */}
          <ClspActionButton
            numberOfOptions={selectedOptions.length}
            totalNumberOfMembers={parseNumber(formState.memberAndAverageInfo.totalNumberOfMembers)}
            contestabilityId={formState.baseInfo.contestabilityId}
            onSaveOption={handleSaveOption}
            emitConditions={setConditions}
            // onCalculate={handleCalculateClick} for future use
          />

          {/* Options Info */}
          <ClspOptionsInfo
            show={savedOptions.length > 0}
            selectedOptions={selectedOptions}
            options={savedOptions}
            onSelectOption={handleSelectOption}
            onCreateAer={handleCreateAer}
            disableCreate={claimsHasErrors}
            status={quotationData?.status}
            handleSaveEditAer={handleSaveEditAer}
            onLoadOption={handleLoadOption}
          />
        </div>
      </div>
      <Modal isOpen={showStandardProposalModal} onClose={() => setShowStandardProposalModal(false)} showHeader={false} showCloseButton={false} modalContainerClassName="max-w-md mx-auto">
        <div className=" p-5">
          <FaInfoCircle className="text-4xl text-yellow-500 mx-auto" />
          <p className="text-center text-lg font-medium">Use standard proposal instead.</p>
          <p className="text-sm mt-6 text-center">For your chosen coverage of less than ₱300,000 and ₱100,000 with rates of 1%, we recommend the standard proposal.</p>
          <div className="flex items-center justify-center gap-3 mt-4">
            <Button
              variant="default"
              classNames="bg-primary text-white rounded-[5px]"
              onClick={() => {
                setShowStandardProposalModal(false);
                navigate(ROUTES.SALES.quotations.key);
              }}
            >
              Leave page
            </Button>
            <Button classNames="!bg-slate-400 text-gray-700 rounded-[5px]" onClick={() => setShowStandardProposalModal(false)}>
              Stay on page
            </Button>
          </div>
        </div>
      </Modal>
    </section>
  );
}
