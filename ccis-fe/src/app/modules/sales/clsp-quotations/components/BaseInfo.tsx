import { ICda } from "@interface/cooperatives-cda";
import { IContestability } from "@interface/contestability.interface";
import { ISharesCoopInformation } from "@interface/shares.interface";
import { useContestabilityActions } from "@state/reducer/contestability";
import { useCooperativesManagementActions } from "@state/reducer/cooperatives";
import { RootState } from "@state/store";
import { useEffect, useMemo, useRef, useState } from "react";
import { useSelector } from "react-redux";
import CooperativeModal from "../../modals/CooperativeModal";
import Select, { TOptions } from "@modules/sales/components/select";
import { useModalController } from "@modules/sales/controller";
import colorMode from "@modules/sales/utility/color";
import { parseNumber } from "@modules/sales/utility/number";
import TextField from "@components/form/TextField";
import { ProductCode } from "@enums/product-code";
import { toast } from "react-toastify";

export type TClspBaseData = {
  cooperativeId: number;
  previousProvider: string;
  contestabilityId: number;
  productId?: number;
  fileName?: string;
};

type TClspBaseInfoProps = {
  value: TClspBaseData;
  onChange: (value: TClspBaseData) => void;
};

const shallowEqualBaseInfo = (a?: TClspBaseData, b?: TClspBaseData) => {
  if (!a || !b) return a === b;
  return a.cooperativeId === b.cooperativeId && a.previousProvider === b.previousProvider && a.contestabilityId === b.contestabilityId && a.productId === b.productId;
};

export const ClspBaseInfo = ({ value, onChange }: TClspBaseInfoProps) => {
  const { getCooperatives, getCooperativeById } = useCooperativesManagementActions();
  const { getContestability } = useContestabilityActions();

  const cooperativesData = useSelector((state: RootState) => state.cooperatives?.cooperatives);
  const contestabilityData = useSelector((state: RootState) => state.contestability?.contestabilities);
  const productData = useSelector((state: RootState) => state.products?.product);
  const getCooperativeByIdState = useSelector((state: RootState) => state.cooperatives?.getCooperativeById);

  const cooperativeModalController = useModalController();

  const [fieldState, setFieldState] = useState<TClspBaseData>(
    value ?? {
      cooperativeId: 0,
      previousProvider: "",
      contestabilityId: 0,
      productId: productData?.id,
    }
  );

  const [dynamicCooperatives, setDynamicCooperatives] = useState<ICda[]>([]);
  const [fetchingCoopIds, setFetchingCoopIds] = useState<Set<number>>(new Set());
  const processedCoopRef = useRef<Set<number>>(new Set());

  // Debounce ref for search
  const searchTimeoutRef = useRef<number | null>(null);

  useEffect(() => {
    getCooperatives({ payload: { filter: "" } });
    getContestability({ filter: ProductCode.CLSP });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const cooperativesSelectItems = useMemo<TOptions[]>(() => {
    const items = [
      ...cooperativesData.map((c: ISharesCoopInformation) => ({
        text: c.coopName,
        value: c.id?.toString() ?? "",
      })),
      ...dynamicCooperatives.map((c) => ({
        text: c.coopName,
        value: c.coopCdaId.toString(),
      })),
    ];
    // dedupe by value
    const seen = new Set<string>();
    return items.filter((it) => {
      if (seen.has(it.value)) return false;
      seen.add(it.value);
      return true;
    });
  }, [cooperativesData, dynamicCooperatives]);

  const contestabilitySelectItems = useMemo<TOptions[]>(
    () =>
      contestabilityData.map((item: IContestability) => ({
        text: item.label,
        value: item.id?.toString() ?? "",
      })) ?? [],
    [contestabilityData]
  );

  const handleChange = (key: keyof TClspBaseData, v: string | number) => {
    const data = { ...fieldState, [key]: v };
    setFieldState(data);
    onChange?.(data);
  };

  // ---- Handle getCooperativeById success/error (once per id) ----
  useEffect(() => {
    const state = getCooperativeByIdState;
    if (!state) return;

    if (state.success && state.data) {
      const data = state.data;
      const coopId = data.id!;
      if (processedCoopRef.current.has(coopId)) return;
      processedCoopRef.current.add(coopId);

      const normalized: ICda = {
        coopCdaId: coopId,
        coopName: data.coopName,
      } as ICda;

      setDynamicCooperatives((prev) => {
        if (prev.some((c) => c.coopCdaId === normalized.coopCdaId)) return prev;
        return [...prev, normalized];
      });

      setFetchingCoopIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(coopId);
        return newSet;
      });
    }

    if (state.error) {
      setFetchingCoopIds(new Set());
      toast.warn("Failed to fetch cooperative by ID");
    }
  }, [getCooperativeByIdState?.success, getCooperativeByIdState?.error, getCooperativeByIdState?.data?.id]);

  // ---- Fetch-by-id ONLY when cooperativeId actually changes ----
  const prevCoopIdRef = useRef<number | null>(null);
  useEffect(() => {
    const coopId = fieldState.cooperativeId;
    if (!coopId) return;

    // Only when the id changes
    if (prevCoopIdRef.current === coopId) return;
    prevCoopIdRef.current = coopId;

    const inRedux = cooperativesData.some((c: ISharesCoopInformation) => c.id === coopId);
    const inDynamic = dynamicCooperatives.some((c: ICda) => c.coopCdaId === coopId);
    const isAlreadyFetching = fetchingCoopIds.has(coopId);

    if (inRedux || inDynamic || isAlreadyFetching) return;

    setFetchingCoopIds((prev) => {
      const s = new Set(prev);
      s.add(coopId);
      return s;
    });

    getCooperativeById({ id: coopId });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fieldState.cooperativeId]);

  // ---- Guarded sync from parent value → local fieldState ----
  useEffect(() => {
    if (!shallowEqualBaseInfo(value, fieldState)) {
      setFieldState(value);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  // ---- Debounced search ----
  const handleCooperativeSearch = (searchTerm?: string) => {
    if (searchTimeoutRef.current) {
      window.clearTimeout(searchTimeoutRef.current);
    }
    searchTimeoutRef.current = window.setTimeout(() => {
      getCooperatives({ payload: { filter: searchTerm ?? "" } });
    }, 300);
  };

  const coopValueStr = fieldState.cooperativeId ? String(fieldState.cooperativeId) : "";

  return (
    <>
      <CooperativeModal
        controller={cooperativeModalController}
        onSelect={(coop: ICda) => {
          setDynamicCooperatives((prev) => {
            if (prev.some((c) => c.coopCdaId === coop.coopCdaId)) return prev;
            return [...prev, coop];
          });
          handleChange("cooperativeId", coop.coopCdaId);
        }}
      />
      <div className="grid grid-cols-12 gap-2 md:gap-y-7 py-6">
        {/* Cooperative */}
        <div className="col-span-12 md:col-span-2 flex flex-col">
          <span
            className={colorMode({
              classLight: "mt-[8px] font-[500] text-[14px] text-gray",
              classDark: "mt-[8px] font-[500] text-[14px] text-white/60",
            })}
          >
            Cooperative
          </span>
        </div>
        <div className="col-span-12 md:col-span-10">
          <Select
            name="cooperativeId"
            placeholder="Select Coop"
            options={cooperativesSelectItems}
            // Keep value stable even if the option is filtered out during search
            value={coopValueStr}
            onChange={(v) => {
              if (v) handleChange("cooperativeId", parseNumber(v));
            }}
            allowSearch={true}
            onSearch={handleCooperativeSearch}
            placeHolderCenter={false}
          >
            <span
              onClick={() => {
                cooperativeModalController.openFn();
              }}
            >
              Add New Coop
            </span>
          </Select>
        </div>

        {/* Previous Provider */}
        <div className="col-span-12 md:col-span-2 flex flex-col">
          <span
            className={colorMode({
              classLight: "mt-[8px] font-[500] text-[14px] text-gray",
              classDark: "mt-[8px] font-[500] text-[14px] text-white/60",
            })}
          >
            Previous Provider
          </span>
        </div>
        <div className="col-span-12 md:col-span-10 flex justify-start">
          <TextField
            name="previousProvider"
            className="border-slate-200"
            placeholder="Enter previous provider"
            onChange={(e) => {
              handleChange("previousProvider", e.target.value);
            }}
            value={fieldState.previousProvider}
          />
        </div>

        {/* Contestability */}
        <div className="col-span-12 md:col-span-2 flex flex-col">
          <span
            className={colorMode({
              classLight: "mt-[8px] font-[500] text-[14px] text-gray",
              classDark: "mt-[8px] font-[500] text-[14px] text-white/60",
            })}
          >
            Contestabilty
          </span>
        </div>
        <div className="col-span-12 md:col-span-10 lg:col-span-3 flex justify-start">
          <Select
            name="contestabilityId"
            placeholder="Select"
            options={contestabilitySelectItems}
            onChange={(e) => {
              handleChange("contestabilityId", parseNumber(e));
            }}
            value={fieldState.contestabilityId.toString()}
          />
        </div>
      </div>
    </>
  );
};
