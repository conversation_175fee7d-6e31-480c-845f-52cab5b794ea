import { FC, useEffect, useState, ChangeEvent } from "react";
import { TableColumn } from "react-data-table-component";
import { IActions, IDefaultParams, ISelectOptions } from "@interface/common.interface";
import Table from "@components/common/Table";
import { RootState } from "@state/store";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useCooperativesManagementActions } from "@state/reducer/cooperatives";
import { capitalizeFirstLetterWords, getTextStatusColor } from "@helpers/text";
import Typography from "@components/common/Typography";
import ActionDropdown from "@components/common/ActionDropdown";
import { GoVersions } from "react-icons/go";
import Filter from "@components/common/Filter";
import Select from "@components/form/Select";
import { useDebounce, useDebouncedCallback } from "use-debounce";
import { Statuses } from "@constants/global-constant-value";
import Button from "@components/common/Button";
import { FaPlus } from "react-icons/fa";
import { ICooperative } from "@interface/product-proposal.interface";
const CooperativesTables: FC = () => {
  const navigate = useNavigate();
  const { getCooperatives } = useCooperativesManagementActions();
  const cooperatives = useSelector((state: RootState) => state?.cooperatives?.getCooperatives?.data ?? []);
  const loading = useSelector((state: RootState) => state?.cooperatives?.getCooperatives?.loading);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [search, setSearch] = useState<string>("");
  const [debouncedSearch] = useDebounce(search, 1000);
  const [resetCounter, setResetCounter] = useState(0);
  const [statusFilter, setStatusFilter] = useState<string | undefined>("");
  const handlePaginate = (pagination: number) => {
    setPage(pagination);
  };
  const statusOptions: ISelectOptions[] = [
    { text: Statuses.ACTIVE, value: Statuses.ACTIVE },
    { text: Statuses.INACTIVE, value: Statuses.INACTIVE },
  ];
  const handleSearch = useDebouncedCallback((event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearch(value);
  }, 500);
  const handleStatusChange = (event: ChangeEvent<HTMLSelectElement>) => {
    const selectedText = event.target.options[event.target.selectedIndex].value;
    setStatusFilter(selectedText);
  };
  const handleClearAll = () => {
    setSearch("");
    setStatusFilter("");
    setResetCounter((prev) => prev + 1);
  };

  const handleRowsChange = (newPerPage: number, pagination: number) => {
    setPageSize(newPerPage);
    setPage(pagination);
  };
  const fetchCooperatives = () => {
    const payload = {
      page: page,
      pageSize: pageSize,
      filter: debouncedSearch,
      statusFilter: statusFilter,
    } as IDefaultParams;
    getCooperatives({ payload });
  };

  useEffect(() => {
    fetchCooperatives();
  }, [page, pageSize, debouncedSearch, statusFilter]);
  const commonSetting = {
    sortable: true,
    reorder: true,
  };
  const handleAddCoopPage = () => {
    const pathRole = location.pathname.split("/")[1]; // e.g., 'admin'
    navigate(`/${pathRole}/utilities/cooperatives/create`);
  };
  const handleEditCoopPage = (data: ICooperative) => {
    const pathRole = location.pathname.split("/")[1];
    navigate(`/${pathRole}/utilities/cooperatives/edit/${data?.id}`, { state: { cooperative: data } });
  };
  const actionEvents: IActions<any>[] = [
    {
      name: "View",
      event: (data: any, _index: number) => {
        handleEditCoopPage(data as ICooperative);
      },
      icon: GoVersions,
      color: "primary",
    },
  ];
  const columns: TableColumn<any>[] = [
    {
      name: "Coop Code",
      selector: (row) => row?.coopCode,
      ...commonSetting,
    },
    {
      name: "Cooperative",
      cell: (row) => row?.coopName,
    },
    {
      name: "Acronym",
      cell: (row) => row?.coopAcronym,
    },
    {
      name: "Business Address",

      cell: (row) => [row?.streetAddress, row?.barangay, row?.city, row?.province].filter(Boolean).join(", "),
    },
    {
      name: "Website",
      cell: (row) => row?.websiteAddress || "N/A",
    },
    {
      name: "Email Address",
      cell: (row) => row?.emailAddress || "N/A",
    },
    {
      name: "Status",
      cell: (row) => <span className={`${getTextStatusColor(row.status)}`}>{capitalizeFirstLetterWords(row.status || "N/A", "_")}</span>,
    },
    {
      name: <Typography className="flex justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => (
        <div className="flex justify-center">
          <ActionDropdown actions={actionEvents} data={row} rowIndex={rowIndex} />
        </div>
      ),
    },
  ];

  return (
    <div className="mt-8">
      <div className="flex flex-row flex-wrap items-center justify-between w-full gap-2">
        <div className="flex flex-row flex-nowrap gap-3 justify-between w-full">
          <div className="w-fit">
            <Filter search={search} onChange={handleSearch}>
              <div className="flex justify-end">
                <button className="text-primary text-xs btn-sm" type="button" onClick={handleClearAll}>
                  Clear All
                </button>
              </div>
              <div>
                <div className="text-xs">Status</div>
                <Select key={`status-${resetCounter}`} size="sm" placeholder="Status" options={statusOptions} value={statusFilter} onChange={handleStatusChange} />
              </div>
            </Filter>
          </div>
          {/* </div> */}
          <Button classNames="w-fit h-[40px] ms-auto" variant="primary" onClick={handleAddCoopPage}>
            <span className="text-nowrap">
              <FaPlus className="inline ms-auto" />
              Add Coop
            </span>
          </Button>
        </div>
      </div>
      <Table
        className="h-[600px] mt-4"
        columns={columns}
        data={cooperatives?.data}
        loading={loading}
        multiSelect={false}
        paginationServer={true}
        paginationTotalRows={cooperatives?.meta?.total}
        onPaginate={handlePaginate}
        onChangeRowsPerPage={handleRowsChange}
        hideButton="invisible"
      />
    </div>
  );
};
export default CooperativesTables;
