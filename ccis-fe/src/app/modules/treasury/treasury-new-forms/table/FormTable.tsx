import React, { useEffect, useState } from "react";
import { TableColumn } from "react-data-table-component";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import Table from "@components/common/Table";
import Typography from "@components/common/Typography";
import ActionDropdown from "@components/common/ActionDropdown";
import { IActions, IDefaultParams } from "@interface/common.interface";
import { GoVersions } from "react-icons/go";
import { RootState } from "@state/reducer";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { capitalizeFirstLetterWords, getTextStatusColor } from "@helpers/text";
import { ROUTES } from "@constants/routes";
import { IFormTransmittal } from "@interface/form-inventory.interface";

interface FormTableProps {
  statusFilter: string;
  searchText?: string;
  divisionFilter?: number;
  areaFilter?: number;
  dateFrom?: string;
  dateTo?: string;
  type?: any;
}

const FormTable: React.FC<FormTableProps> = ({
  statusFilter,
  searchText,
  divisionFilter,
  areaFilter,
  dateFrom,
  dateTo,
  type,
}) => {
  const navigate = useNavigate();
  // const transmittalForms = useSelector(
  //   (state: RootState) => state.formInventoryTransmittal.transmittalForms
  // );
  const { data: FORMS } = useSelector(
    (state: RootState) => state.formInventoryTransmittal.getTransmittalForms
  );
  const loading = useSelector(
    (state: RootState) =>
      state.formInventoryTransmittal.getTransmittalForms.loading
  );
  const divisions = useSelector(
    (state: RootState) => state.formInventoryUtilitiesDivisions.divisions
  );
  const formTypes = useSelector(
    (state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes
  );
  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const { getTransmittalForms } = useTransmittalFormActions();
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();

  const [filterDivision] = useState("");
  const [filterFormType] = useState("");
  const [filterArea] = useState("");

  const commonSetting = {
    sortable: true,
    reorder: true,
  };
  const handleRowsChange = (newPerPage: number, pagination: number) => {
    setPageSize(newPerPage);
    setPage(pagination);
  };

  const handlePaginate = (page: number) => {
    setPage(page);
  };

  const getActionEvents = (SeriesAssignment: any): IActions<any>[] => [
    {
      name: "View",
      event: () => {
        navigate(ROUTES.TREASURY.viewForm.parse(SeriesAssignment.id));
      },
      icon: GoVersions,
      color: "primary",
    },
  ];
  const sortedTransmittal = FORMS?.data
    ?.slice()
    .sort((a: IFormTransmittal, b: IFormTransmittal) => {
      return Number(b.id) - Number(a.id); // Explicitly convert to number
    });

  const columns: TableColumn<any>[] = [
    {
      name: "No.",
      cell: (row) => row.id,
      width: "80px",
      ...commonSetting,
    },
    {
      name: "Transmittal No.",
      cell: (row) => row.transmittalNumber,
    },
    {
      name: "Division",
      cell: (row) => {
        const division = divisions.find(
          (division) =>
            division.id === row.padAssignments?.[0]?.form?.divisionId
        );
        return division ? division.divisionName : "N/A";
      },
    },
    {
      name: "Type",
      cell: (row) => {
        const formType = formTypes.find(
          (type) => type.id === row.padAssignments?.[0]?.form?.formTypeId
        );
        return formType ? formType.formTypeName : "N/A";
      },
    },
    {
      name: "ATP No.",
      cell: (row) => row.padAssignments?.[0]?.form?.atpNumber || "N/A",
    },
    {
      name: "Released Area",
      cell: (row) => {
        const releasedArea = area.find(
          (areaItem) => areaItem.id === row.releasedAreaId
        );
        return releasedArea ? releasedArea.areaName : "N/A";
      },
    },
    {
      name: "Status",
      cell: (row) => (
        <Typography size="xs" className={`${getTextStatusColor(row.status)}`}>
          {capitalizeFirstLetterWords(row.status, "_")}
        </Typography>
      ),
    },
    {
      name: (
        <Typography className="flex flex-1 justify-center !text-black !text-xs">
          Actions
        </Typography>
      ),
      cell: (row, rowIndex) => (
        <ActionDropdown
          actions={getActionEvents(row)}
          data={row}
          rowIndex={rowIndex}
        />
      ),
    },
  ];

  const fetchForms = () => {
    const payload = {
      filter: searchText,
      divisionFilter: divisionFilter,
      type: type,
      areaFilter: areaFilter,
      dateFrom: dateFrom,
      dateTo: dateTo,
      page: page,
      pageSize: pageSize,
      statusFilter: statusFilter,
    } as IDefaultParams;
    getTransmittalForms(payload);
  };

  useEffect(() => {
    const timeout = setTimeout(() => {
      fetchForms();
    }, 500);
    return () => clearTimeout(timeout);
  }, [searchText, divisionFilter, type, page, pageSize]);

  useEffect(() => {
    getDivisions({ filter: filterDivision });
    getFormTypes({ filter: filterFormType });
    getAreas({ filter: filterArea });
    // fetchForms();
  }, []);

  return (
    <div className="mt-4">
      <Table
        className="!min-h-[100%] h-[500px] border-[1px] border-zinc-300 mt-4"
        columns={columns}
        paginationServer={true}
        paginationTotalRows={FORMS?.meta?.total}
        data={sortedTransmittal}
        onChangeRowsPerPage={handleRowsChange}
        onPaginate={handlePaginate}
        loading={loading}
        searchable={false}
        multiSelect={false}
      />
    </div>
  );
};

export default FormTable;
