import Button from "@components/common/Button";
import Typography from "@components/common/Typography";
import Loader from "@components/Loader";
import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { IPadAssignments } from "@interface/form-inventory.interface";
import { toast } from "react-toastify";
import { ROUTES } from "@constants/routes";
import { VerifyFormsSchema } from "@services/form-inventory-incoming-received-form/form-inventory-incoming-received-form.schema";
import { confirmSaveOrEdit } from "@helpers/prompt";
import { useFormik } from "formik";
import Modal from "@components/common/Modal";
import TextField from "@components/form/TextField";
import { FormStatus } from "@enums/form-status";
import { useIncomingReceivedFormActions } from "@state/reducer/form-inventory-incoming-received-form";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";


const ViewForm: React.FC = () => {
  const navigate = useNavigate();

  // ID in url
  const { id } = useParams<{ id: string }>();

  // States
  const [showRejectModal, setShowRejectModal] = useState(false);

  // Global States
  const { getTransmittalForm: transmittalForm } = useSelector((state: RootState) => state.formInventoryTransmittal);
  const approvalSuccess = useSelector((state: RootState) => state.formInventoryIncomingReceivedForms.putApprovalStatus?.success);
  const { data, loading } = transmittalForm;

  // Actions
  const { putApprovalStatus } = useIncomingReceivedFormActions();
  const { getTransmittalForm } = useTransmittalFormActions();

  const formik = useFormik({
    initialValues: {
      status: "",
      remarks: "",
      signatoryId: "", // Use approvalSigntoriesId for signatoryId
    },
    validationSchema: VerifyFormsSchema,
    onSubmit: async (values, { resetForm }) => {
      const isConfirmed = await confirmSaveOrEdit(values.status === FormStatus.REJECTED ? "Are you sure you want to reject this form?" : "Are you sure you want to verify this form?");
      if (isConfirmed) {
        await putApprovalStatus(values);
        resetForm();
      }
    },
  });

  useEffect(() => {
    if (approvalSuccess) {
      navigate(ROUTES.TREASURY.newForms.key);
    }
  }, [approvalSuccess]);

  useEffect(() => {
    if (id && id !== undefined) {
      getTransmittalForm({ id: parseInt(id) });
    }
  }, [id]);

  useEffect(() => {
    // Check appoval id and signatories is not undefined
    if (data?.approval?.id !== undefined && data.approval?.signatories?.length) {
      // Set signatoryId
      formik.setFieldValue("signatoryId", data.approval?.signatories[0].id);
    }
  }, [data]);

  return loading ? (
    <div className="flex justify-center items-center h-screen">
      <Loader />
    </div>
  ) : (
    <div>
      <Button classNames="btn bg-slate-600 btn-sm" onClick={() => navigate(ROUTES.TREASURY.newForms.key)}>
        Back
      </Button>
      <form onSubmit={formik.handleSubmit}>
        <div className="mx-6">
          <Typography className="mt-6 text-primary font-poppins-semibold">FORM DETAILS</Typography>
          <div className="mt-8 gap-4 flex justify-center">
            <div className="w-full">
              <div className="flex w-full flex-col">
                <div className="divider divider-start">Series Overview</div>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-3 grid-flow-col gap-4">
                  <div className="p-2">
                    <p className="text-sm">Area Released</p>
                    <div className="border-b-2 border-slate-300 w-32 text-sm">
                      <p>{data?.releasedArea ? data.releasedArea?.areaName : "N/A"}</p>
                    </div>
                  </div>
                  <div className="p-2">
                    <p className="text-sm">Released To</p>
                    <div className="border-b-2 border-slate-300 w-32 text-sm">
                      <p>{data?.releasedTo ? `${data.releasedTo.firstname} ${data.releasedTo.lastname}` : "N/A"}</p>
                    </div>
                  </div>
                  <div className="p-2">
                    <p className="text-sm">Date Released</p>
                    <div className="border-b-2 border-slate-300 w-32 text-sm">
                      <p>{data?.createdAt ? new Date(data.createdAt).toLocaleDateString("en-US") : "N/A"}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="border rounded-md border-slate-300 p-2">
                <div className="p-6">
                  <div className="grid grid-cols-5 gap-4">
                    <div className="p-2">
                      <p className="text-sm">Transmittal No.</p>
                      <div className="border-b-2 border-slate-300 w-32 text-sm">
                        <p>{data?.transmittalNumber}</p>
                      </div>
                    </div>
                    <div className="p-2">
                      <p className="text-sm">Division</p>
                      <div className="border-b-2 border-slate-300 w-32 text-sm">
                        <p>{data?.padAssignments ? data?.padAssignments[0]?.form?.division?.divisionName : "N/A"}</p>
                      </div>
                    </div>
                    <div className="p-2">
                      <p className="text-sm">Type</p>
                      <div className="border-b-2 border-slate-300 w-32 text-sm">
                        <p>{data?.padAssignments ? data?.padAssignments[0]?.form?.formType?.formTypeName : "N/A"}</p>
                      </div>
                    </div>
                    <div className="p-2">
                      <p className="text-sm">Area</p>
                      <div className="border-b-2 border-slate-300 w-32 text-sm">
                        <p>{data?.padAssignments ? data?.padAssignments[0]?.form?.area?.areaName : "N/A"}</p>
                      </div>
                    </div>
                    <div className="p-2">
                      <p className="text-sm">ATP No.</p>
                      <div className="border-b-2 border-slate-300 w-32 text-sm">
                        <p>{data?.padAssignments?.[0]?.form?.atpNumber}</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-4 mb-4 flex w-full">
                  <div className="overflow-auto max-h-64 w-full">
                    <table className="w-full">
                      <thead className="bg-gradient-to-r from-zinc-50 to-indigo-50 p-4 sticky top-0 z-10">
                        <tr>
                          <th className="p-4 text-sm border-zinc-100">Pad Number</th>
                          <th className="p-4 text-sm">Series From</th>
                          <th className="p-4 text-sm">Series To</th>
                        </tr>
                      </thead>
                      <tbody>
                        {data?.padAssignments?.map((assignment: IPadAssignments) => (
                          <tr key={assignment.id}>
                            <td className="p-4 text-sm border border-slate-100 text-center">{assignment.padNumber}</td>
                            <td className="p-4 text-sm border border-slate-100 text-center">{assignment.seriesFrom}</td>
                            <td className="p-4 text-sm border border-slate-100 text-center">{assignment.seriesTo}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  <div className="divider divider-horizontal"></div>
                  <div className="bg-slate-50 p-4 min-w-96 w-full">
                    <div className="p-2 flex justify-center bg-white rounded mb-2">Remarks</div>
                    <div className="bg-white p-4 text-sm rounded">
                      {data?.remarks?.split("\n").map((line: string, index: number) => (
                        <p key={index}>- {line}</p>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {data?.status === FormStatus.FOR_APPROVAL && (
            <div className="flex justify-center gap-2 mt-4">
              <Button classNames="bg-red-500 hover:bg-red-700 btn w-80 font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2" onClick={() => setShowRejectModal(true)}>
                Disapprove
              </Button>
              <Button
                type="submit"
                classNames="bg-sky-500 w-80 font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2"
                onClick={() => {
                  if (formik.values.signatoryId === "") {
                    toast.error("Approval Signatory ID is missing. Cannot verify.");
                    return;
                  }
                  formik.setFieldValue("status", FormStatus.APPROVED);
                }}
              >
                Approve
              </Button>
            </div>
          )}
        </div>
      </form>
      {showRejectModal && (
        <Modal title="Reject Pads" modalContainerClassName="max-w-md" titleClass="text-primary text-lg uppercase" isOpen={showRejectModal} onClose={() => setShowRejectModal(false)}>
          <form onSubmit={formik.handleSubmit}>
            <div>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700">Date</label>
                <TextField
                  type="date"
                  disabled
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  value={new Date().toISOString().split("T")[0]}
                  onChange={(e) => formik.setFieldValue("date", e.target.value)}
                />
              </div>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700">Remarks</label>
                <textarea
                  name="remarks"
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm px-2"
                  rows={3}
                  onChange={formik.handleChange}
                  // onChange={(e) => formik.setFieldValue("remarks", e.target.value)}
                />
              </div>
              <div className="flex justify-end">
                <Button
                  classNames="text-gray-700 border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2"
                  onClick={() => setShowRejectModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  classNames="bg-red-500 text-white font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2"
                  onClick={() => {
                    formik.setFieldValue("status", FormStatus.REJECTED);
                  }}
                >
                  Disapprove
                </Button>
              </div>
            </div>
          </form>
        </Modal>
      )}
    </div>
  );
};

export default ViewForm;
