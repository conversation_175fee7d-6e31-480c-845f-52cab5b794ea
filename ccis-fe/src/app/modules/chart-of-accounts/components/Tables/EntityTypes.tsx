import React from "react";
import Table from "@components/common/Table";
import Button from "@components/common/Button";
// import { FaSquarePlus } from "react-icons/fa6";
import TextField from "@components/form/TextField";
import { FaSearch } from "react-icons/fa";
import { <PERSON><PERSON><PERSON><PERSON> } from "react-icons/lu";
import { MdOutlineFileDownload } from "react-icons/md";
import { IoIosCheckmarkCircle } from "react-icons/io";
import { FaCircleXmark } from "react-icons/fa6";
import { LiaEdit } from "react-icons/lia";
// import { GoTrash } from "react-icons/go";
import { useSelectOptions } from "@hooks/useSelectOptions";
import Select1 from "@components/form/Combo-box";
import { TbTableOptions } from "react-icons/tb";
// import { TbSquareChevronRightFilled } from "react-icons/tb";

const EntityTypes: React.FC = () => {
  // Example data and columns for the Table component
  const columns = [
    {
      name: "Code",

      selector: (row: any) => row.code,
    },
    {
      name: "Name",
      selector: (row: any) => row.name,
    },

    {
      name: "Description",
      selector: (row: any) => row.description,
    },
    {
      name: "Status",
      selector: (row: any) => row.status,
      cell: (row: any) =>
        row.status === "Active" ? (
          <div className="flex items-center justify-center gap-1 bg-green-100 text-green-600 px-6  text-xs py-1 rounded-full">
            <IoIosCheckmarkCircle size={15} />
            {row.status}
          </div>
        ) : (
          <div className="flex items-center justify-center gap-1 bg-red-100 text-red-700 px-6  text-xs py-1 rounded-full">
            <FaCircleXmark />
            {row.status}
          </div>
        ),
    },
    {
      name: "Date Created",
      selector: (row: any) => {
        const date = new Date(row.dateCreated);
        return date.toLocaleDateString("en-US", {
          year: "numeric",
          month: "long",
          day: "numeric",
        });
      },
    },

    {
      name: "",
      width: "150px",
      cell: (_row: any, _rowIndex: number) => (
        <div className="flex flex-row justify-center items-center ">
          <button className="px-2 py-1 text-info  rounded">
            <LiaEdit size={25} />
          </button>
          <button className="px-2 py-1 text-primary  rounded">
            <TbTableOptions size={25} />
          </button>
          {/* <button className="px-2 py-1 text-red-500  rounded">
            <GoTrash size={20} />
          </button> */}
        </div>
      ),
    },
  ];

  const data = [
    { code: "1001", name: "Cash", description: "Physical and digital cash on hand", status: "Active", dateCreated: "2024-06-01" },
    { code: "1002", name: "Accounts Receivable", description: "Money owed by customers", status: "Active", dateCreated: "2024-06-02" },
    { code: "2001", name: "Revenue", description: "Income from sales and services", status: "Offline", dateCreated: "2024-06-03" },
    { code: "2002", name: "Expenses", description: "Operational costs and expenditures", status: "Active", dateCreated: "2024-06-04" },
    { code: "3001", name: "Equity", description: "Owner's equity in the business", status: "Active", dateCreated: "2024-06-05" },
    { code: "4001", name: "Liabilities", description: "Outstanding debts and obligations", status: "Offline", dateCreated: "2024-06-06" },
    { code: "5001", name: "Inventory", description: "Goods available for sale", status: "Active", dateCreated: "2024-06-07" },
    { code: "6001", name: "Fixed Assets", description: "Long-term tangible assets", status: "Active", dateCreated: "2024-06-08" },
    { code: "7001", name: "Prepaid Expenses", description: "Payments made in advance", status: "Active", dateCreated: "2024-06-09" },
    { code: "8001", name: "Accrued Expenses", description: "Expenses incurred but not yet paid", status: "Offline", dateCreated: "2024-06-10" },
  ];

  // Example status options for filtering
  const statusOptions = [
    { id: "all", label: "All" },
    { id: "Active", label: "Active" },
    { id: "Offline", label: "Inactive" },
    { id: "Pending", label: "Pending" },
    { id: "Archived", label: "Archived" },
    { id: "Suspended", label: "Suspended" },
  ];

  // Use useSelectOptions to generate select options
  const filteredOptions = useSelectOptions({
    data: statusOptions,
    valueKey: "id",
    textKey: "label",
    firstOptionText: "Show All: Recent or Status",
  });

  return (
    <div className="pt-20">
      <div className="flex w-full items-center justify-between">
        <div>
          {" "}
          <div className="text-xl font-poppins-semibold mb-2 text-primary">Entity Types</div>
          <div className="text-sm  text-zinc-500">Manage your accounting structure with heirarchial account organization.</div>
        </div>
        <div>
          {/* <Button variant="primary" classNames="text-sm flex gap-2 items-center justify-center">
            <FaSquarePlus size={15} /> Add Account
          </Button> */}
        </div>
      </div>

      <div className="w-full flex justify-between items-center mt-4">
        <div className="">
          {" "}
          <TextField leftIcon={<FaSearch className="text-primary" />} placeholder="Search . . ." className="input-md !my-4 !w-96 " variant="primary" onChange={() => {}} />
        </div>

        <div className="flex gap-2 ">
          <div className="w-80">
            <Select1 placeholder="Show All: Recent or Status" options={filteredOptions} onChange={(selected) => console.log(selected)} className="w-96" />
          </div>
          <Button outline classNames=" btn text-sm border-zinc-300 text-zinc-700 w-14 flex gap-2 items-center justify-center">
            <MdOutlineFileDownload size={22} />
          </Button>
          <Button outline classNames=" btn text-sm border-zinc-300 text-zinc-700 w-14 flex gap-2 items-center justify-center">
            <LuPrinter size={20} />
          </Button>
        </div>
      </div>

      <Table
        columns={columns}
        data={data}
        className="h-[600px]"
        //   loading={loading}
        // searchable={false}
        // multiSelect={false}
        //   paginationTotalRows={responseData?.meta?.total}
        // paginationServer={true}
        //   onPaginate={handlePaginate}
        //   onChangeRowsPerPage={handleRowsChange}
      />
    </div>
  );
};

export default EntityTypes;
