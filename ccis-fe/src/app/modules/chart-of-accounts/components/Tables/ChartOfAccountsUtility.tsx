import React, { useState } from "react";
import Table from "@components/common/Table";
import Button from "@components/common/Button";
import { FaSquarePlus } from "react-icons/fa6";
import TextField from "@components/form/TextField";
import { FaSearch } from "react-icons/fa";
import { Lu<PERSON><PERSON><PERSON> } from "react-icons/lu";
import { MdOutlineFileDownload } from "react-icons/md";
import { IoIosCheckmarkCircle } from "react-icons/io";
import { FaCircleXmark } from "react-icons/fa6";
import { LiaEdit } from "react-icons/lia";
// import { GoTrash } from "react-icons/go";
import { useSelectOptions } from "@hooks/useSelectOptions";
import Select1 from "@components/form/Combo-box";
import CreatenewUtility from "../Modals/CreateNewUtility";
import { IoChevronForward } from "react-icons/io5";
import { TbSquareChevronRightFilled } from "react-icons/tb";

const ChartOfAccountsUtility: React.FC = () => {
  const [openCreateUtilityModal, setOpenCreateUtilityModal] = useState(false);

  const handleOpenCreateUtility = () => {
    setOpenCreateUtilityModal((prev) => !prev);
  };

  // Example data and columns for the Table component
  const columns = [
    {
      name: "Code",

      selector: (row: any) => row.code,
    },
    {
      name: "Name",
      selector: (row: any) => row.name,
    },
    {
      name: "Status",
      selector: (row: any) => row.status,
      cell: (row: any) =>
        row.status === "Active" ? (
          <div className="flex items-center justify-center gap-1 bg-green-100 text-green-600 px-6  text-xs py-1 rounded-full">
            <IoIosCheckmarkCircle size={15} />
            {row.status}
          </div>
        ) : (
          <div className="flex items-center justify-center gap-1 bg-red-100 text-red-700 px-6  text-xs py-1 rounded-full">
            <FaCircleXmark />
            {row.status}
          </div>
        ),
    },
    {
      name: "Date Created",
      selector: (row: any) => {
        const date = new Date(row.dateCreated);
        return date.toLocaleDateString("en-US", {
          year: "numeric",
          month: "long",
          day: "numeric",
        });
      },
    },
    {
      name: "",
      width: "100px",
      cell: (_row: any, _rowIndex: number) => (
        <div className="flex flex-row justify-center items-center ">
          <button className="px-2 py-1 text-info  rounded">
            <LiaEdit size={25} />
          </button>
          <button className="px-2 py-1 text-primary rounded">
            <TbSquareChevronRightFilled size={25} />
          </button>
          {/* <button className="px-2 py-1 text-red-500  rounded">
                <GoTrash size={20} />
              </button> */}
        </div>
      ),
      center: true,
      ignoreRowClick: true,
      allowOverflow: true,
    },
  ];

  const data = [
    { code: "1001", name: "Cash", status: "Active", dateCreated: "2024-06-01" },
    { code: "1002", name: "Accounts Receivable", status: "Active", dateCreated: "2024-06-02" },
    { code: "2001", name: "Revenue", status: "Offline", dateCreated: "2024-06-03" },
    { code: "2002", name: "Expenses", status: "Active", dateCreated: "2024-06-04" },
    { code: "3001", name: "Equity", status: "Active", dateCreated: "2024-06-05" },
    { code: "4001", name: "Liabilities", status: "Offline", dateCreated: "2024-06-06" },
    { code: "5001", name: "Inventory", status: "Active", dateCreated: "2024-06-07" },
    { code: "6001", name: "Fixed Assets", status: "Active", dateCreated: "2024-06-08" },
    { code: "7001", name: "Prepaid Expenses", status: "Active", dateCreated: "2024-06-09" },
    { code: "8001", name: "Accrued Expenses", status: "Offline", dateCreated: "2024-06-10" },
  ];

  // Example status options for filtering
  const statusOptions = [
    { id: "all", label: "All" },
    { id: "Active", label: "Active" },
    { id: "Offline", label: "Inactive" },
    { id: "Pending", label: "Pending" },
    { id: "Archived", label: "Archived" },
    { id: "Suspended", label: "Suspended" },
  ];

  // Use useSelectOptions to generate select options
  const filteredOptions = useSelectOptions({
    data: statusOptions,
    valueKey: "id",
    textKey: "label",
    firstOptionText: "Show All: Recent or Status",
  });

  return (
    <div className="pt-20">
      {openCreateUtilityModal && <CreatenewUtility isOpen={openCreateUtilityModal} onClose={handleOpenCreateUtility} />}
      <div className="flex w-full items-center justify-between">
        <div>
          {" "}
          <div className="text-xl font-poppins-semibold mb-2 text-primary">Chart of Accounts Utility</div>
          <div className="text-sm  text-zinc-500">Manage your accounting structure with heirarchial account organization.</div>
        </div>
        <div>
          <Button variant="primary" classNames="text-sm flex gap-2 items-center justify-center" onClick={handleOpenCreateUtility}>
            <FaSquarePlus size={15} /> Add Utility
          </Button>
        </div>
      </div>

      <div className="w-full flex justify-between items-center mt-4">
        <div className="">
          {" "}
          <TextField leftIcon={<FaSearch className="text-primary" />} placeholder="Search . . ." className="input-md !my-4 !w-96 " variant="primary" onChange={() => {}} />
        </div>

        <div className="flex gap-2 ">
          <div className="w-80">
            <Select1 placeholder="Show All: Recent or Status" options={filteredOptions} onChange={(selected) => console.log(selected)} className="w-96" />
          </div>
          <Button outline classNames=" btn text-sm border-zinc-300 text-zinc-700 w-14 flex gap-2 items-center justify-center">
            <MdOutlineFileDownload size={22} />
          </Button>
          <Button outline classNames=" btn text-sm border-zinc-300 text-zinc-700 w-14 flex gap-2 items-center justify-center">
            <LuPrinter size={20} />
          </Button>
        </div>
      </div>

      <div className="w-full flex items-center gap-2 text-sm  text-zinc-700 mt-8">
        <div className="flex gap-2 items-center justify-center">
          {" "}
          <span className="text-info bg-sky-100 p-1 font-poppins-semibold bg-info-50 rounded-md px-3">Category</span>
          <IoChevronForward size={20} />
        </div>
        <div className="flex gap-2 items-center justify-center">
          {" "}
          <span className="text-amber-500 p-1 font-poppins-semibold bg-amber-100 rounded-md px-3">Group (7)</span> <IoChevronForward size={20} />
        </div>
        <div className="flex gap-2 items-center justify-center">
          {" "}
          <span className="text-red-500 p-1 font-poppins-semibold bg-red-100 rounded-md px-3">Sub-Group (3)</span> <IoChevronForward size={20} />
        </div>
        <div className="flex gap-2 items-center justify-center">
          <span className="text-zinc-500 p-1 font-poppins-semibold bg-zinc-100 rounded-md px-3">Detail (5)</span>
        </div>
      </div>

      <Table
        columns={columns}
        data={data}
        className="h-[540px]"
        //   loading={loading}
        // searchable={false}
        // multiSelect={false}
        //   paginationTotalRows={responseData?.meta?.total}
        // paginationServer={true}
        //   onPaginate={handlePaginate}
        //   onChangeRowsPerPage={handleRowsChange}
      />
    </div>
  );
};

export default ChartOfAccountsUtility;
