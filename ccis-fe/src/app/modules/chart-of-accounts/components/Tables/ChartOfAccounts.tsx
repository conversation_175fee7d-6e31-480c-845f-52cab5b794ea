import React, { useEffect, useState } from "react";
import Table from "@components/common/Table";
import Button from "@components/common/Button";
import { FaSquarePlus } from "react-icons/fa6";
import TextField from "@components/form/TextField";
import { FaSearch } from "react-icons/fa";
import { <PERSON><PERSON><PERSON><PERSON> } from "react-icons/lu";
import { MdOutlineFileDownload } from "react-icons/md";
import { IoIosCheckmarkCircle } from "react-icons/io";
import { FaCircleXmark } from "react-icons/fa6";
import { LiaEdit } from "react-icons/lia";
// import { GoTrash } from "react-icons/go";
import { useSelectOptions } from "@hooks/useSelectOptions";
import Select1 from "@components/form/Combo-box";
// import { CiMenuKebab } from "react-icons/ci";
// import { TbSquareChevronRightFilled } from "react-icons/tb";
import { LuFileScan } from "react-icons/lu";
import { HiOutlineUser } from "react-icons/hi";
import SelectOption from "../Modals/SelectOption";
import CreateNewChartOfAccount from "../Modals/CreateNewChartOfAccount";

const ChartOfAccounts: React.FC = () => {
  const [selectedOption, setSelectedOption] = useState("");
  const [selectOptionModal, setSelectOptionModal] = useState(false);
  const handleSelectOptionModal = () => {
    setSelectOptionModal((prev) => !prev);
  };
  const [createNewChartOfAccountModal, setCreateNewChartOfAccountModal] = useState(false);
  const handleCreateChartOfAccount = () => {
    setCreateNewChartOfAccountModal((prev) => !prev);
  };

  const handleSelectedOption = (option: string) => {
    setSelectedOption(option);
  };

  useEffect(() => {
    if (selectedOption) {
      setSelectOptionModal(false);
      setCreateNewChartOfAccountModal(true);
    }
  }, [selectedOption]);

  // Example data and columns for the Table component
  const columns = [
    {
      name: "Chart of Account Code",
      width: "200px",
      selector: (row: any) => row.code,
    },
    {
      name: "Account Name",
      selector: (row: any) => row.name,
      cell: (row: any) => (
        <div>
          <div>{row.name}</div>
          <div className="text-xs text-zinc-400">{row.description}</div>
        </div>
      ),
    },
    {
      name: "Entity",
      selector: (row: any) => row.entity,
      cell: (row: any) => (
        <div className="flex items-center gap-2 py-2">
          <div>
            <HiOutlineUser size={20} className="text-sky-500" />
          </div>
          <div>
            <div>{row.entity}</div>
            <div className="text-xs text-sky-500 bg-sky-100 p-1 rounded-full px-3">Employee</div>
          </div>
        </div>
      ),
      center: true,
    },
    {
      name: "Old Code",
      width: "150px",
      selector: (row: any) => row.oldCode,
      center: true,
    },
    {
      name: "Status",
      selector: (row: any) => row.status,
      cell: (row: any) =>
        row.status === "Active" ? (
          <div className="flex items-center justify-center gap-1 bg-green-100 text-green-600 px-6  text-xs py-1 rounded-full">
            <IoIosCheckmarkCircle size={15} />
            {row.status}
          </div>
        ) : (
          <div className="flex items-center justify-center gap-1 bg-red-100 text-red-700 px-6  text-xs py-1 rounded-full">
            <FaCircleXmark />
            {row.status}
          </div>
        ),
      center: true,
    },
    {
      name: "Date Created",
      selector: (row: any) => {
        const date = new Date(row.dateCreated);
        return date.toLocaleDateString("en-US", {
          year: "numeric",
          month: "long",
          day: "numeric",
        });
      },
      center: true,
    },
    {
      name: "",
      width: "100px",
      cell: (_row: any, _rowIndex: number) => (
        <div className="flex flex-row justify-center items-center ">
          <button className="px-2 py-1 text-info  rounded">
            <LiaEdit size={25} />
          </button>
          <button className="px-2 py-1 text-primary rounded">
            <LuFileScan size={20} />
          </button>
        </div>
      ),
      center: true,
      ignoreRowClick: true,
      allowOverflow: true,
    },
  ];

  const data = [
    { code: "1001", oldCode: "001", name: "Cash", entity: "Main Office", status: "Active", dateCreated: "2024-06-01", description: "Cash on hand and in bank" },
    { code: "1002", oldCode: "002", name: "Accounts Receivable", entity: "Branch A", status: "Active", dateCreated: "2024-06-02", description: "Amounts owed by customers" },
    { code: "2001", oldCode: "003", name: "Revenue", entity: "Main Office", status: "Offline", dateCreated: "2024-06-03", description: "Income from sales and services" },
    { code: "2002", oldCode: "004", name: "Expenses", entity: "Branch B", status: "Active", dateCreated: "2024-06-04", description: "Operating expenses" },
    { code: "3001", oldCode: "005", name: "Equity", entity: "Main Office", status: "Active", dateCreated: "2024-06-05", description: "Owner's equity" },
    { code: "4001", oldCode: "006", name: "Liabilities", entity: "Branch A", status: "Offline", dateCreated: "2024-06-06", description: "Company debts and obligations" },
    { code: "5001", oldCode: "007", name: "Inventory", entity: "Warehouse", status: "Active", dateCreated: "2024-06-07", description: "Goods available for sale" },
    { code: "6001", oldCode: "008", name: "Fixed Assets", entity: "Main Office", status: "Active", dateCreated: "2024-06-08", description: "Long-term assets" },
    { code: "7001", oldCode: "009", name: "Prepaid Expenses", entity: "Branch B", status: "Active", dateCreated: "2024-06-09", description: "Expenses paid in advance" },
    { code: "8001", oldCode: "010", name: "Accrued Expenses", entity: "Main Office", status: "Offline", dateCreated: "2024-06-10", description: "Expenses incurred but not yet paid" },
  ];

  // Example status options for filtering
  const statusOptions = [
    { id: "all", label: "All" },
    { id: "Active", label: "Active" },
    { id: "Offline", label: "Inactive" },
    { id: "Pending", label: "Pending" },
    { id: "Archived", label: "Archived" },
    { id: "Suspended", label: "Suspended" },
  ];

  // Use useSelectOptions to generate select options
  const filteredOptions = useSelectOptions({
    data: statusOptions,
    valueKey: "id",
    textKey: "label",
    firstOptionText: "All Status",
  });

  const categoryOptions = useSelectOptions({
    data: [
      { id: "asset", label: "Asset" },
      { id: "liability", label: "Liability" },
      { id: "equity", label: "Equity" },
      { id: "income", label: "Income" },
      { id: "expense", label: "Expense" },
    ],
    valueKey: "id",
    textKey: "label",
    firstOptionText: "All Categories",
  });

  const levelOptions = useSelectOptions({
    data: [
      { id: "group", label: "Group" },
      { id: "sub-group", label: "Sub-Group" },
      { id: "detail", label: "Detail" },
    ],
    valueKey: "id",
    textKey: "label",
    firstOptionText: "All Level",
  });

  return (
    <div className="pt-20">
      {selectOptionModal && <SelectOption isOpen={selectOptionModal} onClose={handleSelectOptionModal} onSelect={handleSelectedOption} />}
      {createNewChartOfAccountModal && <CreateNewChartOfAccount isOpen={createNewChartOfAccountModal} onClose={handleCreateChartOfAccount} />}
      <div className="flex w-full items-center justify-between">
        <div>
          {" "}
          <div className="text-xl font-poppins-semibold mb-2 text-primary">Chart of Accounts</div>
          <div className="text-sm  text-zinc-500">Manage your accounting structure with heirarchial account organization.</div>
        </div>
        <div>
          <Button variant="primary" classNames="text-sm flex gap-2 items-center justify-center" onClick={handleSelectOptionModal}>
            <FaSquarePlus size={15} /> Add Chart of Account
          </Button>
        </div>
      </div>

      <div className="w-full flex justify-between items-center mt-4">
        <div className="">
          {" "}
          <TextField leftIcon={<FaSearch className="text-primary" />} placeholder="Search . . ." className="input-md !my-4 !w-96 " variant="primary" onChange={() => {}} />
        </div>

        <div className="flex gap-2 ">
          <div className="w-48">
            <Select1 placeholder="Show All: Recent or Status" options={filteredOptions} onChange={(selected) => console.log(selected)} className="w-60" />
          </div>
          <div className="w-48">
            <Select1 placeholder="Show All: Categories" options={categoryOptions} onChange={(selected) => console.log(selected)} className="w-60" />
          </div>
          <div className="w-48">
            <Select1 placeholder="Show All: Level" options={levelOptions} onChange={(selected) => console.log(selected)} className="w-60" />
          </div>
          <Button outline classNames=" btn text-sm border-zinc-300 text-zinc-700 w-14 flex gap-2 items-center justify-center">
            <MdOutlineFileDownload size={22} />
          </Button>
          <Button outline classNames=" btn text-sm border-zinc-300 text-zinc-700 w-14 flex gap-2 items-center justify-center">
            <LuPrinter size={20} />
          </Button>
        </div>
      </div>

      <Table
        columns={columns}
        data={data}
        className="h-[600px]"
        //   loading={loading}
        // searchable={false}
        // multiSelect={false}
        //   paginationTotalRows={responseData?.meta?.total}
        // paginationServer={true}
        //   onPaginate={handlePaginate}
        //   onChangeRowsPerPage={handleRowsChange}
      />
    </div>
  );
};

export default ChartOfAccounts;
