import React, { useState } from "react";
import Modal from "@components/common/Modal";
import TextField from "@components/form/TextField";
import Button from "@components/common/Button";
import { useFormik } from "formik";
import Select1 from "@components/form/Combo-box";
import { useSelectOptions } from "@hooks/useSelectOptions";
import Switch from "@components/switch";
import { FaSearch } from "react-icons/fa";
import Table from "@components/common/Table";

interface CreateNewUtilityProps {
  isOpen: boolean;
  onClose: () => void;
  onCreate?: (values: any) => void;
}

enum AccountLevel {
  Category = "Category",
  Group = "Group",
  SubGroup = "Sub-Group",
  Detail = "Detail",
}

const CreateNewChartOfAccount: React.FC<CreateNewUtilityProps> = ({ isOpen, onClose, onCreate }) => {
  const [confirmModal, setConfirmModal] = useState(false);
  const handleConfirmModal = () => {
    setConfirmModal((prev) => !prev);
  };
  const formik = useFormik({
    initialValues: {
      entityType: "",
      entity: "",
      entityCode: "",
      status: "active",
    },
    onSubmit: (values) => {
      onCreate?.(values);
      // resetForm();
      // onClose();
      setConfirmModal(true);
    },
    enableReinitialize: true,
  });

  const handleClose = () => {
    formik.resetForm();
    onClose();
  };

  const categoryOptions = useSelectOptions({
    data: [
      { id: "employee", label: "Employee" },
      { id: "department", label: "Department" },
      { id: "project", label: "Project" },
      { id: "supplier", label: "Supplier" },
      { id: "cooperative", label: "Cooperative" },
      { id: "bank", label: "Bank" },
      { id: "non-coops", label: "Non-Coops" },
    ],
    valueKey: "id",
    textKey: "label",
    firstOptionText: "Select Entity Type",
  });

  const columns = [
    {
      name: "Code",
      selector: (row: any) => row.code,
    },
    {
      name: "Name",
      selector: (row: any) => row.name,
    },
    {
      name: "Level",
      cell: (row: any) => {
        let className = "";
        switch (row.level) {
          case AccountLevel.Category:
            className = "bg-sky-100 text-sky-500";
            break;
          case AccountLevel.Group:
            className = "bg-amber-100 text-amber-500";
            break;
          case AccountLevel.SubGroup:
            className = "bg-red-100 text-red-500";
            break;
          case AccountLevel.Detail:
            className = "bg-zinc-100 text-zinc-500";
            break;
          default:
            className = "";
        }
        return <span className={`px-2 py-1 rounded text-xs font-semibold ${className}`}>{row.level}</span>;
      },
    },
    {
      name: "Description",
      selector: (row: any) => row.description,
    },
    {
      name: "Old Code",
      selector: (row: any) => row.oldCode,
    },
  ];

  const data = [
    {
      code: "1000",
      name: "Assets",
      description: "Resources owned by the company",
      oldCode: "A",
      level: AccountLevel.Category,
    },
    {
      code: "1001",
      name: "Current Assets",
      description: "Assets expected to be converted to cash within a year",
      oldCode: "A-1",
      level: AccountLevel.Group,
    },
    {
      code: "1002",
      name: "Cash and Cash Equivalents",
      description: "Physical cash and equivalents",
      oldCode: "A-1-1",
      level: AccountLevel.SubGroup,
    },
    {
      code: "1003",
      name: "Cash on Hand",
      description: "Physical cash available",
      oldCode: "A-1-1-1",
      level: AccountLevel.Detail,
    },
    {
      code: "1004",
      name: "Cash in Bank",
      description: "Cash deposited in banks",
      oldCode: "A-1-1-2",
      level: AccountLevel.Detail,
    },
    {
      code: "1100",
      name: "Liabilities",
      description: "Obligations owed to outsiders",
      oldCode: "L",
      level: AccountLevel.Category,
    },
    {
      code: "1101",
      name: "Current Liabilities",
      description: "Liabilities due within a year",
      oldCode: "L-1",
      level: AccountLevel.Group,
    },
    {
      code: "1102",
      name: "Accounts Payable",
      description: "Money owed to suppliers",
      oldCode: "L-1-1",
      level: AccountLevel.Detail,
    },
    {
      code: "1200",
      name: "Equity",
      description: "Owner's interest in the company",
      oldCode: "E",
      level: AccountLevel.Category,
    },
    {
      code: "1201",
      name: "Retained Earnings",
      description: "Accumulated profits",
      oldCode: "E-1",
      level: AccountLevel.Detail,
    },
    {
      code: "1300",
      name: "Revenue",
      description: "Income from operations",
      oldCode: "R",
      level: AccountLevel.Category,
    },
    {
      code: "1301",
      name: "Sales Revenue",
      description: "Income from sales",
      oldCode: "R-1",
      level: AccountLevel.Group,
    },
    {
      code: "1302",
      name: "Product Sales",
      description: "Sales of products",
      oldCode: "R-1-1",
      level: AccountLevel.Detail,
    },
    {
      code: "1303",
      name: "Service Revenue",
      description: "Income from services",
      oldCode: "R-2",
      level: AccountLevel.Group,
    },
    {
      code: "1304",
      name: "Consulting Services",
      description: "Income from consulting",
      oldCode: "R-2-1",
      level: AccountLevel.Detail,
    },
    {
      code: "1400",
      name: "Expenses",
      description: "Costs incurred in operations",
      oldCode: "X",
      level: AccountLevel.Category,
    },
    {
      code: "1401",
      name: "Operating Expenses",
      description: "Expenses related to operations",
      oldCode: "X-1",
      level: AccountLevel.Group,
    },
    {
      code: "1402",
      name: "Salaries Expense",
      description: "Employee salaries",
      oldCode: "X-1-1",
      level: AccountLevel.Detail,
    },
    {
      code: "1403",
      name: "Utilities Expense",
      description: "Electricity, water, etc.",
      oldCode: "X-1-2",
      level: AccountLevel.Detail,
    },
    {
      code: "1404",
      name: "Administrative Expenses",
      description: "General admin expenses",
      oldCode: "X-2",
      level: AccountLevel.Group,
    },
    {
      code: "1405",
      name: "Office Supplies",
      description: "Supplies for office use",
      oldCode: "X-2-1",
      level: AccountLevel.Detail,
    },
  ];

  const confirmData = [
    { code: "EMP-1001", entity: "John Doe", entityType: "Employee" },
    { code: "EMP-1002", entity: "Jane Smith", entityType: "Employee" },
    { code: "DEPT-1100", entity: "Finance", entityType: "Department" },
    { code: "DEPT-1200", entity: "HR", entityType: "Department" },
    { code: "PROJ-2001", entity: "Project Alpha", entityType: "Project" },
    { code: "PROJ-2002", entity: "Project Beta", entityType: "Project" },
    { code: "SUP-3001", entity: "ABC Supplies", entityType: "Supplier" },
    { code: "COOP-4001", entity: "Coop One", entityType: "Cooperative" },
    { code: "BANK-5001", entity: "Metro Bank", entityType: "Bank" },
    { code: "NCOOP-6001", entity: "Non-Coop Org", entityType: "Non-Coops" },
    { code: "SUP-3002", entity: "XYZ Traders", entityType: "Supplier" },
    { code: "BANK-5002", entity: "BPI", entityType: "Bank" },
  ];

  return (
    <>
      {/* PARENT MODAL */}
      <Modal isOpen={isOpen} onClose={handleClose} title={`Add New Chart of Account`} titleClass="font-poppins-semibold text-zinc-700 text-xl pl-5 " modalContainerClassName="max-w-5xl">
        {/* CONFIRM MODAL */}
        <Modal
          isOpen={confirmModal}
          onClose={handleConfirmModal}
          title={`Confirm Add New Chart of Account?`}
          titleClass="font-poppins-semibold text-zinc-700 text-xl pl-5 "
          modalContainerClassName="max-w-3xl"
        >
          <div className="text-sm text-zinc-500 -translate-y-5">Are your sure you want to add this entity accounts? This action cannot be undone.</div>
          <table className="min-w-full border border-zinc-200 rounded-3xl bg-zinc-100">
            <thead className="border-t-3xl">
              <tr className="bg-zinc-100 font-poppins-semibold">
                <th className="px-4 py-2 text-left text-xs font-semibold text-zinc-600">COA Code</th>
                <th className="px-4 py-2 text-left text-xs font-semibold text-zinc-600">Entity Type</th>
                <th className="px-4 py-2 text-left text-xs font-semibold text-zinc-600">Entity Name</th>
              </tr>
            </thead>
            <tbody className="border-b-3xl">
              {confirmData.map((item, idx) => (
                <tr key={item.code} className={idx % 2 === 0 ? "bg-white" : "bg-zinc-50"}>
                  <td className="px-4 py-2 text-sm text-zinc-700">{item.code}</td>
                  <td className="px-4 py-2 text-sm text-zinc-700">{item.entityType}</td>
                  <td className="px-4 py-2 text-sm text-zinc-700">{item.entity}</td>
                </tr>
              ))}
            </tbody>
          </table>
          <div className="flex justify-end gap-4 mt-4">
            <Button outline classNames="px-4 py-2 !border-none bg-gray-200 font-poppins-semibold text-gray-800 text-sm hover:bg-gray-300" onClick={handleConfirmModal} type="button">
              Cancel
            </Button>
            <Button variant="primary" classNames="px-4 py-2 rounded bg-blue-700 font-poppins-semibold text-white text-sm hover:bg-blue-800" onClick={handleConfirmModal} type="button">
              Create Account
            </Button>
          </div>
        </Modal>
        <div className="text-sm text-zinc-500 -translate-y-5">Create a new entity account by linking an entity to an account</div>
        <form onSubmit={formik.handleSubmit}>
          <div className="bg-white  rounded-lg  min-w-[350px] flex flex-col">
            <div className="flex flex-col">
              <label htmlFor="category" className="block  font-poppins-semibold text-sm mb-2">
                Entity Type
              </label>
              <Select1
                name="entityType"
                placeholder="All Types"
                value={formik.values.entityType}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.entityType && !!formik.errors.entityType}
                className="mb-1 border-zinc-300"
                options={categoryOptions}
              />
              <div className="text-sm text-zinc-500">Filter entities by type to make selection easier</div>
            </div>
            <div className=" mt-8  border-b border-zinc-300 h-max">
              <div className="flex gap-4  w-full">
                <div className="w-2/3 h-full ">
                  <label htmlFor="category" className="block  font-poppins-semibold text-sm mb-2">
                    Entity
                  </label>
                  <TextField
                    leftIcon={<FaSearch className="text-zinc-300" />}
                    name="entity"
                    placeholder="Search Entities"
                    value={formik.values.entity}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.entity && !!formik.errors.entity}
                    className="mb-1 border-zinc-300"
                  />
                  <div className="text-sm text-zinc-500">Select the entity this account belongs to</div>
                </div>
                <div className="w-1/3 h-full ">
                  <label htmlFor="category" className="block  font-poppins-semibold text-sm mb-2">
                    Entity Code
                  </label>
                  <TextField
                    name="entityCode"
                    placeholder="e.g., EMP-1001, DEPT 1100"
                    value={formik.values.entity}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.entity && !!formik.errors.entity}
                    className="mb-1 border-zinc-300"
                  />
                  <div className="text-sm text-zinc-500">Auto-populated from selected entity</div>
                </div>
              </div>

              {/* dapat conditional ni nga e display ang table diri */}
              <div className=" w-full h-max mt-8">
                <div className="block mb-1 font-medium font-poppins-semibold mr-4">COA Template</div>
                <div className="text-xs text-zinc-400">All Connected Utility to the account base on Entity.</div>
                <Table columns={columns} data={data} className="h-[300px]" />
              </div>
            </div>

            <div className="mb-4 py-2 flex gap-2 items-center border-b border-zinc-300 pb-4">
              <div>
                <Switch checked={formik.values.status === "active"} onChange={() => formik.setFieldValue("status", formik.values.status === "active" ? "inactive" : "active")} />
              </div>
              <div className="pt-3">
                <div className="block mb-1 font-medium font-poppins-semibold text-sm mr-4">Set status as Active</div>
                <div className="text-xs text-zinc-400">Turn the toggle on or off for active and inactive status.</div>
              </div>
            </div>

            <div className="flex justify-end gap-2 ">
              <Button outline classNames="px-4 py-2 !border-none bg-gray-200 font-poppins-semibold text-gray-800 text-sm hover:bg-gray-300" onClick={handleClose} type="button">
                Cancel
              </Button>
              <Button variant="primary" classNames="px-4 py-2 rounded bg-blue-700 font-poppins-semibold text-white text-sm hover:bg-blue-800" type="submit">
                Create Account
              </Button>
            </div>
          </div>
        </form>
      </Modal>
    </>
  );
};

export default CreateNewChartOfAccount;
