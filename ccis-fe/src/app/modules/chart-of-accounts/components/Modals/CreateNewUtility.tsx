import React from "react";
import Modal from "@components/common/Modal";
import TextField from "@components/form/TextField";
import Button from "@components/common/Button";
import { useFormik } from "formik";
import TextArea from "@components/form/TextArea";
import Select1 from "@components/form/Combo-box";
import { useSelectOptions } from "@hooks/useSelectOptions";
import Switch from "@components/switch";
import { FaSearch } from "react-icons/fa";

interface CreateNewUtilityProps {
  isOpen: boolean;
  onClose: () => void;
  onCreate?: (values: any) => void;
}

const CreateNewUtility: React.FC<CreateNewUtilityProps> = ({ isOpen, onClose, onCreate }) => {
  const formik = useFormik({
    initialValues: {
      category: "",
      level: "",
      parentAccount: "",
      accountName: "",
      accountCode: "",
      description: "",
      status: "",
    },
    onSubmit: (values, { resetForm }) => {
      onCreate?.(values);
      resetForm();
      onClose();
    },
    enableReinitialize: true,
  });

  const handleClose = () => {
    formik.resetForm();
    onClose();
  };

  const categoryOptions = useSelectOptions({
    data: [
      { id: "employee", label: "Employee" },
      { id: "department", label: "Department" },
      { id: "project", label: "Project" },
      { id: "supplier", label: "Supplier" },
      { id: "cooperative", label: "Cooperative" },
      { id: "bank", label: "Bank" },
      { id: "non-coops", label: "Non-Coops" },
    ],
    valueKey: "id",
    textKey: "label",
    firstOptionText: "All Category",
  });

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Add New Utility" titleClass="font-poppins-semibold text-zinc-700 text-xl" modalContainerClassName="max-w-2xl">
      <form onSubmit={formik.handleSubmit}>
        <div className="bg-white  rounded-lg  min-w-[350px] flex flex-col">
          <div className="flex flex-col">
            <label htmlFor="category" className="block mb-1 font-medium text-zinc-500 text-sm">
              Entity Type<span className="text-red-500 ml-[.1rem]">*</span>
            </label>
            <Select1
              name="category"
              placeholder="Select Entity Type"
              value={formik.values.category}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.category && !!formik.errors.category}
              className="mb-4 border-zinc-300"
              options={categoryOptions}
            />

            <div className="mb-4">
              <label htmlFor="parentAccount" className="block mb-1 font-medium text-zinc-500 text-sm">
                Parent Account<span className="text-red-500 ml-[.1rem]">*</span>
              </label>
              <TextField
                leftIcon={<FaSearch className="text-zinc-300" />}
                name="parentAccount"
                placeholder="Search Parent"
                value={formik.values.parentAccount}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.parentAccount && !!formik.errors.parentAccount}
                className="border-zinc-300"
              />
            </div>
            <div className="mb-4">
              <label htmlFor="accountName" className="block mb-1 font-medium text-zinc-500 text-sm">
                Account Name<span className="text-red-500 ml-[.1rem]">*</span>
              </label>
              <TextField
                name="accountName"
                placeholder="e.g. Cash and Cash Equivalents"
                value={formik.values.accountName}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.accountName && !!formik.errors.accountName}
                className="border-zinc-300"
              />
            </div>
            <div className="mb-4">
              <label htmlFor="accountCode" className="block mb-1 font-medium text-zinc-500 text-sm">
                Account Code<span className="text-red-500 ml-[.1rem]">*</span>
              </label>
              <TextField
                name="accountCode"
                placeholder="e.g. 1000, 1100 etc."
                value={formik.values.accountCode}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.accountCode && !!formik.errors.accountCode}
                className="border-zinc-300"
              />
            </div>
            <div className="mb-4">
              <label htmlFor="description" className="block mb-1 font-medium text-zinc-500 text-sm">
                Description <span className="text-zinc-300 text-xs">(Optional)</span>
              </label>
              <TextArea
                className="w-full border-zinc-300"
                name="description"
                placeholder="Optional description of account"
                value={formik.values.description}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.description && !!formik.errors.description}
              />
            </div>
            <div className="mb-4 flex gap-2 items-center border-b border-zinc-300 pb-4">
              <div>
                <Switch checked={formik.values.status === "active"} onChange={() => formik.setFieldValue("status", formik.values.status === "active" ? "inactive" : "active")} />
              </div>
              <div className="pt-3">
                <div className="block mb-1 font-medium font-poppins-semibold text-sm mr-4">Set status as Active</div>
                <div className="text-xs text-zinc-400">Turn the toggle on or off for active and inactive status.</div>
              </div>
            </div>
          </div>
          <div className="flex justify-end gap-2 ">
            <Button outline classNames="px-4 py-2 !border-none bg-gray-200 font-poppins-semibold text-gray-800 text-sm hover:bg-gray-300" onClick={handleClose} type="button">
              Cancel
            </Button>
            <Button variant="primary" classNames="px-4 py-2 rounded bg-blue-700 font-poppins-semibold text-white text-sm hover:bg-blue-800" type="submit">
              Create Utility
            </Button>
          </div>
        </div>
      </form>
    </Modal>
  );
};

export default CreateNewUtility;
