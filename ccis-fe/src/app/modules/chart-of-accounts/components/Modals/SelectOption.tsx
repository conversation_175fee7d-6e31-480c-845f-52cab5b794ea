import React from "react";
import Modal from "@components/common/Modal";
import Button from "@components/common/Button";
import { LuFileBox } from "react-icons/lu";
import { LuFileCog } from "react-icons/lu";
import { toast } from "react-toastify";

interface SelectOptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (option: string) => void;
  selectedOption?: string;
}

enum Selection {
  Template = "template",
  Customized = "customized",
}

const SelectOptionModal: React.FC<SelectOptionModalProps> = ({ isOpen, onClose, onSelect, selectedOption = "" }) => {
  const [option, setOption] = React.useState<Selection | "">(selectedOption as Selection | "");

  const handleSelect = () => {
    if (!option) {
      toast.error("Please select an option");
      return;
    }
    onSelect(option as Selection | "");
    onClose();
  };

  return (
    <Modal isOpen={isOpen} title=" " onClose={onClose} modalContainerClassName="max-w-3xl">
      <div className="flex items-center justify-center flex-col">
        <div className="text-3xl font-poppins-semibold mb-2">Select your Option</div>
        <div className="text-center text-sm text-zinc-500 px-32">To streamline the creation of your product proposal, please select one of the following options.</div>

        <div className="mt-6 flex items-center justify-center gap-8">
          <div
            className={`h-68 w-60 flex flex-col justify-center items-center p-4 gap-4 border-2 rounded-2xl cursor-pointer transition-colors
              ${option === Selection.Template ? "bg-gradient-to-bl from-sky-400 via-sky-400 to-info border-sky-400" : "border-zinc-300"}`}
            onClick={() => setOption(Selection.Template)}
          >
            <div>
              <LuFileBox className={option === Selection.Template ? "text-white" : "text-info"} size={50} />
            </div>
            <div className={`text-lg ${option === Selection.Template ? "text-white font-poppins-semibold" : ""}`}>Template Base</div>
            <div className={`text-center text-sm ${option === Selection.Template ? "text-white" : "text-zinc-500"}`}>
              It follows a set structure, ensuring consistency and reliability but may lack personalization or uniqueness.
            </div>
          </div>

          <div
            className={`h-68 w-60 flex flex-col justify-center items-center p-4 gap-4 border-2 rounded-2xl cursor-pointer transition-colors
              ${option === Selection.Customized ? "border-primary bg-gradient-to-tr from-black to-primary" : "border-zinc-300 bg-white"}`}
            onClick={() => setOption(Selection.Customized)}
          >
            <div>
              <LuFileCog className={option === Selection.Customized ? "text-white" : "text-primary"} size={50} />
            </div>
            <div className={`text-lg ${option === Selection.Customized ? "text-white font-poppins-semibold" : ""}`}>Customized</div>
            <div className={`text-center text-sm ${option === Selection.Customized ? "text-white" : "text-zinc-500"}`}>
              Customized creation involves tailoring a product, service, or solution to specific preferences, needs, or specifications.
            </div>
          </div>
        </div>

        <div className="flex items-center justify-center mt-8">
          <Button
            onClick={handleSelect}
            variant="primary"
            classNames="px-8 py-2 rounded bg-blue-600 text-white text-sm hover:bg-blue-700 font-poppins-semibold disabled:opacity-50"
            type="button"
            disabled={!option}
          >
            Continue
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default SelectOptionModal;
