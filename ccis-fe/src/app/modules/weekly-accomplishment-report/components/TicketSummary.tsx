import { issueTypes } from "../EmployeeReportForm";

export function TicketSummary() {


  return (
    <div>
      <label className="flex items-center justify-between">
        <span>Current Work Types</span>
      </label>
      <div className="grid grid-cols-2 gap-3">
        {issueTypes.map((type) => {
          const Icon = type.icon;
          return (
            <div key={type.code} className="flex items-center justify-between p-3 rounded-lg border">
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-md ${type.color}`}>
                  <Icon className="h-4 w-4 text-white" />
                </div>
                <div>
                  <p className="font-medium">{type.name}</p>
                  <p className="text-xs text-muted-foreground">Type {type.code}</p>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
