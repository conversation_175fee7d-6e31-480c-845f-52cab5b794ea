import Modal from "@components/common/Modal";
import { formatDate, issueTypes } from "@modules/weekly-accomplishment-report/EmployeeReportForm";
import { Clock, Target, User, Calendar, Hash, Activity } from "lucide-react";

interface EmployeeReportModalProps {
  employee: any;
  isOpen: boolean;
  onClose: () => void;
}

export function EmployeeReportModal({ employee, isOpen, onClose }: EmployeeReportModalProps) {
  if (!employee) return null;

  const targetTasks = employee.accomplishmentTasks.filter((task: any) => task.taskGroup.taskGroupCode === "Target");
  const actualTasks = employee.accomplishmentTasks.filter((task: any) => task.taskGroup.taskGroupCode === "Actual");

  const getIssueTypeColor = (code: string) => {
    switch (code) {
      case "F":
        return "bg-primary text-white"; // #042882 (deep blue)
      case "B":
        return "bg-danger text-white"; // #FF3548 (bright red)
      case "E":
        return "bg-success text-white"; // #28A845 (green)
      case "R":
        return "bg-warning text-white"; // #E3C000 (yellow)
      case "U":
        return "bg-primary3 text-white"; // #0c14e7 (vibrant blue, as no purple in config)
      case "Q":
        return "bg-saffron-mango-500 text-white"; // #f97e07 (vibrant orange)
      default:
        return "bg-gray text-white"; // #474747 (medium gray)
    }
  };
  //For Future use
  // const handleDownloadAccomplishmentReport = async (accomplishmentReportId: number) => {
  //   try {
  //     const response: any = await exportAccomplishmentReportService(accomplishmentReportId);
  //     const pdfBlob = new Blob([response], { type: "application/pdf" });
  //     const url = window.URL.createObjectURL(pdfBlob);
  //     const a = document.createElement("a");
  //     a.href = url;
  //     a.download = `Accomplishment-Report-${accomplishmentReportId}.pdf`;
  //     document.body.appendChild(a);
  //     a.click();
  //     document.body.removeChild(a);
  //     URL.revokeObjectURL(url);
  //   } catch (error: any) {
  //     toast.error(error);
  //   }
  // };

  return (
    <Modal title="Employee Report" isOpen={isOpen} onClose={onClose} modalContainerClassName="max-w-4xl" className="overflow-y-auto">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold flex items-center gap-2 mb-2">
            <User className="h-5 w-5" />
            {employee.createdBy.firstname} {employee.createdBy.lastname} - Current Work
          </h2>
          {/* <Button onClick={() => handleDownloadAccomplishmentReport(employee.id)} variant="primary" classNames="">
            Print Report
          </Button> */}
        </div>
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <span className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            {formatDate(employee.periodFrom)} to {formatDate(employee.periodTo)}
          </span>
          <span>
            Week {employee.weekNumber}, {employee.applicableMonth}
          </span>
        </div>
      </div>

      <div className="space-y-6">
        {/* Employee Info */}
        <div className="border rounded-lg">
          <div className="p-4 border-b">
            <h3 className="text-lg font-semibold">Employee Information</h3>
          </div>
          <div className="p-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Name:</span> {employee.createdBy.firstname} {employee.createdBy.middlename} {employee.createdBy.lastname}
              </div>
              <div>
                <span className="font-medium">Email:</span> {employee.createdBy.email}
              </div>
              <div>
                <span className="font-medium">Position ID:</span> {employee.createdBy.positionId}
              </div>
              <div>
                <span className="font-medium">Department ID:</span> {employee.createdBy.departmentId}
              </div>
            </div>
          </div>
        </div>

        {/* Current Assigned Tasks */}
        <div className="border rounded-lg">
          <div className="p-4 border-b">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Target className="h-5 w-5 text-cyan-800" />
              Current Target Tasks ({targetTasks.length})
            </h3>
          </div>
          <div className="p-4">
            <div className="space-y-3">
              {targetTasks.map((task: any) => (
                <div key={task.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="inline-flex items-center px-2 py-1 text-xs border rounded-md">
                        <Hash className="h-3 w-3 mr-1" />
                        {task.referenceNumber}
                      </span>
                      <span
                        className={`inline-flex items-center px-2 py-1 text-xs rounded-md text-white ${issueTypes.find((type) => type.code === task.issueType.issueTypeCode)?.color} text-black`} // Use text-black to match the earlier example's text color
                        title={issueTypes.find((type) => type.code === task.issueType.issueTypeCode)?.name} // Add tooltip with issue type name
                      >
                        {(() => {
                          const issueType = issueTypes.find((type) => type.code === task.issueType.issueTypeCode);
                          const IconComponent = issueType?.icon;
                          return (
                            <>
                              {IconComponent ? <IconComponent className="h-4 w-4 mr-1" color={issueType?.textColor} /> : task.issueType.issueTypeCode}
                              {task.issueType.issueTypeName}
                            </>
                          );
                        })()}
                      </span>
                    </div>
                    <p className="font-medium">{task.description}</p>
                    {task.taskBlockers && task.taskBlockers.length > 0 && (
                      <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded w-fit">
                        <p className="text-xs font-semibold text-yellow-800 mb-1">Blockers:</p>
                        {task.taskBlockers.map((blocker: any) => (
                          <div key={blocker.id} className="text-xs text-yellow-700">
                            <p>
                              <span className="font-medium">Remarks:</span> {blocker.remarks}
                            </p>
                            <p>
                              <span className="font-medium">Started:</span> {formatDate(blocker.startedAt)} • {blocker.numberOfHours}h
                            </p>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-1 text-sm font-medium">
                    <Clock className="h-4 w-4" />
                    {task.numberOfHours}h planned
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <hr className="border-gray-200" />

        {/* Work Completed */}
        <div className="border rounded-lg">
          <div className="p-4 border-b">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Activity className="h-5 w-5 text-green-600" />
              Actual Worked Tasks ({actualTasks.length})
            </h3>
          </div>
          <div className="p-4">
            <div className="space-y-3">
              {actualTasks.map((task: any) => (
                <div key={task.id} className="flex items-center justify-between p-3 border rounded-lg bg-green-50">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="inline-flex items-center px-2 py-1 text-xs border rounded-md">
                        <Hash className="h-3 w-3 mr-1" />
                        {task.referenceNumber}
                      </span>
                      <span className={`inline-flex items-center px-2 py-1 text-xs rounded-md ${getIssueTypeColor(task.issueType.issueTypeCode)}`}>
                        {task.issueType.issueTypeCode} - {task.issueType.issueTypeName}
                      </span>
                    </div>
                    <p className="font-medium">{task.description}</p>
                    {task.taskBlockers && task.taskBlockers.length > 0 && (
                      <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded w-fit">
                        <p className="text-xs font-semibold text-yellow-800 mb-1">Blockers:</p>
                        {task.taskBlockers.map((blocker: any) => (
                          <div key={blocker.id} className="text-xs text-yellow-700">
                            <p>
                              <span className="font-medium">Remarks:</span> {blocker.remarks}
                            </p>
                            <p>
                              <span className="font-medium">Started:</span> {formatDate(blocker.startedAt)} • {blocker.numberOfHours}h
                            </p>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-1 text-sm font-medium text-green-700">
                    <Clock className="h-4 w-4" />
                    {task.numberOfHours}h completed
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="border rounded-lg">
          <div className="p-4 border-b">
            <h3 className="text-lg font-semibold">Work Status</h3>
          </div>
          <div className="p-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div className="p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{targetTasks.length}</div>
                <p className="text-sm text-gray-600">Assigned Tasks</p>
              </div>
              <div className="p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{actualTasks.length}</div>
                <p className="text-sm text-gray-600">Actual Tasks</p>
              </div>
              <div className="p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">{targetTasks.reduce((sum: number, task: any) => sum + task.numberOfHours, 0)}h</div>
                <p className="text-sm text-gray-600">Planned Hours</p>
              </div>
              <div className="p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">{actualTasks.reduce((sum: number, task: any) => sum + task.numberOfHours, 0)}h</div>
                <p className="text-sm text-gray-600">Hours Worked</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
}
