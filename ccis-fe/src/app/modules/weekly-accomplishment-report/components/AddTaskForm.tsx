// Updated AddTaskForm.tsx - With comprehensive validation
import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import { CreateTaskFormSchema, getFieldError, getFieldErrorMessage } from "@services/weekly-management-report/weekly-management-report.schema";
import { RootState } from "@state/store";
import { useFormik } from "formik";
import { IoIosAddCircleOutline, IoIosArrowDown } from "react-icons/io";
import { useSelector } from "react-redux";
import { useWeeklyAccomplishmentReportActions } from "@state/reducer/weekly-accomplishment-report";
import { IAccomplishmentReportPayload } from "@interface/weekly-accomplishment-report.interface";
import React, { useEffect } from "react";

// Separate interfaces for target and actual tasks
interface TargetTaskRow {
  id: number;
  ticketNo: string;
  taskType: string;
  hours: string;
  description: string;
  showBlockers: boolean;
  blockerDetails: { text: string; date: string; number: string };
}

interface ActualTaskRow {
  id: number;
  ticketNo: string;
  taskType: string;
  hours: string;
  description: string;
  showBlockers: boolean;
  blockerDetails: { text: string; date: string; number: string };
}

interface AddTaskFormProps {
  onCancel: () => void;
  isOpen: boolean;
  handleFetchData: () => void;
  weekNumber: number;
  periodFrom: string;
  periodTo: string;
}

export default function AddTaskForm({ isOpen, onCancel, weekNumber, periodFrom, periodTo }: AddTaskFormProps) {
  const issueTypesData = useSelector((state: RootState) => state?.weeklyAccomplishmentReport?.getIssueTypes?.data);
  const taskTypesData = useSelector((state: RootState) => state?.weeklyAccomplishmentReport?.getTaskTypes?.data);
  const taskGroupsData = useSelector((state: RootState) => state?.weeklyAccomplishmentReport?.getTaskGroups?.data);

  const { postAddAccomplishmentReport, clearPostAddAccomplishmentReport } = useWeeklyAccomplishmentReportActions();

  function addDays(dateStr: string, days: number): string {
    const [year, month, day] = dateStr.split("-").map(Number);
    const date = new Date(year, month - 1, day);
    date.setDate(date.getDate() + days);

    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, "0");
    const dd = String(date.getDate()).padStart(2, "0");

    return `${yyyy}-${mm}-${dd}`;
  }

  const updatedPeriodFrom = addDays(periodFrom ? periodFrom : "", 7);
  const updatedPeriodTo = addDays(periodTo ? periodTo : "", 7);

  // Separate initial states for target and actual tasks
  const initialTargetTasks: TargetTaskRow[] = [
    {
      id: 1,
      ticketNo: "",
      taskType: "",
      hours: "",
      description: "",
      showBlockers: false,
      blockerDetails: { text: "", date: "", number: "" },
    },
  ];

  const initialActualTasks: ActualTaskRow[] = [
    {
      id: 1,
      ticketNo: "",
      taskType: "",
      hours: "",
      description: "",
      showBlockers: false,
      blockerDetails: { text: "", date: "", number: "" },
    },
  ];

  const formik = useFormik({
    initialValues: {
      monthYear: "",
      weekNo: weekNumber.toString(),
      periodFrom: updatedPeriodFrom,
      periodTo: updatedPeriodTo,
      targetTasks: initialTargetTasks.map((task) => ({
        ...task,
        showBlockers: false,
        blockerDetails: { text: "", date: "", number: "" },
      })),
      actualTasks: initialActualTasks.map((task) => ({
        ...task,
        showBlockers: false,
        blockerDetails: { text: "", date: "", number: "" },
      })),
    },
    validationSchema: CreateTaskFormSchema,
    onSubmit: async (values) => {
      const periodFromISO = values.periodFrom ? new Date(values.periodFrom).toISOString() : "";
      const periodToISO = values.periodTo ? new Date(values.periodTo).toISOString() : "";

      const accomplishmentTasks: any[] = [];

      // Process Target tasks independently
      values.targetTasks.forEach((task) => {
        if (task.ticketNo || task.hours || task.description) {
          const issueTypeId = getIssueTypeId(task.taskType);
          const taskTypeId = getDefaultTaskTypeId();

          const blockers =
            task.showBlockers && task.blockerDetails.text && task.blockerDetails.date && task.blockerDetails.number
              ? [
                  {
                    remarks: task.blockerDetails.text || "No remarks provided",
                    startedAt: task.blockerDetails.date ? new Date(task.blockerDetails.date).toISOString() : "",
                    numberOfHours: parseInt(task.blockerDetails.number) || 0,
                  },
                ]
              : [];

          accomplishmentTasks.push({
            referenceNumber: task.ticketNo || "",
            description: task.description || "No description provided",
            numberOfHours: parseInt(task.hours) || 0,
            issueTypeId: issueTypeId,
            taskTypeId: taskTypeId,
            taskGroupId: getTargetTaskGroupId(), // taskGroupId: 1
            accomplishmentTaskBlockers: blockers,
          });
        }
      });

      // Process Actual tasks independently
      values.actualTasks.forEach((task) => {
        if (task.ticketNo || task.hours || task.description) {
          const issueTypeId = getIssueTypeId(task.taskType);
          const taskTypeId = getDefaultTaskTypeId();

          const blockers =
            task.showBlockers && task.blockerDetails.text && task.blockerDetails.date && task.blockerDetails.number
              ? [
                  {
                    remarks: task.blockerDetails.text || "No remarks provided",
                    startedAt: task.blockerDetails.date ? new Date(task.blockerDetails.date).toISOString() : "",
                    numberOfHours: parseInt(task.blockerDetails.number) || 0,
                  },
                ]
              : [];

          accomplishmentTasks.push({
            referenceNumber: task.ticketNo || "",
            description: task.description || "No description provided",
            numberOfHours: parseInt(task.hours) || 0,
            issueTypeId: issueTypeId,
            taskTypeId: taskTypeId,
            taskGroupId: getActualTaskGroupId(), // taskGroupId: 2
            accomplishmentTaskBlockers: blockers,
          });
        }
      });

      const payload: IAccomplishmentReportPayload = {
        periodFrom: periodFromISO,
        periodTo: periodToISO,
        weekNumber: parseInt(values.weekNo) || 1,
        accomplishmentTasks: accomplishmentTasks.filter((task) => task.referenceNumber),
      };
      await postAddAccomplishmentReport(payload);
      clearPostAddAccomplishmentReport();
      handleCancel();
    },
  });

  useEffect(() => {
    if (!formik.touched.weekNo) {
      formik.setFieldValue("weekNo", weekNumber.toString());
    }
  }, [weekNumber]);

  const getTaskTypeOptions = () => {
    if (issueTypesData && issueTypesData.length > 0) {
      return issueTypesData.map((issueType: any) => ({
        value: issueType.id.toString(),
        label: `${issueType.issueTypeCode} - ${issueType.issueTypeName}`,
        id: issueType.id,
      }));
    }
    return [];
  };

  const taskTypeOptions = getTaskTypeOptions();

  const getIssueTypeId = (selectedValue: string) => {
    if (!issueTypesData || !selectedValue) return 1;
    const issueType = issueTypesData.find((type: any) => type.id.toString() === selectedValue);
    return issueType?.id || 1;
  };

  const getDefaultTaskTypeId = () => {
    return taskTypesData?.[0]?.id || 1;
  };

  const getTargetTaskGroupId = () => {
    return taskGroupsData?.find((group: any) => group.taskGroupCode === "Target")?.id || 1;
  };

  const getActualTaskGroupId = () => {
    return taskGroupsData?.find((group: any) => group.taskGroupCode === "Actual")?.id || 2;
  };

  // Target task management functions
  const updateTargetTask = (id: number, field: keyof TargetTaskRow, value: string | boolean | { text: string; date: string; number: string }) => {
    const updatedTasks = formik.values.targetTasks.map((task) => (task.id === id ? { ...task, [field]: value } : task));
    formik.setFieldValue("targetTasks", updatedTasks);

    // Trigger validation for the specific field
    formik.setFieldTouched(`targetTasks.${id - 1}.${field}`, true);
  };

  const removeTargetTask = (id: number) => {
    if (formik.values.targetTasks.length > 1) {
      const updatedTasks = formik.values.targetTasks.filter((task) => task.id !== id).map((task, index) => ({ ...task, id: index + 1 }));
      formik.setFieldValue("targetTasks", updatedTasks);
    }
  };

  const addTargetTask = () => {
    const newTask: TargetTaskRow = {
      id: formik.values.targetTasks.length + 1,
      ticketNo: "",
      taskType: "",
      hours: "",
      description: "",
      showBlockers: false,
      blockerDetails: { text: "", date: "", number: "" },
    };
    formik.setFieldValue("targetTasks", [...formik.values.targetTasks, newTask]);
  };

  // Actual task management functions
  const updateActualTask = (id: number, field: keyof ActualTaskRow, value: string | boolean | { text: string; date: string; number: string }) => {
    const updatedTasks = formik.values.actualTasks.map((task) => (task.id === id ? { ...task, [field]: value } : task));
    formik.setFieldValue("actualTasks", updatedTasks);

    // Trigger validation for the specific field
    formik.setFieldTouched(`actualTasks.${id - 1}.${field}`, true);
  };

  const removeActualTask = (id: number) => {
    if (formik.values.actualTasks.length > 1) {
      const updatedTasks = formik.values.actualTasks.filter((task) => task.id !== id).map((task, index) => ({ ...task, id: index + 1 }));
      formik.setFieldValue("actualTasks", updatedTasks);
    }
  };

  const addActualTask = () => {
    const newTask: ActualTaskRow = {
      id: formik.values.actualTasks.length + 1,
      ticketNo: "",
      taskType: "",
      hours: "",
      description: "",
      showBlockers: false,
      blockerDetails: { text: "", date: "", number: "" },
    };
    formik.setFieldValue("actualTasks", [...formik.values.actualTasks, newTask]);
  };

  const handleCancel = () => {
    formik.resetForm();
    onCancel();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleCancel} title="Add New Task" modalContainerClassName="max-w-8xl">
      <form onSubmit={formik.handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pb-6 border-b">
          <div className="form-control">
            <label className="label">
              <span className="label-text">Week No:</span>
            </label>
            <input
              type="number"
              name="weekNo"
              className={`input input-bordered w-full ${formik.touched.weekNo && formik.errors.weekNo ? "border-error" : ""}`}
              value={formik.values.weekNo}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            {formik.touched.weekNo && formik.errors.weekNo && (
              <label className="label">
                <span className="label-text-alt text-error">{formik.errors.weekNo}</span>
              </label>
            )}
          </div>
          <div className="form-control">
            <label className="label">
              <span className="label-text">Period From:</span>
            </label>
            <input
              type="date"
              name="periodFrom"
              className={`input input-bordered w-full ${formik.touched.periodFrom && formik.errors.periodFrom ? "border-error" : ""}`}
              value={formik.values.periodFrom}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            {formik.touched.periodFrom && formik.errors.periodFrom && (
              <label className="label">
                <span className="label-text-alt text-error">{formik.errors.periodFrom}</span>
              </label>
            )}
          </div>
          <div className="form-control">
            <label className="label">
              <span className="label-text">Period To:</span>
            </label>
            <input
              type="date"
              name="periodTo"
              className={`input input-bordered w-full ${formik.touched.periodTo && formik.errors.periodTo ? "border-error" : ""}`}
              value={formik.values.periodTo}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            {formik.touched.periodTo && formik.errors.periodTo && (
              <label className="label">
                <span className="label-text-alt text-error">{formik.errors.periodTo}</span>
              </label>
            )}
          </div>
        </div>

        <div className="overflow-x-auto py-5">
          <div className="flex flex-col md:flex-row">
            {/* Target Tasks Table */}
            <div className="flex-1">
              <table className="table table-bordered w-full border border-base-300">
                <thead>
                  <tr className="bg-base-100">
                    <th colSpan={4} className="text-center font-semibold">
                      TARGET TASKS
                    </th>
                  </tr>
                  <tr className="bg-base-200">
                    <th className="text-center text-sm font-medium border-r border-base-300">Ticket No</th>
                    <th className="text-center text-sm font-medium border-r border-base-300">Description</th>
                    <th className="text-center text-sm font-medium border-r border-base-300">Target Type</th>
                    <th className="text-center text-sm font-medium">
                      Target No of Hours
                      <br />
                      to Complete
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {formik.values.targetTasks.map((task, index) => (
                    <React.Fragment key={task.id}>
                      <tr key={task.id}>
                        <td className="border-r border-base-300 p-2">
                          <div className="flex items-center">
                            <span className="text-sm font-medium mr-2">{index + 1}.</span>
                            <div className="flex-1">
                              <input
                                type="text"
                                className={`input input-sm w-full ${getFieldError(formik.errors, formik.touched, "targetTasks", index, "ticketNo") ? "border-error" : ""}`}
                                value={task.ticketNo}
                                onChange={(e) => updateTargetTask(task.id, "ticketNo", e.target.value)}
                                onBlur={() => formik.setFieldTouched(`targetTasks.${index}.ticketNo`, true)}
                                placeholder="Ticket Identifier"
                                required
                              />
                              {getFieldError(formik.errors, formik.touched, "targetTasks", index, "ticketNo") && (
                                <div className="text-error text-xs mt-1">{getFieldErrorMessage(formik.errors, "targetTasks", index, "ticketNo")}</div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="border-r border-base-300 p-2">
                          <input
                            type="text"
                            className={`input input-sm w-full ${getFieldError(formik.errors, formik.touched, "targetTasks", index, "description") ? "border-error" : ""}`}
                            value={task.description}
                            onChange={(e) => updateTargetTask(task.id, "description", e.target.value)}
                            onBlur={() => formik.setFieldTouched(`targetTasks.${index}.description`, true)}
                            placeholder="Task description"
                            required
                          />
                          {getFieldError(formik.errors, formik.touched, "targetTasks", index, "description") && (
                            <div className="text-error text-xs mt-1">{getFieldErrorMessage(formik.errors, "targetTasks", index, "description")}</div>
                          )}
                        </td>
                        <td className="border-r border-base-300 p-2">
                          <select
                            className={`select select-sm w-full ${getFieldError(formik.errors, formik.touched, "targetTasks", index, "taskType") ? "border-error" : ""}`}
                            value={task.taskType}
                            onChange={(e) => updateTargetTask(task.id, "taskType", e.target.value)}
                            onBlur={() => formik.setFieldTouched(`targetTasks.${index}.taskType`, true)}
                            required
                          >
                            <option value="">Select Issue Type</option>
                            {taskTypeOptions.map((type: any) => (
                              <option key={type.value} value={type.value}>
                                {type.label}
                              </option>
                            ))}
                          </select>
                          {getFieldError(formik.errors, formik.touched, "targetTasks", index, "taskType") && (
                            <div className="text-error text-xs mt-1">{getFieldErrorMessage(formik.errors, "targetTasks", index, "taskType")}</div>
                          )}
                        </td>
                        <td className="p-2">
                          <div className="flex items-center gap-2">
                            <div className="flex-1">
                              <input
                                type="number"
                                className={`input input-sm w-full ${getFieldError(formik.errors, formik.touched, "targetTasks", index, "hours") ? "border-error" : ""}`}
                                value={task.hours}
                                onChange={(e) => updateTargetTask(task.id, "hours", e.target.value)}
                                onBlur={() => formik.setFieldTouched(`targetTasks.${index}.hours`, true)}
                                placeholder="0"
                                min="0"
                                step="0.5"
                                required
                              />
                              {getFieldError(formik.errors, formik.touched, "targetTasks", index, "hours") && (
                                <div className="text-error text-xs mt-1">{getFieldErrorMessage(formik.errors, "targetTasks", index, "hours")}</div>
                              )}
                            </div>
                            <button
                              type="button"
                              className="btn btn-sm btn-slate-300 btn-circle text-white"
                              title="has Blockers?"
                              onClick={() => updateTargetTask(task.id, "showBlockers", !task.showBlockers)}
                            >
                              <IoIosArrowDown size={12} className={`cursor-pointer text-black ${task.showBlockers ? "rotate-180" : ""}`} />
                            </button>

                            {formik.values.targetTasks.length > 1 && (
                              <button type="button" className="btn btn-sm btn-error btn-circle text-white" onClick={() => removeTargetTask(task.id)} title="Remove task">
                                ×
                              </button>
                            )}
                          </div>
                        </td>
                      </tr>
                      {task.showBlockers && (
                        <tr>
                          <td colSpan={4} className="border border-base-300 p-2 bg-base-100">
                            <div className="flex gap-2 w-full">
                              <textarea
                                className="input input-sm flex-1 border border-base-300"
                                value={task.blockerDetails?.text || ""}
                                onChange={(e) => updateTargetTask(task.id, "blockerDetails", { ...task.blockerDetails, text: e.target.value })}
                                placeholder="Blocker description"
                              />
                              <input
                                type="date"
                                className="input input-sm w-40 border border-base-300"
                                value={task.blockerDetails?.date || ""}
                                onChange={(e) => updateTargetTask(task.id, "blockerDetails", { ...task.blockerDetails, date: e.target.value })}
                              />
                              <input
                                type="number"
                                className="input input-sm w-24 border border-base-300"
                                value={task.blockerDetails?.number || ""}
                                onChange={(e) => updateTargetTask(task.id, "blockerDetails", { ...task.blockerDetails, number: e.target.value })}
                                placeholder="hours"
                                min="0"
                              />
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))}
                </tbody>
              </table>
              <div className="flex justify-center mt-2">
                <IoIosAddCircleOutline className="cursor-pointer" size={20} onClick={addTargetTask} />
              </div>
            </div>

            {/* Actual Tasks Table */}
            <div className="flex-1">
              <table className="table table-bordered w-full border border-base-300">
                <thead>
                  <tr className="bg-base-100">
                    <th colSpan={4} className="text-center font-semibold">
                      ACTUAL ACCOMPLISHED TASKS
                    </th>
                  </tr>
                  <tr className="bg-base-200">
                    <th className="text-center text-sm font-medium border-r border-base-300">Ticket No</th>
                    <th className="text-center text-sm font-medium border-r border-base-300">Description</th>
                    <th className="text-center text-sm font-medium border-r border-base-300">Issue Type</th>
                    <th className="text-center text-sm font-medium">
                      Actual No of Hours
                      <br />
                      Completed
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {formik.values.actualTasks.map((task, index) => (
                    <React.Fragment key={task.id}>
                      <tr key={task.id}>
                        <td className="border-r border-base-300 p-2">
                          <div className="flex items-center">
                            <span className="text-sm font-medium mr-2">{index + 1}.</span>
                            <div className="flex-1">
                              <input
                                type="text"
                                className={`input input-sm w-full ${getFieldError(formik.errors, formik.touched, "actualTasks", index, "ticketNo") ? "border-error" : ""}`}
                                value={task.ticketNo}
                                onChange={(e) => updateActualTask(task.id, "ticketNo", e.target.value)}
                                onBlur={() => formik.setFieldTouched(`actualTasks.${index}.ticketNo`, true)}
                                placeholder="Actual Ticket Identifier"
                              />
                              {getFieldError(formik.errors, formik.touched, "actualTasks", index, "ticketNo") && (
                                <div className="text-error text-xs mt-1">{getFieldErrorMessage(formik.errors, "actualTasks", index, "ticketNo")}</div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="border-r border-base-300 p-2">
                          <input
                            type="text"
                            className={`input input-sm w-full ${getFieldError(formik.errors, formik.touched, "actualTasks", index, "description") ? "border-error" : ""}`}
                            value={task.description}
                            onChange={(e) => updateActualTask(task.id, "description", e.target.value)}
                            onBlur={() => formik.setFieldTouched(`actualTasks.${index}.description`, true)}
                            placeholder="Task description"
                          />
                          {getFieldError(formik.errors, formik.touched, "actualTasks", index, "description") && (
                            <div className="text-error text-xs mt-1">{getFieldErrorMessage(formik.errors, "actualTasks", index, "description")}</div>
                          )}
                        </td>
                        <td className="border-r border-base-300 p-2">
                          <select
                            className={`select select-sm w-full ${getFieldError(formik.errors, formik.touched, "actualTasks", index, "taskType") ? "border-error" : ""}`}
                            value={task.taskType}
                            onChange={(e) => updateActualTask(task.id, "taskType", e.target.value)}
                            onBlur={() => formik.setFieldTouched(`actualTasks.${index}.taskType`, true)}
                          >
                            <option value="">Select Issue Type</option>
                            {taskTypeOptions.map((type: any) => (
                              <option key={type.value} value={type.value}>
                                {type.label}
                              </option>
                            ))}
                          </select>
                          {getFieldError(formik.errors, formik.touched, "actualTasks", index, "taskType") && (
                            <div className="text-error text-xs mt-1">{getFieldErrorMessage(formik.errors, "actualTasks", index, "taskType")}</div>
                          )}
                        </td>
                        <td className="p-2">
                          <div className="flex items-center gap-2">
                            <div className="flex-1">
                              <input
                                type="number"
                                className={`input input-sm w-full ${getFieldError(formik.errors, formik.touched, "actualTasks", index, "hours") ? "border-error" : ""}`}
                                value={task.hours}
                                onChange={(e) => updateActualTask(task.id, "hours", e.target.value)}
                                onBlur={() => formik.setFieldTouched(`actualTasks.${index}.hours`, true)}
                                placeholder="0"
                                min="0"
                                step="0.5"
                              />
                              {getFieldError(formik.errors, formik.touched, "actualTasks", index, "hours") && (
                                <div className="text-error text-xs mt-1">{getFieldErrorMessage(formik.errors, "actualTasks", index, "hours")}</div>
                              )}
                            </div>
                            <button
                              type="button"
                              className="btn btn-sm btn-slate-300 btn-circle text-white"
                              title="has Blockers?"
                              onClick={() => updateActualTask(task.id, "showBlockers", !task.showBlockers)}
                            >
                              <IoIosArrowDown size={12} className={`cursor-pointer text-black ${task.showBlockers ? "rotate-180" : ""}`} />
                            </button>
                            {formik.values.actualTasks.length > 1 && (
                              <div className="flex items-center gap-1">
                                <button type="button" className="btn btn-sm btn-error btn-circle text-white" onClick={() => removeActualTask(task.id)} title="Remove task">
                                  ×
                                </button>
                              </div>
                            )}
                          </div>
                        </td>
                      </tr>
                      {task.showBlockers && (
                        <tr>
                          <td colSpan={4} className="border border-base-300 p-2 bg-base-100">
                            <div className="flex gap-2 w-full">
                              <textarea
                                className="input input-sm flex-1 border border-base-300"
                                value={task.blockerDetails?.text || ""}
                                onChange={(e) => updateActualTask(task.id, "blockerDetails", { ...task.blockerDetails, text: e.target.value })}
                                placeholder="Blocker description"
                              />
                              <input
                                type="date"
                                className="input input-sm w-40 border border-base-300"
                                value={task.blockerDetails?.date || ""}
                                onChange={(e) => updateActualTask(task.id, "blockerDetails", { ...task.blockerDetails, date: e.target.value })}
                              />
                              <input
                                type="number"
                                className="input input-sm w-24 border border-base-300"
                                value={task.blockerDetails?.number || ""}
                                onChange={(e) => updateActualTask(task.id, "blockerDetails", { ...task.blockerDetails, number: e.target.value })}
                                placeholder="hours"
                                min="0"
                              />
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))}
                </tbody>
              </table>
              <div className="flex justify-center mt-2">
                <IoIosAddCircleOutline className="cursor-pointer" size={20} onClick={addActualTask} />
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end items-center">
          <div className="flex gap-3">
            <Button type="button" classNames="bg-gray-500 hover:bg-gray-600 text-white text-sm w-fit" onClick={handleCancel}>
              Cancel
            </Button>
            <Button type="submit" classNames="bg-sky-600 hover:bg-sky-700 text-white text-sm w-fit">
              Submit Report
            </Button>
          </div>
        </div>
      </form>
    </Modal>
  );
}
