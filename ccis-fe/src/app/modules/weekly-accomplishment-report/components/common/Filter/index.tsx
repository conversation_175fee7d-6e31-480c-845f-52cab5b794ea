import { FC, ChangeEvent, useState, useRef, useEffect } from "react";
import { FiFilter } from "react-icons/fi";
import TextField from "@components/form/TextField";

type Props = {
  search?: string;
  children?: React.ReactNode;
  isOpen?: boolean;
  onClose?: () => void;
  onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
  hideButton?: boolean;
  hideSearch?: boolean;
  placeholder?: string;
};

const EnhancedFilter: FC<Props> = ({ onChange, children, isOpen, hideButton = false, hideSearch = false, placeholder = "" }) => {
  const [filter, setFilter] = useState<boolean>(isOpen ?? false);
  const [searchValue, setSearchValue] = useState<string>("");

  const filterRef = useRef<HTMLDivElement>(null);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(e);
    }
    setSearchValue(e.target.value);
  };

  const handleFilter = () => {
    setFilter((prev) => !prev);
  };

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (filterRef.current && !filterRef.current.contains(event.target as Node)) {
        setFilter(false);
      }
    }

    if (filter) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [filter]);

  return (
    <div className="relative" ref={filterRef}>
      {!hideButton && (
        <button className="rounded-full flex items-center gap-2 btn btn-sm" onClick={handleFilter}>
          <FiFilter size={16} />
        </button>
      )}
      <div className={`dropdown dropdown-end absolute top-full right-0 z-50 ${filter ? "dropdown-open" : ""}`}>
        <div
          tabIndex={0}
          className={`card shadow-xl bg-base-100 border border-base-200 p-6 mt-2 w-96 transition-all duration-300 ${filter ? "opacity-100 scale-100 visible" : "opacity-0 scale-95 invisible"}`}
        >
          {!hideSearch && (
            <div className="mb-4">
              <TextField placeholder={`Search ${placeholder}`} className="w-full" size="sm" variant="primary" value={searchValue} onChange={handleChange} />
            </div>
          )}
          <div>{children}</div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedFilter;
