import { Calendar, Activity } from "lucide-react";
import { useMemo } from "react";

interface AccomplishmentReport {
  id: number;
  periodFrom: string;
  periodTo: string;
  applicableMonth: string;
  weekNumber: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  accomplishmentTasks: Array<{
    id: number;
    referenceNumber: string;
    description: string;
    numberOfHours: number;
    issueTypeId: number;
    taskTypeId: number;
    taskGroupId: number;
    accomplishmentReportId: number;
  }>;
  createdBy?: {
    id: number;
    firstname: string;
    middlename: string;
    lastname: string;
    email: string;
    departmentId: number;
  };
}

interface WeeklyOverviewProps {
  accomplishmentReports?: AccomplishmentReport[];
  loading?: boolean;
}

export function WeeklyOverview({ accomplishmentReports = [], loading = false }: WeeklyOverviewProps) {
  const weeklyStats = useMemo(() => {
    if (!accomplishmentReports || accomplishmentReports.length === 0) {
      return {
        totalEmployees: 0,
        activeReports: 0,
        currentWeek: "No data available",
        totalHours: 0,
        uniqueTickets: 0,
      };
    }

    // Get unique employees
    const uniqueEmployees = new Set(
      accomplishmentReports
        .filter((report) => report.createdBy !== undefined && report.createdBy !== null)
        .map((report) => report.createdBy!.id)
    );

    // Get the most recent report for current week display
    const mostRecentReport = accomplishmentReports.reduce((latest, current) => (new Date(current.updatedAt) > new Date(latest.updatedAt) ? current : latest));

    // Calculate total hours across all tasks
    const totalHours = accomplishmentReports.reduce((total, report) => total + report.accomplishmentTasks.reduce((taskTotal, task) => taskTotal + task.numberOfHours, 0), 0);

    // Get unique tickets/reference numbers
    const uniqueTickets = new Set();
    accomplishmentReports.forEach((report) => {
      report.accomplishmentTasks.forEach((task) => {
        uniqueTickets.add(task.referenceNumber);
      });
    });

    return {
      totalEmployees: uniqueEmployees.size,
      activeReports: accomplishmentReports.length,
      currentWeek: `Week ${mostRecentReport.weekNumber} - ${mostRecentReport.applicableMonth}`,
      totalHours,
      uniqueTickets: uniqueTickets.size,
    };
  }, [accomplishmentReports]);

  if (loading) {
    return (
      <div>
        <label className="flex items-center gap-2">
          <Calendar className="h-5 w-5 text-primary" />
          Current Week Overview
        </label>
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="text-center p-4 bg-muted rounded-lg animate-pulse">
                <div className="h-8 bg-gray-300 rounded mb-2"></div>
                <div className="h-4 bg-gray-300 rounded"></div>
              </div>
            ))}
          </div>
          <div className="flex items-center gap-2 p-3 bg-muted rounded-lg animate-pulse">
            <div className="h-4 w-4 bg-gray-300 rounded"></div>
            <div className="h-4 bg-gray-300 rounded flex-1"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <label className="flex items-center gap-2">
        <Calendar className="h-5 w-5 text-primary" />
        Current Week Overview
      </label>
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <div className="text-center p-4 bg-muted rounded-lg">
            <div className="text-2xl font-bold text-primary">{weeklyStats.currentWeek}</div>
            <p className="text-sm text-muted-foreground">Current Period</p>
          </div>
          <div className="text-center p-4 bg-muted rounded-lg">
            <div className="text-2xl font-bold text-secondary">{weeklyStats.totalEmployees}</div>
            <p className="text-sm text-muted-foreground">Total Employees</p>
          </div>
          <div className="text-center p-4 bg-muted rounded-lg">
            <div className="text-2xl font-bold text-chart-3">{weeklyStats.activeReports}</div>
            <p className="text-sm text-muted-foreground">Active Reports</p>
          </div>
        </div>

        <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
          <Activity className="h-4 w-4 text-primary" />
          <span className="text-sm text-slate-700 font-medium">
            Monitoring current employee work activities and task assignments
            {weeklyStats.totalEmployees > 0 && ` (${weeklyStats.totalEmployees} employees actively reporting)`}
          </span>
        </div>
      </div>
    </div>
  );
}
