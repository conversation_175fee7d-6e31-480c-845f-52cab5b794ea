import Loader from "@components/Loader";
import { SYSTEM_SETTINGS_IDS } from "@constants/global-constant-value";
import { getRolePath } from "@helpers/navigatorHelper";
// import { useGlobalSettingsManagementActions } from "@state/reducer/global-settings";
import { useWeeklyAccomplishmentReportActions } from "@state/reducer/weekly-accomplishment-report";
import { RootState } from "@state/store";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { Navigate } from "react-router-dom";

export default function WeeklyAccomplishmentReport() {
  const { getIssueTypes, getTaskTypes, getTaskGroups } = useWeeklyAccomplishmentReportActions();
  const user = useSelector((state: RootState) => state?.auth?.user?.data);
  const globalSettingsState = useSelector((state: RootState) => state?.globalSettings?.getGlobalSettings);

  useEffect(() => {
    getIssueTypes({ params: {} });
    getTaskTypes({ params: {} });
    getTaskGroups({ params: {} });
  }, []);

  const renderDashboard = () => {
    if (globalSettingsState?.loading || !globalSettingsState?.data?.data) {
      return (
        <div className="flex flex-1 w-full items-center justify-center">
          <Loader />
        </div>
      );
    }

    const isManager = globalSettingsState.data.data.find((setting: any) => setting.key === `${SYSTEM_SETTINGS_IDS.IT_MANAGER_USER_ID}`)?.value.includes(user?.id);

    return isManager ? <Navigate to={getRolePath("/manager-weekly-accomplishment-dashboard")} replace /> : <Navigate to={getRolePath("/employee-weekly-accomplishment-dashboard")} replace />;
  };

  // Add this return statement
  return renderDashboard();
}
