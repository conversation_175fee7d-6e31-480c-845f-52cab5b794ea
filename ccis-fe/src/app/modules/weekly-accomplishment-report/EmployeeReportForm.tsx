import Button from "@components/common/Button";
import { useFormik } from "formik";
import { useWeeklyAccomplishmentReportActions } from "@state/reducer/weekly-accomplishment-report";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import { IoIosAddCircleOutline, IoIosArrowDown } from "react-icons/io";
import AddTaskForm from "./components/AddTaskForm";
import { TbEditCircle } from "react-icons/tb";
import { MdDeleteOutline, MdPrint } from "react-icons/md";
import { IoCloseSharp } from "react-icons/io5";
import Loader from "@components/Loader";
import { toast } from "react-toastify";
import { confirmApproval } from "@modules/dashboard/RequestorDashboard/components/prompts/DepartmentalTicketingPrompts";
import { Bug, Palette, Rota<PERSON><PERSON>cw, Sparkle, TestTube, Wrench } from "lucide-react";
import { exportAccomplishmentReportService } from "@services/weekly-management-report/weekly-management-report.service";
import { UpdateTaskFormSchema } from "@services/weekly-management-report/weekly-management-report.schema";

export interface TaskRow {
  id: number;
  targetTicketNo: string;
  targetTaskType: string;
  targetHours: string;
  actualTicketNo: string;
  actualHours: string;
  description: string;
  taskGroupId?: number; // Add this to track the task group
}
export const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "long",
    day: "2-digit",
  };
  return date.toLocaleDateString("en-US", options);
};

export interface TargetTask {
  id: number;
  ticketNo: string;
  taskType: string;
  hours: string;
  description: string;
  showBlockers: boolean;
  blockerDetails: { text: string; date: string; number: string };
}

export interface ActualTask {
  id: number;
  ticketNo: string;
  hours: string;
  description: string;
  taskType: string;
  showBlockers: boolean;
  blockerDetails: { text: string; date: string; number: string };
}

export const issueTypes = [
  { code: "B", name: "Bugs", count: 3, icon: Bug, color: "bg-red-500", textColor: "white" },
  { code: "F", name: "Features", count: 8, icon: Sparkle, color: "bg-blue", textColor: "white" },
  { code: "E", name: "Enhancement", count: 2, icon: Wrench, color: "bg-green-500", textColor: "white" },
  { code: "R", name: "Revision", count: 2, icon: RotateCcw, color: "bg-yellow-500", textColor: "white" },
  { code: "U", name: "UI/UX", count: 1, icon: Palette, color: "bg-purple-500", textColor: "white" },
  { code: "Q", name: "QA", count: 0, icon: TestTube, color: "bg-orange-500", textColor: "white" },
];

export default function EmployeeReportForm() {
  const {
    destroyAccomplishmentReport,
    getIssueTypes,
    getTaskTypes,
    getTaskGroups,
    getLoggedInUserAccomplishmentReport,
    putUpdateAccomplishmentReport,
    clearDeleteAccomplishmentReport,
    clearPutUpdateAccomplishmentReport,
  } = useWeeklyAccomplishmentReportActions();
  const getLoggedInUserAccomplishmentReportData = useSelector((state: any) => state?.weeklyAccomplishmentReport?.getLoggedInUserAccomplishmentReport?.data);
  const issueTypesData = useSelector((state: RootState) => state?.weeklyAccomplishmentReport?.getIssueTypes?.data);
  const taskTypesData = useSelector((state: RootState) => state?.weeklyAccomplishmentReport?.getTaskTypes?.data);
  const taskGroupsData = useSelector((state: RootState) => state?.weeklyAccomplishmentReport?.getTaskGroups?.data);
  const positionName = useSelector((state: RootState) => state?.auth?.login?.data?.user?.position?.positionName);
  const firstName = useSelector((state: RootState) => state?.auth?.user?.data?.firstname);
  const lastName = useSelector((state: RootState) => state?.auth?.user?.data?.lastname);
  const [isAddTaskModalOpen, setIsAddTaskModalOpen] = useState(false);
  const [selectedDateInterval, setSelectedDateInterval] = useState("");
  const employeeName = `${firstName} ${lastName}`;
  const user = useSelector((state: RootState) => state?.auth?.user?.data);
  // Loading states

  const postAddAccomplishmentReportServiceState = useSelector((state: any) => state?.weeklyAccomplishmentReport?.postAddAccomplishmentReport);
  const destroyAccomplishmentReportState = useSelector((state: any) => state?.weeklyAccomplishmentReport?.destroyAccomplishmentReport);
  const putUpdateAccomplishmentReportState = useSelector((state: any) => state?.weeklyAccomplishmentReport?.putUpdateAccomplishmentReport);
  const getLoggedInUserAccomplishmentReportDataLoadingState = useSelector((state: any) => state?.weeklyAccomplishmentReport?.getLoggedInUserAccomplishmentReport?.loading);
  const [isFetching, setIsFetching] = useState(false);
  const [isEditable, setIsEditable] = useState(false);
  const [currentAccomplishmentId, setCurrentAccomplishmentId] = useState<number>(0);
  const [validationErrors, setValidationErrors] = useState<any>({});

  useEffect(() => {
    handleFetchData();
  }, [postAddAccomplishmentReportServiceState?.success, destroyAccomplishmentReportState?.success, putUpdateAccomplishmentReportState?.success]);

  const handleFetchData = async (newInterval?: string) => {
    setIsFetching(true);
    await getIssueTypes({ params: {} });
    await getTaskTypes({ params: {} });
    await getTaskGroups({ params: {} });
    await getLoggedInUserAccomplishmentReport({ params: {} });
    if (newInterval) {
      handleDateIntervalChange(newInterval);
    }
    setIsFetching(false);
  };

  useEffect(() => {
    if (getLoggedInUserAccomplishmentReportData && getLoggedInUserAccomplishmentReportData.length > 0) {
      const firstInterval = getDateIntervalOptions()[0]?.value || "";
      if (firstInterval && selectedDateInterval !== firstInterval) {
        handleDateIntervalChange(firstInterval);
      }
    }
  }, [getLoggedInUserAccomplishmentReportData]);

  // Get available date intervals from API data
  const getDateIntervalOptions = () => {
    if (getLoggedInUserAccomplishmentReportData && getLoggedInUserAccomplishmentReportData.length > 0) {
      return getLoggedInUserAccomplishmentReportData.map((report: any) => ({
        value: `${report.periodFrom} - ${report.periodTo}`,
        label: `${formatDate(report.periodFrom)} - ${formatDate(report.periodTo)}`,
        data: report,
      }));
    }
    return [];
  };

  // Handle date interval selection
  const handleDateIntervalChange = (selectedInterval: string) => {
    setSelectedDateInterval(selectedInterval);

    if (!selectedInterval) {
      formik.setValues({
        ...formik.values,
        monthYear: "",
        weekNo: "",
        periodFrom: "",
        periodTo: "",
        targetTasks: [
          {
            id: 1,
            ticketNo: "",
            taskType: "",
            hours: "",
            description: "",
            showBlockers: false,
            blockerDetails: { text: "", date: "", number: "" },
          },
        ],
        actualTasks: [
          {
            id: 1,
            ticketNo: "",
            hours: "",
            description: "",
            taskType: "",
            showBlockers: false,
            blockerDetails: { text: "", date: "", number: "" },
          },
        ],
      });
      return;
    }

    const selectedReport = getLoggedInUserAccomplishmentReportData?.find((report: any) => `${report.periodFrom} - ${report.periodTo}` === selectedInterval);

    if (selectedReport) {
      setCurrentAccomplishmentId(selectedReport.id);

      // Separate target and actual tasks
      // Inside handleDateIntervalChange function, replace the targetTasks mapping:
      const targetTasks = selectedReport.accomplishmentTasks
        ?.filter((task: any) => task.taskGroupId === 1)
        ?.map((task: any, index: number) => {
          const blocker = task.taskBlockers?.[0]; // Get first blocker if exists
          return {
            id: index + 1,
            ticketNo: task.referenceNumber || "",
            description: task.description || "",
            taskType: task.issueType ? `${task.issueType.issueTypeCode}-${task.issueType.issueTypeName}` : "",
            hours: task.numberOfHours?.toString() || "",
            showBlockers: blocker ? true : false,
            blockerDetails: blocker
              ? {
                  text: blocker.remarks || "",
                  date: blocker.startedAt || "",
                  number: blocker.numberOfHours || "",
                }
              : { text: "", date: "", number: "" },
          };
        }) || [{ id: 1, ticketNo: "", taskType: "", hours: "", description: "", showBlockers: false, blockerDetails: { text: "", date: "", number: "" } }];

      // Replace the actualTasks mapping:
      const actualTasks = selectedReport.accomplishmentTasks
        ?.filter((task: any) => task.taskGroupId === 2)
        ?.map((task: any, index: number) => {
          const blocker = task.taskBlockers?.[0]; // Get first blocker if exists
          return {
            id: index + 1,
            ticketNo: task.referenceNumber || "",
            description: task.description || "",
            taskType: task.issueType ? `${task.issueType.issueTypeCode}-${task.issueType.issueTypeName}` : "",
            hours: task.numberOfHours?.toString() || "",
            showBlockers: blocker ? true : false,
            blockerDetails: blocker
              ? {
                  text: blocker.remarks || "",
                  date: blocker.startedAt || "",
                  number: blocker.numberOfHours || "",
                }
              : { text: "", date: "", number: "" },
          };
        }) || [{ id: 1, ticketNo: "", hours: "", description: "", taskType: "", showBlockers: false, blockerDetails: { text: "", date: "", number: "" } }];
      formik.setValues({
        ...formik.values,
        monthYear: selectedReport.applicableMonth || "",
        weekNo: selectedReport.weekNumber?.toString() || "",
        periodFrom: selectedReport.periodFrom || "",
        periodTo: selectedReport.periodTo || "",
        targetTasks,
        actualTasks,
      });
    }
  };

  const formik = useFormik({
    initialValues: {
      employeeName: employeeName || "",
      position: positionName || "",
      monthYear: "",
      weekNo: "",
      periodFrom: "",
      periodTo: "",
      targetTasks: [
        {
          id: 1,
          ticketNo: "",
          taskType: "",
          hours: "",
          description: "",
        },
      ] as TargetTask[],
      actualTasks: [
        {
          id: 1,
          ticketNo: "",
          hours: "",
          description: "",
          taskType: "",
        },
      ] as ActualTask[],
    },
    validationSchema: UpdateTaskFormSchema,
    validateOnChange: true,
    validateOnBlur: true,
    onSubmit: () => {},
  });

  // Updated handleUpdateReport function
  const handleUpdateReport = async () => {
    if (!currentAccomplishmentId) {
      toast.error("No accomplishment ID found for update");
      return;
    }

    const isValid = await validateBeforeUpdate();
    if (!isValid) {
      return;
    }

    const accomplishmentTasks: any[] = [];

    // Process target tasks independently
    formik.values.targetTasks.forEach((task) => {
      if (task.ticketNo && task.description && task.taskType && task.hours) {
        const issueTypeId = getIssueTypeId(task.taskType);
        const defaultTaskTypeId = getTaskTypeId();

        const blockers =
          task.showBlockers && task.blockerDetails.text && task.blockerDetails.date && task.blockerDetails.number
            ? [
                {
                  remarks: task.blockerDetails.text || "No remarks provided",
                  startedAt: task.blockerDetails.date ? new Date(task.blockerDetails.date).toISOString() : "",
                  numberOfHours: parseInt(task.blockerDetails.number) || 0,
                },
              ]
            : [];

        if (!issueTypeId || !defaultTaskTypeId) {
          toast.error(`Invalid task configuration for target task: ${task.ticketNo}`);
          return;
        }

        accomplishmentTasks.push({
          referenceNumber: task.ticketNo.trim(),
          description: task.description.trim(),
          numberOfHours: parseInt(task.hours) || 0,
          issueTypeId: issueTypeId,
          taskTypeId: defaultTaskTypeId,
          taskGroupId: getTargetTaskGroupId(),
          accomplishmentTaskBlockers: blockers,
        });
      }
    });

    // Process actual tasks independently
    formik.values.actualTasks.forEach((task) => {
      if (task.ticketNo && task.description && task.taskType && task.hours) {
        const issueTypeId = getIssueTypeId(task.taskType);
        const defaultTaskTypeId = getTaskTypeId();

        const blockers =
          task.showBlockers && task.blockerDetails.text && task.blockerDetails.date && task.blockerDetails.number
            ? [
                {
                  remarks: task.blockerDetails.text || "No remarks provided",
                  startedAt: task.blockerDetails.date ? new Date(task.blockerDetails.date).toISOString() : "",
                  numberOfHours: parseInt(task.blockerDetails.number) || 0,
                },
              ]
            : [];

        if (!issueTypeId || !defaultTaskTypeId) {
          toast.error(`Invalid task configuration for actual task: ${task.ticketNo}`);
          return;
        }

        accomplishmentTasks.push({
          referenceNumber: task.ticketNo.trim(),
          description: task.description.trim(),
          numberOfHours: parseInt(task.hours) || 0,
          issueTypeId: issueTypeId,
          taskTypeId: defaultTaskTypeId,
          taskGroupId: getActualTaskGroupId(),
          accomplishmentTaskBlockers: blockers,
        });
      }
    });

    if (accomplishmentTasks.length === 0) {
      toast.error("At least one complete task is required for update");
      return;
    }

    const payload = {
      periodFrom: formik.values.periodFrom ? new Date(formik.values.periodFrom).toISOString() : "",
      periodTo: formik.values.periodTo ? new Date(formik.values.periodTo).toISOString() : "",
      applicableMonth: formik.values.monthYear,
      weekNumber: parseInt(formik.values.weekNo) || 1,
      accomplishmentTasks: accomplishmentTasks,
    };

    try {
      await putUpdateAccomplishmentReport({
        accomplishmentId: currentAccomplishmentId,
        payload,
      });
      clearPutUpdateAccomplishmentReport();
      toast.success("Report updated successfully!");
      await handleFetchData();
      setIsEditable(false);
      setValidationErrors({});
    } catch (error: any) {
      toast.error(`Failed to update report: ${error.message || error}`);
    }
  };

  // const clearForm = () => {
  //   formik.resetForm();
  //   setSelectedDateInterval("");
  // };

  const getTaskTypeOptions = () => {
    if (issueTypesData && issueTypesData.length > 0) {
      return issueTypesData.map((issueType: any) => ({
        value: `${issueType.issueTypeCode}-${issueType.issueTypeName}`,
        label: `${issueType.issueTypeCode}-${issueType.issueTypeName}`,
      }));
    }
  };

  const taskTypeOptions = getTaskTypeOptions() || [];
  const dateIntervalOptions = getDateIntervalOptions();

  const getIssueTypeId = (taskType: string) => {
    if (!issueTypesData || !taskType) return null;
    const issueType = issueTypesData.find((type: any) => `${type.issueTypeCode}-${type.issueTypeName}` === taskType);
    return issueType?.id || null;
  };

  const getTaskTypeId = () => {
    return taskTypesData?.[0]?.id || null;
  };
  // For future use
  // const getTaskGroupId = () => {
  //   return taskGroupsData?.[0]?.id || null;
  // };
  // Helper functions for managing tasks
  const updateTargetTask = (id: number, field: keyof TargetTask, value: string | boolean | { text: string; date: string; number: string }) => {
    const updatedTasks = formik.values.targetTasks.map((task) => (task.id === id ? { ...task, [field]: value } : task));
    formik.setFieldValue("targetTasks", updatedTasks);

    // Clear specific field error when user starts typing
    if (validationErrors.targetTasks?.[id - 1]?.[field]) {
      const newErrors = { ...validationErrors };
      if (newErrors.targetTasks?.[id - 1]) {
        delete newErrors.targetTasks[id - 1][field];
        setValidationErrors(newErrors);
      }
    }
  };

  // Updated updateActualTask function with validation
  const updateActualTask = (id: number, field: keyof ActualTask, value: string | boolean | { text: string; date: string; number: string }) => {
    const updatedTasks = formik.values.actualTasks.map((task) => (task.id === id ? { ...task, [field]: value } : task));
    formik.setFieldValue("actualTasks", updatedTasks);

    // Clear specific field error when user starts typing
    if (validationErrors.actualTasks?.[id - 1]?.[field]) {
      const newErrors = { ...validationErrors };
      if (newErrors.actualTasks?.[id - 1]) {
        delete newErrors.actualTasks[id - 1][field];
        setValidationErrors(newErrors);
      }
    }
  };

  const addTask = () => {
    setIsAddTaskModalOpen(true);
  };

  if (
    getLoggedInUserAccomplishmentReportDataLoadingState ||
    isFetching ||
    postAddAccomplishmentReportServiceState?.loading ||
    destroyAccomplishmentReportState?.loading ||
    putUpdateAccomplishmentReportState?.loading
  ) {
    return (
      <div className="flex flex-1 w-full items-center justify-center">
        <Loader />
      </div>
    );
  }

  // for future use
  // const removeTargetTask = (id: number) => {
  //   if (formik.values.targetTasks.length > 1) {
  //     const updatedTasks = formik.values.targetTasks.filter((task) => task.id !== id).map((task, index) => ({ ...task, id: index + 1 }));
  //     formik.setFieldValue("targetTasks", updatedTasks);
  //   }
  // };

  const removeActualTask = (id: number) => {
    if (formik.values.actualTasks.length > 1) {
      const updatedTasks = formik.values.actualTasks.filter((task) => task.id !== id).map((task, index) => ({ ...task, id: index + 1 }));
      formik.setFieldValue("actualTasks", updatedTasks);
    }
  };
  // for future use
  // const addTargetRow = () => {
  //   const newTask: TargetTask = {
  //     id: formik.values.targetTasks.length + 1,
  //     ticketNo: "",
  //     taskType: "",
  //     hours: "",
  //     description: "",
  //   };
  //   formik.setFieldValue("targetTasks", [...formik.values.targetTasks, newTask]);
  // };

  const addActualRow = () => {
    const newTask: ActualTask = {
      id: formik.values.actualTasks.length + 1,
      ticketNo: "",
      hours: "",
      description: "",
      taskType: "",
      showBlockers: false,
      blockerDetails: { text: "", date: "", number: "" },
    };
    formik.setFieldValue("actualTasks", [...formik.values.actualTasks, newTask]);
  };

  const getTargetTaskGroupId = () => {
    // Return taskGroupId for "Target" (id: 1)
    return taskGroupsData?.find((group: any) => group.taskGroupCode === "Target")?.id || 1;
  };

  const getActualTaskGroupId = () => {
    // Return taskGroupId for "Actual" (id: 2)
    return taskGroupsData?.find((group: any) => group.taskGroupCode === "Actual")?.id || 2;
  };

  const handleDownloadAccomplishmentReport = async (accomplishmentReportId: number) => {
    try {
      const response: any = await exportAccomplishmentReportService(accomplishmentReportId);
      const pdfBlob = new Blob([response], { type: "application/pdf" });
      const url = window.URL.createObjectURL(pdfBlob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `Accomplishment-Report-${accomplishmentReportId}.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error: any) {
      toast.error(error);
    }
  };

  const handleDeleteAccomplishmentReport = async () => {
    if (!currentAccomplishmentId) return;
    const result = await confirmApproval("Accomplishment Report", "Are you sure you want to delete this accomplishment report?", undefined, "info");
    if (result?.isConfirmed) {
      try {
        // Wait for the deletion to complete
        destroyAccomplishmentReport({ accomplishmentReportId: currentAccomplishmentId });
        clearDeleteAccomplishmentReport();
        // Then refresh the data
        handleFetchData();
        // Reset the current ID and selected interval
        setCurrentAccomplishmentId(0);
        setSelectedDateInterval("");
      } catch (error: any) {
        toast.error("Failed to delete accomplishment report:", error);
        // Handle error (show toast, etc.)
      }
    }
  };

  const validateBeforeUpdate = async () => {
    try {
      await UpdateTaskFormSchema.validate(formik.values, { abortEarly: false });
      setValidationErrors({});
      return true;
    } catch (error: any) {
      const errors: any = {};

      if (error.inner) {
        error.inner.forEach((err: any) => {
          const path = err.path;
          if (path) {
            // Handle nested paths like targetTasks[0].ticketNo
            const pathParts = path.split(".");
            let current = errors;

            for (let i = 0; i < pathParts.length - 1; i++) {
              const part = pathParts[i];
              const match = part.match(/(\w+)\[(\d+)\]/);

              if (match) {
                const arrayName = match[1];
                const index = parseInt(match[2]);

                if (!current[arrayName]) current[arrayName] = {};
                if (!current[arrayName][index]) current[arrayName][index] = {};
                current = current[arrayName][index];
              } else {
                if (!current[part]) current[part] = {};
                current = current[part];
              }
            }

            const finalKey = pathParts[pathParts.length - 1];
            current[finalKey] = err.message;
          }
        });
      }

      setValidationErrors(errors);
      toast.error("Please fix validation errors before updating");
      return false;
    }
  };

  const getFieldError = (fieldPath: string) => {
    const pathParts = fieldPath.split(".");
    let current = validationErrors;

    for (const part of pathParts) {
      const match = part.match(/(\w+)\[(\d+)\]/);
      if (match) {
        const arrayName = match[1];
        const index = parseInt(match[2]);
        current = current?.[arrayName]?.[index];
      } else {
        current = current?.[part];
      }
    }

    return current;
  };

  const getInputClassName = (fieldPath: string, baseClass: string = "input input-sm w-full") => {
    const error = getFieldError(fieldPath);
    return error ? `${baseClass} input-error` : baseClass;
  };

  // Helper function to get select class with error styling
  const getSelectClassName = (fieldPath: string, baseClass: string = "select select-sm w-full") => {
    const error = getFieldError(fieldPath);
    return error ? `${baseClass} select-error` : baseClass;
  };

  return (
    <div className="card bg-base-100 w-full">
      <div className="card-body">
        <div className="text-center pb-6">
          <h1 className="text-xl font-bold">{user?.department?.departmentName?.toUpperCase()} EMPLOYEE WEEKLY TARGET AND ACCOMPLISHMENT REPORT</h1>
        </div>

        <form onSubmit={formik.handleSubmit} className="space-y-6">
          {/* Header Information */}
          <div className="space-y-4">
            <div className="form-control">
              <label className="label">
                <span className="label-text">Select Date Interval</span>
              </label>
              <select className="select select-bordered w-full" value={selectedDateInterval} onChange={(e) => handleDateIntervalChange(e.target.value)}>
                <option value="">Select Date Interval</option>
                {dateIntervalOptions.map((option: any, index: number) => (
                  <option key={index} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 pb-6 border-b">
            <div className="form-control">
              <label className="label">
                <span className="label-text">Period From:</span>
              </label>
              <input
                type="date"
                name="periodFrom"
                className={getInputClassName("periodFrom", "input input-bordered w-full")}
                value={formik.values.periodFrom}
                onChange={formik.handleChange}
                readOnly={!isEditable}
              />
              {getFieldError("periodFrom") && (
                <label className="label">
                  <span className="label-text-alt text-error">{getFieldError("periodFrom")}</span>
                </label>
              )}
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">Period To:</span>
              </label>
              <input
                type="date"
                name="periodTo"
                className={getInputClassName("periodTo", "input input-bordered w-full")}
                value={formik.values.periodTo}
                onChange={formik.handleChange}
                readOnly={!isEditable}
              />
              {getFieldError("periodTo") && (
                <label className="label">
                  <span className="label-text-alt text-error">{getFieldError("periodTo")}</span>
                </label>
              )}
            </div>
            <div className="space-y-4">
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Week</span>
                </label>
                <input
                  type="text"
                  name="weekNo"
                  className={getInputClassName("weekNo", "input input-bordered w-full")}
                  value={formik.values.weekNo}
                  onChange={formik.handleChange}
                  readOnly={!isEditable}
                />
                {getFieldError("weekNo") && (
                  <label className="label">
                    <span className="label-text-alt text-error">{getFieldError("weekNo")}</span>
                  </label>
                )}{" "}
              </div>
            </div>
            <div className="space-y-4">
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Month/Year</span>
                </label>
                <input type="text" name="monthYear" className={getInputClassName("monthYear", "input input-bordered w-full")} value={formik.values.monthYear} onChange={formik.handleChange} readOnly />
                {getFieldError("monthYear") && (
                  <label className="label">
                    <span className="label-text-alt text-error">{getFieldError("monthYear")}</span>
                  </label>
                )}{" "}
              </div>
            </div>
          </div>
          <div className="flex justify-end gap-3">
            {currentAccomplishmentId ? (
              <Button onClick={handleDeleteAccomplishmentReport} variant="danger" classNames="">
                <MdDeleteOutline />
              </Button>
            ) : null}
          </div>
          {/* Tasks Table */}

          {/* Tasks Table */}
          <div className="overflow-x-auto py-5">
            {currentAccomplishmentId ? (
              <div className="flex flex-col md:flex-row md:items-start">
                {/* Target Tasks Table */}
                <table className="table table-bordered w-full border border-base-300">
                  {/* Table Header */}
                  <thead>
                    <tr className="bg-base-100">
                      <th colSpan={4} className="text-center font-semibold border-r border-base-300">
                        TARGET TASKS
                      </th>
                    </tr>
                    <tr className="bg-base-200">
                      <th className="text-center text-sm font-medium border-r border-base-300">Ticket No</th>
                      <th className="text-center text-sm font-medium border-r border-base-300">Description</th>
                      <th className="text-center text-sm font-medium border-r border-base-300">TICKET TYPE</th>
                      <th className="text-center text-sm font-medium border-r border-base-300">
                        Target No of Hours
                        <br />
                        to Complete
                      </th>
                    </tr>
                  </thead>

                  {/* Target Task Rows */}
                  <tbody>
                    {formik.values.targetTasks && formik.values.targetTasks.length > 0 ? (
                      formik.values.targetTasks.map((task, index) => (
                        <React.Fragment key={task.id}>
                          <tr key={task.id}>
                            <td className="border-r border-base-300 p-2">
                              <div className="flex items-center">
                                <span className="text-sm font-medium mr-2">{index + 1}.</span>
                                <input
                                  type="text"
                                  className={getInputClassName(`targetTasks[${index}].ticketNo`)}
                                  value={task.ticketNo}
                                  onChange={(e) => updateTargetTask(task.id, "ticketNo", e.target.value)}
                                  placeholder="Ticket name"
                                  readOnly={!isEditable}
                                />
                              </div>
                              {getFieldError(`targetTasks.${index}.ticketNo`) && <span className="text-xs text-error mt-1">{getFieldError(`targetTasks.${index}.ticketNo`)}</span>}
                            </td>
                            <td className="border-r border-base-300 p-2">
                              <textarea
                                className={getInputClassName(`targetTasks[${index}].description`)}
                                value={task.description}
                                onChange={(e) => updateTargetTask(task.id, "description", e.target.value)}
                                placeholder="Task description"
                                readOnly={!isEditable}
                              />
                              {getFieldError(`targetTasks.${index}.description`) && <span className="text-xs text-error mt-1">{getFieldError(`targetTasks.${index}.description`)}</span>}
                            </td>
                            <td className="border-r border-base-300 p-2">
                              <select
                                disabled={!isEditable}
                                style={{ pointerEvents: isEditable ? "auto" : "none" }}
                                className={getSelectClassName(`targetTasks[${index}].taskType`)}
                                value={task.taskType}
                                onChange={(e) => updateTargetTask(task.id, "taskType", e.target.value)}
                              >
                                <option value="" disabled>
                                  Select type
                                </option>
                                {taskTypeOptions.map((type: { value: string; label: string }) => (
                                  <option key={type.value} value={type.value}>
                                    {type.label}
                                  </option>
                                ))}
                              </select>
                              {getFieldError(`targetTasks.${index}.taskType`) && <span className="text-xs text-error mt-1">{getFieldError(`targetTasks.${index}.taskType`)}</span>}
                            </td>
                            <td className="border-r border-base-300 p-2">
                              <div className="flex items-center gap-2">
                                <div className="flex flex-col">
                                  <input
                                    readOnly={!isEditable}
                                    type="number"
                                    className={getInputClassName(`targetTasks[${index}].hours`)}
                                    value={task.hours}
                                    onChange={(e) => updateTargetTask(task.id, "hours", e.target.value)}
                                    placeholder="0"
                                    min="0"
                                  />
                                  {getFieldError(`targetTasks.${index}.hours`) && <span className="text-xs text-error mt-1">{getFieldError(`targetTasks.${index}.hours`)}</span>}
                                </div>
                                <button
                                  onClick={() => updateTargetTask(task.id, "showBlockers", !task.showBlockers)}
                                  type="button"
                                  className="btn btn-sm btn-slate-300 btn-circle text-white"
                                  title="has Blockers?"
                                >
                                  <IoIosArrowDown size={12} className={`cursor-pointer text-black ${task.showBlockers ? "rotate-180" : ""}`} />
                                </button>
                                {/* {isEditable && (
                                <button type="button" className="btn btn-sm btn-error btn-circle" onClick={() => removeTargetTask(task.id)} title="Remove task">
                                  <IoCloseSharp color="#fff" />
                                </button>
                              )} */}
                              </div>
                            </td>
                          </tr>
                          {task.showBlockers && (
                            <tr>
                              <td colSpan={4} className="border border-base-300 p-2 bg-base-100">
                                <div className="flex gap-2 w-full">
                                  <textarea
                                    className="input input-sm flex-1 border border-base-300"
                                    value={task.blockerDetails?.text || ""}
                                    onChange={(e) => updateTargetTask(task.id, "blockerDetails", { ...task.blockerDetails, text: e.target.value })}
                                    placeholder="Blocker description"
                                    readOnly={!isEditable || isEditable}
                                  />
                                  <input
                                    type="date"
                                    className="input input-sm w-40 border border-base-300"
                                    value={task.blockerDetails?.date || ""}
                                    onChange={(e) => updateTargetTask(task.id, "blockerDetails", { ...task.blockerDetails, date: e.target.value })}
                                    readOnly={!isEditable || isEditable}
                                  />
                                  <input
                                    type="number"
                                    className="input input-sm w-24 border border-base-300"
                                    value={task.blockerDetails?.number || ""}
                                    onChange={(e) => updateTargetTask(task.id, "blockerDetails", { ...task.blockerDetails, number: e.target.value })}
                                    placeholder="hours"
                                    min="0"
                                    readOnly={!isEditable || isEditable}
                                  />
                                </div>
                              </td>
                            </tr>
                          )}
                        </React.Fragment>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={4} className="text-center p-4">
                          <span className="text-sm text-gray-500">No target tasks available</span>
                        </td>
                      </tr>
                    )}
                  </tbody>

                  {/* Add Row Button for Target Tasks */}
                  {/* {isEditable && (
                    <tfoot>
                      <tr>
                        <td colSpan={4} className="text-center p-2">
                          <button type="button" className="btn btn-sm btn-white" onClick={addTargetRow}>
                            <IoIosAddCircleOutline size={20} />
                          </button>
                        </td>
                      </tr>
                    </tfoot>
                  )} */}
                </table>
                {/* Actual Tasks Table */}
                <table className="table table-bordered w-full border border-base-300">
                  {/* Table Header */}
                  <thead>
                    <tr className="bg-base-100">
                      <th colSpan={4} className="text-center font-semibold">
                        ACTUAL ACCOMPLISHED TASKS
                      </th>
                    </tr>
                    <tr className="bg-base-200">
                      <th className="text-center text-sm font-medium border-r border-base-300">Actual Ticket No</th>
                      <th className="text-center text-sm font-medium border-r border-base-300">Description</th>
                      <th className="text-center text-sm font-medium border-r border-base-300">TICKET TYPE</th>
                      <th className="text-center text-sm font-medium">
                        Actual No of Hours
                        <br />
                        Completed
                      </th>
                    </tr>
                  </thead>

                  {/* Actual Task Rows */}
                  <tbody>
                    {formik.values.actualTasks && formik.values.actualTasks.length > 0 ? (
                      formik.values.actualTasks.map((task, index) => (
                        <React.Fragment key={task.id}>
                          <tr key={task.id}>
                            <td className="border-r border-base-300 p-2">
                              <div className="flex items-center">
                                <span className="text-sm font-medium mr-2">{index + 1}.</span>
                                <input
                                  type="text"
                                  className={getInputClassName(`actualTasks[${index}].ticketNo`)}
                                  value={task.ticketNo}
                                  onChange={(e) => updateActualTask(task.id, "ticketNo", e.target.value)}
                                  placeholder="Actual Ticket Identifier"
                                  readOnly={!isEditable}
                                />
                              </div>
                              {getFieldError(`actualTasks.${index}.ticketNo`) && <span className="text-xs text-error mt-1">{getFieldError(`actualTasks.${index}.ticketNo`)}</span>}
                            </td>
                            <td className="border-r border-base-300 p-2">
                              <textarea
                                className={getInputClassName(`actualTasks[${index}].description`)}
                                value={task.description}
                                onChange={(e) => updateActualTask(task.id, "description", e.target.value)}
                                placeholder="Task description"
                                readOnly={!isEditable}
                              />
                              {getFieldError(`actualTasks.${index}.description`) && <span className="text-xs text-error mt-1">{getFieldError(`actualTasks.${index}.description`)}</span>}
                            </td>
                            <td className="border-r border-base-300 p-2">
                              <select
                                disabled={!isEditable}
                                style={{ pointerEvents: isEditable ? "auto" : "none" }}
                                className={getSelectClassName(`actualTasks[${index}].taskType`)}
                                value={task.taskType}
                                onChange={(e) => updateActualTask(task.id, "taskType", e.target.value)}
                              >
                                <option value="" disabled>
                                  Select type
                                </option>
                                {taskTypeOptions.map((type: { value: string; label: string }) => (
                                  <option key={type.value} value={type.value}>
                                    {type.label}
                                  </option>
                                ))}
                              </select>
                              {getFieldError(`actualTasks.${index}.taskType`) && <span className="text-xs text-error mt-1">{getFieldError(`actualTasks.${index}.taskType`)}</span>}
                            </td>
                            <td className="p-2">
                              <div className="flex items-center gap-2">
                                <div className="flex flex-col">
                                  <input
                                    readOnly={!isEditable}
                                    type="number"
                                    className={getInputClassName(`actualTasks[${index}].hours`)}
                                    value={task.hours}
                                    onChange={(e) => updateActualTask(task.id, "hours", e.target.value)}
                                    placeholder="0"
                                    min="0"
                                  />
                                  {getFieldError(`actualTasks.${index}.hours`) && <span className="text-xs text-error mt-1">{getFieldError(`actualTasks.${index}.hours`)}</span>}
                                </div>
                                <button
                                  onClick={() => updateActualTask(task.id, "showBlockers", !task.showBlockers)}
                                  type="button"
                                  className="btn btn-sm btn-slate-300 btn-circle text-white"
                                  title="has Blockers?"
                                >
                                  <IoIosArrowDown size={12} className={`cursor-pointer text-black ${task.showBlockers ? "rotate-180" : ""}`} />
                                </button>
                                {isEditable && (
                                  <div className="flex items-center gap-1">
                                    <button type="button" className="btn btn-sm btn-error btn-circle" onClick={() => removeActualTask(task.id)} title="Remove task">
                                      <IoCloseSharp color="#fff" />
                                    </button>
                                  </div>
                                )}
                              </div>
                            </td>
                          </tr>
                          {task.showBlockers && (
                            <tr>
                              <td colSpan={4} className="border border-base-300 p-2 bg-base-100">
                                <div className="flex gap-2 w-full">
                                  <textarea
                                    className="input input-sm flex-1 border border-base-300"
                                    value={task.blockerDetails?.text || ""}
                                    onChange={(e) => updateActualTask(task.id, "blockerDetails", { ...task.blockerDetails, text: e.target.value })}
                                    placeholder="Blocker description"
                                    readOnly={!isEditable}
                                  />
                                  <input
                                    type="date"
                                    className="input input-sm w-40 border border-base-300"
                                    value={task.blockerDetails?.date || ""}
                                    onChange={(e) => updateActualTask(task.id, "blockerDetails", { ...task.blockerDetails, date: e.target.value })}
                                    readOnly={!isEditable}
                                  />
                                  <input
                                    type="number"
                                    className="input input-sm w-24 border border-base-300"
                                    value={task.blockerDetails?.number || ""}
                                    onChange={(e) => updateActualTask(task.id, "blockerDetails", { ...task.blockerDetails, number: e.target.value })}
                                    placeholder="hours"
                                    min="0"
                                    readOnly={!isEditable}
                                  />
                                </div>
                              </td>
                            </tr>
                          )}
                        </React.Fragment>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={4} className="text-center p-4">
                          <span className="text-sm text-gray-500">No actual tasks available</span>
                        </td>
                      </tr>
                    )}
                  </tbody>

                  {/* Add Row Button for Actual Tasks */}
                  {isEditable && (
                    <tfoot>
                      <tr>
                        <td colSpan={4} className="text-center p-2">
                          <button type="button" className="btn btn-sm btn-white" onClick={addActualRow}>
                            <IoIosAddCircleOutline size={20} onClick={addActualRow} />
                          </button>
                        </td>
                      </tr>
                    </tfoot>
                  )}
                </table>
              </div>
            ) : (
              <div className="flex flex-col justify-center items-center text-center p-8 bg-gray-50 rounded-lg border border-gray-200">
                <div className="mb-4">
                  <svg className="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Weekly Report Found</h3>
                </div>
                <Button onClick={addTask} classNames="bg-sky-600 hover:bg-sky-700 text-white text-sm px-4 py-2 rounded-md flex gap-2 items-center font-medium transition-colors">
                  <IoIosAddCircleOutline size={18} />
                  Create Weekly Report
                </Button>
              </div>
            )}
            {validationErrors.targetTasks && typeof validationErrors.targetTasks === "string" && (
              <div className="alert alert-error mt-4">
                <span className="text-white">{validationErrors.targetTasks}</span>
              </div>
            )}
            {validationErrors.actualTasks && typeof validationErrors.actualTasks === "string" && (
              <div className="alert alert-error mt-4">
                <span className="text-white">{validationErrors.actualTasks}</span>
              </div>
            )}
          </div>

          {/* Updated Action Buttons - Remove the general "Add Row" button */}
          {isEditable ? (
            <div className="flex justify-end pb-6 mt-4 gap-3">
              <Button onClick={() => setIsEditable(false)} classNames="bg-sky-600 hover:bg-sky-700 text-white text-sm w-fit flex gap-1 items-center">
                Cancel
              </Button>
              <Button onClick={handleUpdateReport} classNames="bg-sky-600 hover:bg-sky-700 text-white text-sm w-fit flex gap-1 items-center">
                Update Weekly Report
              </Button>
            </div>
          ) : (
            <div className="flex justify-end pb-6 mt-4 gap-3">
              {currentAccomplishmentId ? (
                <Button onClick={() => handleDownloadAccomplishmentReport(currentAccomplishmentId)} classNames="bg-sky-600 hover:bg-sky-700 text-white text-sm w-fit flex gap-1 items-center">
                  <MdPrint />
                  Print Report
                </Button>
              ) : null}
              {currentAccomplishmentId ? (
                <Button onClick={() => setIsEditable(true)} classNames="bg-sky-600 hover:bg-sky-700 text-white text-sm w-fit flex gap-1 items-center">
                  <TbEditCircle />
                  Edit Task
                </Button>
              ) : null}
              {currentAccomplishmentId ? (
                <Button classNames="bg-sky-600 hover:bg-sky-700 text-white text-sm w-fit flex gap-1 items-center" onClick={addTask}>
                  <IoIosAddCircleOutline />
                  Add Weekly Report
                </Button>
              ) : null}
            </div>
          )}

          {/* Action Buttons */}
        </form>
      </div>
      <AddTaskForm
        handleFetchData={handleFetchData}
        onCancel={() => setIsAddTaskModalOpen(false)}
        isOpen={isAddTaskModalOpen}
        weekNumber={getLoggedInUserAccomplishmentReportData?.[0]?.weekNumber + 1}
        periodFrom={getLoggedInUserAccomplishmentReportData?.[0]?.periodFrom}
        periodTo={getLoggedInUserAccomplishmentReportData?.[0]?.periodTo}
      />
    </div>
  );
}
