//This is for all of the necessary imports
import React, { ChangeEvent, useEffect, useState } from "react";
import Filter from "@components/common/Filter";
import TextField from "@components/form/TextField";
import { useDebouncedCallback } from "use-debounce";

//For the create button and other navigation stuff
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { getRolePath } from "@helpers/navigatorHelper";
// import { ROUTES } from "@constants/routes";

import { useNavigate } from "react-router-dom";

//For the icons
import { FaPlus, FaList, FaRegClock } from "react-icons/fa";
import { BsDownload } from "react-icons/bs";
import { GrDocumentText } from "react-icons/gr";

import Tabs from "@components/common/Tabs";
import Button from "@components/common/Button";
import { SharedRoutes } from "@enums/shared-routes";

// Will comment this out for the time being. Replacing this with the Product Proposal API
// import { useIssuanceActions } from "@state/reducer/master-policy";
import { getProductProposal } from "@state/reducer/product-proposal";
import IssuanceTable from "./components/Tables/IssuanceTable";

const Issuance: React.FC = () => {
  //For quick navigation
  const navigate = useNavigate();
  //For the filter modal
  const [searchText, setSearchText] = useState<string>("");
  const [dateFrom, setDateFrom] = useState<string>("");
  const [dateTo, setDateTo] = useState<string>("");
  const [_statusFilter, setStatusFilter] = useState<string | undefined>("");
  const [_typeFilter, setTypeFilter] = useState<number | undefined>(undefined);

  //For other components in the page
  //const { getIssuancesRequest } = useIssuanceActions();

  //* For navigation to the create issuance page
  const handleNavigation = () => {
    // TODO: Add create issuance route when functionality is implemented
    //navigate(ROUTES.UNDERWRITING.createIssuance.key);
    //handleRoleBasedNavigation(currentUser?.roles ?? [], navigate, "");
  };

  const handleSearch = useDebouncedCallback((event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchText(value);
  }, 500);

  //For the filter modal, mainly to clear all fields when the modal is closed
  const [resetCounter, setResetCounter] = useState(0);

  //TODO: Adjust the table modal to fit the current columns
  //Events for the filter modal
  const handleClearAll = () => {
    setSearchText("");
    setDateFrom("");
    setDateTo("");
    setStatusFilter(undefined);
    setTypeFilter(undefined);
    setResetCounter(resetCounter + 1);
  };

  const handleDateFromChange = (event: ChangeEvent<HTMLInputElement>) => {
    setDateFrom(event.target.value);
  };

  const handleDateToChange = (event: ChangeEvent<HTMLInputElement>) => {
    setDateTo(event.target.value);
  };

  useEffect(() => {
    //getIssuancesRequest({ filter: "", dateFrom: "", dateTo: "", statusFilter: "", productTypeFilter: undefined });
    getProductProposal({ filter: "", dateFrom: "", dateTo: "", statusFilter: "", productTypeFilter: undefined });
  }, []);

  //Tabs system
  const [currentTab, setCurrentTab] = useState(0);

  // Add this function to calculate tab counts
  // Adjust this based on the filters of the API
  const getTabCounts = () => {
    if (!issuanceProposals) return { all: 0, pending: 0, issued: 0 };

    const pending = issuanceProposals.filter((p: any) => p.status === "Pending").length;
    const issued = issuanceProposals.filter((p: any) => p.status === "Issued").length;

    return {
      all: issuanceProposals.length,
      pending,
      issued,
    };
  };

  // Get the selector for issuance proposals
  const issuanceProposals = useSelector((state: RootState) => state.productProposal.productProposals);

  // Update headers to use calculated counts
  const tabCounts = getTabCounts();
  const headers = [
    { label: "All", icon: FaList, count: tabCounts.all },
    { label: "Pending", icon: FaRegClock, count: tabCounts.pending }, //, unread: tabCounts.pending > 0
    { label: "Waiting for first remittance", icon: GrDocumentText, count: tabCounts.issued }, //, unread: tabCounts.issued > 0
  ];
  const contents = [<></>, <></>, <></>]; // Empty contents
  //const [showMobileMenu, setShowMobileMenu] = useState(false); //Mobile variables

  const headerClass = "text-sm pb-2";
  const contentClass = "w-full !p-0 !border-0";

  return (
    <>
      {/* Header text */}
      <div className="flex flex-col gap-2 my-4">
        <div className="text-xl font-semibold">For Issuance</div>
        <div className="text-sm">Manage and monitor all policies set for issuance in this section, with complete details and status updates for each record.</div>
      </div>

      {/* Tabs */}
      {/*TODO: Adjust the tabs system so that we can add the number of records in the tab */}
      <Tabs headers={headers} contents={contents} fullWidthHeader={false} onTabChange={setCurrentTab} headerClass={headerClass} contentClass={contentClass} fullWidthBar={true} />

      {/* Filter and buttons */}
      <div className="flex flex-row justify-between mt-4">
        <Filter search={searchText} onChange={handleSearch}>
          <div className="flex justify-end">
            <button className="text-primary text-xs btn-sm" type="button" onClick={handleClearAll}>
              Clear All
            </button>
          </div>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <div className="text-xs">Date From</div>
              <TextField className="" type="date" size="sm" value={dateFrom} onChange={handleDateFromChange} />
            </div>
            <div>
              <div className="text-xs">Date To</div>
              <TextField className="" type="date" size="sm" value={dateTo} onChange={handleDateToChange} />
            </div>
          </div>
        </Filter>

        <Button
          classNames="text-zinc-950 border border-zinc-200 btn rounded-l btn-sm"
          onClick={() => {
            navigate(getRolePath(SharedRoutes.REQUEST_FORM));
          }}
        >
          <BsDownload />
        </Button>

        {/* Create button */}
        <div className="w-full flex flex-1 items-center">
          <div className="flex flex-1 flex-row justify-end items-center gap-x-2">
            <button
              className="btn btn-sm bg-primary text-white min-w-20 min-h-6 p-2 pb-5"
              onClick={() => {
                handleNavigation();
              }}
            >
              <FaPlus />
              Add
            </button>
          </div>
        </div>
      </div>

      {/* Table */}
      <IssuanceTable searchText={searchText} dateFrom={dateFrom} dateTo={dateTo} currentTab={currentTab} />
    </>
  );
};

export default Issuance;
