import React from "react";
import { TableColumn } from "react-data-table-component";
import Table from "@components/common/Table";

const ProductProvisions: React.FC = () => {
  // State for form inputs
  const [formData] = React.useState({
    policyProductGroupNo: "",
    productGroupName: "",
    productGroupOptionName: "",
    productLongName: "",
    productNumber: "",
    gracePeriod: "30 days",
    periodRenewal: "1 year",
    contestabilityUnit: "Fetched from proposal",
    contestabilityPeriod: "1 year",
    reinstatementPeriod: "2 - 3 years",
    reinstatementPaymentCount: "Fetched from proposal",
    minimumMemberParticipation: "Fetched from proposal",
  });

  // Handle input changes
  // const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const { name, value } = e.target;
  //   setFormData((prev) => ({ ...prev, [name]: value }));
  // };

  // Table columns definition
  const columns: TableColumn<any>[] = [
    { name: "Policy Product Group No.", selector: (row) => row.policyProductGroupNo, sortable: true },
    { name: "Product Group Name", selector: (row) => row.productGroupName, sortable: true },
    { name: "Product Long Name", selector: (row) => row.productLongName, sortable: true },
    { name: "Product Number", selector: (row) => row.productNumber, sortable: true },
  ];

  // Table data
  const data = [
    {
      policyProductGroupNo: formData.policyProductGroupNo || "1",
      productGroupName: formData.productGroupName || "CLPP",
      productLongName: formData.productLongName || "Coop Loan Protection Plan",
      productNumber: formData.productNumber || "0001",
    },
  ];

  return (
    <div className="max-w-[1400px] p-6">
      <h2 className="text-base font-bold mb-4">POLICY PRODUCT GROUP</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Policy Product Group No.</legend>
          <input type="text" className="input input-bordered text-center" placeholder="Auto-generated" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Policy Group Name</legend>
          <input type="text" className="input input-bordered text-center" placeholder="Auto-generated" readOnly />
        </fieldset>{" "}
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Policy Group Option Name</legend>
          <input type="text" className="input input-bordered text-center" placeholder="Auto-generated" readOnly />
        </fieldset>
      </div>

      <Table selectable={false} columns={columns} data={data} searchable={false} pagination={false} fixHeader={false} className="mb-6" />

      <div className="divider my-4"></div>

      <h2 className="text-base font-bold mb-4">POLICY PROVISION</h2>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Grace Period</legend>
          <input type="text" className="input input-bordered text-center" placeholder="30 days" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Period Renewal</legend>
          <input type="text" className="input input-bordered text-center" placeholder="1 year" readOnly />
        </fieldset>{" "}
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Contestability Unit</legend>
          <input type="text" className="input input-bordered text-center" placeholder="Fetched from proposal" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Contestability Period</legend>
          <input type="text" className="input input-bordered text-center" placeholder="1 year" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Reinstated Period</legend>
          <input type="text" className="input input-bordered text-center" placeholder="2 - 3 years" readOnly />
        </fieldset>{" "}
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Reinstatement Payment Count</legend>
          <input type="text" className="input input-bordered text-center" placeholder="Fetched from proposal" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Minimum Member Participation</legend>
          <input type="text" className="input input-bordered text-center" placeholder="Fetched from proposal" readOnly />
        </fieldset>
      </div>

      <div className="divider my-4"></div>

      <h2 className="text-base font-bold mb-4">UNDERWRITING PROVISIONS</h2>

      <div className="flex md:flex-row flex-col gap-4">
        <div className="flex flex-col overflow-x-auto">
          <label className="font-regular text-xs mb-2">Non - Medical Limit</label>
          <Table selectable={false} columns={columns} data={data} searchable={false} pagination={false} fixHeader={false} className="mb-6" />
        </div>
        <div className="flex flex-col overflow-x-auto">
          <label className="font-regular text-xs mb-2">No Evidence Limit</label>
          <Table selectable={false} columns={columns} data={data} searchable={false} pagination={false} fixHeader={false} className="mb-6" />
        </div>
      </div>
    </div>
  );
};

export default ProductProvisions;
