import { ReactNode, useState } from "react";
import Tabs from "@components/common/Tabs";
import DependentRates from "./component/DependentRates";
import ProductRateTab from "./component/ProductRates";
import { IoInformationCircle } from "react-icons/io5";
import CoverageBrackets from "../../modals/CoverageBrackets";

export default function ProductRates() {
  const [viewCoverageBrackets, setViewCoverageBrackets] = useState<boolean>(false);
  let headers: string[] = ["Product Rates", "Dependent Rates"];
  let contents: ReactNode[] = [<ProductRateTab />, <DependentRates />];
  return (
    <div className=" w-full">
      <div className="relative">
        <Tabs
          headers={headers}
          headerClass="min-h-[60px] py-4 px-6 text-sm"
          contentClass="min-h-[390px] mt-[14px] border border-zinc-300"
          contents={contents}
          fullWidthHeader={false}
          activeTabClassName="text-primary"
          inActiveTabClassName="text-gray"
        />
        <p
          onClick={() => setViewCoverageBrackets(true)}
          className="absolute cursor-pointer top-2 right-0 flex items-center gap-1 mt-4 text-xs text-primary font-regular whitespace-nowrap hover:underline"
        >
          <IoInformationCircle /> View Coverage Brackets
        </p>
      </div>
      <CoverageBrackets isOpen={viewCoverageBrackets} onClose={() => setViewCoverageBrackets(false)} />
    </div>
  );
}
