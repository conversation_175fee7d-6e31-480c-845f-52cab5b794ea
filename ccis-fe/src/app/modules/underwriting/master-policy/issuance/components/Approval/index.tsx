//Normal Imports
import { FC, ReactNode, useEffect, useState } from "react";
import Tabs from "@components/common/Tabs";
import Button from "@components/common/Button";
import { IoChevronBack } from "react-icons/io5";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import Loader from "@components/Loader";
import { toast } from "react-toastify";
// import { ROUTES } from "@constants/routes";
import { CustomizeType, ProposableTypes } from "@enums/enums";
// import { Statuses } from "@constants/global-constant-value";

//Locations to the pages
import ProductProposal from "@modules/sales/product-proposal/Approval/ProductProposal";
import PartnershipAgreement from "@modules/sales/product-proposal/Approval/PartnershipAgreement";
import CommissionStructure from "@modules/sales/product-proposal/Approval/CommissionStructure";
import Requirements from "@modules/sales/product-proposal/Approval/Requirements";
import ProductProposalAER from "@modules/sales/product-proposal/Approval/ProductProposalAer";
import MasterPolicy from "@modules/underwriting/master-policy/issuance/components/Approval/MasterPolicy";

//Actual API calls
//For the User Roles
// import { UserRoles } from "@interface/routes.interface";
//* Permissions so that you can only view
// import { canCreateProductProposal } from "@helpers/product-proposal/product-proposal-permissions";
//For the get services
import { getProductProposalByIDService } from "@services/proposal/proposal.service";

//For the interfaces
//import { IIssuanceInterface } from "@interface/master-policy.interface";
import { IProductProposal } from "@interface/product-proposal.interface";
//For the proposal status, may need to delete.
import { ProposalStatus } from "@enums/proposal-status";

const IssuanceViewProposal: FC = () => {
  const navigate = useNavigate();
  const params = useParams();
  const location = useLocation();

  const [data, setData] = useState<IProductProposal>();

  const [loading, setLoading] = useState<boolean>(true);

  // const isRequirementsValid = (data?.status ?? "").toUpperCase() === Statuses.VALID;

  // const isCommissionStructureApproved = (data?.status ?? "").toUpperCase() === Statuses.APPROVED;

  //Change the API for getting the data by ID
  const fetchData = async () => {
    try {
      if (location.state?.proposableType === CustomizeType.AER) {
        const { data } = await getProductProposalByIDService(Number(params.id));
        setData(data);
      } else {
        const { data } = await getProductProposalByIDService(Number(params.id));
        setData(data);
      }
      setLoading(false);
    } catch (error: any) {
      toast.error("Error details:", error);
      toast.error("Error fetching product proposal");
    }
  };
  useEffect(() => {
    fetchData();
  }, []);

  //TODO: Modify the headers depending on the role, would need to ask
  const headers = [
    {
      label: "Product Proposal",
      disabled: false,
    },
    {
      label: "Requirements",
      disabled: data?.status === ProposalStatus.rejected,
    },
    {
      label: "Commission Structure",
      //disabled: data?.status === ProposalStatus.rejected || !isRequirementsValid,
      disabled: data?.status === ProposalStatus.rejected,
    },
    {
      label: "Partnership Agreement",
      //disabled: data?.status === ProposalStatus.rejected || !isCommissionStructureApproved,
      disabled: data?.status === ProposalStatus.rejected,
    },
    {
      label: "Master Policy",
      disabled: data?.status === ProposalStatus.rejected,
    },
  ];

  //TODO: Modify the contents depending on the role, would need to ask
  const contents: ReactNode[] = [
    //Condition where if it's not create permissions, don't show buttons.

    location.state?.proposal?.proposableType === ProposableTypes.AER ? <ProductProposalAER data={data} /> : <ProductProposal data={data} />,
    <Requirements data={data} onSuccess={fetchData} />,
    <CommissionStructure data={data} onSuccess={fetchData} />,
    <PartnershipAgreement data={data} />,
    <MasterPolicy data={data} />,
  ];

  //Change this depending on the roles, would need to ask
  const handleBack = () => {
    navigate(-1);
    // if (location.pathname.includes(UserRoles.sales)) {
    //   navigate(ROUTES.SALES.productProposal.key);
    //   return;
    // }
    // if (location.pathname.includes(UserRoles.rnd)) {
    //   navigate(ROUTES.RESEARCHANDDEVELOPMENT.productProposal.key);
    //   return;
    // }
    // if (location.pathname.includes(UserRoles.actuary)) {
    //   navigate(ROUTES.ACTUARY.productProposal.key);
    //   return;
    // }
    // if (location.pathname.includes(UserRoles.marketing)) {
    //   navigate(ROUTES.MARKETING.productProposal.key);
    //   return;
    // } else {
    //   navigate(ROUTES.ADMIN.productProposal.key);
    // }
  };

  return (
    <div className="w-full">
      {" "}
      {loading && (
        <div>
          <Loader />
        </div>
      )}
      {!loading && (
        <>
          <div className=" w-full mb-2">
            <Button classNames="flex items-center justify-center border-none" type="button" variant="primary" outline onClick={handleBack}>
              <IoChevronBack />
              Back
            </Button>
          </div>
          <div className="w-full">
            <Tabs
              headers={headers}
              contents={contents}
              size="sm"
              headerClass="min-h-14"
              activeTabClassName="bg-primary text-white"
              inActiveTabClassName="bg-primary4"
              contentClass="min-h-[48rem] "
              className="w-full"
            />
          </div>
        </>
      )}
    </div>
  );
};

export default IssuanceViewProposal;
