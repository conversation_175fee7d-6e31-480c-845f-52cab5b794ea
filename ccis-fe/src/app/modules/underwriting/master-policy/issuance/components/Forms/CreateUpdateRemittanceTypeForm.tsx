// Copy the content from the remittance-type form
// ccis-fe/src/app/modules/admin/remittances-utilities/remittance-type/components/Forms/CreateUpdateRemittanceTypeForm.tsx
// and paste it here for your master policy module
// Important imports
import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import TextField from "@components/form/TextField";
import { Form, FormikProvider, useFormik } from "formik";
import React, { useEffect } from "react";

// Importing the Remittance Type from the utilities interface 
import { capitalizeFirstLetterWords } from "@helpers/text";
import { IUtilitiesRemittanceType } from "@interface/utilities.interface";

// Schema to be used for both create and edit
import {
  CreateRemittanceTypeSchema,
  EditRemittanceTypeSchema,
} from "@services/utilities-remittance-type/utilities-remittance-type.schema";
// Toast for the in-page notifications
import { toast } from "react-toastify";

// Setting up the Modal Props for the Remittance Type
type RemittanceTypeModalProps = {
  isOpen: boolean;
  isEditMode: boolean;
  initialValues: IUtilitiesRemittanceType;
  onClose: () => void;
  onSubmits: (values: IUtilitiesRemittanceType) => void;
};

// All modal props would be corresponded to a function
const CreateUpdateRemittanceTypeForm: React.FC<RemittanceTypeModalProps> = ({
  isOpen,
  isEditMode,
  initialValues,
  onClose,
  onSubmits,
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const formattedValue = capitalizeFirstLetterWords(value, " ");
    formik.setFieldValue(name, formattedValue);
  };
  //For Data Validation
  const formik = useFormik({
    initialValues,
    enableReinitialize: true, // <-- Ensure form is reset when initialValues change
    validationSchema: isEditMode
      ? EditRemittanceTypeSchema
      : CreateRemittanceTypeSchema,
    onSubmit: async (values, { resetForm }) => {
      try {
        await onSubmits(values);
        resetForm();
        onClose();
      } catch (error) {
        toast.error(`Error submitting form: ${(error as any).message}`);
      }
    },
  });

  useEffect(() => {
    if (isEditMode) {
      formik.setValues(initialValues);
    }
  }, [initialValues, isEditMode]);

  return (
    <Modal
      title={
        isEditMode 
        ? "Edit Remittance Type" 
        : "Create New Remittancce Type"
      }
      modalContainerClassName="max-w-3xl"
      titleClass="text-primary text-lg uppercase"
      isOpen={isOpen}
      onClose={onClose}
    >
      <FormikProvider value={formik}>
        <Form className="min-w-full my-4">
          <div className="flex-none w-full my-4">
            <label>Remittance Type Code</label>
            <TextField
              name="remittanceTypeCode"
              placeholder="Enter Remittance Type Code"
              max={8}
              min={2}
              type="text"
              className="bg-white"
              error={
                formik.touched.remittanceTypeCode && 
                !!formik.errors.remittanceTypeCode
              }
              errorText={formik.errors.remittanceTypeCode}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.remittanceTypeCode}
              required
            />
          </div>
          <div className="flex-none w-full my-4">
            <label>Remittance Type Name</label>
            <TextField
              name="remittanceTypeName"
              placeholder="Enter Remittance Type Code Name"
              max={255}
              type="text"
              className="bg-white"
              error={
                formik.touched.remittanceTypeName && !!formik.errors.remittanceTypeName
              }
              errorText={formik.errors.remittanceTypeName}
              onChange={handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.remittanceTypeName}
              required
            />
          </div>
          <div className="flex-none w-full my-4">
            <label>Description</label>
            <TextField
              name="description"
              type="text"
              placeholder="Enter Description"
              max={255}
              className="bg-white"
              error={formik.touched.description && !!formik.errors.description}
              errorText={formik.errors.description}
              onChange={handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.description}
              required
            />
          </div>
          <div className="flex w-full mt-6">
            <Button
              type="submit"
              variant="primary"
              classNames="w-full justify-center btn rounded-xl"
            >
              {isEditMode ? "Save Changes" : "Submit"}
            </Button>
          </div>
        </Form>
      </FormikProvider>
    </Modal>
  );
};

export default CreateUpdateRemittanceTypeForm;
