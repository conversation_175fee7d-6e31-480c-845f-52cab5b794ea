import Button from "@components/common/Button";
import { ReactNode, useState } from "react";
import { IoChevronBack } from "react-icons/io5";
import { useNavigate } from "react-router-dom";
import ProductProvisions from "./Tabs/ProductProvisions";
import ProductOption from "./Tabs/ProductOption";
import ProductRates from "./Tabs/ProductRates";
import BenefitStructure from "./Tabs/BenefitStructure";
import { FaRegEdit } from "react-icons/fa";
import PolicyHeader from "./component/PolicyHeader";
import { TbBinaryTree2, TbStack3 } from "react-icons/tb";
import { TfiStamp } from "react-icons/tfi";
import Tabs, { TTabHeader } from "./common/Tabs";

export default function IssuanceViewPolicy() {
  const headers: TTabHeader[] = [
    { label: "PRODUCT PROVISION", icon: FaRegEdit },
    { label: "PRODUCT OPTION", icon: TbStack3 },
    { label: "PRODUCT RATES", icon: TfiStamp },
    { label: "BENEFIT STRUCTURE", icon: TbBinaryTree2 },
  ];
  const contents: ReactNode[] = [<ProductProvisions />, <ProductOption />, <ProductRates />, <BenefitStructure />];
  const navigate = useNavigate();

  // State to manage the active tab index
  const [activeTabIndex, setActiveTabIndex] = useState(0); // Default to first tab (ProductProvisions)

  // Handle Back button (for tabs)
  const handleTabBack = () => {
    if (activeTabIndex > 0) {
      setActiveTabIndex(activeTabIndex - 1);
    }
  };

  // Handle Next button
  const handleNext = () => {
    if (activeTabIndex < headers.length - 1) {
      setActiveTabIndex(activeTabIndex + 1);
    }
  };

  // Handle Back button (for navigation)
  const handleBack = () => {
    navigate(-1);
  };

  return (
    <div className="w-full">
      <div className="mb-2">
        <Button classNames="flex items-center justify-center border-none" type="button" variant="primary" outline onClick={handleBack}>
          <IoChevronBack />
          Back
        </Button>
      </div>

      <div className="flex flex-col gap-6">
        <PolicyHeader />

        <Tabs
          headers={headers}
          contents={contents}
          size="sm"
          headerClass="min-h-14"
          activeTabClassName="bg-primary4 text-white"
          inActiveTabClassName="bg-primary text-white p-3"
          contentClass="min-h-[48rem] border border-none"
          controlTab={activeTabIndex} // Changed from activeTabIndex to controlTab
          onTabChange={setActiveTabIndex} // Handle tab changes
          className=""
        />
      </div>

      <div className="fixed bottom-6 right-6">
        <div className="flex items-center gap-3">
          <button
            className="flex items-center justify-center bg-default hover:bg-default-dark px-4 py-2 text-black text-sm rounded-lg"
            onClick={handleTabBack}
            disabled={activeTabIndex === 0} // Disable when on first tab
          >
            Back
          </button>
          <button
            className="flex items-center justify-center bg-primary hover:bg-primary-dark px-4 py-2 text-white text-sm rounded-lg"
            onClick={handleNext}
            disabled={activeTabIndex === headers.length - 1} // Disable when on last tab
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
}
