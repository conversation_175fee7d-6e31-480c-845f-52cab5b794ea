//Normal imports
import { FC, Fragment, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { TableColumn } from "react-data-table-component";
import { RootState } from "@state/store";
import { IActions } from "@interface/common.interface";
import Table from "@components/common/Table";
import ActionDropdown from "@components/common/ActionDropdown";
import { formatDate } from "@helpers/date";
import Typography from "@components/common/Typography";
import { formatStringAtoZ0to9 } from "@helpers/text";
import { useNavigate } from "react-router-dom";
import { ROUTES } from "@constants/routes";
// import { confirmArchive, confirmDelete, showSuccess } from "@helpers/prompt";
// import { toast } from "react-toastify";
import { GoVersions } from "react-icons/go";
import { extractFirstPathSegment } from "@helpers/navigatorHelper";

//For the icons
// import { CiEdit } from "react-icons/ci";
import { Fa<PERSON><PERSON><PERSON>, Fa<PERSON><PERSON>ing, FaShieldAlt } from "react-icons/fa";

//switch the apis to the current dummy data we use
// import { ProposableTypes, ProposalTypes } from "@enums/enums";
//import { useIssuanceActions } from "@state/reducer/master-policy";
import { useProductProposalActions } from "@state/reducer/product-proposal";
import { IProductProposal } from "@interface/product-proposal.interface";
// import { IIssuanceInterface } from "@interface/master-policy.interface";
import { MdOutlinePolicy } from "react-icons/md";
//import { IIssuanceInterface } from "@interface/master-policy.interface";
// import { deleteIssuanceService } from "@services/master-policy/master-policy.service";
// import { deleteProductProposalService } from "@services/product-proposal/product-proposal.service";

interface IssuanceTableProps {
  searchText?: string;
  dateFrom?: string;
  dateTo?: string;
  currentTab?: number;
}

const IssuanceTable: FC<IssuanceTableProps> = ({ searchText, dateFrom, dateTo, currentTab = 0 }) => {
  const navigate = useNavigate();
  const [page, setPage] = useState<number>(1);
  const [rows, setRows] = useState<number>(10);

  const issuanceProposals = useSelector((state: RootState) => state.productProposal.productProposals);
  const { getProductProposal } = useProductProposalActions();

  //For the different selectors and states
  const responseData = useSelector((state: RootState) => state.productProposal.getProductProposal?.data?.data);
  const loading = useSelector((state: RootState) => state.productProposal.getProductProposal?.loading);

  const [deleting, _setDeleting] = useState<boolean>(false);

  //For URL formating
  const [pathBase, setPathBase] = useState<string>("");

  //For extracting the first part URL, determining the role
  useEffect(() => {
    const firstSegment = extractFirstPathSegment(window.location.pathname, 0);
    if (firstSegment) {
      setPathBase(formatStringAtoZ0to9(firstSegment).toUpperCase());
    }
  }, []);

  //For filtering stuff when switching tabs
  const getFilteredData = () => {
    switch (currentTab) {
      case 1:
        return issuanceProposals?.filter((p: any) => p.status === "Pending") || [];
      case 2:
        return issuanceProposals?.filter((p: any) => p.status === "Issued") || [];
      default:
        return issuanceProposals;
    }
  };

  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  //Redundant text extraction, may delete this soon
  useEffect(() => {
    const firstSegment = extractFirstPathSegment(window.location.pathname, 0);
    if (firstSegment) {
      setPathBase(formatStringAtoZ0to9(firstSegment).toUpperCase());
    }
  }, []);

  //For the actions when a button is pressed
  const getActionEvents = (_issuanceProposal: IProductProposal): IActions<IProductProposal>[] => {
    const actions: IActions<IProductProposal>[] = [
      {
        name: "View",
        event: (data: IProductProposal) => {
          navigate((ROUTES[pathBase as keyof typeof ROUTES] as any)?.issuanceViewProposal.parse(data?.id ?? ""), {
            state: { proposal: data },
          });
        },
        icon: GoVersions,
        color: "primary",
      },
      {
        name: "View Policy",
        event: (data: IProductProposal) => {
          navigate((ROUTES[pathBase as keyof typeof ROUTES] as any)?.issuanceViewPolicy.parse(data?.id ?? ""), {
            state: { proposal: data },
          });
        },
        icon: MdOutlinePolicy,
        color: "primary",
      },
    ];

    return actions;
  };

  const columns: TableColumn<IProductProposal>[] = [
    // Cooperative Column
    {
      name: "Cooperative",
      cell: (row) => (
        <div className="flex flex-col gap-1 py-2">
          <div className="text-xs">CC-{row?.cooperativeId ?? "0000"}</div>
          <div className="flex items-center gap-2 text-xs text-zinc-400">
            <FaBuilding className="text-gray-400 w-3 h-3 flex-shrink-0" />
            <span>{row?.cooperative?.name ?? "Not Set"}</span>
            <span>•</span>
            <span>{row?.cooperative?.branchName ?? "Not Set"}</span>
          </div>
        </div>
      ),
      ...commonSetting,
      width: "300px",
    },
    // Product Proposal Column
    {
      name: "Product Proposal",
      cell: (row) => (
        <div className="flex flex-col gap-1 py-2">
          <div className="text-xs">{row.proposableId ?? "Not Set"}</div>
          <div className="flex items-center gap-2 text-xs text-zinc-400">
            <FaShieldAlt className="text-gray-400 w-3 h-3 flex-shrink-0" />
            <span>{row?.product?.name ?? "Not Set"}</span>
          </div>
        </div>
      ),
      ...commonSetting,
      width: "250px",
    },
    // Date Column
    {
      name: "Date",
      cell: (row) => <div className="text-xs">{formatDate(row?.createdAt, "MMM dd, yyyy")}</div>,
      ...commonSetting,
      width: "120px",
    },
    // Actions Column
    {
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => {
        return (
          <div className="flex flex-1 flex-row justify-center items-center gap-x-2">
            {deleting && <FaSpinner className="animate-spin" />}
            {!deleting && <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />}
          </div>
        );
      },
      width: "80px",
    },
  ];

  const fetchProposals = () => {
    getProductProposal({
      filter: searchText,
      dateFrom: dateFrom,
      dateTo: dateTo,
      page,
      pageSize: rows,
    });
  };

  // const handleDelete = async (proposal: IProductProposal) => {
  //   try {
  //     const confirm = await confirmDelete(`proposal for ${proposal?.cooperative?.coopName ?? "Not Set"}`);
  //     if (confirm.isConfirmed) {
  //       setDeleting(true);
  //       const { data } = await deleteProductProposalService(proposal.id);
  //       if (data) {
  //         showSuccess("Success", "Issuance proposal has been deleted successfully");
  //         fetchProposals();
  //       }
  //     }
  //   } catch (error: any) {
  //     toast.error(error?.response?.data?.message);
  //   } finally {
  //     setDeleting(false);
  //   }
  // };

  // const handleArchive = async (proposal: IProductProposal) => {
  //   try {
  //     const confirm = await confirmArchive(`proposal for ${proposal?.cooperative?.coopName ?? "Not Set"}`);
  //     if (confirm.isConfirmed) {
  //       setDeleting(true);
  //       const { data } = await deleteProductProposalService(proposal.id); // need to verify for API
  //       if (data) {
  //         showSuccess("Success", "Issuance proposal has been archived successfully");
  //         fetchProposals();
  //       }
  //     }
  //   } catch (error: any) {
  //     toast.error(error?.response?.data?.message);
  //   } finally {
  //     setDeleting(false);
  //   }
  // };

  const handlePaginate = (pagination: number) => {
    setPage(pagination);
  };

  const handleRowsChange = (rowsPerPage: number, pagination: number) => {
    setRows(rowsPerPage);
    setPage(pagination);
  };

  useEffect(() => {
    fetchProposals();
  }, [searchText, dateFrom, dateTo, page, rows]);

  return (
    <Fragment>
      <div className="flex mt-4 rounded-lg overflow-hidden border-[1px] border-zinc-300 shadow-md">
        <Table
          className="h-[600px]"
          columns={columns}
          //Get data here
          data={getFilteredData()}
          loading={loading}
          searchable={false}
          multiSelect={false}
          paginationTotalRows={responseData?.meta?.total}
          paginationServer={true}
          onPaginate={handlePaginate}
          onChangeRowsPerPage={handleRowsChange}
        />
      </div>
    </Fragment>
  );
};

export default IssuanceTable;
