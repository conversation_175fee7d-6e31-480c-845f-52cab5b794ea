import React from "react";
// import Typography from "@components/common/Typography";
import empty_img from "@assets/folder-empty.svg";

type Props = {
  data?: any;
  canCreateProposal?: boolean;
};

const MasterPolicy: React.FC<Props> = () => {
  return (
    <div className="mt-10">
      <div className="flex flex-col items-center justify-center w-full gap-3">
        <img src={empty_img} alt="empty" className="h-[25%] w-[25%] m-5" />

        <span className="font-semibold ">No Master Policy Folders to Show</span>
        <span className="text-[#474747] w-1/2 text-center">Generate a policy number first. When it's activated, your master policy documents will appear here.</span>
        <button className="bg-primary text-white px-6 py-2 hover:bg-primary-dark my-5">Generate Policy Number</button>
      </div>
    </div>
  );
};

export default MasterPolicy;
