import { TfiSave } from "react-icons/tfi";
import { VscDiffAdded } from "react-icons/vsc";

export default function ProductOption() {
  return (
    <div className="mb-6 p-6">
      <h2 className="text-base font-bold mb-4">PRODUCT OPTION</h2>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Product Option No.</legend>
          <input type="text" className="input input-bordered text-center" placeholder="Auto - generated" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Option Name</legend>
          <input type="text" className="input input-bordered text-center" placeholder="Rate 1.10 with Rider" readOnly />
        </fieldset>{" "}
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Effective Date</legend>
          <input type="text" className="input input-bordered text-center" placeholder="01/31/2025" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">End of Effective Date</legend>
          <input type="text" className="input input-bordered text-center" placeholder="01/31/2026" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Basic Benefit</legend>
          <input type="text" className="input input-bordered text-center" placeholder="1 Life Insurance" readOnly />
        </fieldset>{" "}
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Principal Sum</legend>
          <input type="text" className="input input-bordered text-center" placeholder="100,000" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Divisor</legend>
          <input type="text" className="input input-bordered text-center" placeholder="400" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Age Lower Limit</legend>
          <input type="text" className="input input-bordered text-center" placeholder="18" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Mode of Payment</legend>
          <input type="text" className="input input-bordered text-center" placeholder="Annual" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Duration Unit</legend>
          <input type="text" className="input input-bordered text-center" placeholder="Semi - Annual" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-1">
          <div className="flex items-center gap-1">
            <input type="checkbox" className="checkbox checkbox-primary size-5" readOnly />
            <legend className="fieldset-legend font-regular text-xs">With Rider?</legend>
          </div>
          <input type="text" className="input input-bordered text-center" placeholder="Auto - generated" readOnly />
        </fieldset>
      </div>

      <div className="w-full flex justify-end">
        <button className="btn btn-sm gap-1 bg-white hover:bg-slate-200 text-secondary min-w-20 min-h-6 p-2 pb-5 text-xs mr-2">
          <VscDiffAdded className="rounded-xs" />
          Add New
        </button>
        <button className="btn btn-sm bg-success hover:bg-success-dark text-white min-w-20 min-h-6 text-xs">
          <TfiSave />
          Save
        </button>
      </div>
    </div>
  );
}
