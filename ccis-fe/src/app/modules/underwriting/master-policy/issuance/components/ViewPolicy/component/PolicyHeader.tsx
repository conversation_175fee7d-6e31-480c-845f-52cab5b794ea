import { IActions } from "@interface/common.interface";
import { useState } from "react";
import { FaCodeBranch, FaEllipsisV, FaInfoCircle } from "react-icons/fa";
import ProposalInfo from "../modals/ProposalInfo";
import SelectBranch from "../modals/SelectBranch";

export default function PolicyHeader() {
  const currentId = 1;
  const [viewPolicyInfoModal, setViewPolicyInfoModal] = useState<boolean>(false);
  const [viewSelectBranchModal, setViewSelectBranchModal] = useState<boolean>(false);
  const getActionEvents = (id: number): IActions<any>[] => {
    const actions: IActions<any>[] = [
      {
        name: "Policy Info",
        event: () => {
          handlePolicyInfo(id);
        },
        icon: FaInfoCircle,
        color: "primary",
      },
      {
        name: "Select Branch",
        event: () => {
          handleSelectBranch(id);
        },
        icon: FaCodeBranch,
        color: "secondary",
      },
    ];

    return actions;
  };
  const [formData] = useState({
    dateCreated: "01/25/2024",
    policyNumber: "LPP-1001",
    productCode: "001",
    product: "CLPP",
    productDescription: "Coop Loan Protection Plan",
    mainCode: "0102",
    coopCode: "001",
    coopName: "Oro Integrated Cooperative",
    coopBranch: "Cagayan de Oro City",
    typeOfCoverage: "Level",
    withRebates: true,
  });
  // @ts-ignore
  const handlePolicyInfo = (id: number) => {
    // Your policy info logic here
    setViewPolicyInfoModal(true);
  };
  // @ts-ignore
  const handleSelectBranch = (id: number) => {
    // Your select branch logic here
    setViewSelectBranchModal(true);
  };
  return (
    <div className="w-full bg-white border border-[#E7E7EC] rounded p-6">
      {/* Header */}
      <div className="flex justify-between items-center w-full">
        <h1 className="text-2xl font-bold text-secondary-custom">POLICY HEADER</h1>
        <div className="divider"></div>
        <div className="dropdown dropdown-end">
          <div tabIndex={0} role="button" className="btn btn-ghost btn-sm">
            <FaEllipsisV />
          </div>
          <ul tabIndex={0} className="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
            {getActionEvents(currentId).map((action, index) => {
              const IconComponent = action.icon;
              return (
                <li key={index}>
                  <a
                    onClick={(e) => {
                      e.preventDefault();
                      action.event({ id: currentId }, index);
                    }}
                    className="flex items-center gap-3"
                  >
                    <IconComponent />
                    {action.name}
                  </a>
                </li>
              );
            })}
          </ul>
        </div>
      </div>

      {/* Form Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6">
        {/* Date Created */}
        <div className="form-control">
          <label className="font-bold">
            <span className=" text-md text-slate-700">Date Created</span>
          </label>
          <div className="text-secondary-custom text-sm">{formData.dateCreated}</div>
        </div>

        {/* Policy Number */}
        <div className="form-control">
          <label className="font-bold">
            <span className=" text-md text-slate-700">Policy Number</span>
          </label>
          <div className="flex items-center gap-2">
            <div className="text-secondary-custom text-sm flex-1">{formData.policyNumber}</div>
            <div className="badge badge-warning text-xs px-2 py-1">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mr-1"></div>
              Not yet active
            </div>
          </div>
        </div>

        {/* Product Code */}
        <div className="form-control">
          <label className="font-bold">
            <span className=" text-md text-slate-700">Product Code</span>
          </label>
          <div className="text-secondary-custom text-sm">{formData.productCode}</div>
        </div>

        {/* Product */}
        <div className="form-control">
          <label className="font-bold">
            <span className=" text-md text-slate-700">Product</span>
          </label>
          <div className="text-secondary-custom text-sm">{formData.product}</div>
        </div>

        {/* Coop Code */}
        <div className="form-control">
          <label className="font-bold">
            <span className=" text-md text-slate-700">Coop Code</span>
          </label>
          <div className="text-secondary-custom text-sm">{formData.coopCode}</div>
        </div>

        {/* Coop Name */}
        <div className="form-control">
          <label className="font-bold">
            <span className=" text-md text-slate-700">Coop Name</span>
          </label>
          <div className="text-secondary-custom text-sm">{formData.coopName}</div>
        </div>

        {/* Coop Branch */}
        <div className="form-control">
          <label className="font-bold">
            <span className=" text-md text-slate-700">Coop Branch</span>
          </label>
          <div className="text-secondary-custom text-sm">{formData.coopBranch}</div>
        </div>

        {/* Type of Coverage */}
        <div className="form-control">
          <label className="font-bold">
            <span className=" text-md text-slate-700">Type of Coverage</span>
          </label>
          <div className="text-secondary-custom text-sm">{formData.typeOfCoverage}</div>
        </div>

        {/* Product Description */}
        <div className="form-control lg:col-span-2">
          <label className="font-bold">
            <span className=" text-md text-slate-700">Product Description</span>
          </label>
          <div className="text-secondary-custom text-sm">{formData.productDescription}</div>
        </div>

        {/* Main Code */}
        <div className="form-control">
          <label className="font-bold">
            <span className=" text-md text-slate-700">Main Code</span>
          </label>
          <div className="text-secondary-custom text-sm">{formData.mainCode}</div>
        </div>

        {/* With Rebates */}
        <div className="form-control">
          <label className="font-bold">
            <span className=" text-md text-slate-700">With Rebates</span>
          </label>
          <div className="flex items-center pt-2">
            <input type="checkbox" checked={formData.withRebates} className="checkbox checkbox-primary" readOnly />
          </div>
        </div>
      </div>
      <ProposalInfo isOpen={viewPolicyInfoModal} onClose={() => setViewPolicyInfoModal(false)} />
      <SelectBranch isOpen={viewSelectBranchModal} onClose={() => setViewSelectBranchModal(false)} />
    </div>
  );
}
