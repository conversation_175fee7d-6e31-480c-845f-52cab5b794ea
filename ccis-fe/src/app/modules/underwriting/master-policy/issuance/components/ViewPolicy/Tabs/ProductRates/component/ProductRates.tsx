import { ImC<PERSON><PERSON><PERSON> } from "react-icons/im";
import { VscDiffAdded } from "react-icons/vsc";

export default function ProductRates() {
  return (
    <div className="mb-6 p-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 ">
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Product Rate No.</legend>
          <input type="text" className="input input-bordered text-center" placeholder="Auto - generated" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Product Option No.</legend>
          <input type="text" className="input input-bordered text-center" placeholder="Rate 1.10 with Rider" readOnly />
        </fieldset>{" "}
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Effective Date</legend>
          <input type="text" className="input input-bordered text-center" placeholder="01/31/2025" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">End of Effective Date</legend>
          <input type="text" className="input input-bordered text-center" placeholder="01/31/2026" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Term</legend>
          <div className="flex">
            <input type="text" className="w-1/2 input input-bordered text-center" placeholder="1" readOnly />
            <div className="flex items-center mx-2 text-slate-400">-</div>
            <input type="text" className="w-1/2 input input-bordered text-center" placeholder="5" readOnly />
          </div>
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Gross Rate</legend>
          <input type="text" className="input input-bordered text-center" placeholder="Auto - generated" readOnly />
        </fieldset>{" "}
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Divisor</legend>
          <input type="text" className="input input-bordered text-center" placeholder="Auto - generated" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Mode of Payment</legend>
          <input type="text" className="input input-bordered text-center" placeholder="Annual" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Duration Unit</legend>
          <input type="text" className="input input-bordered text-center" placeholder="Quarterly" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Age Range</legend>
          <div className="flex">
            <input type="text" className="w-1/2 input input-bordered text-center" placeholder="18" readOnly />
            <div className="flex items-center mx-2 text-slate-400">-</div>
            <input type="text" className="w-1/2 input input-bordered text-center" placeholder="65" readOnly />
          </div>
        </fieldset>{" "}
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Exit Age</legend>
          <input type="text" className="input input-bordered text-center" placeholder="70" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Management Fee</legend>
          <input type="text" className="input input-bordered text-center" placeholder="Fetched from proposal" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Maximum Coverage</legend>
          <input type="text" className="input input-bordered text-center" placeholder="100,000" readOnly />
        </fieldset>
      </div>
      <div className="flex justify-between items-center w-full mt-6">
        <button className="btn btn-sm bg-primary hover:bg-primary-dark text-white min-w-20 min-h-6 p-2 pb-5 text-xs mr-2">
          <ImCalculator />
          Premium Calculator
        </button>
        <button className="btn btn-sm gap-1 bg-white hover:bg-slate-200 text-secondary min-w-20 min-h-6 p-2 pb-5 text-xs mr-2">
          <VscDiffAdded className="rounded-xs" />
          Add New
        </button>
      </div>
    </div>
  );
}
