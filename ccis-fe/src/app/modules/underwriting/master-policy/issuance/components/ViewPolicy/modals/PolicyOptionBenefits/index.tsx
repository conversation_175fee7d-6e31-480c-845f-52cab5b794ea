import Modal from "@components/common/Modal";

export default function ViewPolicyOptionBenefits({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  return (
    <Modal title="Policy Option Benefits" modalContainerClassName="max-w-4xl" titleClass="text-primary text-lg uppercase" isOpen={isOpen} onClose={onClose}>
      <>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-6">
          <fieldset className="fieldset flex flex-col gap-2">
            <legend className="fieldset-legend font-regular text-xs mb-2">Policy Product Option Benefit No.</legend>
            <input type="text" className="input input-bordered text-start" placeholder="01" readOnly />
          </fieldset>
          <fieldset className="fieldset flex flex-col gap-2">
            <legend className="fieldset-legend font-regular text-xs mb-2">Policy Benefit Structure No.</legend>
            <input type="text" className="input input-bordered text-start" placeholder="1 year" readOnly />
          </fieldset>{" "}
          <fieldset className="fieldset flex flex-col gap-2">
            <legend className="fieldset-legend font-regular text-xs mb-2">Benefit Name</legend>
            <input type="text" className="input input-bordered text-start" placeholder="Life Insurance" readOnly />
          </fieldset>
          <fieldset className="fieldset flex flex-col gap-2">
            <legend className="fieldset-legend font-regular text-xs mb-2">Effective Date</legend>
            <input type="text" className="input input-bordered text-start" placeholder="2023-01-15" readOnly />
          </fieldset>
          <fieldset className="fieldset flex flex-col gap-2">
            <legend className="fieldset-legend font-regular text-xs mb-2">End of Effective Date</legend>
            <input type="text" className="input input-bordered text-start" placeholder="2023-01-15" readOnly />
          </fieldset>{" "}
          <fieldset className="fieldset flex flex-col gap-2">
            <legend className="fieldset-legend font-regular text-xs mb-2">Insured Type</legend>
            <input type="text" className="input input-bordered text-start" placeholder="Spouse" readOnly />
          </fieldset>
          <fieldset className="fieldset flex flex-col gap-2">
            <legend className="fieldset-legend font-regular text-xs mb-2">Number of Claim</legend>
            <input type="text" className="input input-bordered text-start" placeholder="65" readOnly />
          </fieldset>
          <fieldset className="fieldset flex flex-col gap-2">
            <legend className="fieldset-legend font-regular text-xs mb-2">Coverage</legend>
            <input type="text" className="input input-bordered text-start" placeholder="100,000" readOnly />
          </fieldset>
          <fieldset className="fieldset flex flex-col gap-2">
            <legend className="fieldset-legend font-regular text-xs mb-2">Age Lower Limit</legend>
            <input type="text" className="input input-bordered text-start" placeholder="18" readOnly />
          </fieldset>
          <fieldset className="fieldset flex flex-col gap-2">
            <legend className="fieldset-legend font-regular text-xs mb-2">Age Upper Age Limit</legend>
            <input type="text" className="input input-bordered text-start" placeholder="18" readOnly />
          </fieldset>
          <fieldset className="fieldset flex flex-col gap-2">
            <legend className="fieldset-legend font-regular text-xs mb-2">Exit Age</legend>
            <input type="text" className="input input-bordered text-start" placeholder="70" readOnly />
          </fieldset>
          <fieldset className="fieldset flex flex-col gap-2">
            <legend className="fieldset-legend font-regular text-xs mb-2">Contestability Period</legend>
            <input type="text" className="input input-bordered text-start" placeholder="2 years" readOnly />
          </fieldset>
        </div>
      </>
    </Modal>
  );
}
