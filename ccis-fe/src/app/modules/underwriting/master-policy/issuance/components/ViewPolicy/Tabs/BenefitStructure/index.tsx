import ActionDropdown from "@components/common/ActionDropdown";
import Table from "@components/common/Table";
import Typography from "@components/common/Typography";
import { commonSetting } from "@components/template/TableColumns";
import { formatDate } from "@helpers/date";
import { IActions } from "@interface/common.interface";
import { IIssuanceInterface } from "@interface/master-policy.interface";
import { useState } from "react";
import { TableColumn } from "react-data-table-component";
import { FaSpinner } from "react-icons/fa";
import { GoVersions } from "react-icons/go";
import { MdOutlineFileDownload } from "react-icons/md";
import { TfiSave } from "react-icons/tfi";
import ViewPolicyOptionBenefits from "../../modals/PolicyOptionBenefits";

const benefitStructureData: any[] = [
  {
    id: "1",
    policyNumber: "PBS-2025-001",
    proposalNumber: "PPO-001",
    date: "2025-06-01T00:00:00Z",
  },
  {
    id: "2",
    policyNumber: "PBS-2025-002",
    proposalNumber: "PPO-002",
    date: "2025-06-01T00:00:00Z",
  },
  {
    id: "3",
    policyNumber: "PBS-2025-003",
    proposalNumber: "PPO-003",
    date: "2025-07-15T00:00:00Z",
  },
  {
    id: "4",
    policyNumber: "PBS-2025-004",
    proposalNumber: "PPO-004",
    date: "2025-08-01T00:00:00Z",
  },
  {
    id: "5",
    policyNumber: "PBS-2025-005",
    proposalNumber: "PPO-005",
    date: "2025-09-01T00:00:00Z",
  },
  {
    id: "6",
    policyNumber: "PBS-2025-006",
    proposalNumber: "PPO-006",
    date: "2025-10-15T00:00:00Z",
  },
  {
    id: "7",
    policyNumber: "PBS-2025-007",
    proposalNumber: "PPO-007",
    date: "2025-11-01T00:00:00Z",
  },
  {
    id: "8",
    policyNumber: "PBS-2025-008",
    proposalNumber: "PPO-008",
    date: "2025-12-01T00:00:00Z",
  },
];

export default function BenefitStructure() {
  const [deleting] = useState<boolean>(false);

  // const navigate = useNavigate();
  // const [pathBase, setPathBase] = useState<string>("");
  const [viewPolicyOptionBenefitsModal, setViewPolicyOptionBenefitsModal] = useState<boolean>(false);

  //What the heck does this do?
  // useEffect(() => {
  //   const firstSegment = extractFirstPathSegment(window.location.pathname, 0);
  //   if (firstSegment) {
  //     setPathBase(formatStringAtoZ0to9(firstSegment).toUpperCase());
  //   }
  // }, []);
  // @ts-ignore
  const getActionEvents = (id: number): IActions<IIssuanceInterface>[] => {
    const actions: IActions<IIssuanceInterface>[] = [
      {
        name: "View Policy Option Benefits",
        event: () => {
          setViewPolicyOptionBenefitsModal(true);
        },
        icon: GoVersions,
        color: "primary",
      },
    ];

    return actions;
  };
  const columns: TableColumn<any>[] = [
    {
      name: "Policy Benefit Structure No.",
      cell: (row) => row.policyNumber ?? "----",
      ...commonSetting,
    },
    {
      name: "Policy Product Option No.",
      cell: (row) => row.proposalNumber ?? "Not Set",
      ...commonSetting,
    },

    {
      name: "Effective Date",
      cell: (row) => formatDate(row?.date, "d MMMM yyyy"),
      ...commonSetting,
    },
    {
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => {
        return (
          <div className="flex flex-1 flex-row justify-center items-center gap-x-2">
            {deleting && <FaSpinner className="animate-spin" />}
            {!deleting && <ActionDropdown actions={getActionEvents(row.id)} data={row} rowIndex={rowIndex} />}
          </div>
        );
      },
    },
  ];
  return (
    <div className="flex flex-col gap-6 mb-6 p-6">
      <h2 className="text-base font-bold mb-4">BENEFIT STRUCTURE</h2>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 ">
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Product Rate No.</legend>
          <input type="text" className="input input-bordered text-center" placeholder="Auto - generated" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Product Option No.</legend>
          <input type="text" className="input input-bordered text-center" placeholder="Auto - generated" readOnly />
        </fieldset>{" "}
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">Effective Date</legend>
          <input type="text" className="input input-bordered text-center" placeholder="01/31/2025" readOnly />
        </fieldset>
        <fieldset className="fieldset flex flex-col gap-2">
          <legend className="fieldset-legend font-regular text-xs mb-2">End of Effective Date</legend>
          <input type="text" className="input input-bordered text-center" placeholder="01/31/2026" readOnly />
        </fieldset>
      </div>
      <div className="w-full flex justify-end">
        <button className="btn btn-sm gap-1 bg-white hover:bg-slate-200 text-secondary min-w-20 min-h-6 p-2 pb-5 text-xs mr-2">
          <MdOutlineFileDownload size={16} className="rounded-xs" />
          Import
        </button>
        <button className="btn btn-sm bg-success hover:bg-success-dark text-white min-w-20 min-h-6 text-xs">
          <TfiSave />
          Save
        </button>
      </div>

      <div className="flex mt-4 rounded-lg overflow-hidden border-[1px] border-zinc-300 shadow-md">
        <Table
          className="min-h-[200px]"
          columns={columns}
          data={benefitStructureData}
          // loading={loading}
          selectable={false}
          searchable={false}
          multiSelect={false}
          // paginationTotalRows={responseData?.meta?.total}
          // paginationServer={true}
          // onPaginate={handlePaginate}
          // onChangeRowsPerPage={handleRowsChange}
        />
      </div>

      <ViewPolicyOptionBenefits isOpen={viewPolicyOptionBenefitsModal} onClose={() => setViewPolicyOptionBenefitsModal(false)} />
    </div>
  );
}
