import Typography from "@components/common/Typography";
import { FormStatus } from "@enums/form-status";
import { capitalizeFirstLetterOnly, capitalizeFirstLetterWords, getTextStatusColor } from "@helpers/text";
import { TableColumn } from "react-data-table-component";
import Table from "@components/common/Table";
import { IGamPadRequest } from "@interface/gam-request-pads";

interface RequestPadTableProps {
  data: IGamPadRequest[]; // You should replace `any` with a proper interface for row data
  loading: boolean;
  totalCount: number;
  onChangeRowsPerPage: (newPerPage: number, page: number) => void;
  onPaginate: (page: number) => void;
}

const RequestPadTable = ({ data, loading, totalCount, onChangeRowsPerPage, onPaginate }: RequestPadTableProps) => {
  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const columns: TableColumn<IGamPadRequest>[] = [
    {
      name: "No.",
      cell: (row) => row.id,
      width: "80px",
      ...commonSetting,
    },
    {
      name: "Request No.",
      cell: (row) => row.id,
      ...commonSetting,
    },
    {
      name: "Request Date",
      cell: (row) =>
        row?.createdAt
          ? new Date(row?.createdAt).toLocaleDateString("en-US", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
            })
          : "",
      ...commonSetting,
    },
    {
      name: "Series From",
      cell: (row) => row.seriesFrom,
      ...commonSetting,
    },
    {
      name: "Series To",
      cell: (row) => row.seriesTo,
      ...commonSetting,
    },
    {
      name: "No. of Pads",
      cell: (row) => row.numberOfPads,
      ...commonSetting,
    },
    {
      name: "Status",
      cell: (row) => (
        <Typography size="xs" className={getTextStatusColor(row.status ?? "")}>
          {row.status === FormStatus.APPROVED ? capitalizeFirstLetterOnly(FormStatus.NOT_YET_RECEIVED.replace(/_/g, " ")) : capitalizeFirstLetterWords(row.status ?? "", "_")}
        </Typography>
      ),
    },
  ];

  return (
    <div>
      <Table
        className="!min-h-[100%] h-[500px] border-[1px] border-zinc-300 mt-8"
        columns={columns}
        data={data}
        searchable={false}
        multiSelect={false}
        paginationTotalRows={totalCount}
        paginationServer={true}
        loading={loading}
        onChangeRowsPerPage={onChangeRowsPerPage}
        onPaginate={onPaginate}
      />
    </div>
  );
};

export default RequestPadTable;
