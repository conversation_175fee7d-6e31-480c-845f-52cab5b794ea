import React, { useState } from "react";
import Modal from "@components/common/Modal";
import {
  FaFilePdf,
  FaFileArchive,
  FaImage,
} from "react-icons/fa";

interface AttachmentItemProps {
  fileName: string;
  filePath: string;
  width?: string; // Add optional width property
}

const AttachmentItem: React.FC<AttachmentItemProps> = ({
  fileName,
  filePath,
  width = "w-60", // Default width if not provided
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <div>
      <div
        className={`flex gap-4 p-2 items-center ${width} bg-slate-100 rounded-md ${
          fileName === "No Attachment Found"
            ? "cursor-not-allowed opacity-50"
            : "hover:bg-slate-200 cursor-pointer"
        }`}
        onClick={
          fileName !== "No Attachment Found" ? handleOpenModal : undefined
        }
      >
        <span className="flex gap-2">
          <span className="p-2 bg-zinc-50 rounded-lg border border-slate-300">
            {fileName === "No Attachment Found" ? (
              <FaFileArchive className="text-slate-500" />
            ) : filePath.match(/\.(jpeg|jpg|gif|png)$/) ? (
              <FaImage className="text-primary" />
            ) : (
              <FaFilePdf className="text-red-500" />
            )}
          </span>
          <p className="text-xs my-auto">{fileName}</p>
        </span>
      </div>

      <Modal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title="Attachment Preview"
        className="w-3/4 mx-auto"
      >
        <div className="flex justify-end"></div>
        {filePath.match(/\.(jpeg|jpg|gif|png)$/) ? (
          <img
            src={filePath}
            alt={fileName}
            className="w-full h-[500px] object-contain border border-zinc-300 rounded-md"
          />
        ) : (
          <iframe
            src={filePath}
            className="w-full h-[800px] border border-zinc-300 rounded-md"
            title={fileName}
          />
        )}
      </Modal>
    </div>
  );
};

export default AttachmentItem;
