import React, { useEffect } from "react";
import Tabs from "@components/common/Tabs";
import AllTab from "./tabs/AllTab";
import ForReceivingTab from "./tabs/ForReceivingTab";
import ReturnTab from "./tabs/ReturnTab";
import Released from "./tabs/TransmittalTab";
import { clearData, hasKey } from "@helpers/storage";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { useAreaActions } from "@state/reducer/utilities-areas";

const ForPrReceiving: React.FC = () => {
  const userRole = useSelector((state: RootState) => state?.auth?.user.data?.areaId);
  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const { getAreas } = useAreaActions();

  const areaName = area.find((a) => a.id === userRole)?.areaName;

  useEffect(() => {
    getAreas({ filter: "" });
  }, []);

  // Define different tab configurations based on role
  const getTabConfig = () => {
    switch (areaName) {
      case "HEADQUARTER":
        return {
          headers: ["All", "For Receiving", "Return"],
          contents: [<AllTab />, <ForReceivingTab />, <ReturnTab />],
          breadcrumb: "SI|OR|PR FORMS",
        };
      default:
        return {
          headers: ["For Receiving", "Released", "Return SI/OR"],
          contents: [<ForReceivingTab />, <Released />, <ReturnTab />],
          breadcrumb: "SI | OR FORMS",
        };
    }
  };

  const tabConfig = getTabConfig();

  useEffect(() => {
    if (hasKey("currentPad")) {
      clearData("currentPad");
    }
  }, []);

  return (
    <div>
      <div className="my-4 py-4 border-b border-zinc-300 w-full text-zinc-500">
        Dashboard / <span className="text-primary font-poppins-semibold">{tabConfig.breadcrumb}</span>
      </div>
      <Tabs headers={tabConfig.headers} contents={tabConfig.contents} size="md" headerClass="w-52" fullWidthHeader={false} />
    </div>
  );
};

export default ForPrReceiving;
