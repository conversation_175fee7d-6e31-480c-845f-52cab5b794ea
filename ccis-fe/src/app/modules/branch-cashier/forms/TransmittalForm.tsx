import Button from "@components/common/Button";
import Typography from "@components/common/Typography";
import TextField from "@components/form/TextField";
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import Loader from "@components/Loader";
import { Form, FormikProvider, useFormik } from "formik";
import { confirmSaveOrEdit } from "@helpers/prompt";
import { toast } from "react-toastify";
import { FormStatus, RoleType } from "@enums/form-status";
import { usePositionsManagementActions } from "@state/reducer/utilities-positions";
import { useUserManagementActions } from "@state/reducer/users-management";
import { navigateBack } from "@helpers/navigatorHelper";
import { findItem, formatSelectOptions } from "@helpers/array";
import { releasedVia } from "@constants/global-constant-value";
import Select from "@components/form/Select";
import { useReleasedMethodActions } from "@state/reducer/form-inventory-utilities-released-methods";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { TFormTransmittalOutgoingPayload } from "@state/types/form-inventory-transmittal";
import { formatStringAtoZ0to9 } from "@helpers/text";
import { ReturnTransmittalValidationSchema } from "@services/form-inventory-transmittal/form-inventory-transmittal.schema";
import { useAreaAdminActions } from "@state/reducer/form-inventory-utilities-area-admins";
// import { UserArea } from "@enums/users-management";
import { Combobox } from "@components/common-v2/Combobox";
import { useFetchWithParams } from "@hooks/useFetchWithParams";
import { useDebouncedSearch } from "@hooks/useDebouncedSearch";

const ViewTransmittalForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [formContainers, setFormContainers] = useState<any[]>([]);
  const [postTrailData, setPostTrailData] = useState<TFormTransmittalOutgoingPayload>({
    id: 0,
    releasedTo: 0,
    status: FormStatus.RELEASED,
    releasedMethodId: undefined,
    deliveredBy: "",
    trackingNo: "",
  });
  const [releasedMethodText, setReleasedMethodText] = useState<string>("");

  // Get data from Redux state
  const formsResponse = useSelector((state: RootState) => state.formInventoryTransmittal.getTransmittalForm);
  const data = formsResponse?.data || null;
  const formsLoading = formsResponse?.loading || false;
  const formsError = formsResponse?.error || false;

  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const formTypes = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const areas = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const areaAdmins = useSelector((state: RootState) => state.formInventoryUtilitiesAreaAdmins.areaAdmins);
  const ReleasedMethod = useSelector((state: RootState) => state.formInventoryUtilitiesReleasedMethods.releasedMethods);
  const users = useSelector((state: RootState) => state.usersManagement.users);

  // Filter out CLIFSA and HEADQUARTER areas
  // const filteredAreas = areas.filter((area) => area.areaName?.toUpperCase() !== UserArea.CLIFSA && area.areaName?.toUpperCase() !== UserArea.HEADQUARTER);

  const filterDivision = "";
  const filterFormType = "";
  const filterArea = "";
  const filterPosition = "";
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();

  const { getAreas } = useAreaActions();
  const { getUsers } = useUserManagementActions();
  const { getAreaAdmins } = useAreaAdminActions();
  const { getPosition } = usePositionsManagementActions();
  const { getReleasedMethods } = useReleasedMethodActions();
  const { getTransmittalForm, postFormTransmittalTrail, clearSelectedTransmittalForm } = useTransmittalFormActions();

  const userArea = useSelector((state: RootState) => state?.auth?.user.data?.areaId);
  // const areaName = areas.find((a) => a.id === userArea)?.areaName;

  const profile = useSelector((state: RootState) => state.profile.profile);

  const postTrailSuccess = useSelector((state: RootState) => state.formInventoryTransmittal.postFormTransmittalTrail?.success);

  // const areaUsers = useSelector((state: RootState) => state.usersManagement.users);
  // const loadingUsers = useSelector((state: RootState) => state.usersManagement.loading);
  // const [localLoadingUsers, setLocalLoadingUsers] = useState(false);

  // const fetchUsersForArea = async (areaId: number) => {
  //   if (!areaId) return;

  //   setLocalLoadingUsers(true);
  //   try {
  //     await getUsers({
  //       filter: "",
  //       areaFilter: areaId,
  //       type: RoleType.CAC,
  //     });
  //   } finally {
  //     setLocalLoadingUsers(false);
  //   }
  // };

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: postTrailData,

    validationSchema: ReturnTransmittalValidationSchema,
    onSubmit: async (values, { resetForm }) => {
      const isConfirmed = await confirmSaveOrEdit("Are you sure you want to release this transmittal?");

      if (isConfirmed) {
        try {
          if (!id) {
            toast.error("Missing form transmittal ID.");
            return;
          }

          await postFormTransmittalTrail(values as TFormTransmittalOutgoingPayload);
          resetForm();
        } catch (error) {
          toast.error("Failed to release transmittal. Please try again.");
        }
      }
    },
  });

  const fetchForm = () => {
    if (id) {
      getTransmittalForm({ id: Number(id) });
    }
  };

  useEffect(() => {
    if (postTrailSuccess) {
      navigateBack();
      clearSelectedTransmittalForm();
    }
  }, [postTrailSuccess]);

  useEffect(() => {
    fetchForm();
  }, [id]);

  const { value: searchUser, handleChange: handleSearchUser } = useDebouncedSearch();

  // Handle error state
  useEffect(() => {
    if (formsError) {
      toast.error("Failed to load proposal data. Please try again later.");
    }
  }, [formsError]);

  //Fetches already selected form
  useEffect(() => {
    if (data && !formContainers.some((f) => f.id === data.id)) {
      setFormContainers((prev) => [...prev, data]);
    }
  }, [data]);

  useEffect(() => {
    getDivisions({ filter: filterDivision });
    getFormTypes({ filter: filterFormType });
    getAreas({ filter: filterArea });
    getReleasedMethods({ filter: "" });
    getAreaAdmins({ filter: "" });
  }, []);

  useEffect(() => {
    getPosition({ params: { filter: filterPosition } });
  }, []);

  const releasedViaOption = formatSelectOptions(ReleasedMethod, "releasedMethodName");
  const defaultOption = {
    value: "",
    text: "Please select a value",
    disabled: !formik.values.releasedMethodId ? false : true,
  };

  const releasedViaOptionOptionsWithDefault = [defaultOption, ...releasedViaOption];

  const handleSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedValue = event.target.value;
    const selectedText = event.target.options[event.target.selectedIndex].text;
    const initialHandedBy = profile ? `${profile.firstname || "N/A"} ${profile.middlename || ""} ${profile.lastname || ""}`.trim() : "N/A";
    const conditionReleasedVia = selectedText === releasedVia.thirdPartyPerson || selectedText === releasedVia.onHand;

    if (selectedValue === "") {
      return;
    }

    const updatedValue = Number(selectedValue);

    setPostTrailData((prevData) => ({
      ...prevData,
      releasedMethodId: updatedValue,
      deliveredBy: conditionReleasedVia ? initialHandedBy : "",
      releasedTo: formik.values.releasedTo,
      trackingNo: conditionReleasedVia ? " " : "",
      id: Number(data?.id),
    }));

    // Update formik values
    formik.setFieldValue("releasedMethodId", updatedValue);
    formik.setFieldValue("deliveredBy", conditionReleasedVia ? initialHandedBy : "");

    setReleasedMethodText(selectedText);
  };

  const handleTextFieldChange = (name: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    // Update both formik and postTrailData state
    formik.setFieldValue(`postTrailData.${name}`, value);
    setPostTrailData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  useFetchWithParams(getUsers, { nameFilter: searchUser, role: RoleType.CAC, areaFilter: userArea }, [searchUser], false);

  return formsLoading ? (
    <div className="flex justify-center items-center h-screen">
      <Loader />
    </div>
  ) : (
    <div>
      <div>
        <Button classNames="btn bg-slate-600 btn-sm" onClick={() => navigateBack()}>
          Back
        </Button>
        <div className="mx-6">
          <Typography className="mt-6 text-primary uppercase font-poppins-semibold-">Transmittal Details</Typography>
          <div className=" mt-8">
            <FormikProvider value={formik}>
              <Form>
                <div className="flex w-full flex-col">
                  <div className="divider divider-start uppercase">Assignee Details</div>
                </div>
                <div>
                  <div className="p-6">
                    <div className="grid grid-cols-3 gap-4">
                      <div className="p-2 flex-1">
                        <p className="text-sm">Released by</p>
                        <div className="w-60 text-sm">
                          <TextField disabled className="w-auto" size="sm" value={profile ? `${profile.firstname || "N/A"} ${profile.middlename || ""} ${profile.lastname || ""}`.trim() : "N/A"} />
                        </div>
                      </div>
                      <div className="p-2 flex-1">
                        <p className="text-sm">Released To *</p>
                        {/* <CustomTextField
                      suggestionOptions={users}
                      getOptionLabel={(u) => `${u.firstname} ${u.lastname}`}
                      getOptionValue={(u) => u.id} // Return the actual ID, not string
                      name="releasedTo"
                      value={selectedUserName} // Use the actual user name for display
                      onChange={(value) => {
                        handleUserSelect(value);
                      }}
                      onInputChange={handleSearchUser}
                      placeholder="Search User"
                      variant="primary"
                      size="sm"
                    /> */}
                        <Combobox
                          suggestionOptions={users}
                          optionLabel={(u) => `${u.firstname} ${u.lastname}`}
                          optionValue={(u) => u.id}
                          placeholder="Select User"
                          onInputChange={handleSearchUser}
                          setData={(u) => {
                            formik.setFieldValue("releasedTo", u.id);
                          }}
                          onClear={() => {
                            formik.setFieldValue("releasedTo", 0);
                          }}
                        />
                        {formik.touched.releasedTo && formik.errors.releasedTo && <p className="text-red-500 text-xs mt-1">{formik.errors.releasedTo}</p>}
                      </div>
                      <div className="p-2 flex-1">
                        <p className="text-sm">Date Released</p>
                        <div className="w-60 text-sm">
                          <TextField
                            className="w-48"
                            value={new Date().toLocaleDateString("en-US", {
                              year: "numeric",
                              month: "long",
                              day: "numeric",
                            })}
                            size="sm"
                            disabled
                          />
                        </div>
                      </div>
                      <div className="p-2 flex-1">
                        <p className="text-sm">Releases Via</p>
                        <div className="w-60 text-sm">
                          <Select
                            name="releasedMethodId"
                            options={releasedViaOptionOptionsWithDefault}
                            placeholder=""
                            className="w-1/4"
                            value={1}
                            size="sm"
                            onChange={handleSelectChange} // Capture selected value
                            error={(formik.touched.releasedMethodId && !!formik.errors?.releasedMethodId) || formik.values.releasedMethodId === 0}
                            errorText={formik.errors.releasedMethodId}
                            required
                          />
                        </div>
                      </div>
                      {formatStringAtoZ0to9(releasedMethodText) === formatStringAtoZ0to9(releasedVia.onHand) ||
                      formatStringAtoZ0to9(releasedMethodText) === formatStringAtoZ0to9(releasedVia.thirdPartyPerson) ? (
                        <div className="p-2 flex-row w-1/3 ">
                          <p className="text-sm">Handed By</p>
                          <div className="w-60 text-sm">
                            <TextField
                              className="w-48"
                              size="sm"
                              name="deliveredBy"
                              value={profile ? `${profile.firstname || "N/A"} ${profile.middlename || ""} ${profile.lastname || ""}`.trim() : "N/A"}
                              onChange={handleTextFieldChange("deliveredBy")}
                              error={formik.touched?.deliveredBy && !!formik.errors.deliveredBy}
                              errorText={formik.errors.deliveredBy}
                            />
                          </div>
                        </div>
                      ) : null}
                      {formatStringAtoZ0to9(releasedMethodText) === formatStringAtoZ0to9(releasedVia.mailCourier) ? (
                        <>
                          <div className="p-2 flex-1">
                            <p className="text-sm">Courier Service Name</p>
                            <div className="w-60 text-sm">
                              <TextField
                                className="w-48"
                                placeholder="Courier Service Name"
                                size="sm"
                                name="deliveredBy"
                                onChange={handleTextFieldChange("deliveredBy")}
                                value={formik.values.deliveredBy}
                                error={formik.touched.deliveredBy && !!formik.errors.deliveredBy}
                                errorText={formik.errors.deliveredBy}
                              />
                            </div>
                          </div>
                          <div className="p-2 flex-1">
                            <p className="text-sm">Tracking Number</p>
                            <div className="w-60 text-sm">
                              <TextField
                                className="w-48"
                                name="trackingNo"
                                size="sm"
                                placeholder="Enter Tracking Number"
                                value={formik.values.trackingNo}
                                onFocus={(e) => {
                                  if (formik.values.trackingNo === " ") {
                                    e.target.value = "";
                                  }
                                }}
                                onChange={handleTextFieldChange("trackingNo")}
                                error={formik.touched.trackingNo && !!formik.errors.trackingNo}
                                errorText={formik.errors.trackingNo}
                              />
                            </div>
                          </div>
                        </>
                      ) : null}
                    </div>
                  </div>
                </div>

                {/* for releasedVia */}
                <div>
                  <div className="p-6">
                    <div className="flex flex-row flex-wrap gap-4"></div>
                  </div>
                </div>
                <div className="flex w-full flex-col">
                  <div className="divider divider-start uppercase">Series Overview</div>
                </div>
                <div>
                  <div className="p-6">
                    <div className="flex flex-wrap gap-4">
                      <div className="p-2 flex-1">
                        <p className="text-sm">Released by</p>
                        <div className="w-60 text-sm">
                          <TextField
                            disabled
                            className="w-auto"
                            size="sm"
                            value={data?.createdBy ? `${data.createdBy.firstname || "N/A"} ${data.createdBy.middlename || ""} ${data.createdBy.lastname || ""}`.trim() : "N/A"}
                          />
                        </div>
                      </div>

                      <div className="p-2 flex-1">
                        <p className="text-sm">Area Released</p>
                        <div className="w-60 text-sm">
                          <TextField className="w-48" disabled size="sm" value={String(findItem(areas, "id", Number(data?.releasedAreaId), "areaName") || "N/A")} />
                        </div>
                      </div>
                      <div className="p-2 flex-1">
                        <p className="text-sm">Released To</p>
                        <div className="w-60 text-sm">
                          <TextField className="w-48" disabled size="sm" value={areaAdmins.find((admin) => admin.userId === data?.releasedToId)?.adminName || "N/A"} />
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* forms with pads assignment  */}
                  {formContainers.map((form, index) => (
                    <div key={form.id || index} className="border rounded border-slate-300 mt-4 mb-4">
                      <div className="flex flex-wrap gap-4 p-4 justify-center">
                        <div className="flex-1">
                          <p className="text-sm">Transmittal No.</p>
                          <div className="border-b-2 border-slate-300 w-32 text-sm mt-2">{form.transmittalNumber ?? ""}</div>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm">Division</p>
                          <div className="border-b-2 border-slate-300 w-32 text-sm mt-2">
                            {String(findItem(divisions, "id", Number(data?.padAssignments?.[0]?.form?.divisionId), "divisionName") || "N/A")}
                          </div>
                        </div>
                        <div className="flex-1 w-full">
                          <p className="text-sm">Type</p>
                          <div className="border-b-2 border-slate-300 w-32 text-sm mt-2 whitespace-nowrap">
                            {String(findItem(formTypes, "id", Number(data?.padAssignments?.[0]?.form?.formTypeId), "formTypeCode") || "N/A")}
                          </div>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm">Area</p>
                          <div className="border-b-2 border-slate-300 w-32 text-sm mt-2">{String(findItem(areas, "id", Number(data?.padAssignments?.[0]?.form?.areaId), "areaName") || "N/A")}</div>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm">ATP No.</p>
                          <div className="border-b-2 border-slate-300 w-32 text-sm mt-2">{form.padAssignments?.[0]?.form?.atpNumber ?? "Auto Generated"}</div>
                        </div>
                      </div>

                      <div className="flex justify-center gap-2 p-2">
                        <div className="flex-1">
                          <div className="overflow-auto max-h-64">
                            <table className="w-full s">
                              <thead className="bg-gradient-to-r from-zinc-100 to-indigo-50 p-4 sticky top-0 z-10">
                                <tr>
                                  <th className="p-4 text-sm border-zinc-100">Pad Number</th>
                                  <th className="p-4 text-sm">Series From</th>
                                  <th className="p-4 text-sm">Series To</th>
                                </tr>
                              </thead>
                              <tbody>
                                {form.padAssignments && form.padAssignments.length > 0 ? (
                                  form.padAssignments.map(
                                    (
                                      pad: {
                                        padNumber: string;
                                        seriesFrom: string;
                                        seriesTo: string;
                                      },
                                      idx: number
                                    ) => (
                                      <tr key={idx}>
                                        <td className="p-4 text-sm border border-slate-100">{pad.padNumber}</td>
                                        <td className="p-4 text-sm border border-slate-100">{pad.seriesFrom}</td>
                                        <td className="p-4 text-sm border border-slate-100">{pad.seriesTo}</td>
                                      </tr>
                                    )
                                  )
                                ) : (
                                  <tr>
                                    <td colSpan={4} className="p-4 text-sm text-center text-zinc-400">
                                      No pad assignments available.
                                    </td>
                                  </tr>
                                )}
                              </tbody>
                            </table>
                          </div>
                        </div>

                        <div className="divider divider-horizontal"></div>
                        <div className="flex-1 bg-slate-100 p-4">
                          <div className="p-2 flex justify-center bg-white mb-2">Remarks</div>
                          <div className="bg-white p-4 text-sm">
                            <textarea disabled placeholder="Input remarks here" cols={40} rows={5} className="w-full p-2" value={form.remarks || ""} />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                  {/* ))} */}
                </div>

                <div className="flex justify-center mt-6">
                  <Button classNames="btn bg-slate-400 btn-sm mr-2 w-48" onClick={() => navigateBack()}>
                    Cancel
                  </Button>
                  <Button
                    classNames="btn btn-sm w-48 bg-primary"
                    type="button"
                    onClick={() => {
                      formik.submitForm();
                    }}
                  >
                    Release
                  </Button>
                </div>
              </Form>
            </FormikProvider>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewTransmittalForm;
