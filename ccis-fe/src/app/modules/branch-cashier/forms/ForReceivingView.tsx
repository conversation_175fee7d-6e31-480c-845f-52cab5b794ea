import Button from "@components/common/Button";
import Typography from "@components/common/Typography";
import Loader from "@components/Loader";
import React, { useEffect } from "react";
import { useParams } from "react-router-dom";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { IFormTransmittal } from "@interface/form-inventory.interface";
import { toast } from "react-toastify";
import { VerifyFormsSchema } from "@services/form-inventory-incoming-received-form/form-inventory-incoming-received-form.schema";
import { confirmSaveOrEdit } from "@helpers/prompt";
import { useFormik } from "formik";
import { FormStatus } from "@enums/form-status";
import { useUserManagementActions } from "@state/reducer/users-management";
import { findItem } from "@helpers/array";
import { navigateBack } from "@helpers/navigatorHelper";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import AttachmentItem from "../components/attachmentItem";

const ViewFormReceiving: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const formTypes = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);

  // Get data from Redux state
  const formsResponse = useSelector((state: RootState) => state.formInventoryTransmittal.getTransmittalFormTrail);
  const data = (formsResponse?.data as IFormTransmittal) || null;
  const formsLoading = formsResponse?.loading || false;
  const formsError = formsResponse?.error || false;

  const attachments = data?.padAssignments?.[0]?.form?.attachments;
  const withBirAttachment = data?.withBirAttachment;

  const putTrailSuccess = useSelector((state: RootState) => state.formInventoryTransmittal.putFormTransmittalTrail?.success);

  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const filterDivision = "";
  const filterFormType = "";
  const filterArea = "";
  const filterUser = "";

  const { getTransmittalFormTrail, putFormTransmittalTrail } = useTransmittalFormActions();

  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const { getUsers } = useUserManagementActions();

  const formik = useFormik({
    initialValues: {
      status: "",
    },
    validationSchema: VerifyFormsSchema,
    onSubmit: async (values, { resetForm }) => {
      const isConfirmed = await confirmSaveOrEdit("Are you sure you want to receive this transmittal?");
      if (isConfirmed) {
        try {
          if (data?.latestFormTransmittalTrail?.id) {
            await putFormTransmittalTrail({
              id: data.latestFormTransmittalTrail?.id,
              status: values.status,
            });
            resetForm();
          } else {
            toast.error("Failed to process form: Form Transmittal Trail ID is undefined");
          }
        } catch (error) {
          toast.error("Failed to approve form");
        }
      }
    },
  });

  // Handle put operation success
  useEffect(() => {
    if (putTrailSuccess) {
      toast.success("Received form successfully");
      navigateBack();
    }
  }, [putTrailSuccess]);

  const fetchForm = () => {
    if (id) {
      getTransmittalFormTrail({ id: Number(id) });
    }
  };

  useEffect(() => {
    fetchForm();
  }, [id]);

  useEffect(() => {
    getDivisions({ filter: filterDivision });
    getFormTypes({ filter: filterFormType });
    getAreas({ filter: filterArea });
  }, []);

  useEffect(() => {
    getUsers({ filter: filterUser });
  }, []);

  // Handle error state
  useEffect(() => {
    if (formsError) {
      toast.error("Failed to load proposal data. Please try again later.");
    }
  }, [formsError]);

  return formsLoading ? (
    <div className="flex justify-center items-center h-screen">
      <Loader />
    </div>
  ) : (
    <div>
      <Button classNames="btn bg-slate-600 btn-sm" onClick={() => navigateBack()}>
        Back
      </Button>
      <div className="mx-6">
        <Typography className="mt-6 text-primary font-poppins-semibold">FORM DETAILS</Typography>

        <div className="mt-8 gap-4 flex justify-center">
          <div className="w-full">
            <div className="flex w-full flex-col">
              <div className="divider divider-start uppercase">Assignee Details</div>

              <div className="grid grid-cols-3 gap-4 p-6">
                <div>
                  <p className="text-sm">Released By</p>
                  <div className="border-b-2 border-slate-300 max-w-40 text-sm">
                    {data?.latestFormTransmittalTrail?.createdBy
                      ? `${data?.latestFormTransmittalTrail?.createdBy?.firstname} 
                    ${data?.latestFormTransmittalTrail?.createdBy?.middlename || ""} 
                    ${data?.latestFormTransmittalTrail?.createdBy?.lastname}`
                      : "N/A"}
                  </div>
                </div>
                <div>
                  <p className="text-sm">Released To</p>
                  <div className="border-b-2 border-slate-300 max-w-40 text-sm">
                    {data?.latestFormTransmittalTrail?.releasedTo
                      ? `${data?.latestFormTransmittalTrail?.releasedTo?.firstname} ${
                          data?.latestFormTransmittalTrail?.releasedTo?.middlename || ""
                        } ${data?.latestFormTransmittalTrail?.releasedTo?.lastname}`
                      : "N/A"}
                  </div>
                </div>
                <div>
                  <p className="text-sm">Date Released</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    {data?.latestFormTransmittalTrail?.createdAt
                      ? new Date(data.latestFormTransmittalTrail?.createdAt).toLocaleDateString("en-US", {
                          day: "2-digit",
                          month: "2-digit",
                          year: "numeric",
                        })
                      : "N/A"}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="divider divider-start">Series Overview</div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-3 grid-flow-col gap-4">
                <div className="p-2">
                  <p className="text-sm">Released By</p>
                  <div className="border-b-2 border-slate-300 max-w-40 text-sm">
                    <p>
                      {data?.createdBy?.firstname} {data?.createdBy?.middlename} {data?.createdBy?.lastname}
                    </p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Area Released</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    <p>{String(findItem(area, "id", Number(data?.releasedAreaId), "areaName") || "N/A")}</p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Released To</p>
                  <div className="border-b-2 border-slate-300 max-w-40 text-sm">
                    <p>
                      {data?.releasedTo?.firstname} {data?.releasedTo?.middlename} {data?.releasedTo?.lastname}
                    </p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Date Released</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    <p>{data?.createdAt ? new Date(data.createdAt).toLocaleDateString("en-US") : "N/A"}</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="border rounded-md border-slate-300 p-2">
              <div className="p-6">
                <div className="grid grid-cols-5 gap-4">
                  <div className="p-2">
                    <p className="text-sm">Transmittal No.</p>
                    <div className="border-b-2 border-slate-300 w-32 text-sm">
                      <p>{data?.transmittalNumber}</p>
                    </div>
                  </div>
                  <div className="p-2">
                    <p className="text-sm">Division</p>
                    <div className="border-b-2 border-slate-300 w-32 text-sm">
                      <p>{String(findItem(divisions, "id", Number(data?.padAssignments?.[0]?.form?.divisionId), "divisionName") || "N/A")}</p>
                    </div>
                  </div>
                  <div className="p-2">
                    <p className="text-sm">Type</p>
                    <div className="border-b-2 border-slate-300 w-32 text-sm">
                      <p>{String(findItem(formTypes, "id", Number(data?.padAssignments?.[0]?.form?.formTypeId), "formTypeCode") || "N/A")}</p>
                    </div>
                  </div>
                  <div className="p-2">
                    <p className="text-sm">Area</p>
                    <div className="border-b-2 border-slate-300 w-32 text-sm">
                      <p>{String(findItem(area, "id", Number(data?.padAssignments?.[0]?.form?.areaId), "areaName") || "N/A")}</p>
                    </div>
                  </div>
                  <div className="p-2">
                    <p className="text-sm">ATP No.</p>
                    <div className="border-b-2 border-slate-300 w-32 text-sm">
                      <p>{data?.padAssignments?.[0]?.form?.atpNumber}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-4 mb-4 flex w-full">
                <div className="overflow-auto max-h-64 w-full">
                  <table className="w-full">
                    <thead className="bg-gradient-to-r from-zinc-50 to-indigo-50 p-4 sticky top-0 z-10">
                      <tr>
                        <th className="p-4 text-sm border-zinc-100">Pad Number</th>
                        <th className="p-4 text-sm">Series From</th>
                        <th className="p-4 text-sm">Series To</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data?.padAssignments?.map((assignment) => (
                        <tr key={assignment.id}>
                          <td className="p-4 text-sm border border-slate-100 text-center">{assignment.padNumber}</td>
                          <td className="p-4 text-sm border border-slate-100 text-center">{assignment.seriesFrom}</td>
                          <td className="p-4 text-sm border border-slate-100 text-center">{assignment.seriesTo}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <div className="divider divider-horizontal"></div>
                <div className="bg-slate-50 p-4 min-w-96 w-full">
                  <div className="p-2 flex justify-center bg-white rounded mb-2">Remarks</div>
                  <div className="bg-white p-4 text-sm rounded">
                    {data?.remarks?.split("\n").map((line, index) => (
                      <p key={index}>-{line}</p>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            {/* Conditionally render attachments based on withBirAttachment */}
            {withBirAttachment === 1 ? (
              <>
                <div className="divider divider-start">Attachments</div>
                <div className="border rounded border-slate-300 p-4 mt-4 mb-4">
                  <div className="mt-4 flex justify-start">
                    <div>
                      <p className="text-sm">Authority To Print:</p>
                      <div>
                        {attachments && attachments[0] ? (
                          <AttachmentItem fileName={attachments[0]?.label || "No Attachment Found"} filePath={`https://ccis-stage.s3.ap-southeast-1.amazonaws.com/${attachments[0]?.filepath}`} />
                        ) : (
                          <span className="text-gray-500">No attachment available</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 flex justify-start">
                    <div>
                      <p className="text-sm">Tax Users Sworn Statement:</p>
                      {attachments && attachments[1] ? (
                        <AttachmentItem fileName={attachments[1]?.label || "No Attachment Found"} filePath={`https://ccis-stage.s3.ap-southeast-1.amazonaws.com/${attachments[1]?.filepath}`} />
                      ) : (
                        <span className="text-gray-500">No attachment available</span>
                      )}
                    </div>
                  </div>
                  <div className="mt-4 flex justify-start">
                    <div>
                      <p className="text-sm">Printers Certificate:</p>
                      <div>
                        {attachments && attachments[2] ? (
                          <AttachmentItem fileName={attachments[2]?.label || "No Attachment Found"} filePath={`https://ccis-stage.s3.ap-southeast-1.amazonaws.com/${attachments[2]?.filepath}`} />
                        ) : (
                          <span className="text-gray-500">No attachment available</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 flex justify-start">
                    <div>
                      <p className="text-sm">Copy of First and Last Stub:</p>
                      <div>
                        {attachments && attachments[3] ? (
                          <AttachmentItem fileName={attachments[3]?.label || "No Attachment Found"} filePath={`https://ccis-stage.s3.ap-southeast-1.amazonaws.com/${attachments[3]?.filepath}`} />
                        ) : (
                          <span className="text-gray-500">No attachment available</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center text-gray-500 mt-4 p-4 border rounded border-slate-300">No BIR attachments available for this form</div>
            )}
          </div>
        </div>
        <div className="flex justify-center gap-2 mt-4">
          <Button
            type="submit"
            classNames="bg-sky-500 w-80 font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2"
            onClick={() => {
              formik.setFieldValue("status", FormStatus.RECEIVED);
              formik.handleSubmit();
            }}
          >
            Receive
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ViewFormReceiving;
