import { useEffect, useState } from "react";

import RequestPadTable from "../components/RequestPadTable";
import Button from "@components/common/Button";
import { TbMessagePlus } from "react-icons/tb";
import Typography from "@components/common/Typography";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import TableFilter from "./TableFilter";
import RequestPadModal from "./RequestPadModal";
import { useFormik } from "formik";
import { useUserManagementActions } from "@state/reducer/users-management";
import { usePadRequestActions } from "@state/reducer/gam-pad-request";
import { useFetchWithParams } from "@hooks/useFetchWithParams";
import { useSelectOptions } from "@hooks/useSelectOptions";
import { useDebouncedSearch } from "@hooks/useDebouncedSearch";
import { createDateChangeHandler, createSelectChangeHandler } from "@helpers/handlers";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { RequestPadStatus } from "@enums/request-pads";
import { RequestPadValidation } from "@services/gam-pad-request/gam-pad-request.shema";

const RequestPads = () => {
  const [resetCounter, setResetCounter] = useState(0);
  const [divisionFilter, setDivisionFilter] = useState<number>(0);
  const [areaFilter, setAreaFilter] = useState<number>(0);
  const [type, setType] = useState<number>(0);
  const [dateFrom, setDateFrom] = useState<string>("");
  const [dateTo, setDateTo] = useState<string>("");
  const [isFormOpen, setIsFormOpen] = useState<boolean>(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [refreshKey, setRefreshKey] = useState(0);

  const { getUsers } = useUserManagementActions();
  const users = useSelector((state: RootState) => state.usersManagement.users);
  const user = useSelector((state: RootState) => state.auth.user.data);
  const { remainingPads, postGamPadRequest: postGAMPADREQUEST, gamPadRequesTable } = useSelector((state: RootState) => state.gamPadRequest);

  // for Select options
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const types = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const areas = useSelector((state: RootState) => state.utilitiesAreas.areas);

  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();

  const { getPadRequests, getRemainingPads, getLastSeries, getNextSeries, postGamPadRequest, postGamPadRequestStateDefault } = usePadRequestActions();

  const formik = useFormik({
    initialValues: {
      divisionId: 0,
      formTypeId: 2,
      areaId: parseInt(user.areaId),
      numberOfPads: 0,
      seriesFrom: 0,
      seriesTo: 0,
      releasedTo: 0,
    },
    validationSchema: RequestPadValidation,
    onSubmit: async (values) => {
      postGamPadRequest(values);
    },
  });

  // custom hooks
  const divisionOptions = useSelectOptions({
    data: divisions,
    firstOptionText: "Select Division",
    valueKey: "id",
    textKey: "divisionName",
  });

  const typeOptions = useSelectOptions({
    data: types,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: "formTypeName",
  });

  const areaOptions = useSelectOptions({
    data: areas,
    firstOptionText: "Select Area",
    valueKey: "id",
    textKey: "areaName",
  });

  const { value: searchUser, handleChange: handleSearchUser } = useDebouncedSearch();
  const { value: searchText, handleChange: handleSearch, setValue: setSearchText } = useDebouncedSearch();

  // Select handlers
  const handleDivisionChange = createSelectChangeHandler(setDivisionFilter);
  const handleTypeChange = createSelectChangeHandler(setType);
  const handleAreaChange = createSelectChangeHandler(setAreaFilter);

  // Date handlers
  const handleDateFromChange = createDateChangeHandler(setDateFrom);
  const handleDateToChange = createDateChangeHandler(setDateTo);

  // Get divisions, area and form type
  useFetchWithParams([getDivisions, getAreas, getFormTypes], { filter: "" }, [], false);

  const validationForNext = () => {
    return ![0, null, undefined, NaN, ""].includes(formik.values.divisionId) && ![0, null, undefined, NaN, ""].includes(formik.values.formTypeId);
  };

  // Fetch getRemainingPads and getLastSeries once
  useFetchWithParams(getLastSeries, { filter: "" });

  // Fetch get Next Series
  useFetchWithParams(
    getNextSeries,
    {
      divisionFilter: formik.values.divisionId,
      formtypeFilter: formik.values.formTypeId,
    },
    [formik.values.formTypeId, formik.values.divisionId],
    true,
    validationForNext
  );

  // Fetch padRequests with filters and pagination
  useFetchWithParams(
    getPadRequests,
    {
      page,
      pageSize,
      filter: searchText,
      divisionFilter,
      areaFilter,
      formtypeFilter: type,
      dateFrom,
      dateTo,
      statusFilter: RequestPadStatus.PENDING,
      user: user.id,
    },
    [searchText, divisionFilter, areaFilter, type, page, pageSize, dateFrom, dateTo, refreshKey] // triggers refetch
  );

  const handleClearAll = () => {
    setSearchText("");
    setDivisionFilter(0);
    setType(0);
    setAreaFilter(0);
    setResetCounter((prev) => prev + 1);
  };

  const handleToggleFormModal = () => {
    setIsFormOpen((prev) => !prev);
    formik.resetForm();
  };

  useEffect(() => {
    if (isFormOpen && searchUser !== "") {
      getUsers({ nameFilter: searchUser });
    }
  }, [searchUser, isFormOpen]);

  // use for refetching data in table
  useEffect(() => {
    if (postGAMPADREQUEST.success) {
      setRefreshKey((prev) => prev + 1);
      postGamPadRequestStateDefault();
    }
  }, [postGAMPADREQUEST.success]);

  useEffect(() => {
    getRemainingPads({ id: user.id });
  }, []);

  return (
    <>
      <div className="my-4 py-4 border-b border-zinc-300 w-full text-zinc-500">
        Dashboard / <span className="text-primary font-poppins-semibold">Request Pads</span>
      </div>
      <Typography size="2xl" className="text-[#042781] my-16 flex flex-row font-semibold">
        REQUESTED PAD(s) LIST
      </Typography>
      <div>
        <div className="flex items-center justify-between">
          <TableFilter
            divisionFilter={divisionFilter}
            divisionOptions={divisionOptions}
            handleClearAll={handleClearAll}
            handleDivisionChange={handleDivisionChange}
            searchText={searchText}
            handleSearch={handleSearch}
            handleTypeChange={handleTypeChange}
            resetCounter={resetCounter}
            type={type}
            typeOptions={typeOptions}
            areaFilter={areaFilter}
            areaOptions={areaOptions}
            handleAreaChange={handleAreaChange}
            dateFrom={dateFrom}
            handleDateFromChange={handleDateFromChange}
            dateTo={dateTo}
            handleDateToChange={handleDateToChange}
          />
          <Button variant="primary" classNames="flex items-center gap-2" onClick={handleToggleFormModal}>
            <TbMessagePlus />
            Request
          </Button>
        </div>
        <RequestPadTable
          data={gamPadRequesTable?.tableData?.data?.data}
          loading={gamPadRequesTable?.tableData?.loading}
          totalCount={gamPadRequesTable?.tableData?.data?.meta?.total !== undefined ? gamPadRequesTable.tableData.data.meta.total : [].length}
          onChangeRowsPerPage={(newPageSize, newPage) => {
            setPageSize(newPageSize);
            setPage(newPage);
          }}
          onPaginate={(newPage) => setPage(newPage)}
        />
        {isFormOpen && (
          <RequestPadModal
            handleToggleFormModal={handleToggleFormModal}
            isFormOpen={isFormOpen}
            divisionOptions={divisionOptions}
            formik={formik}
            remainingPads={remainingPads?.data?.remaining_pads}
            handleSearchUser={handleSearchUser}
            data={users}
            typeOptions={typeOptions}
            areaOptions={areaOptions}
          />
        )}
      </div>
    </>
  );
};

export default RequestPads;
