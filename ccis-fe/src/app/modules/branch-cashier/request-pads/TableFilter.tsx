import Filter from "@components/common/Filter";
import Select from "@components/form/Select";
import TextField from "@components/form/TextField";
import { ChangeEvent } from "react";

export type TOption = {
  text: string;
  value: string | number;
};

type TableFilterProps = {
  searchText: string;
  handleSearch: (e: ChangeEvent<HTMLInputElement>) => void;
  handleClearAll: () => void;
  resetCounter: number;
  divisionOptions: TOption[];
  divisionFilter: number;
  handleDivisionChange: (e: ChangeEvent<HTMLSelectElement>) => void;
  typeOptions: TOption[];
  type: number;
  handleTypeChange: (e: ChangeEvent<HTMLSelectElement>) => void;
  areaOptions: TOption[];
  areaFilter: number;
  handleAreaChange: (e: ChangeEvent<HTMLSelectElement>) => void;
  dateFrom: string;
  handleDateFromChange: (e: ChangeEvent<HTMLInputElement>) => void;
  dateTo: string;
  handleDateToChange: (e: ChangeEvent<HTMLInputElement>) => void;
};

const TableFilter = ({
  searchText,
  handleSearch,
  handleClearAll,
  resetCounter,
  divisionOptions,
  divisionFilter,
  typeOptions,
  handleDivisionChange,
  type,
  handleTypeChange,
  areaOptions,
  areaFilter,
  handleAreaChange,
  dateFrom,
  handleDateFromChange,
  dateTo,
  handleDateToChange,
}: TableFilterProps) => {
  return (
    <Filter search={searchText} onChange={handleSearch}>
      <div className="flex justify-end">
        <button className="text-primary text-xs btn-sm" type="button" onClick={handleClearAll}>
          Clear All
        </button>
      </div>
      <div className="flex flex-col gap-4">
        <div>
          <div className="text-xs">Division</div>
          <Select key={`division-${resetCounter}`} size="sm" options={divisionOptions} value={divisionFilter} onChange={handleDivisionChange} />
        </div>
        <div>
          <div className="text-xs">Type</div>
          <Select key={`type-${resetCounter}`} size="sm" options={typeOptions} value={type} onChange={handleTypeChange} />
        </div>
        <div>
          <div className="text-xs">Area</div>
          <Select key={`area-${resetCounter}`} size="sm" options={areaOptions} value={areaFilter} onChange={handleAreaChange} />
        </div>
        <div className="w-full">
          <div className="text-xs mb-1">Date Range</div>
          <div className="flex flex-col gap-4  flex-1">
            <div className="text-xs w-full">
              From :
              <br />
              <TextField type="date" className="border border-zinc-300" value={dateFrom} onChange={handleDateFromChange} />
            </div>
            <div className="text-xs flex-1">
              To :
              <br />
              <TextField type="date" className="border border-zinc-300" value={dateTo} onChange={handleDateToChange} />
            </div>
          </div>
        </div>
      </div>
    </Filter>
  );
};

export default TableFilter;
