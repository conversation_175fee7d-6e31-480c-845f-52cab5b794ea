import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import Select from "@components/form/Select";
import TextField from "@components/form/TextField";
import { ChangeEvent, useEffect, useRef, useState } from "react";
import { TOption } from "./TableFilter";
import { FormikProps } from "formik";
import { ICreatePadRequest } from "@interface/gam-request-pads";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { IUser } from "@interface/user.interface";
import { showAlert } from "@helpers/prompt";
import { Combobox } from "@components/common-v2/Combobox";

type RequestPadModalProps = {
  isFormOpen: boolean;
  handleToggleFormModal: () => void;
  divisionOptions: TOption[];
  formik: FormikProps<ICreatePadRequest>;
  handleSearchUser: (event: ChangeEvent<HTMLInputElement>) => void;
  data: IUser[];
  remainingPads: number;
  typeOptions: TOption[];
  areaOptions: TOption[];
};

const RequestPadModal = ({ isFormOpen, handleToggleFormModal, divisionOptions, formik, handleSearchUser, data, remainingPads = 0, typeOptions, areaOptions }: RequestPadModalProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const { nextSeries } = useSelector((state: RootState) => state.gamPadRequest);

  const { postGamPadRequest } = useSelector((state: RootState) => state.gamPadRequest);

  const calculateSeriesTo = () => {
    const pad = 50;

    if (formik.values.numberOfPads > 0) {
      formik.setFieldValue("seriesTo", formik.values.numberOfPads * pad + nextSeries?.data?.next_series_from - 1);
    } else {
      formik.setFieldValue("seriesTo", 0);
    }
  };

  const setFormikValues = () => {
    formik.setFieldValue("seriesFrom", nextSeries?.data?.next_series_from);
  };

  const handleClick = () => {
    if (formik.values.divisionId !== 0 && formik.values.formTypeId !== 0) {
      if (nextSeries.data !== undefined) {
        setIsEditing(true);
        // Delay to ensure state updates before focus
        setTimeout(() => {
          inputRef.current?.focus();
        }, 0);
      } else {
        showAlert("Warning", "No pads available at the moment. Please contact the CLIFSA Officer for assistance.");
      }
    } else {
      showAlert("Warning", "Please Select Division and Form Type!");
    }
  };

  const handleBlur = () => {
    if (typeof formik.values.numberOfPads === "string") {
      formik.setFieldValue("numberOfPads", 0);
    }
    setIsEditing(false);
  };

  useEffect(() => {
    calculateSeriesTo();
  }, [formik.values.numberOfPads]);

  useEffect(() => {
    if (isFormOpen) {
      formik.resetForm();
    }
  }, [isFormOpen]);

  useEffect(() => {
    if (nextSeries?.data !== undefined) {
      setFormikValues();
    }
  }, [nextSeries?.data]);

  useEffect(() => {
    if (postGamPadRequest.success) {
      handleToggleFormModal();
    }
  }, [postGamPadRequest]);

  return (
    <Modal title="Add Request" modalContainerClassName="max-w-4xl" titleClass="text-primary text-lg uppercase" isOpen={isFormOpen} onClose={handleToggleFormModal}>
      <form onSubmit={formik.handleSubmit}>
        <div className="w-full flex flex-col gap-10">
          {/* Pads container */}
          <div className="flex items-center justify-between gap-4">
            <div className="h-52 flex-1">
              <div className="border-2 rounded-3xl border-primary h-40 ">
                <div className="h-full w-full flex flex-col items-center justify-center">
                  <h3 className="font-semibold text-4xl text-primary">{remainingPads}</h3>
                  <span className="text-xl text-[#47474780]">Number of Remaining Pads</span>
                </div>
              </div>
            </div>
            <div className="flex flex-col h-52 flex-1">
              <div className="border-2 rounded-3xl border-primary h-40 cursor-pointer" onClick={handleClick}>
                <div className="h-full w-full flex flex-col items-center justify-center px-4">
                  {isEditing ? (
                    <input
                      name="numberOfPads"
                      ref={inputRef}
                      type="number"
                      className="text-4xl text-center text-primary font-bold focus:outline-none"
                      onBlur={handleBlur}
                      value={formik.values.numberOfPads}
                      onChange={(e) => {
                        if (nextSeries?.data?.total_available_pads >= e.target.value) {
                          formik.setFieldValue("numberOfPads", parseInt(e.target.value));
                        } else {
                          showAlert("Warning", `The total available pads is ${nextSeries?.data?.total_available_pads}`);
                          formik.setFieldValue("numberOfPads", 0);
                        }
                      }}
                    />
                  ) : (
                    <h3 className="font-semibold text-4xl text-primary">{formik.values.numberOfPads}</h3>
                  )}
                  <span className="text-xl text-[#47474780]">Number of Requested Pads</span>
                </div>
              </div>
              <div className="flex justify-center">{formik.touched.numberOfPads && formik.errors.numberOfPads && <span className="text-red-500">{formik.errors.numberOfPads}</span>}</div>
            </div>
          </div>

          {/* Textfields */}
          <div className="flex gap-4">
            {/* inputs left side*/}
            <div className="flex flex-col gap-4 flex-1 min-w-0">
              <div className="flex flex-col ">
                <div className="flex gap-4 items-center">
                  <span className="w-36 text-sm">Division:</span>
                  <Select
                    name="divisionId"
                    size="sm"
                    options={divisionOptions}
                    value={formik.values.divisionId}
                    onChange={(e) => {
                      const { name, value } = e.target;
                      formik.setFieldValue(name, parseInt(value));
                    }}
                  />
                </div>
                <div className="flex justify-end">{formik.touched.divisionId && formik.errors.divisionId && <span className="text-red-500">{formik.errors.divisionId}</span>}</div>
              </div>
              <div className="flex flex-col">
                <div className="flex gap-4 items-center">
                  <span className="w-36 text-sm">Area:</span>
                  <Select name="areaId" size="sm" disabled options={areaOptions} value={areaOptions.find((opt) => opt.value === formik.values.areaId)?.value} onChange={formik.handleChange} />
                </div>
                <div className="flex justify-center">{formik.touched.areaId && formik.errors.areaId && <span className="text-red-500">{formik.errors.areaId}</span>}</div>
              </div>
              <div className="flex flex-col">
                <div className="flex gap-4 items-center">
                  <span className="w-36 text-sm">Series From:</span>
                  <TextField name="seriesFrom" placeholder={"Series From"} size="sm" variant="primary" disabled value={formik.values.seriesFrom} onChange={formik.handleChange} />
                </div>
                <div className="flex justify-end">{formik.touched.seriesFrom && formik.errors.seriesFrom && <span className="text-red-500">{formik.errors.seriesFrom}</span>}</div>
              </div>
            </div>

            {/* inputs right side*/}
            <div className="flex flex-col gap-4 flex-1 min-w-0">
              <div className="flex flex-col">
                <div className="flex gap-4 items-center">
                  <span className="w-36 text-sm">Type:</span>
                  <Select name="formTypeId" size="sm" options={typeOptions} value={formik.values.formTypeId} onChange={formik.handleChange} disabled />
                </div>
                <div className="flex justify-end">{formik.touched.formTypeId && formik.errors.formTypeId && <span className="text-red-500">{formik.errors.formTypeId}</span>}</div>
              </div>
              <div className="flex flex-col">
                <div className="flex gap-4 items-center">
                  <span className="w-36 text-sm">Requested To:</span>
                  <Combobox
                    suggestionOptions={data}
                    optionLabel={(u) => `${u.firstname} ${u.lastname}`}
                    optionValue={(u) => u.id}
                    placeholder="Select User"
                    onInputChange={handleSearchUser}
                    setData={(u) => {
                      formik.setFieldValue("releasedTo", u.id);
                    }}
                    onClear={() => {
                      formik.setFieldValue("releasedTo", 0);
                    }}
                  />
                </div>
                <div className="flex justify-end">{formik.touched.releasedTo && formik.errors.releasedTo && <span className="text-red-500">{formik.errors.releasedTo}</span>}</div>
              </div>
              <div className="flex flex-col">
                <div className="flex gap-4 items-center">
                  <span className="w-36 text-sm">Series to:</span>
                  <TextField name="seriesTo" placeholder={"Division"} size="sm" variant="primary" disabled value={formik.values.seriesTo} onChange={formik.handleChange} />
                </div>
                <div className="flex justify-end">{formik.touched.seriesTo && formik.errors.seriesTo && <span className="text-red-500">{formik.errors.seriesTo}</span>}</div>
              </div>
            </div>
          </div>

          {/* Buttons */}
          <div className="flex items-center justify-center w-full">
            <div className="w-[550px] flex gap-2">
              <Button variant="secondary" isSubmitting={postGamPadRequest.loading} classNames="w-full" onClick={handleToggleFormModal}>
                Cancel
              </Button>
              <Button variant="primary" isSubmitting={postGamPadRequest.loading} classNames="w-full" type="submit">
                Submit
              </Button>
            </div>
          </div>
        </div>
      </form>
    </Modal>
  );
};

export default RequestPadModal;
