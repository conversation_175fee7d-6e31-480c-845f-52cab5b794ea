import React from "react";
import GenericInventoryTab from "@components/template/GenericInventoryTab";
import { RoleType } from "@enums/form-status";
// import { useInventoryMetrics } from "@components/template/InventoryMetricType";

const Inventory: React.FC = () => {
  // const { headCashierMetrics } = useInventoryMetrics();
  return (
    <GenericInventoryTab
      userRole={RoleType.CASHIER}
      title="INVENTORY (ON-HAND)"
      description="This page lists all forms across all received series. Use filters to narrow down results by division, or type."
      // customMetrics={headCashierMetrics}
    />
  );
};

export default Inventory;
