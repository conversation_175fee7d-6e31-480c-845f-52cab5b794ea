// Note in Changes it later from enum to constants since it is For CONSTANTS file

import { ISelectOptions } from "@interface/common.interface";

export enum Status {
  forApproval = "FOR APPROVAL",
  forOrIssuance = "FOR OR ISSUANCE",
  invalidRequirement = "INVALID REQUIREMENT",
  completed = "COMPLETED",
  for_approval = "FOR_APPROVAL",
  for_review = "FOR_REVIEW",
  draft = "DRAFT",
  approved = "APPROVED",
  received = "RECEIVED",
  verified = "VERIFIED",
  notYetReceived = "NOT YET RECEIVED",
  released = "RELEASED",
  for_signatory = "FOR_SIGNATORY",
  pending = "PENDING",
  for_revision = "FOR_REVISION",
  rejected = "REJECTED",
  valid = "VALID",
  notarized = "NOTARIZED",
}

export const membershipName = {
  newWithRemittance: "New with remittance",
  newWithoutRemittance: "New without remittance",
  existing: "Existing",
} as const;
export enum typeCoInsured {
  secondary = "SECONDARY",
  primary = "PRIMARY",
}

export enum ePremiumBasis {
  packageRate = "PACKAGE RATE",
  perInsuredRating = "PER INSURED RATING",
}

export enum eInsuredType {
  principalMember = "PRINCIPAL MEMBER",
  coInsuredDependent = "CO-INSURED DEPENDENT",
  principal = "PRINCIPAL",
  coInsured = "COINSURED",
  coInsuredPrimary = "COINSURED PRIMARY",
  coInsuredSecondary = "COINSURED SECONDARY",
}

export const releasedVia = {
  onHand: "On hand method",
  mailCourier: "Mail Courier",
  thirdPartyPerson: "Third Party Person",
} as const;
export const eAgeType = {
  STANDARD: "STANDARD",
  AGE_BRACKET: "AGE BRACKET",
  ALL_IN: "ALL IN",
} as const;

// for Changes use CONSTANTS not ENUMS
export const Statuses = {
  FORAPPROVAL: "FOR APPROVAL",
  FOR_OR_ISSUANCE: "FOR OR ISSUANCE",
  INVALID_REQUIREMENT: "INVALID REQUIREMENT",
  COMPLETED: "COMPLETED",
  FOR_APPROVAL: "FOR_APPROVAL",
  DRAFT: "DRAFT",
  APPROVED: "APPROVED",
  RECEIVED: "RECEIVED",
  VERIFIED: "VERIFIED",
  NOT_YET_RECEIVED: "NOT YET RECEIVED",
  RELEASED: "RELEASED",
  PENDING: "PENDING",
  DISAPPROVED: "DISAPPROVED",
  REJECTED: "REJECTED",
  VALID: "VALID",
  INVALID: "INVALID",
  UNRESOLVED: "UNRESOLVED",
  RESOLVED: "RESOLVED",
  CANCELLED: "CANCELLED",
  FOR_SIGNATORY: "FOR_SIGNATORY",
  ACTIVE: "ACTIVE",
  INACTIVE: "INACTIVE",
} as const;
export const STATUS_OPTIONS: ISelectOptions[] = [
  { text: Statuses.APPROVED, value: Statuses.APPROVED },
  { text: Statuses.REJECTED, value: Statuses.REJECTED },
];
export const quotationRemarks = {
  ALLIN: "All in",
  CONTESTABILITY: "Waived contestability",
  FIVEPERCENT66TO69: "5% of Members aged 66-69",
} as const;
export const APPROVABLE_TYPE = {
  PRODUCT_PROPOSAL_COMMISSION_STRUCTURE: "PRODUCT_PROPOSAL_COMMISSION_STRUCTURE",
};

export const SYSTEM_SETTINGS_IDS = {
  PRESIDENT_USER_ID: "president_user_id",
  VICE_PRESIDENT_USER_ID: "vice_president_user_ids",
  MANAGER_USER_ID: "managers_user_ids",
  IT_MANAGER_USER_ID: "it_manager_user_id",
} as const;
