import { useNavigate } from "react-router-dom";
import { ROUTES } from "@constants/routes";
import { FormStatus, RoleType } from "@enums/form-status";
import { IFormTransmittal } from "@interface/form-inventory.interface";
import { IActions } from "@interface/common.interface";
import { GoVersions } from "react-icons/go";

export const useRoleBasedActions = () => {
  const navigate = useNavigate();

  const getActionEvents = (
    transmittal: IFormTransmittal, 
    userRole: RoleType, 
    isReturned: boolean, 
    isVerifyList: boolean, 
    getDisplayStatus: (row: IFormTransmittal) => string): IActions<any>[] => {
    const displayStatus = getDisplayStatus(transmittal);
    let route;

    const transmittalId = transmittal.id?.toString() || "";

    switch (userRole) {
      case RoleType.IOC:
        if (isReturned) {
          if (displayStatus === FormStatus.RETURNED) {
            route = ROUTES.INCOMINGOUTGOINGCASHIER.viewReturnedForm.parse(transmittalId);
          } else if (displayStatus === FormStatus.FOR_RECEIVING) {
            route = ROUTES.INCOMINGOUTGOINGCASHIER.forReturnedReceivingForm.parse(transmittalId);
          } else if (displayStatus === FormStatus.ON_HAND) {
            route = ROUTES.INCOMINGOUTGOINGCASHIER.viewIOCTransmittal.parse(transmittalId);
          } else {
            route = ROUTES.INCOMINGOUTGOINGCASHIER.viewNewForm.parse(transmittalId);
          }
        } else if (isVerifyList){
          if (displayStatus === FormStatus.RELEASED) {
            route = ROUTES.INCOMINGOUTGOINGCASHIER.viewNewForm.parse(transmittalId);
          } else if (displayStatus === FormStatus.FOR_RECEIVING) {
            route = ROUTES.INCOMINGOUTGOINGCASHIER.viewNewForm.parse(transmittalId);
          } else if (displayStatus === FormStatus.ON_HAND) {
            route = ROUTES.INCOMINGOUTGOINGCASHIER.viewNewForm.parse(transmittalId);
          } else {
            route = ROUTES.INCOMINGOUTGOINGCASHIER.viewNewForm.parse(transmittalId);
          }
        } else {
          if (displayStatus === FormStatus.RELEASED) {
            route = ROUTES.OUTGOINGADMIN.viewOutgoingAdminTransmittalTrail.parse(transmittalId);
          } else if (displayStatus === FormStatus.FOR_RECEIVING) {
            route = ROUTES.OUTGOINGADMIN.forOutgoingAdminReceivingForm.parse(transmittalId);
          } else if (displayStatus === FormStatus.ON_HAND) {
            route = ROUTES.OUTGOINGADMIN.viewOutgoingAdminTransmittal.parse(transmittalId);
          } else {
            route = ROUTES.OUTGOINGADMIN.forOutgoingAdminReceivingForm.parse(transmittalId);
          }
        }

        break;

      case RoleType.CHIEFCASHIER:
        if (isReturned) {
          if (displayStatus === FormStatus.RETURNED) {
            route = ROUTES.CHIEFCASHIER.viewReturnedForm.parse(transmittalId);
          } else if (displayStatus === FormStatus.FOR_RECEIVING) {
            route = ROUTES.CHIEFCASHIER.forReturnedReceivingForm.parse(transmittalId);
          } else {
            route = ROUTES.CHIEFCASHIER.viewReturnedForm.parse(transmittalId);
          }
        } else {
          if (displayStatus === FormStatus.VERIFY_LIST) {
            route = ROUTES.CHIEFCASHIER.verificationListForm.parse(transmittalId);
          } else if (displayStatus === FormStatus.VERIFIED) {
            route = ROUTES.CHIEFCASHIER.seriesAssignmentForm.parse(transmittalId);
          } else if (displayStatus === FormStatus.REJECTED) {
            route = ROUTES.CHIEFCASHIER.verificationListForm.parse(transmittalId);
          } else {
            route = ROUTES.CHIEFCASHIER.viewSeriesAssignment.parse(transmittalId);
          }
        }
        
        break;

      case RoleType.ADMINOUTGOING:
        if (displayStatus === FormStatus.RELEASED) {
          route = ROUTES.OUTGOINGADMIN.viewOutgoingAdminTransmittalTrail.parse(transmittalId);
        } else if (displayStatus === FormStatus.FOR_RECEIVING) {
          route = ROUTES.OUTGOINGADMIN.forOutgoingAdminReceivingForm.parse(transmittalId);
        } else if (displayStatus === FormStatus.ON_HAND) {
          route = ROUTES.OUTGOINGADMIN.viewOutgoingAdminTransmittal.parse(transmittalId);
        } else {
          route = ROUTES.OUTGOINGADMIN.forOutgoingAdminReceivingForm.parse(transmittalId);
        }
        break;

      case RoleType.ADMININCOMING:
        if (displayStatus === FormStatus.RELEASED) {
          route = ROUTES.INCOMINGADMIN.viewIncomingAdminTransmittalTrail.parse(transmittalId);
        } else if (displayStatus === FormStatus.FOR_RECEIVING) {
          route = ROUTES.INCOMINGADMIN.forIncomingAdminReceivingForm.parse(transmittalId);
        } else if (displayStatus === FormStatus.ON_HAND) {
          route = ROUTES.INCOMINGADMIN.viewIncomingAdminTransmittal.parse(transmittalId);
        } else {
          route = ROUTES.INCOMINGADMIN.forIncomingAdminReceivingForm.parse(transmittalId);
        }
        break;

      case RoleType.CLIFSA:
        if(isReturned) {
          if (displayStatus === FormStatus.RETURNED) {
            route = ROUTES.CLIFSAADMIN.viewReturnedForm.parse(transmittalId);
          } else if (displayStatus === FormStatus.FOR_RECEIVING) {
            route = ROUTES.CLIFSAADMIN.viewReturnedReceivingForm.parse(transmittalId);
          } else if (displayStatus === FormStatus.ON_HAND) {
            route = ROUTES.CLIFSAADMIN.viewReturnTransmittalForm.parse(transmittalId);
          } else {
            route = ROUTES.CLIFSAADMIN.viewReturnedForm.parse(transmittalId);
          }
        } else {
          if (displayStatus === FormStatus.RELEASED) {
            route = ROUTES.CLIFSAADMIN.viewReleasedForm.parse(transmittalId);
          } else if (displayStatus === FormStatus.FOR_RECEIVING) {
            route = ROUTES.CLIFSAADMIN.viewForReceivingForm.parse(transmittalId);
          } else if (displayStatus === FormStatus.ON_HAND) {
            route = ROUTES.CLIFSAADMIN.viewTransmittalForm.parse(transmittalId);
          } else {
            route = ROUTES.CLIFSAADMIN.viewReleasedForm.parse(transmittalId);
          }
        }
        
        break;

      case RoleType.GAM:
        if (displayStatus === FormStatus.RELEASED) {
          route = ROUTES.GAM.viewReturnedForm.parse(transmittalId);
        } else if (displayStatus === FormStatus.FOR_RECEIVING) {
          route = ROUTES.GAM.viewForReceivingForm.parse(transmittalId);
        } else if (displayStatus === FormStatus.ON_HAND) {
          route = ROUTES.GAM.viewReturnedForm.parse(transmittalId);
        } else {
          route = ROUTES.GAM.viewReturnedForm.parse(transmittalId);
        }
        break;

      default:
        if (displayStatus === FormStatus.RELEASED) {
          route = ROUTES.CLIFSAADMIN.viewReleasedForm.parse(transmittalId);
        } else if (displayStatus === FormStatus.FOR_RECEIVING) {
          route = ROUTES.CLIFSAADMIN.viewForReceivingForm.parse(transmittalId);
        } else if (displayStatus === FormStatus.ON_HAND) {
          route = ROUTES.CLIFSAADMIN.viewTransmittalForm.parse(transmittalId);
        } else {
          route = ROUTES.CLIFSAADMIN.viewReleasedForm.parse(transmittalId);
        }
        break;
    }

    return [
      {
        name: "View",
        event: () => navigate(route),
        icon: GoVersions,
        color: "primary",
      },
    ];
  };

  return { getActionEvents };
};
