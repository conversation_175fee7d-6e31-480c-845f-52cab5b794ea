import { FormStatus, RoleType } from "@enums/form-status";
import { IFormTransmittal } from "@interface/form-inventory.interface";
import { IIncomingReceivedForm } from "@interface/form-inventory.interface";

const getIocStatus = (row: IIncomingReceivedForm) => {
  if (row) {
    const status = row.status || "No Status";
    if (status === FormStatus.PENDING) return FormStatus.INCOMING_RECEIVED;
    if (status === FormStatus.VERIFIED) return FormStatus.VERIFIED;
    if (status === FormStatus.REJECTED) return FormStatus.DENIED;
    return status;
  } else {
    return FormStatus.FOR_RECEIVING;
  }
};

const getHeadCashierStatus = (row: IIncomingReceivedForm) => {
  if (row) {
    const status = row.status || "No Status";
    if (status === FormStatus.PENDING) return FormStatus.VERIFY_LIST;
    if (status === FormStatus.VERIFIED) return FormStatus.VERIFIED;
    if (status === FormStatus.REJECTED) return FormStatus.REJECTED;
    if (status === FormStatus.RELEASED) return FormStatus.RELEASED;
    return status;
  } else {
    return FormStatus.FOR_RECEIVING;
  }
};

// Fixed getTransmittalInventoryStatus function

const getTransmittalInventoryStatus = (row: IFormTransmittal, userId: number) => {
  const trail = row?.formTransmittalTrails?.find((item: any) => item.createdBy?.id === userId);
  const receivingTrail = row?.latestFormTransmittalTrail?.releasedTo?.id === userId;
  const chiefCashier = row?.latestFormTransmittalTrail?.createdBy?.position?.positionName === RoleType.CHIEFCASHIER;
  if (receivingTrail || chiefCashier) {
    return FormStatus.FOR_RECEIVING;
  }

  if (trail) {
    const status = trail.status || "No Status";
    if (status === FormStatus.RELEASED) {
      return FormStatus.RELEASED;
    }
    if (status === FormStatus.RECEIVED) {
      return FormStatus.RELEASED;
    }
    return status;
  } else {
    // No trail found - return ON_HAND
    return FormStatus.ON_HAND;
  }
};

const getGamDisplayStatus = (row: IFormTransmittal, userId: number) => {

  const trail = row?.formTransmittalTrails?.find((item: any) => item.createdBy?.id === userId);
  const receivingTrail = row?.latestFormTransmittalTrail?.releasedTo?.id === userId;

  if (receivingTrail) {
    return FormStatus.FOR_RECEIVING;
  }

  if (row.status === FormStatus.RETURNED) {
    return FormStatus.RETURNED;
  }

  if (trail) {
    const status = trail.status || "No Status";
    if (row.status === FormStatus.RETURNED) {
      return FormStatus.RETURNED;
    }
    if (status === FormStatus.RECEIVED) {
      return FormStatus.RETURNED;
    }
    return status;
  } else {
    return FormStatus.ON_HAND;
  }
};

const getReturnedInventoryStatus = (row: IFormTransmittal) => {
  
  // const trail = row?.formTransmittalTrails?.find((item: any) => item.createdBy?.id === userId);
  const trail = row.latestFormTransmittalTrail;

  if (trail) {
    const status = trail.status || "No Status";
    if (status === FormStatus.RETURNED) {
      return FormStatus.FOR_RECEIVING;
    }
    if (status === FormStatus.RECEIVED) {
      return FormStatus.ON_HAND;
    }
    return status;
  } else {
    return FormStatus.RETURNED;
  }
};

export const FormStatusLogic = {
  getIocStatus,
  getHeadCashierStatus,
  getTransmittalInventoryStatus,
  getReturnedInventoryStatus,
  getGamDisplayStatus,
};
