import { useSelector } from "react-redux";
import { useMemo } from "react";
import { hasAnyRole, hasOnlyValidRoles } from "@helpers/roleChecker";
import { RouteItem } from "@interface/routes.interface";
import { RootState } from "@state/store";
import { hasRequiredPermissions } from "@helpers/permissions";
import { IRole } from "@interface/roles.interface";
import useGlobalSettings from "./useGlobalSettings";

type UseAuthorizedRoutesParams = {
  rawRoutes?: RouteItem[]; // Fully-formed paths like "/admin/dashboard"
  sharedRoutes?: RouteItem[]; // Agnostic paths like "/profile" or "/notification"
};

export const useAuthorizedRoutes = ({ rawRoutes = [], sharedRoutes = [] }: UseAuthorizedRoutesParams): RouteItem[] => {
  // Get current logged user
  const user = useSelector((state: RootState) => state.auth.user?.data);
  const { globalSettings } = useGlobalSettings();

  const currentUserRoles = user?.roles?.map((role: IRole) => role.name) ?? [];

  // assuming single role for now
  const userRole = user?.roles?.[0].name;

  const buildRoutePath = (path: string): string => {
    if (!userRole) return path;

    // Ensure path starts with "/"
    const normalizedPath = path.startsWith("/") ? path : `/${path}`;
    return `/${userRole}${normalizedPath}`;
  };

  const filterRoutes = (routes: RouteItem[]): RouteItem[] => {
    return routes
      .filter((route) => {
        // Skip if hidden
        if (route.hidden) return false;

        // Role based filtering
        if (route.roles) {
          if (!hasOnlyValidRoles(route.roles)) return false;
          if (!hasAnyRole(currentUserRoles, route.roles)) return false;
        }

        // Permission based filtering
        if (route.permissions && !hasRequiredPermissions(user?.roles?.[0].permissions, route.permissions)) return false;

        // Custom condition function
        if (route.conditions && !route.conditions(user, globalSettings)) return false;

        return true;
      })
      .map((route) => ({
        ...route,
        // Recursively filter children if any
        children: route.children ? filterRoutes(route.children) : undefined,
      }));
  };

  return useMemo(() => {
    if (!user || !userRole) return [];

    // Normalize shared routes to include /{current user role} prefix
    const normalizedSharedRoutes = sharedRoutes.map((route) => ({
      ...route,
      path: buildRoutePath(route.path),
    }));

    // Combine and filter all routes
    const allRoutes = [...rawRoutes, ...normalizedSharedRoutes];

    return filterRoutes(allRoutes);
  }, [user, rawRoutes, sharedRoutes]);
};
