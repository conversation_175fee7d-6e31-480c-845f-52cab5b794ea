import { useEffect, useState } from "react";
import Echo from "laravel-echo";
import Pusher from "pusher-js";
import axios from "axios";
import { RootState } from "@state/store";
import { useSelector } from "react-redux";

// Extend the Window interface to include the <PERSON>usher property
declare global {
  interface Window {
    Pusher: typeof Pusher;
  }
}

// Assign Pusher to the window for Laravel Echo
window.Pusher = Pusher;

const useEcho = (): Echo<"reverb"> | null => {
  const [echoInstance, setEchoInstance] = useState<Echo<"reverb"> | null>(null);

  const { data } = useSelector((state: RootState) => state.auth.login);

  useEffect(() => {
    if (!data?.token) return;

    const echo = new Echo({
      broadcaster: "reverb",
      key: import.meta.env.VITE_REVERB_APP_KEY,
      authorizer: (channel: any) => {
        return {
          authorize: (socketId: string, callback: (error: boolean, data: any) => void) => {
            axios
              .post(
                `${import.meta.env.VITE_API_ENDPOINT}/broadcasting/auth`,
                {
                  socket_id: socketId,
                  channel_name: channel.name,
                },
                {
                  headers: {
                    Authorization: `Bearer ${data.token}`,
                  },
                }
              )
              .then((response) => {
                callback(false, response.data);
              })
              .catch((error) => {
                callback(true, error);
              });
          },
        };
      },
      wsHost: import.meta.env.VITE_REVERB_HOST,
      wsPort: import.meta.env.VITE_REVERB_WSPORT,
      wssPort: import.meta.env.VITE_REVERB_WSSPORT,
      forceTLS: import.meta.env.VITE_REVERB_FORCETLS,
      disableStats: import.meta.env.VITE_REVERB_DISABLE_STATS,
      enabledTransports: import.meta.env.VITE_REVERB_ENABLED_TRANSPORTS,
      wsPath: "/reverb",
    });

    setEchoInstance(echo);

    return () => {
      echo.disconnect();
    };
  }, [data?.token]);

  return echoInstance;
};

export default useEcho;
