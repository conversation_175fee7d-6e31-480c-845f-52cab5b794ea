export enum ProposalStatus {
  draft = "DRAFT",
  for_approval = "FOR_APPROVAL",
  approved = "APPROVED",
  rejected = "REJECTED",
  pending = "PENDING",
  active = "ACTIVE",
  for_signatory = "FOR_SIGNATORY",
  archived = "ARCHIVED",
}

export enum ProductProposalStatusFilter {
  rejected = "REJECTED",
  active = "ACTIVE",
  draft = "DRAFT",
  approved = "APPROVED",
}

export enum AttachmentStatus {
  valid = "VALID",
  invalid = "INVALID",
}

export enum ProposalApprovalStatus {
  approved = "APPROVED",
  rejected = "REJECTED",
  disapproved = "DISAPPROVED",
  pending = "FOR APPROVAL",
  forReview = "FOR_REVIEW",
}

export enum FormatStatus {
  Approved = "Approved",
  Disapproved = "Disapproved",
  Reject = "Reject",
  Rejected = "Rejected",
  ForApproval = "For Approval",
  ForRevision = "For Revision",
}
