export enum ProposalAerGuidelines {
  SalientFeatures = "SALIENT FEATURES",
  ClaimProcessFlow = "CLAIM PROCESS FLOW",
  UnderwritingProcedure = "UNDERWRITING PROCEDURE FOR APPLICANTS APPLICABLE FOR",
  ScheduleOfBenefits = "SCHEDULE OF BENEFITS",
  BenefitDescription = "BENEFIT DESCRIPTION",
  ScheduleOfBenefitsForAccidentalDismemberment = "SCHEDULE OF BENEFITS FOR ACCIDENTAL DISMEMBERMENT & DISABILITY BENEFITS",
  ScheduleOfPremiums = "SCHEDULE OF PREMIUMS",
  UnderwritingApproval = "UNDERWRITING APPROVAL",
  Contestability = "CONTESTABILITY",
  Termination = "TERMINATION",
  PremiumComputation = "PREMIUM COMPUTATION",
  GeneralProvisions = "GENERAL PROVISIONS",
  OtherInsuranceProvisions = "OTHER INSURANCE PROVISIONS",
  InsuranceCoverageEffectivity = "INSURANCE COVERAGE EFFECTIVITY",
  CFPScheduleOfPremium = "CFP SCHEDULE OF PREMIUM",
  MaximumNumberOfEnrollees = "MAXIMUM NUMBER OF ENROLLEES",
  GracePeriod = "GRACE PERIOD",
  ScheduleOfBenefitsAndAnnualPremium = "SCHEDULE OF BENEFITS AND ANNUAL PREMIUM",
}

export enum ProductGuidelineType {
  Texteditor = "texteditor",
  TextField = "textfield",
  Table = "table",
  List = "list",
}
