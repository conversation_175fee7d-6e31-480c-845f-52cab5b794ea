import { FormStatus, PadStatus } from "@enums/form-status";
import { NotarizationStatus } from "@enums/notarization-status";
import { ProposalStatus } from "@enums/proposal-status";

//Import for images here:
import { TbAlertTriangle, TbCircleX, TbCircleDashed, TbClockHour4, TbCircleDot } from "react-icons/tb";
import { PiPaperPlaneTiltBold, PiMagnifyingGlassBold, PiCheckCircleBold } from "react-icons/pi";

const capitalizeFirstLetterWords = (text: string, wildcard?: string): string => {
  if (text.length === 0) return text;

  const wc = wildcard ?? "";

  return text
    .toLowerCase()
    .split(wc)
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};
const capitalizeFirstLetterOnly = (text: string): string => {
  // if (text.length === 0) return text;
  if (typeof text !== "string" || text.length === 0) return "";
  return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
};

const getTextStatusColor = (status: string, withTextSize: boolean = true) => {
  const textSize = withTextSize ? "text-xs" : "";
  const base = `font-semibold px-4 py-2 rounded-md ${textSize}`;
  switch (status) {
    case "APPROVED":
    case "ACTIVE":
    case "VALID":
    case "ACCEPTED":
    case "COMPLETED":
    case FormStatus.RELEASED:
    case FormStatus.RECEIVED:
    case NotarizationStatus.notarized:
    case "NOTARIZED":
      return `!text-green-500 bg-green-100 ${base}`;
    case "DRAFT":
    case PadStatus.UNUSED:
      return `!text-sky-400 bg-slate-100 ${base}`;
    case "DISAPPROVED":
    case "REJECTED":
    case "INVALID":
    case "DECLINED":
    case "INVALID REQUIREMENT":
    case "OVERDUE":
    case PadStatus.CANCELLED:
      return `!text-red-500 bg-red-100 ${base}`;
    case "FOR_APPROVAL":
    case "FOR APPROVAL":
    case "PENDING":
    case FormStatus.NOT_YET_RECEIVED:
      return `!text-yellow-500 bg-yellow-100 ${base}`;
    case "FOR_REVISION":
      return `!text-orange-500 bg-orange-100 ${base}`;
    case "FOR OR ISSUANCE":
      return `!text-sky-50 bg-indigo-800 ${base}`;
    case "ASSIGNED":
      return `!text-sky-500 bg-sky-100 ${base}`;
    case "ON-HAND":
      return `!text-yellow-400 bg-yellow-100 ${base}`;
    case ProposalStatus.for_signatory:
      return `!text-zinc-500 bg-zinc-100 ${base}`;
    case PadStatus.USED:
      return `!text-green-500 bg-green-100 ${base}`;
    default:
      return `!text-slate-400 bg-slate-100 ${base}`;
  }
};
const getNewTextStatusColor = (status: string) => {
  const icons = {
    TbAlertTriangle,
    TbCircleX,
    TbCircleDashed,
    TbClockHour4,
    TbCircleDot,
    PiPaperPlaneTiltBold,
    PiMagnifyingGlassBold,
    PiCheckCircleBold,
  };
  const statusTag = (type: string, text: string) => {
    switch (type) {
      case "saffron":
        return {
          type: "enhanced",
          icon: icons["TbAlertTriangle" as keyof typeof icons],
          text: text,
          className: "flex items-center font-poppins-semibold justify-center w-max gap-1 p-2 rounded-xl text-saffron-mango-600 bg-saffron-mango-100 text-sm",
        };
      case "red":
        return {
          type: "enhanced",
          icon: icons["TbCircleX" as keyof typeof icons],
          text: text,
          className: "flex items-center font-poppins-semibold justify-center w-max gap-1 p-2 rounded-xl text-red-600 bg-red-100 text-sm",
        };
      case "sky":
        return {
          type: "enhanced",
          icon: icons["TbCircleDashed" as keyof typeof icons],
          text: text,
          className: "flex items-center font-poppins-semibold justify-center w-max gap-1 p-2 rounded-xl text-sky-500 bg-sky-100 text-sm",
        };
      case "violet":
        return {
          type: "enhanced",
          icon: icons["PiPaperPlaneTiltBold" as keyof typeof icons],
          text: text,
          className: "flex items-center font-poppins-semibold justify-center w-max gap-1 p-2 rounded-xl text-violet-500 bg-violet-100 text-sm",
        };
      case "amber":
        return {
          type: "enhanced",
          icon: icons["PiMagnifyingGlassBold" as keyof typeof icons],
          text: text,
          className: "flex items-center font-poppins-semibold justify-center w-max gap-1 p-2 rounded-xl text-amber-400 bg-amber-100 text-sm",
        };
      case "yellow":
        return {
          type: "enhanced",
          icon: icons["TbClockHour4" as keyof typeof icons],
          text: text,
          className: "flex items-center font-poppins-semibold justify-center w-max gap-1 p-2 rounded-xl text-yellow-500 bg-yellow-100 text-sm",
        };
      case "green":
        return {
          type: "enhanced",
          icon: icons["PiCheckCircleBold" as keyof typeof icons],
          text: text,
          className: "flex items-center font-poppins-semibold justify-center w-max gap-1 p-2 rounded-xl text-green-500 bg-green-100 text-sm",
        };
      case "zinc":
        return {
          type: "enhanced",
          icon: icons["TbClockHour4" as keyof typeof icons],
          text: text,
          className: "flex items-center font-poppins-semibold justify-center w-max gap-1 p-2 rounded-xl text-zinc-500 bg-zinc-200 text-sm",
        };
      case "indigo":
        return {
          type: "enhanced",
          icon: icons["TbCircleDashed" as keyof typeof icons],
          text: text,
          className: "flex items-center font-poppins-semibold justify-center w-max gap-1 p-2 rounded-xl text-indigo-500 bg-indigo-100 text-sm",
        };
      case "slate":
      default:
        return {
          type: "enhanced",
          icon: icons["TbCircleDot" as keyof typeof icons],
          text: text,
          className: "flex items-center font-poppins-semibold justify-center w-max gap-1 p-2 rounded-xl text-slate-400 bg-slate-100 text-sm",
        };
    }
  };
  switch (status) {
    case "APPROVED":
      return statusTag("green", status);
    case "ACTIVE":
      return statusTag("green", status);
    case "DRAFT":
      return statusTag("slate", status);
    case "DISAPPROVED":
      return statusTag("red", status);
    case "FOR_APPROVAL":
      return statusTag("yellow", status);
    case "FOR APPROVAL":
      return statusTag("yellow", status);
    case "PENDING":
      return statusTag("yellow", status);
    case "FOR_REVISION":
      return statusTag("orange", status);
    case "REJECTED":
      return statusTag("red", status);
    case "INVALID":
      return statusTag("red", status);
    case "VALID":
      return statusTag("green", status);
    case "COMPLETED":
      return statusTag("green2", status);
    case "FOR OR ISSUANCE":
      return statusTag("indigo", status);
    // return statusTag("green",status);
    //Adding UATStatus cases
    case "ACCEPTED":
      return statusTag("green", status);
    case "DECLINED":
      return statusTag("red", status);
    // Adding NotarizationStatus cases
    case NotarizationStatus.notarized:
    case "NOTARIZED":
      return statusTag("green", status);
    case "INVALID REQUIREMENT":
      return statusTag("red", status);
    case "OVERDUE":
      return statusTag("red", status);
    case "ASSIGNED":
      return statusTag("sky", status);
    // Adding FormStatus cases
    case FormStatus.RELEASED:
      return statusTag("green", status);
    case FormStatus.RECEIVED:
      return statusTag("green", status);
    case FormStatus.NOT_YET_RECEIVED:
      return statusTag("yellow", status);
    case ProposalStatus.for_signatory:
      return statusTag("yellow", status);
    case "IN_PROGRESS":
      return statusTag("violet", status);
    case "RESOLVED":
      return statusTag("green", status);
    case "UNRESOLVED":
      return statusTag("red", status);
    default:
      return statusTag("slate", status);
  }
};

/**
 * Truncates a name if it exceeds a certain character limit.
 * @param name - The full name to be truncated.
 * @param maxChars - The maximum number of characters to display before truncating.
 * @returns The truncated name with "..." if it's too long, or the full name if within the limit.
 */
const truncateName = (name: string, maxChars: number = 20): string => {
  // If the name length is within the limit, return it as is
  if (name.length <= maxChars) {
    return name;
  }

  // Return the substring with "..." appended
  return name.slice(0, maxChars) + " ...";
};

const formatStringAtoZ0to9 = (str: string): string => {
  return str.replace(/[^a-z0-9]/gi, "").toLowerCase();
};

const formatConstants = (constant: string): string => {
  return constant
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
};

// Helper function to convert number to Roman numeral
const formatToRomanNumeral = (num: number): string => {
  const romanNumerals = [
    { value: 1000, symbol: "M" },
    { value: 900, symbol: "CM" },
    { value: 500, symbol: "D" },
    { value: 400, symbol: "CD" },
    { value: 100, symbol: "C" },
    { value: 90, symbol: "XC" },
    { value: 50, symbol: "L" },
    { value: 40, symbol: "XL" },
    { value: 10, symbol: "X" },
    { value: 9, symbol: "IX" },
    { value: 5, symbol: "V" },
    { value: 4, symbol: "IV" },
    { value: 1, symbol: "I" },
  ];

  let result = "";
  for (const { value, symbol } of romanNumerals) {
    while (num >= value) {
      result += symbol;
      num -= value;
    }
  }
  return result;
};

export { capitalizeFirstLetterWords, capitalizeFirstLetterOnly, getTextStatusColor, truncateName, formatStringAtoZ0to9, formatConstants, formatToRomanNumeral, getNewTextStatusColor };
