// permissions.ts
import { IUserRPermission } from "@interface/user.interface";
import { PermissionType, UserRoles } from "@interface/routes.interface";
import { hasPermission } from "../permissions";

const normalize = (s: string) => s?.trim().toLowerCase() ?? "";

// Include admin-like roles if they should always pass
const PROPOSAL_ROLES: UserRoles[] = [UserRoles.sales, UserRoles.rnd, UserRoles.actuary, UserRoles.marketing, UserRoles.admin];

const getMatchedRoles = (user: IUserRPermission, allowed: UserRoles[]) => {
  const allowedSet = new Set(allowed.map(normalize));
  return (user?.roles ?? []).filter((r) => allowedSet.has(normalize(r.name)));
};

const collectPerms = (roles: any[]) => new Set(roles.flatMap((r) => (r.permissions ?? []).map((p: any) => p.name)));

export const hasAnyPermission = (user: IUserRPermission, permissions: PermissionType[], roles: UserRoles[]) => {
  const matched = getMatchedRoles(user, roles);
  // If you want to allow “has the permission regardless of role”, uncomment next line:
  // const matched = getMatchedRoles(user, roles).length ? getMatchedRoles(user, roles) : (user?.roles ?? []);
  if (!matched.length) return false;
  const permSet = collectPerms(matched);
  return permissions.some((p) => permSet.has(p));
};

export const canViewProductProposal = (u: IUserRPermission) => hasPermission(u, PermissionType.PRODUCT_PROPOSAL_VIEW, PROPOSAL_ROLES);
export const canCreateProductProposal = (u: IUserRPermission) => hasPermission(u, PermissionType.PRODUCT_PROPOSAL_CREATE, PROPOSAL_ROLES);
export const canUpdateProductProposal = (u: IUserRPermission) => hasPermission(u, PermissionType.PRODUCT_PROPOSAL_UPDATE, PROPOSAL_ROLES);
export const canDeleteProductProposal = (u: IUserRPermission) => hasPermission(u, PermissionType.PRODUCT_PROPOSAL_DELETE, PROPOSAL_ROLES);
