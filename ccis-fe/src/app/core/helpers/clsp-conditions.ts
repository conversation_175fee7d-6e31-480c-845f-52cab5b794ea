import condition, { condition2, condition3, condition4 } from "@modules/sales/clsp-quotations/template/condition";

  
  export const getCondition = ({
    totalNumberOfMembers,
    contestabilityText,
    numberOfOptions,
  }: {
    totalNumberOfMembers: number;
    contestabilityText: string;
    numberOfOptions: number;
  }) => {
    const label = contestabilityText.toLowerCase().trim();
  
    if (numberOfOptions === 1) {
      if (label === "1 year") {
        return condition3(totalNumberOfMembers);
      } else if (label === "waived with masterlist") {
        return condition4(totalNumberOfMembers);
      }
    } else if (numberOfOptions > 1) {
      if (label === "1 year") {
        return condition(totalNumberOfMembers);
      } else if (label === "waived with masterlist") {
        return condition2(totalNumberOfMembers);
      }
    }
  
    return condition(totalNumberOfMembers);
  };
  