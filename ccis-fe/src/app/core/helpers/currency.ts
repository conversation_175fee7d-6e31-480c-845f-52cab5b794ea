export const formatCurrency = (value: number): string => {
  return `₱${value.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};

/**
 * Formats a number into Philippine Peso currency with commas and 2 decimals
 * Example: 1234567.5 => ₱1,234,567.50
 */
export const formatPeso = (value: number | string | null | undefined): string => {
  if (value === null || value === undefined || value === "") return "₱0.00";

  const num = typeof value === "string" ? parseFloat(value) : value;
  if (isNaN(num)) return "₱0.00";

  return num.toLocaleString("en-PH", {
    style: "currency",
    currency: "PHP",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};
