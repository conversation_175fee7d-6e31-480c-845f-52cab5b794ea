import { RevisionStatus } from "@enums/revision-status";
import { IGuideline } from "@interface/guidelines.interface";
import { IProductRevisions } from "@interface/products.interface";
// import { toast } from "react-toastify";
import { removeProperties } from "./objects";

// TODO: Refactor functions for better processing

// const validateTables = (content?: IGuidelineContent | null) => {
//   const tableContent = content?.value as IGuidelineContentTable;
//   if (tableContent === undefined) return false;
//   if (tableContent === null) return false;

//   //   check columns
//   if (tableContent.columns === undefined) return false;
//   if (tableContent.columns === null) return false;
//   if (tableContent.columns?.length === 0) return false;

//   const columnResult = tableContent.columns.map((column) => {
//     if (column.value === "") return false;
//     if (column.value === undefined) return false;
//     if (column.value === null) return false;
//   });

//   if (columnResult.includes(false)) return false;
//   // check rows
//   if (tableContent.rows === undefined) return false;
//   if (tableContent.rows === null) return false;
//   if (tableContent.rows.length === 0) return false;

//   const result = tableContent.rows.map((row) => {
//     if (row.length === 0) return false;
//     return row.map((item) => {
//       if (item.value === "") return false;
//       if (item.value === undefined) return false;
//       if (item.value === null) return false;
//     });
//   });

//   if (result.includes(false)) return false;
//   return true;
// };

// const validateTextEditors = (content?: IGuidelineContent | null) => {
//   //TODO: Handle clearing process of text editor as it retains <p></p> tags when cleared
//   if (content?.value === "") return false;
//   if (content?.value === undefined) return false;
//   if (content?.value === null) return false;
//   return true;
// };

// const validateList = (content?: IGuidelineContent | null): boolean => {
//   if (content === undefined) return false;
//   if (content === null) return false;

//   if (content.label === "") return false;
//   if (content.label === undefined) return false;
//   if (content.label === null) return false;

//   // check if there is a sub-item and if it is an array
//   const contentArray = content.value as IGuidelineContent[];

//   if (Array.isArray(contentArray) && contentArray.length > 0) {
//     const result = contentArray.map((item) => {
//       if (item.value === "") return false;
//       if (item.value === undefined) return false;
//       if (item.value === null) return false;
//     });

//     if (result.includes(false)) return false;
//   }

//   return true;
// };

// const validateTextField = (content?: IGuidelineContent | null): boolean => {
//   if (content?.value === "") return false;
//   if (content?.value === undefined) return false;
//   if (content?.value === null) return false;

//   return true;
// };

// const validateGuidelines = (guidelines?: IGuideline[]): boolean => {
//   // validate if guidelines is not empty
//   if (!guidelines) return false;
//   if (!Array.isArray(guidelines)) return false;
//   let valid = true;

//   // check if each guideline has a productGuideline content
//   guidelines.map((guideline) => {
//     if (guideline.productGuideline.length === 0) {
//       valid = false;
//     }
//   });

//   return valid;
// };

// const validateGuidelineContents = (guidelines?: IGuideline[]): boolean => {
//   let valid = false;

//   try {
//     const result = guidelines
//       ?.map((guideline) => {
//         return guideline.productGuideline
//           .map((content) => {
//             switch (content.type) {
//               case "table":
//                 valid = validateTables(content);
//                 if (!valid) toast.error(`${guideline.label} table content is invalid`);
//                 break;
//               case "texteditor":
//                 valid = validateTextEditors(content);
//                 if (!valid) toast.error(`${guideline.label} text editor content is invalid`);
//                 break;
//               case "list":
//                 valid = validateList(content);
//                 if (!valid) toast.error(`${guideline.label} list content is invalid`);
//                 break;
//               case "textfield":
//                 valid = validateTextField(content);
//                 if (!valid) toast.error(`${guideline.label} text field content is invalid`);
//                 break;
//               default:
//                 break;
//             }
//             return valid;
//           })
//           .includes(false);
//       })
//       .includes(false);
//     return result ?? false;
//   } catch (e) {
//     toast.error("An error occurred while validating guidelines");
//     return false;
//   }
// };

const validateGuidelines = (guidelines?: IGuideline[]): boolean => {
  if (!guidelines || !Array.isArray(guidelines)) return false;
  let valid = true;
  guidelines.forEach((g) => {
    if (!isEditableGuideline(g)) return; // skip non-editable
    if (g.productGuideline.length === 0) valid = false;
  });
  return valid;
};

const validateGuidelineContents = (guidelines?: IGuideline[]): boolean => {
  if (!guidelines || !Array.isArray(guidelines)) return false;

  let allValid = true;

  return allValid;
};

const selectLatestApprovedProductGuideline = (productRevisions: IProductRevisions[]) => {
  if (productRevisions.length === 0) {
    return null;
  }

  const approvedRevisions = productRevisions.filter((revision: any) => revision.approvalStatus === RevisionStatus.approved);

  if (approvedRevisions.length === 0) {
    return null;
  }

  const latestApprovedRevision = approvedRevisions.reduce((latest: any, current: any) => {
    return new Date(current.updatedAt) > new Date(latest.updatedAt) ? current : latest;
  });

  const data = removeProperties(latestApprovedRevision, ["id", "revisionNumber", "approvalStatus", "approvalType", "approvedAt"]);
  return data;
};

const isEditableGuideline = (g: IGuideline) => g?.editable === 1 || g?.editable === true;

export { selectLatestApprovedProductGuideline, validateGuidelines, validateGuidelineContents, isEditableGuideline };
