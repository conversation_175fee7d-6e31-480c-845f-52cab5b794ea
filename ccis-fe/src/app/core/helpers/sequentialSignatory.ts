import { Statuses } from "@constants/global-constant-value";
export const isPrecedingSignatoryApproved = (signatories: any[], userId: number | string): boolean => {
  if (!Array.isArray(signatories) || signatories.length === 0) return false;

  // Check if all statuses are PENDING
  const allPending = signatories.every((s) => String(s?.status).toUpperCase() === Statuses.PENDING);

  // Find the current user's signatory
  const current = signatories.find((s) => s?.userId === userId);
  if (!current) return false;

  if (allPending) {
    // If all are pending and current user is sequence 1, allow
    return current.sequence === 1;
  }

  // Find the preceding signatory by sequence
  const preceding = signatories.find((s) => s?.sequence === current.sequence - 1);
  if (!preceding) return false;

  // Check if the preceding signatory's status is APPROVED
  return String(preceding.status).toUpperCase() === Statuses.APPROVED;
};
export const getPrecedingUserNameOfLatestApproved = (signatories: any[]): string | null => {
  if (!Array.isArray(signatories) || signatories.length === 0) return null;

  // Find all APPROVED signatories
  const approved = signatories.filter((s) => String(s.status).toUpperCase() === "APPROVED").sort((a, b) => b.sequence - a.sequence);

  if (approved.length > 0) {
    // Get the greatest sequence with APPROVED
    const latestApproved = approved[0];
    // Find the next signatory (sequence + 1)
    const nextSignatory = signatories.find((s) => s.sequence === latestApproved.sequence + 1);
    if (nextSignatory && nextSignatory.user) {
      const { firstname = "", middlename = "", lastname = "" } = nextSignatory.user;
      const middleInitial = middlename ? ` ${middlename[0]}.` : "";
      return `${firstname}${middleInitial} ${lastname}`.trim();
    }
    return null;
  } else {
    // No APPROVED found, return user info of the smallest sequence
    const sorted = signatories.slice().sort((a, b) => a.sequence - b.sequence);
    const first = sorted[0];
    if (first && first.user) {
      const { firstname = "", middlename = "", lastname = "" } = first.user;
      const middleInitial = middlename ? ` ${middlename[0]}.` : "";
      return `${firstname}${middleInitial} ${lastname}`.trim();
    }
    return null;
  }
};
export const getRejectedUserName = (signatories: any[]): string | null => {
  if (!Array.isArray(signatories) || signatories.length === 0) return null;

  const rejected = signatories.find((s) => String(s.status).toUpperCase() === "REJECTED");

  if (rejected && rejected.user) {
    const { firstname = "", middlename = "", lastname = "" } = rejected.user;
    const middleInitial = middlename ? ` ${middlename[0]}.` : "";
    return `${firstname}${middleInitial} ${lastname}`.trim();
  }

  return null;
};
