export const formatNumber = (value: number | string, hasDecimal = false): string => {
  const decimal = hasDecimal ? { minimumFractionDigits: 2, maximumFractionDigits: 2 } : {};
  const number = typeof value === "string" ? parseFloat(value) : value;
  if (isNaN(number)) return "N/A";
  return new Intl.NumberFormat("en-US", decimal).format(number);
};
/**
 * Formats a number string with commas for thousands and preserves decimals.
 * @param value The input value (string or number)
 * @returns Formatted string with commas
 */
export function formatNumberWithCommas(value: string | number): string {
  if (value === null || value === undefined || value === "") return "";

  // Convert to string and remove all non-numeric except dot and minus
  let cleaned = String(value).replace(/[^0-9.\-]/g, "");

  // Allow only the first dot (for decimals)
  const parts = cleaned.split(".");
  const integer = parts[0];
  const decimal = parts.length > 1 ? parts.slice(1).join("") : undefined;

  // Format integer part with commas
  const formattedInt = integer.replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  // Return with decimals if present
  return decimal !== undefined ? `${formattedInt}.${decimal}` : formattedInt;
}

export function parseFormattedNumber(value: string): number {
  const numericString = value.replace(/,/g, "");
  return parseFloat(numericString) || 0;
}
