import { useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import Loader from "../components/Loader";
import { RootState } from "../state/reducer";
import { useSelector } from "react-redux";
import { UserRoles } from "../interface/routes.interface";
import { ROUTES } from "../constants/routes";
import { hasRole } from "@helpers/roleChecker";

const AuthLayout = ({ children }: { children?: React.ReactNode }) => {
  const navigate = useNavigate();

  const { data: currentUser, loading: getUserLoading, success: getUserSuccess } = useSelector((state: RootState) => state.auth.user);
  const { data: auth } = useSelector((state: RootState) => state?.auth?.login);

  const isAuthenticated = useMemo(() => {
    return !getUserLoading && getUserSuccess && currentUser && auth?.token;
  }, [getUserLoading, currentUser, getUserSuccess, auth?.token]);

  useEffect(() => {
    if (!getUserLoading) {
      if (isAuthenticated) {
        if (hasRole(currentUser?.roles ?? [], UserRoles.admin)) {
          //admin
          navigate(ROUTES.ADMIN.dashboard.key);
          //marketing
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.marketing)) {
          navigate(ROUTES.MARKETING.marketingDashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.actuary)) {
          //actuary
          navigate(ROUTES.ACTUARY.actuaryDashboard.key);
          //sales executive assistant
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.sea)) {
          navigate(ROUTES.SALESEXECUTIVEASSISTANT.salesExecutiveAssistantDashboard.key);
          //uatadmin
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.uatadmin)) {
          navigate(ROUTES.UATADMIN.uatManagement.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.cashier)) {
          navigate(ROUTES.CASHIER.actuaryDashboard.key);
        }
        //treasury
        else if (hasRole(currentUser?.roles ?? [], UserRoles.treasury)) {
          navigate(ROUTES.TREASURY.treasuryDashboard.key);
        }
        //compliance
        else if (hasRole(currentUser?.roles ?? [], UserRoles.compliance)) {
          navigate(ROUTES.COMPLIANCE.complianceDashboard.key);
        }
        //sales
        else if (hasRole(currentUser?.roles ?? [], UserRoles.sales)) {
          navigate(ROUTES.SALES.salesDashboard.key);
        }
        //cashier incoming
        else if (hasRole(currentUser?.roles ?? [], UserRoles.incomingCashier)) {
          navigate(ROUTES.INCOMINGCASHIER.incomingCashierDashboard.key);
        }
        //cashier outgoing
        else if (hasRole(currentUser?.roles ?? [], UserRoles.outgoingCashier)) {
          navigate(ROUTES.OUTGOINGCASHIER.outgoingCashierDashboard.key);
          //outgoing admin
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.outgoingAdmin)) {
          navigate(ROUTES.OUTGOINGADMIN.outgoingAdminDashboard.key);
          //chief cashier
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.chiefCashier)) {
          navigate(ROUTES.CHIEFCASHIER.chiefCashierDashboard.key);
        }
        //clifsa admin
        else if (hasRole(currentUser?.roles ?? [], UserRoles.clifsaAdmin)) {
          navigate(ROUTES.CLIFSAADMIN.clifsaAdminDashboard.key);
        }
        //area admin
        else if (hasRole(currentUser?.roles ?? [], UserRoles.areaAdmin)) {
          navigate(ROUTES.AREAADMIN.areaAdminDashboard.key);
        }
        //gam
        else if (hasRole(currentUser?.roles ?? [], UserRoles.gam)) {
          navigate(ROUTES.GAM.gamDashboard.key);
        }
        // admin satellite
        else if (hasRole(currentUser?.roles ?? [], UserRoles.adminSatellite)) {
          navigate(ROUTES.ADMINSATELLITE.adminSatelliteDashboard.key);
        }
        //IO Cashier
        else if (hasRole(currentUser?.roles ?? [], UserRoles.ioc)) {
          navigate(ROUTES.INCOMINGOUTGOINGCASHIER.incomingOutgoingCashierDashboard.key);
        }
        //CAC
        else if (hasRole(currentUser?.roles ?? [], UserRoles.cac)) {
          navigate(ROUTES.CAC.cacDashboard.key);
        }
        //underwriting
        else if (hasRole(currentUser?.roles ?? [], UserRoles.underwriting)) {
          navigate(ROUTES.UNDERWRITING.underwritingDashboard.key);
        }
        //claims
        else if (hasRole(currentUser?.roles ?? [], UserRoles.claims)) {
          navigate(ROUTES.CLAIMS.claimsDashboard.key);
        }
        //incoming admin
        else if (hasRole(currentUser?.roles ?? [], UserRoles.incomingAdmin)) {
          navigate(ROUTES.INCOMINGADMIN.incomingAdminDashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.actuaryManager)) {
          navigate(ROUTES.ACTUARYMANAGER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.actuaryAnalyst1)) {
          navigate(ROUTES.ACTUARYANALYST1.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.actuaryAssistant1)) {
          navigate(ROUTES.ACTUARYASSISTANT1.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.avpLifeNonLife)) {
          navigate(ROUTES.AVPFORLIFEANDNONLIFE.dashboard.key);
          //Research and Development officer
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.rnd)) {
          navigate(ROUTES.RESEARCHANDDEVELOPMENT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.claimsManager)) {
          navigate(ROUTES.CLAIMSMANAGER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.vicePresidentSales)) {
          navigate(ROUTES.VICEPRESIDENTFORSALES.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.vicePresidentOperations)) {
          navigate(ROUTES.VICEPRESIDENTFOROPERATION.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.presidentCeo)) {
          navigate(ROUTES.PRESIDENTANDCEO.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.areaSalesManager)) {
          navigate(ROUTES.AREASALESMANAGER.dashboard.key);
        }
        // infrastructure officer
        else if (hasRole(currentUser?.roles ?? [], UserRoles.infraOfficer)) {
          navigate(ROUTES.INFRASTRUCTUREOFFICER.dashboard.key);
        }
        // accounting
        else if (hasRole(currentUser?.roles ?? [], UserRoles.accounting)) {
          navigate(ROUTES.ACCOUNTING.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.propertyCustodian)) {
          navigate(ROUTES.PROPERTY_CUSTODIAN.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.regionalSalesManager)) {
          navigate(ROUTES.REGIONAL_SALES_MANAGER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.vpAgencyDistributionChannelManagement)) {
          navigate(ROUTES.VP_AGENCY_DISTRIBUTION_CHANNEL_MANAGEMENT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.dataProcessingAssistant)) {
          navigate(ROUTES.DATA_PROCESSING_ASSISTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.dataProcessingAssistant1)) {
          navigate(ROUTES.DATA_PROCESSING_ASSISTANT_1.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.maintenance)) {
          navigate(ROUTES.MAINTENANCE.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.frontEndProgrammer)) {
          navigate(ROUTES.FRONT_END_PROGRAMMER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.administrativeAssistant)) {
          navigate(ROUTES.ADMINISTRATIVE_ASSISTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.managerAgencyDistributionChannel)) {
          navigate(ROUTES.MANAGER_AGENCY_DISTRIBUTION_CHANNEL.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.avpAdminCorplan)) {
          navigate(ROUTES.AVP_ADMIN_CORPLAN.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.bookkeeper)) {
          navigate(ROUTES.BOOKKEEPER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.adminSpecialist)) {
          navigate(ROUTES.ADMIN_SPECIALIST.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.claimsAssistant)) {
          navigate(ROUTES.CLAIMS_ASSISTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.uiUxDesigner)) {
          navigate(ROUTES.UI_UX_DESIGNER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.lifeCashier)) {
          navigate(ROUTES.LIFE_CASHIER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.policyIssuanceAssistant)) {
          navigate(ROUTES.POLICY_ISSUANCE_ASSISTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.backEndProgrammer)) {
          navigate(ROUTES.BACK_END_PROGRAMMER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.nlPropertyClaimsAssistant1)) {
          navigate(ROUTES.NL_PROPERTY_CLAIMS_ASSISTANT_1.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.filingClerk)) {
          navigate(ROUTES.FILING_CLERK.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.bookkeeperNonlife)) {
          navigate(ROUTES.BOOKKEEPER_NONLIFE.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.underwritingStaff)) {
          navigate(ROUTES.UNDERWRITING_STAFF.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.dataEncoder)) {
          navigate(ROUTES.DATA_ENCODER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.salesDevelopmentOfficerNonlife)) {
          navigate(ROUTES.SALES_DEVELOPMENT_OFFICER_NONLIFE.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.businessDevelopmentManager)) {
          navigate(ROUTES.BUSINESS_DEVELOPMENT_MANAGER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.memberRelationsAssistant2Life)) {
          navigate(ROUTES.MEMBER_RELATIONS_ASSISTANT_2_LIFE.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.assistantCashier1)) {
          navigate(ROUTES.ASSISTANT_CASHIER_1.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.treasuryOfficer)) {
          navigate(ROUTES.TREASURY_OFFICER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.vpFinanceInvestmentTreasuryCompliance)) {
          navigate(ROUTES.VP_FINANCE_INVESTMENT_TREASURY_COMPLIANCE.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.claimsAssistant1Life)) {
          navigate(ROUTES.CLAIMS_ASSISTANT_1_LIFE.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.adminAssistantVpFinance)) {
          navigate(ROUTES.ADMIN_ASSISTANT_VP_FINANCE.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.businessAnalyst)) {
          navigate(ROUTES.BUSINESS_ANALYST.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.dataProcessingManager)) {
          navigate(ROUTES.DATA_PROCESSING_MANAGER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.oicOperationsManagerNonLife)) {
          navigate(ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.oicSalesDevelopmentSpecialistVisayas)) {
          navigate(ROUTES.OIC_SALES_DEVELOPMENT_SPECIALIST_VISAYAS.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.claimsProcessor)) {
          navigate(ROUTES.CLAIMS_PROCESSOR.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.actuaryAnalyst)) {
          navigate(ROUTES.ACTUARY_ANALYST.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.technicalSupport)) {
          navigate(ROUTES.TECHNICAL_SUPPORT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.hrAssistant)) {
          navigate(ROUTES.HR_ASSISTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.internalAuditor)) {
          navigate(ROUTES.INTERNAL_AUDITOR.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.corporatePlanningAssistant1)) {
          navigate(ROUTES.CORPORATE_PLANNING_ASSISTANT_1.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.subsidiaryProjectAccountant)) {
          navigate(ROUTES.SUBSIDIARY_PROJECT_ACCOUNTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.claimsAssistant2)) {
          navigate(ROUTES.CLAIMS_ASSISTANT_2.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.disbursementAssistant)) {
          navigate(ROUTES.DISBURSEMENT_ASSISTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.complianceManagerLife)) {
          navigate(ROUTES.COMPLIANCE_MANAGER_LIFE.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.claimsAdminAsst)) {
          navigate(ROUTES.CLAIMS_ADMIN_ASST.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.dataWarehousingManager)) {
          navigate(ROUTES.DATA_WAREHOUSING_MANAGER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.salesDevelopmentAnalystNcrCentralLuzon)) {
          navigate(ROUTES.SALES_DEVELOPMENT_ANALYST_NCR_CENTRAL_LUZON.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.underwritingAssistant1)) {
          navigate(ROUTES.UNDERWRITING_ASSISTANT_1.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.oicBookkeeperTiano)) {
          navigate(ROUTES.OIC_BOOKKEEPER_TIANO.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.adminAssistant1VpAdminCorplan)) {
          navigate(ROUTES.ADMIN_ASSISTANT_1_VP_ADMIN_CORPLAN.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.reinsuranceSpecialist)) {
          navigate(ROUTES.REINSURANCE_SPECIALIST.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.communicationsAssistant)) {
          navigate(ROUTES.COMMUNICATIONS_ASSISTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.bankReconciliationAssistant1Life)) {
          navigate(ROUTES.BANK_RECONCILIATION_ASSISTANT_1_LIFE.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.adminAsstOutgoing)) {
          navigate(ROUTES.ADMIN_ASST_OUTGOING.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.oicAsm)) {
          navigate(ROUTES.OIC_ASM.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.accountingAssistant)) {
          navigate(ROUTES.ACCOUNTING_ASSISTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.technicalWriter)) {
          navigate(ROUTES.TECHNICAL_WRITER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.underwritingAssistant)) {
          navigate(ROUTES.UNDERWRITING_ASSISTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.salesDevelopmentAnalystSouthLuzon)) {
          navigate(ROUTES.SALES_DEVELOPMENT_ANALYST_SOUTH_LUZON.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.driver)) {
          navigate(ROUTES.DRIVER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.accountant)) {
          navigate(ROUTES.ACCOUNTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.vpSalesMarketing)) {
          navigate(ROUTES.VP_SALES_MARKETING.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.socialMediaAssistant)) {
          navigate(ROUTES.SOCIAL_MEDIA_ASSISTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.administrativeAssistantOperationsLife)) {
          navigate(ROUTES.ADMINISTRATIVE_ASSISTANT_OPERATIONS_LIFE.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.vpAdminCorplanCeoPrincipalCcp)) {
          navigate(ROUTES.VP_ADMIN_CORPLAN_CEO_PRINCIPAL_CCP.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.fireMarshall)) {
          navigate(ROUTES.FIRE_MARSHALL.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.messengerUtilityStaff)) {
          navigate(ROUTES.MESSENGER_UTILITY_STAFF.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.bordereaux)) {
          navigate(ROUTES.BORDEREAUX.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.bankReconciliationAssistant1)) {
          navigate(ROUTES.BANK_RECONCILIATION_ASSISTANT_1.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.nafecoopBusinessDevtAssistant)) {
          navigate(ROUTES.NAFECOOP_BUSINESS_DEVT_ASSISTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.qualityDataProcessorRetrievalTechnicalSupport)) {
          navigate(ROUTES.QUALITY_DATA_PROCESSOR_RETRIEVAL_TECHNICAL_SUPPORT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.accountingAssistant1)) {
          navigate(ROUTES.ACCOUNTING_ASSISTANT_1.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.qaDocumentationAnalyst1)) {
          navigate(ROUTES.QA_DOCUMENTATION_ANALYST_1.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.actuaryAnalyst2)) {
          navigate(ROUTES.ACTUARY_ANALYST_2.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.avpInvestmentTreasury)) {
          navigate(ROUTES.AVP_INVESTMENT_TREASURY.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.collectionAnalyst)) {
          navigate(ROUTES.COLLECTION_ANALYST.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.digitalMediaAssistantReliever)) {
          navigate(ROUTES.DIGITAL_MEDIA_ASSISTANT_RELIEVER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.investmentAssistant)) {
          navigate(ROUTES.INVESTMENT_ASSISTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.learningProgramCoordinator)) {
          navigate(ROUTES.LEARNING_PROGRAM_COORDINATOR.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.qualityAssuranceDocumentationAssistant1)) {
          navigate(ROUTES.QUALITY_ASSURANCE_DOCUMENTATION_ASSISTANT_1.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.businessDevelopmentJuniorManager)) {
          navigate(ROUTES.BUSINESS_DEVELOPMENT_JUNIOR_MANAGER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.avpNlSalesLuzon)) {
          navigate(ROUTES.AVP_NL_SALES_LUZON.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.executiveManager)) {
          navigate(ROUTES.EXECUTIVE_MANAGER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.underwriter)) {
          navigate(ROUTES.UNDERWRITER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.adminAsstCashier)) {
          navigate(ROUTES.ADMIN_ASST_CASHIER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.administrativeAssistantProcurement)) {
          navigate(ROUTES.ADMINISTRATIVE_ASSISTANT_PROCUREMENT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.policyIssuanceAsst)) {
          navigate(ROUTES.POLICY_ISSUANCE_ASST.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.oicUnderwritingOfficer)) {
          navigate(ROUTES.OIC_UNDERWRITING_OFFICER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.systemProgrammer)) {
          navigate(ROUTES.SYSTEM_PROGRAMMER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.treasuryAssistant1)) {
          navigate(ROUTES.TREASURY_ASSISTANT_1.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.bookkeeperNl)) {
          navigate(ROUTES.BOOKKEEPER_NL.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.networkAdministrator)) {
          navigate(ROUTES.NETWORK_ADMINISTRATOR.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.claimsAssistant2Microinsurance)) {
          navigate(ROUTES.CLAIMS_ASSISTANT_2_MICROINSURANCE.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.cashieringAssistant)) {
          navigate(ROUTES.CASHIERING_ASSISTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.avpSalesLife)) {
          navigate(ROUTES.AVP_SALES_LIFE.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.claimsExaminer)) {
          navigate(ROUTES.CLAIMS_EXAMINER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.marketingAssistant)) {
          navigate(ROUTES.MARKETING_ASSISTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.oicCompensationBenefitsAnalyst1)) {
          navigate(ROUTES.OIC_COMPENSATION_BENEFITS_ANALYST_1.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.operationsManager)) {
          navigate(ROUTES.OPERATIONS_MANAGER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.insurtechSeniorManager)) {
          navigate(ROUTES.INSURTECH_SENIOR_MANAGER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.memberRelationsAssistant)) {
          navigate(ROUTES.MEMBER_RELATIONS_ASSISTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.avpNonlifeSalesVismin)) {
          navigate(ROUTES.AVP_NONLIFE_SALES_VISMIN.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.claimsManagerLife)) {
          navigate(ROUTES.CLAIMS_MANAGER_LIFE.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.salesDevelopmentAnalystSouthMindanao)) {
          navigate(ROUTES.SALES_DEVELOPMENT_ANALYST_SOUTH_MINDANAO.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.nlReinsurance2)) {
          navigate(ROUTES.NL_REINSURANCE_2.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.systemDevelopmentSystemAdministrationManager)) {
          navigate(ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.legalCounselInHouse)) {
          navigate(ROUTES.LEGAL_COUNSEL_IN_HOUSE.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.avpLifeNonlife)) {
          navigate(ROUTES.AVP_LIFE_NONLIFE.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.juniorProgrammer1)) {
          navigate(ROUTES.JUNIOR_PROGRAMMER_1.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.policyIssuanceNl)) {
          navigate(ROUTES.POLICY_ISSUANCE_NL.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.policyIssuanceClerk)) {
          navigate(ROUTES.POLICY_ISSUANCE_CLERK.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.complianceManagerNonlife)) {
          navigate(ROUTES.COMPLIANCE_MANAGER_NONLIFE.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.infrastructureDataCenterManager)) {
          navigate(ROUTES.INFRASTRUCTURE_DATA_CENTER_MANAGER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.digitalMediaAssistant)) {
          navigate(ROUTES.DIGITAL_MEDIA_ASSISTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.programManager)) {
          navigate(ROUTES.PROGRAM_MANAGER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.dataWarehousingAssistant)) {
          navigate(ROUTES.DATA_WAREHOUSING_ASSISTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.dataQualityManagementSpecialist)) {
          navigate(ROUTES.DATA_QUALITY_MANAGEMENT_SPECIALIST.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.claimsSpecialist)) {
          navigate(ROUTES.CLAIMS_SPECIALIST.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.nonLifeClaimsClerk)) {
          navigate(ROUTES.NON_LIFE_CLAIMS_CLERK.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.hrManager)) {
          navigate(ROUTES.HR_MANAGER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.bookkeeper1)) {
          navigate(ROUTES.BOOKKEEPER_1.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.actuarialAssistant)) {
          navigate(ROUTES.ACTUARIAL_ASSISTANT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.avpFinance)) {
          navigate(ROUTES.AVP_FINANCE.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.actuaryAssistantReliever)) {
          navigate(ROUTES.ACTUARY_ASSISTANT_RELIEVER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.customerServiceAcctRetentionSpecialist)) {
          navigate(ROUTES.CUSTOMER_SERVICE_ACCT_RETENTION_SPECIALIST.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.reinsuranceAsst)) {
          navigate(ROUTES.REINSURANCE_ASST.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.assistantDivisionManagerNl)) {
          navigate(ROUTES.ASSISTANT_DIVISION_MANAGER_NL.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.claimsEvaluatorProcessor)) {
          navigate(ROUTES.CLAIMS_EVALUATOR_PROCESSOR.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.memberRelationsManager)) {
          navigate(ROUTES.MEMBER_RELATIONS_MANAGER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.underwritingFilingClerkClimbsCaresStaff)) {
          navigate(ROUTES.UNDERWRITING_FILING_CLERK_CLIMBS_CARES_STAFF.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.administrativeAssistant1Oop)) {
          navigate(ROUTES.ADMINISTRATIVE_ASSISTANT_1_OOP.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.occupationalHealthNurse)) {
          navigate(ROUTES.OCCUPATIONAL_HEALTH_NURSE.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.paralegal)) {
          navigate(ROUTES.PARALEGAL.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.adminEnvironmentalAssistant1)) {
          navigate(ROUTES.ADMIN_ENVIRONMENTAL_ASSISTANT_1.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.chiefInternalAuditor)) {
          navigate(ROUTES.CHIEF_INTERNAL_AUDITOR.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.reinsuranceUnderwritingManager)) {
          navigate(ROUTES.REINSURANCE_UNDERWRITING_MANAGER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.projectResearchMelOfficer)) {
          navigate(ROUTES.PROJECT_RESEARCH_MEL_OFFICER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.billingCollections)) {
          navigate(ROUTES.BILLING_COLLECTIONS.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.salesDevelopmentSpecialist)) {
          navigate(ROUTES.SALES_DEVELOPMENT_SPECIALIST.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.bankReconSpecialist)) {
          navigate(ROUTES.BANK_RECON_SPECIALIST.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.chiefOfStaff)) {
          navigate(ROUTES.CHIEF_OF_STAFF.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.underwritingAssistantNl)) {
          navigate(ROUTES.UNDERWRITING_ASSISTANT_NL.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.dataProcessor)) {
          navigate(ROUTES.DATA_PROCESSOR.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.adminOfficer)) {
          navigate(ROUTES.ADMIN_OFFICER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.claimsAssistantNl)) {
          navigate(ROUTES.CLAIMS_ASSISTANT_NL.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.claimsAnalyst)) {
          navigate(ROUTES.CLAIMS_ANALYST.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.cashierNl)) {
          navigate(ROUTES.CASHIER_NL.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.finance)) {
          navigate(ROUTES.FINANCE.chartOfAccounts.key);
        } else {
          navigate(ROUTES.USERS.dashboard.key);
        }
      } else {
        if (window.location.pathname === "/") navigate(ROUTES.AUTH.login.key);
      }
    }
  }, [isAuthenticated]);

  return (
    <div className="w-screen h-screen grid place-items-center">
      {!getUserLoading && children}
      {getUserLoading && <Loader />}
    </div>
  );
};

export default AuthLayout;
