import Sidebar from "@components/template/Sidebar";
import { areaAdminRoutes } from "../services/routes/area-admin-route";
import Nav from "@components/template/Nav";
import NavBar from "@components/template/NavBar";
import {
  DashboardContext,
  DashboardContextProvider,
} from "../context/dashboardContext";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import { useProfileActions } from "@state/reducer/profile";
import { useContext, useEffect } from "react";

const AreaAdminLayout = ({ children }: { children: React.ReactNode }) => {
  const user = useSelector((state: RootState) => state.auth.user);
  const { setProfile } = useProfileActions();

  useEffect(() => {
    if (user?.data && !user?.loading) {
      setProfile(user);
    }
  }, [user, setProfile]);

  const Content = () => {
    const ctx = useContext(DashboardContext);
    return (
      <div className="flex flex-row flex-1 h-dvh w-full ">
        <div
          className={`h-screen absolute z-[100] top-16 ${
            ctx?.collapse ? "-left-20" : "left-0"
          } lg:relative lg:z-0 lg:left-0 lg:top-0`}
        >
          <Sidebar>
            <Nav list={areaAdminRoutes} />
          </Sidebar>
        </div>
        <div className="flex flex-col h-screen w-full overflow-auto">
          <div className="row-span-1 z-50 ">
            <NavBar />
          </div>
          <div className="row-span-10 xl:p-4 p-2 h-full overflow-auto">
            {children}
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <DashboardContextProvider>
        <Content />
      </DashboardContextProvider>
    </>
  );
};

export default AreaAdminLayout;
