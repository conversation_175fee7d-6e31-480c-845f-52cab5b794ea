import { IProduct, IProductRevisions } from "./products.interface";

import { IAttachments, IRequirementable } from "./compliance";
import { IPosition } from "./position.interface";
import { ICommissionStructure } from "./commission-structure.interface";
import { CooperativeDataSourceType } from "@enums/enums";
import { IDefaultPaginatedLinks, IMeta } from "./common.interface";
import { ReactNode } from "react";
import { IUser } from "./user.interface";
import { IQuotationData, IQuotation } from "./quotation.interface";

export interface IProductProposal {
  id: string;
  proposalApproval?: IProductProposalUpdateStatusPayload;
  proposalAgreement?: IPartnershipAgreementUpdateStatusPayload;
  proposalNotarization?: IProductProposalNotary;
  productId?: IProduct;
  product?: IProduct;
  cooperativeId?: string;
  cooperative?: ICooperative;
  managementPercentFee: string | number;
  productType: string;
  proposableId?: string;
  proposableType?: string;
  proposalType?: string;
  productStatus?: string;
  productRevision?: IProductRevisions;
  proposable?: IProductRevisions | IQuotationData;
  status: string;
  createdAt?: string;
  createdBy?: string;
  updatedAt?: string;
  commissionStatus?: string;
  commissionStructure?: ICommissionStructure;
  deletedAt?: string;
  requirementable?: IRequirementable;
  quotation?: IQuotation;
}

export interface ICooperative {
  source?: CooperativeDataSourceType;
  id?: number | string;
  name?: string;
  coopCode?: string;
  coopName?: string;
  branchName?: string;
  coopAcronym?: string;
  streetAddress?: string;
  barangay?: string;
  city?: string;
  province?: string;
  zipCode?: string;
  emailAddress?: string;
  websiteAddress?: string;
  telephoneNumber?: string;
  cdaRegistrationNumber?: string;
  cdaRegistrationDate?: string;
  cdaCocNumber?: string;
  cdaCocDate?: string;
  taxIdNumber?: string;
  taxIdDate?: string;
  taxCteNumber?: string;
  taxCteExpiryDate?: string;
  coopBranchesCount?: number;
  coopMembersCount?: number;
  coopMembersMaleCount?: number;
  coopMembersFemaleCount?: number;
  coopTotalAssets?: number;
  status?: string;
  mainBranchId?: number;
  cooperativeTypeId?: string;
  cooperativeCategoryId?: string;
  cooperativeAffiliations?: IAffiliation[];
  cooperativeOfficers?: ICooperativeOfficer[];
  createdBy?: string;
  createdAt?: string;
  updatedAt?: string;
  marketArea?: number;
  regionId?: number;
}

export interface IAffiliation {
  id?: string;
  affiliation?: IAffiliation;
  affiliationName?: string;
  affiliationId: string;
  description?: string;
  effectivityDate: string;
  status: string;
}

export interface ICooperativeOfficer {
  id?: string;
  title?: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  gender: string;
  generation?: string;
  emailAddress?: string;
  contactNumber?: string;
  effectivityDate?: string;
  status?: string;
  positionId?: string;
  positionName?: string;
  signatory?: boolean;
  position?: IPosition;
  maritalStatus?: string;
  address?: string;
}

export interface IProductProposalUpdateStatusPayload {
  approveRejectDate?: string;
  approveRejectRemarks?: string;
  hasPricingRemarks?: boolean | number;
  hasUnderwritingRemarks?: boolean | number;
  status?: string;
  attachments?: IAttachments[];
  createdAt?: string;
  updatedAt?: string;
}

export interface IPartnershipAgreementUpdateStatusPayload {
  agreementSignedDate?: string;
  agreementSignedRemarks?: string;
  attachments?: IAttachments[];
}

export interface IProductProposalCommissionPayload {
  productProposalId: string;
  status: string;
  commissionDetails: IProductProposalCommissionDetails[];
  totalCommission: number;
}

export interface IProductProposalCommissionDetails {
  commissionTypeId: number;
  commissionPercentage: number;
}

export interface IProductProposalNotary {
  id: number | string;
  productProposal: IProductProposal;
  cooperative?: ICooperative;
  cooperativeOfficers: ICooperativeOfficer;
  product: IProduct;
  proposalNotarization?: IProductProposalNotaryUpdateStatusPayload;
  proposalAgreement?: IPartnershipAgreementUpdateStatusPayload;
  proposalApproval?: IProductProposalUpdateStatusPayload;
  attachments?: IAttachments[];
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: number;
}

export interface IProductProposalNotaryUpdateStatusPayload {
  productProposalId: number | string;
  agreementNotarizationDate?: string;
  agreementNotarizationStatus?: string;
  agreementNotarizedDocumentFilename?: string;
  agreementNotarizationRemarks?: string;
  agreementStatus?: string;
  attachments?: IAttachments[];
}

export interface IProductProposalResponse {
  data: IProductProposal[];
  links?: IDefaultPaginatedLinks;
  meta?: IMeta;
}

export interface ITimelineItemProps {
  icon?: ReactNode;
  title?: string;
  date?: string;
  component?: ReactNode | ReactNode[];
}

export interface IProductProposalApprovalUnderwriting {
  id: number | string;
  productProposal: IProductProposal;
  proposable: IProductRevisions;
  cooperative?: ICooperative;
  product?: IProduct;
  productProposalApproval?: IProductProposalApprovalUpdateStatusPayload;
  underwritingStatus?: string;
  claimStatus?: string;
  provisionApproval?: IProvisionApproval[];
  createdAt?: string;
  createdBy?: IUser;
}

export interface IProvisionApproval {
  id: number;
  productProposalId: number;
  userId: number;
  user?: IUser;
  approvalType: string;
  status: string;
  remarks?: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
}
export interface IProductProposalApprovalUpdateStatusPayload {
  productProposalId: number | string;
  status?: string;
}
export interface IProductProposalCommissionApproval {
  signatoryTemplateId: number;
  approvableType: string;
  approvableId: number;
  status: string;
}
