import { IUtilitiesCooperativeCategory, IUtilitiesCooperativeMembershipType } from "@interface/utilities.interface";
import { IAttachments, IRequirementable } from "@interface/compliance";
import { IDefaultPaginatedLinks, IMeta } from "./common.interface";
export interface coopAffiliation {
  index: number;
  status: string;
  effectivityDate: string;
  affiliationId: number;
  cooperativeId: number;
}
export interface coopOfficer {
  index?: number;
  id?: number;
  title: string;
  firstName: string;
  middleName: string;
  lastName: string;
  generation: string;
  gender: string;
  emailAddress: string;
  contactNumber: string;
  effectivityDate: string;
  status: string;
  positionId: number;
  positionName?: string;
  maritalStatus?: string;
  address?: string;
  signatory: boolean;
}
export interface ISharesCoopInformation {
  id?: number;
  coopCode?: string;
  coopName: string;
  coopAcronym: string;
  streetAddress: string;
  barangay: string;
  city: string;
  province: string;
  zipCode: string;
  emailAddress: string;
  websiteAddress: string;
  telephoneNumber: number | string;
  cdaRegistrationNumber: number | string;
  cdaRegistrationDate: string;
  cdaCocNumber: number | string;
  cdaCocDate: string;
  taxIdNumber: number | string;
  taxIdDate: string;
  taxCteNumber: number | string;
  taxCteExpiryDate: string;
  coopBranchesCount: number;
  coopMembersCount: number;
  coopMembersMaleCount: number;
  coopMembersFemaleCount: number;
  coopTotalAssets: number;
  status: "PENDING" | "FOR_APPROVAL" | "COMPLETED" | "DISAPPROVED";
  mainBranchId: number | string;
  cooperativeTypeId: number;
  cooperativeCategoryId: number;
  cooperativeCategory?: IUtilitiesCooperativeCategory;
  cooperativeAffiliations: coopAffiliation[];
  cooperativeOfficers: coopOfficer[];
  category?: string;
  type?: string;
  createdAt?: string;
  updatedAt?: string;
}
export interface ShareRequirementFiles {
  requirementId?: number; // Optional since it may not exist for all files
  // fileLocation?: string | File | undefined;
  // remarks?: string; // Optional since not all entries have this
  file?: string | File | undefined;
  label?: string; // Optional since not all entries have this
  description?: string; // Optional since not all entries have this
}
export interface ISharePayments {
  id?: number;
  shareId: number;
  orNumber: string;
  orDate: string;
}
export interface IShares {
  id?: number;
  coopId: number;
  shareTypeId: number;
  paymentTypeId: number;
  initialPaidUpAmount: number;
  numberOfShare: number;
  coopMembershipTypeId: number;
  cosDatePrinted?: string;
  status: string;
  // attachments: ShareRequirementFiles[];
  requirementable?: IRequirementable;
  sharePayment?: ISharePayments;
  coopName?: string;
  cooperativeMembershipType?: IUtilitiesCooperativeMembershipType;
  cooperative?: ISharesCoopInformation;
  shareType?: IShareType;
  filter?: string;
}

export interface IShareType {
  id?: number;
  shareTypeCode: string;
  shareTypeName: string;
  description: string;
}
export interface IRequirementsPayloadUpdate {
  id?: number;
  name?: string;
  status?: string;
  remarks?: string;
  completionDate?: string;
}
export interface IRequirementableRequirements {
  id?: number;
  name?: string;
  remarks?: string;
  requirementId?: number;
  requirementableId?: number;
  status?: string;
  submissionDate?: string;
  attachments?: IAttachments[];
  isBlurryFile?: boolean;
  isInvalidDocument?: boolean;
}
export interface ICooperativesResponse {
  data: ISharesCoopInformation[];
  links?: IDefaultPaginatedLinks;
  meta?: IMeta;
}
