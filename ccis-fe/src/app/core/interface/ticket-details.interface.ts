import { RequestStatus } from "@enums/ticket-status";

export interface TicketDetailsProps {
  ticketId: number;
  priorityLevel: string | null;
  closureStatus: RequestStatus;
  status: RequestStatus;
  createdAt: string | null;
  expectedCompletionDate: string | null;
  toDepartmentName: string | null;
  assignedToFirstname: string | null;
  assignedToLastname: string | null;
  extensionDate: string | null;
  requestTypeName: string | null;
  createdBy: number | null;
  assignedToId: number | null;
  handleFetchTicketByID: () => void;
  managerIds: number[] | null;
  toDepartmentId: number | null;
  ticketAssignees: any[] | null;
  requestingDepartment: string | null;
  userTicketCreatorFirstName: string | null;
  userTicketCreatorLastName: string | null;
  assignedTo: any
}

export interface IForwardTicketState {
  forwardTicket: {
    loading: boolean;
    success: boolean;
    error: boolean;
    errorMessage: string | null;
  };
}


