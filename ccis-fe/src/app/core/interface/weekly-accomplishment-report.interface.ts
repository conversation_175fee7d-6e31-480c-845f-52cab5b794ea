import { IDefaultPaginatedLinks, IMeta } from "./common.interface";
import { IUser } from "./user.interface";

export interface IIssueType {
  id: number;
  issueTypeCode: string;
  issueTypeName: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface ITaskType {
  id: number;
  taskTypeCode: string;
  taskTypeName: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface ITaskGroup {
  id: number;
  taskGroupCode: string;
  taskGroupName: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface IAccomplishmentTask {
  id: number;
  referenceNumber: string;
  description: string;
  numberOfHours: number;
  issueTypeId: number;
  taskTypeId: number;
  taskGroupId: number;
  accomplishmentReportId: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  issueType: IIssueType;
  taskType: ITaskType;
  taskGroup: ITaskGroup;
}

export interface IAccomplishmentReport {
  id: number;
  periodFrom: string;
  periodTo: string;
  applicableMonth: string;
  weekNumber: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  accomplishmentTasks?: IAccomplishmentTask[];
}

export interface IAccomplishmentTaskRequest {
  referenceNumber: string;
  description: string;
  numberOfHours: number;
  issueTypeId: number;
  taskTypeId: number;
  taskGroupId: number;
}

export interface IAccomplishmentReportDataResponse {
  data: IAccomplishmentReport[];
  links?: IDefaultPaginatedLinks;
  meta?: IMeta;
}

export interface IAccomplishmentReportByIdDataResponse {
  data: IAccomplishmentReport[];
}

export interface IAccomplishmentReportPayload {
  periodFrom: string;
  periodTo: string;
  weekNumber: number;
  accomplishmentTasks: IAccomplishmentTaskRequest[];
}

export interface IAccomplishmentReportResponseData extends IAccomplishmentReport {
  createdBy: IUser | null;
  updatedBy: IUser | null;
  deletedBy: IUser | null;
}

export interface IAccomplishmentReportPostResponse {
  data: IAccomplishmentReportResponseData;
}

export interface IAccomplishmentReportUpdateResponse {
  data: IAccomplishmentReportResponseData;
}

export interface IGetIssueTypesDataResponse {
  data: IIssueType[];
}

export interface IGetTaskTypesDataResponse {
  data: ITaskType[];
}

export interface IGetTaskGroupsDataResponse {
  data: ITaskGroup[];
}
