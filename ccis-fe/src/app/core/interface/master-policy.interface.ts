import { IDefaultPaginatedLinks, IMeta } from "./common.interface";
import { ProposableTypes } from "@enums/enums";
//The main Issuance payload system
export interface IIssuanceInterface {
  id: number;
  policyNumber: string;
  proposalNumber: string;
  mainCode: string;
  CoopCode: string;
  CoopName: string;
  CoopBranch: string;
  productName: string;
  date: string;
  status: string;
  proposalType: ProposableTypes;
}

//The main Issuance payload system
export interface IIssuance {
  id?: number;
  policyNumber: string;
  proposalNumber: string;
  mainCode: string;
  CoopCode: string;
  CoopName: string;
  CoopBranch: string;
  productName: string;
  date: string;
  status: string;
  proposalType: ProposableTypes;
}
export interface IssuanceResponse {
  data: IIssuance[];
  links?: IDefaultPaginatedLinks;
  meta?: IMeta;
}