export interface IGuideline {
  label: string;
  sequence: number;
  productGuideline: IGuidelineContent[];
  editable: boolean | number;
  productGuidelineTagId?: string;
}

export interface IGuidelineContent {
  id?: string;
  type: string;
  label?: string;
  value?: string | IGuidelineContent | IGuidelineContent[] | IGuidelineContentTable;
  tag?: string;
}

export interface IGuidelineContentTable {
  columns?: IGuidelineContent[];
  rows?: IGuidelineContent[][];
}

export interface IGuidelineHeaders {
  id: string | number;
  productRevisionGuidelineTag: IGuidelineTag;
  tagId: string | number;
}

export interface IGuidelineTag {
  id: string | number;
  productRevisionGuidelineTag: string;
}
