import { IDefaultPaginatedLinks, IMeta } from "./common.interface";
import { IUser } from "./user.interface";

export interface IChoice {
  id: number;
  questionId: number;
  name: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface IQuestion {
  id: number;
  question: string;
  status: number;
  choices: IChoice[];
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  createdBy: IUser;
}

export interface ITransactionType {
  id: number;
  name: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface IUserFeedback {
  id: number;
  userTransactionTypeId: number;
  name: string;
  remark: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface IUserFeedbackApiResponse<T> {
  data: T;
  meta?: IMeta;
  links?: IDefaultPaginatedLinks;
}

export interface INewQuestionForm {
  question: string;
  status: number;
  choices: { name: string }[];
}

export interface IFeedbackResponse {
  questionId: number;
  choiceId: number;
}

export interface ISelectedChoice {
  questionId: number;
  choiceId: number;
  emoji: string;
  label: string;
}

export interface ISaveFeedbackPayload {
  userTransactionTypeId?: number;
  name?: string;
  remark?: string;
  feedbackResponse?: IFeedbackResponse[];
}

export interface IUserFeedbackResponse {
  id: number;
  userTransactionTypeId: number;
  name: string;
  remark: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface IGetFeedbackResponse {
  id: number;
  questionId: number;
  choiceId: number;
  userFeedbackId: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}
