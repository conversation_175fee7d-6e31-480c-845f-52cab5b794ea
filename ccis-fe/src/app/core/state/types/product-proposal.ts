import { IAttachments, LoadingResult } from "@interface/common.interface";
import {
  IAffiliation,
  ICooperative,
  ICooperativeOfficer,
  IProductProposal,
  IProductProposalApprovalUpdateStatusPayload,
  IProductProposalCommissionApproval,
  IProductProposalNotaryUpdateStatusPayload,
  IProductProposalResponse,
  IProvisionApproval,
} from "@interface/product-proposal.interface";
import { IProduct, IProductRevisions } from "@interface/products.interface";
import { IUser } from "@interface/user.interface";
import { PayloadAction } from "@reduxjs/toolkit";

export type TProductProposalState = {
  step: number | null;
  customType?: string;
  isEditedGuidelines?: boolean;
  withRebates: boolean;
  paymentMethod: string;
  cooperative?: ICooperative;
  cooperatives: ICooperative[];
  managementPercentFee: number;
  cdaCoopearative?: ICooperative;
  cdaCooperatives: ICooperative[];
  productProposals: IProductProposal[];
  productProposalProvisions: IProductProposal[];
  productProposalNotarizations: TProductProposalNotary[];
  updatePartnershipAgreement?: TProductProposalNotary;
  proposedProduct?: IProductProposal;
  getProductProposal: TGetProductProposal;
  postProductProposal: LoadingResult;
  putProductProposal: LoadingResult;
  getCooperatives: LoadingResult;
  getCdaCooperatives: LoadingResult;
  getProductProposalNotarization: LoadingResult;
  postProductProposalsNotarizations: LoadingResult;
  getMarketingProductProposals: TGetProductProposal;
  getProductProposalProvisions: LoadingResult;
  signatory?: IProvisionApproval;
  getProposal: TGetProductProposal; // gamiton para makuha ang previous coop & product na comstruct.
  postProposalCommission: TProductProposalCommissionResponse;
  putProductProposalCommission: LoadingResult;
  updateProductProposalStatus: LoadingResult;
};

export type TProductProposalPayload = {
  id?: number | string;
  cooperativeId?: string | number;
  productId?: string;
  managementPercentFee?: string | number;
  modeOfPayment?: string;
  withRebates?: boolean;
  proposableId?: string;
  proposableType?: string;
  status?: string;
  proposalType?: string;
  productStatus?: string;
  productRevision?: IProductRevisions;
  attachments?: IAttachments[]; // need to check if this is needed
};

export type TCooperativePayload = {
  id?: number | string;
  coopCode?: string;
  coopName?: string;
  coopAcronym?: string;
  streetAddress?: string;
  barangay?: string;
  city?: string;
  province?: string;
  zipCode?: string;
  emailAddress?: string;
  websiteAddress?: string;
  telephoneNumber?: number | string;
  cdaRegistrationNumber?: number | string;
  cdaRegistrationDate?: string;
  cdaCocNumber?: number | string;
  cdaCocDate?: string;
  taxIdNumber?: number | string;
  taxIdDate?: string;
  taxCteNumber?: number | string;
  taxCteExpiryDate?: string;
  coopBranchesCount?: number;
  coopMembersCount?: number;
  coopMembersMaleCount?: number;
  coopMembersFemaleCount?: number;
  coopTotalAssets?: number;
  status?: string;
  mainBranchId?: number | string;
  cooperativeTypeId?: number | string;
  cooperativeCategoryId?: number | string;
  cooperativeAffiliations?: IAffiliation[];
  cooperativeOfficers?: ICooperativeOfficer[];
  category?: string;
  type?: string;
  createdAt?: string;
  updatedAt?: string;
};

export interface TProductProposalNotary {
  agreementNotarizationStatus: string;
  id: number | string;
  productProposal: IProductProposal;
  cooperative: ICooperative;
  cooperativeOfficers: ICooperativeOfficer;
  product: IProduct;
  proposalNotarization?: IProductProposalNotaryUpdateStatusPayload;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: number;
}

export type TGetProductProposal = LoadingResult & {
  data?: IProductProposalResponse;
};

export interface TProductProposalApproval {
  id: number | string;
  productProposal?: IProductProposal;
  cooperative?: ICooperative;
  product?: IProduct;
  productProposalApproval?: TProductProposalApprovalUpdateStatusPayload;
}

export interface TProductProposalApprovalUpdateStatusPayload {
  productProposalId: number | string;
  approvalStatus?: string;
  approvalDate?: string;
  approvalRemarks?: string;
}

export interface TProductProposalApprovalUnderwriting {
  id: number | string;
  productProposal: IProductProposal;
  proposable: IProductRevisions;
  cooperative?: ICooperative;
  product?: IProduct;
  productProposalApproval?: IProductProposalApprovalUpdateStatusPayload;
  underwritingStatus?: string;
  claimStatus?: string;
  provisionApproval?: IProvisionApproval[];
  createdAt?: string;
}

export interface TProvisionApproval {
  id: number;
  productProposalId: number;
  userId: number;
  user?: IUser;
  approvalType: string;
  status: string;
  remarks?: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
}

export interface IUpdateProductProposalStatusPayload {
  id: string;
  status: string;
}

export type TUpdateProductProposalStatusResponse = LoadingResult & {
  data?: IProductProposal;
};

export type TProductProposalCommissionResponse = LoadingResult & {
  data?: IProductProposalCommissionApproval;
};
export type TIProductProposalCommisionActionPayloadPostPut = PayloadAction<IProductProposalCommissionApproval>;
