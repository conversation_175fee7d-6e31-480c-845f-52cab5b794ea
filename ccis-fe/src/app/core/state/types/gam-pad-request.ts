import { LoadingResult } from "@interface/common.interface";
import { ICreatePadRequest, IGamPadRequest, IUpdatePadRequest } from "@interface/gam-request-pads";
import { PayloadAction } from "@reduxjs/toolkit";
import { TableState } from "./common-table-types";

export type TGamPadRequestState = {
  remainingPads: LoadingResult;
  lastSeries: LoadingResult;
  postGamPadRequest: LoadingResult;
  gamPadRequesTable: TableState<IGamPadRequest[]>;
  gamPadRequestDetails: LoadingResult;
  putGamPadRequest: LoadingResult;
  nextSeries: LoadingResult;
};

export type TGamPadRequestWithIndex = {
  id: number;
  data?: IGamPadRequest;
  index: number;
};

export type TGamPadRequestCreatePayloadAction = PayloadAction<ICreatePadRequest>;

export type TGamPadRequestUpdatePayloadAction = PayloadAction<IUpdatePadRequest>;
