import {
  IAccomplishmentReportByIdDataResponse,
  IAccomplishmentReportDataResponse,
  IAccomplishmentReportPostResponse,
  IAccomplishmentReportUpdateResponse,
  IGetIssueTypesDataResponse,
  IGetTaskGroupsDataResponse,
  IGetTaskTypesDataResponse,
} from "@interface/weekly-accomplishment-report.interface";

export type TWeeklyAccomplishmentReport = {
  getAllAccomplishmentReport: IAccomplishmentReportDataResponse;
  getAccomplishmentReportById: IAccomplishmentReportByIdDataResponse;
  postAddAccomplishmentReport: IAccomplishmentReportPostResponse;
  //  For future use kay wala pay update/delete sa postman
  putUpdateAccomplishmentReport: IAccomplishmentReportUpdateResponse;
  //    destroyAccomplishmentReport:
  getIssueTypes: IGetIssueTypesDataResponse;
  getTaskTypes: IGetTaskTypesDataResponse;
  getTaskGroups: IGetTaskGroupsDataResponse;
  getLoggedInUserAccomplishmentReport: IAccomplishmentReportDataResponse;
  exportAccomplishmentReport: any;
  destroyAccomplishmentReport: IAccomplishmentReportDataResponse
};
