import { PayloadAction } from "@reduxjs/toolkit"; // Importing PayloadAction type from redux toolkit
import { LoadingResult } from "."; // Importing LoadingResult type from the current module
import { IUtilitiesPositions } from "@interface/utilities.interface"; // Importing IUser interface from user.interface module
import { ITableApiResponse } from "./common-table-types";
// Defining the state type for users management
export type TUtilitiesPositionsManagementState = {
  positions: IUtilitiesPositions[];
  selectedPosition: TUtilitiesPositionsPayloadWithIndex; // State to set user selected
  getPosition?: GetPositions; // State for get operation
  postPosition?: TUtilitiesPositionsResponse; // State for post operation
  putPosition?: TUtilitiesPositionsResponse; // State for put operation
  destroyPosition?: LoadingResult; // State for destroy operation
};

// Extending LoadingResult type to include a single IUser data
export type TUtilitiesPositionsResponse = LoadingResult & {
  data?: IUtilitiesPositions; // Optional user data
};
// Defining the payload type for post user action
/**
 * @type {object}
 * @property {number | string} [id] - Optional user ID, can be a number or a string
 * @property {string} positionName - Positions name
 * @property {string} positionCode - Positions Code
 * @property {string} description - Positions Description
 *
 * This payload type is use for post and put request
 */

export type TUtilitiesPositionsPayload = {
  id?: number | string;
  positionName: string;
  positionCode: string;
  description?: string;
};

// Defining the id of Positions as payload
export type TUtilitiesPositionsIDAndIndexPayload = {
  id: number | string;
  index: number | string;
};

// Defining payload that handles TUtilitiesPositions type as data
export type TIUtilitiesPositionsWithIndexPayload = {
  data: IUtilitiesPositions;
  index: number;
};
// Defining payload that handles TUtilitiesPositions type as data
export type TUtilitiesPositionsPayloadWithIndex = {
  data: TUtilitiesPositionsPayload;
  index: number;
};

// Defining the payload type for get Positions action with filter
export type TGetUtilitiesPositionsWithFilterActionPayload = PayloadAction<{
  filter?: string;
  page?: number;
  pageSize?: number;
  condition?: string;
}>;

export type GetPositions = LoadingResult & {
  data?: ITableApiResponse;
};
// Defining the payload type for post Positions action, extending PayloadAction
export type TUtilitiesPositionsActionPayload = PayloadAction<TUtilitiesPositionsPayload>;
export type TIUtilitiesPositionsActionPayload = PayloadAction<IUtilitiesPositions>;

// Defining the payload type for put users action, extending PayloadAction
export type TUtilitiesPositionsWithIndexActionPayload = PayloadAction<TUtilitiesPositionsPayloadWithIndex>;
export type TIUtilitiesPositionsWithIndexActionPayload = PayloadAction<TIUtilitiesPositionsWithIndexPayload>;

// Defining the payload type for delete users action, extending PayloadAction
export type TDeleteUtilitiesPositionsActionPayload = PayloadAction<TUtilitiesPositionsIDAndIndexPayload>;
