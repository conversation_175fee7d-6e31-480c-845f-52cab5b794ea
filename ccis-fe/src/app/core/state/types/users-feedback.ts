import { IDefaultPaginatedLinks, IMeta } from "@interface/common.interface";
import { IUserFeedbackResponse } from "@interface/users-feedback.interface";

export type TUsersFeedbackState = {
  getFeedBackQuestions: TGetUserFeedbackApiResponse;
  getTransactionTypes: any;
  getUserFeedbacks: any;
  postSaveQuestions: any;
  postSaveChoice: any;
  postSaveTransactionType: any;
  postSaveFeedback: any;
  putUpdateTransactionType: any;
  putUpdateQuestion: any;
};

export type TGetUserFeedbackApiResponse = {
  data: IUserFeedbackResponse;
  meta?: IMeta;
  links?: IDefaultPaginatedLinks;
};
