import { PayloadAction } from "@reduxjs/toolkit"; // Importing PayloadAction type from redux toolkit
import { LoadingResult } from "."; // Importing LoadingResult type from the current module
//import { ICooperatives } from "../../interface/cooperatives"; // Importing IUser interface from user.interface module
import { ICooperativesResponse, ISharesCoopInformation as ISharesCoopInformationType, coopAffiliation, coopOfficer } from "@interface/shares.interface";

export type TCooperativesManagementState = {
  latestId: number;
  cooperatives: ISharesCoopInformationType[];
  selectedCooperatives: TCooperativesDataAndIndexPayload;
  // getCooperatives?: LoadingResult;
  getCooperatives?: GetCooperatives;
  postCooperatives?: TCooperativesResponse;
  putCooperatives?: TCooperativesResponse;
  destroyCooperatives?: LoadingResult;
  getCooperativeById?: TCooperativesResponse;
};

export type TCooperativesResponse = LoadingResult & {
  data?: ISharesCoopInformationType;
};
export type GetCooperatives = LoadingResult & {
  data?: ICooperativesResponse;
};
export type TCooperativesPayload = {
  id?: number;
  coopCode?: string;
  coopName: string;
  coopAcronym: string;
  streetAddress: string;
  barangay: string;
  city: string;
  province: string;
  zipCode: string;
  emailAddress: string;
  websiteAddress: string;
  telephoneNumber: number | string;
  cdaRegistrationNumber: number | string;
  cdaRegistrationDate: string;
  cdaCocNumber: number | string;
  cdaCocDate: string;
  taxIdNumber: number | string;
  taxIdDate: string;
  taxCteNumber: number | string;
  taxCteExpiryDate: string;
  coopBranchesCount: number;
  coopMembersCount: number;
  coopMembersMaleCount: number;
  coopMembersFemaleCount: number;
  coopTotalAssets: number;
  status: "PENDING" | "FOR_APPROVAL" | "COMPLETED" | "DISAPPROVED";
  mainBranchId: number | string;
  cooperativeTypeId: number;
  cooperativeCategoryId: number;
  cooperativeAffiliations: coopAffiliation[];
  cooperativeOfficers: coopOfficer[];
  category?: string;
  type?: string;
  createdAt?: string;
  updatedAt?: string;
};

export type TCooperativesWithIDAndIndexPayload = {
  id: number;
  index: number | string;
};

export type TICooperativesDataAndIndexPayload = {
  data: ISharesCoopInformationType;
  index: number;
};

export type TCooperativesDataAndIndexPayload = {
  data: TCooperativesPayload;
  index: number;
};

export type TGetCooperativesWithFilterActionPayload = PayloadAction<{
  filter?: string;
}>;

export type TGetCooperativeByIdPayload = PayloadAction<{
  id: number;
}>;

// For Post And Put in Reducer
export type TCooperativesActionPayloadPostPut = PayloadAction<TCooperativesPayload>;
// For Post Success in Reducer
export type TICooperativesActionPayloadPostSuccess = PayloadAction<ISharesCoopInformationType>;
// For Selected Data And Put Success in Reducer
export type TICooperativesActionPayloadSelectedPutSuccess = PayloadAction<TICooperativesDataAndIndexPayload>;
// For Destroy in Reducer
export type TCooperativesDelete = PayloadAction<TCooperativesWithIDAndIndexPayload>;
