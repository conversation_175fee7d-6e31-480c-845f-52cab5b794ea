// For introducing Payload actions from redux toolkit
import { PayloadAction } from "@reduxjs/toolkit";
// Importing LoadingResult type from the current module 
import { LoadingResult } from ".";
// Importing IUser interface from user.interface module
import { 
  IRemittanceData, 
  IUtilitiesRemittanceData, 
  RemittanceDataResponse, 
  IUtilitiesRemittanceTemplate
} from "@interface/utilities.interface";

// All of the types that would be called under different actions, 
// mostly CRUD or selection operations.
export type TUtilitiesRemittanceDataManagementState = {
  //New stuff for single and multiple variables
  remittanceDatas: IRemittanceData[];
  remittanceData?: IRemittanceData;

  selectedRemittanceData: TUtilitiesRemittanceDataDataAndIndexPayload;

  //Use this to Get instead of using the data from the Loading Result
  getRemittanceData?: GetRemittanceData;
  postRemittanceData?: TUtilitiesRemittanceDataResponse;
  putRemittanceData?: TUtilitiesRemittanceDataResponse;
  destroyRemittanceData?: LoadingResult;
};

//The new Get function for Remittance
export type GetRemittanceData = LoadingResult & {
  data?: RemittanceDataResponse;
};


//For getting the response after loading
export type TUtilitiesRemittanceDataResponse = LoadingResult & {
  data?: IUtilitiesRemittanceData;
};

//The payload that would be sent to the State Management
export type TUtilitiesRemittanceDataPayload = {
  id?: number;
  periodFrom: string; 
  periodTo: string;
  remittanceTypeId: number | string;
  requirementTemplate?: IUtilitiesRemittanceTemplate;
};

//For searching functions based on the ID and Index
export type TUtilitiesRemittanceDataWithIDAndIndexPayload = {
  id: number;
  index: number | string;
};

//Different types of payloads are here and are self-explainatory.
export type TIUtilitiesRemittanceDataDataAndIndexPayload = {
  data: IUtilitiesRemittanceData;
  index: number;
};

export type TUtilitiesRemittanceDataDataAndIndexPayload = {
  data: TUtilitiesRemittanceDataPayload;
  index: number;
};

// For Post And Put in Reducer
export type TUtilitiesRemittanceDataActionPayloadPostPut =
  PayloadAction<TUtilitiesRemittanceDataPayload>;
// For Post Success in Reducer
export type TIUtilitiesRemittanceDataActionPayloadPostSuccess =
  PayloadAction<IUtilitiesRemittanceData>;
// For Selected Data And Put Success in Reducer
export type TIUtilitiesRemittanceDataActionPayloadSelectedPutSuccess =
  PayloadAction<TIUtilitiesRemittanceDataDataAndIndexPayload>;
// For Destroy in Reducer
export type TUtilitiesRemittanceDataDelete =
  PayloadAction<TUtilitiesRemittanceDataWithIDAndIndexPayload>;
