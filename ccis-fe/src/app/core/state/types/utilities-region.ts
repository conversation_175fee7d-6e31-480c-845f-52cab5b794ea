//I will add all of the necessary stuff for CRUD
import { UtilitiesRegionResponse, IUtilitiesRegion } from "@interface/utilities.interface";
import { PayloadAction } from "@reduxjs/toolkit";
import { LoadingResult } from ".";

export type TUtilitiesRegionState = {
   regionTypes: IUtilitiesRegion[];
   regionType?: IUtilitiesRegion;

   selectedRegionType: TUtilitiesRegionDataAndIndexPayload;

   //May need to change Get, will check this out soon.
   getRegion: GetRegion;
   getSingleRegion: GetRegion;
   postRegion: TUtilitiesRegionResponse;
   putRegion: TUtilitiesRegionResponse;
   destroyRegion: LoadingResult;
};

//For the new Get function
export type GetRegion = LoadingResult & {
  data?: UtilitiesRegionResponse;
};

//For a response after loading
export type TUtilitiesRegionResponse = LoadingResult & {
  data?: IUtilitiesRegion;
};

//Payload from the state management
export type TUtilitiesRegionPayload = {
  id?: number;
  regionName: string;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string | null;
};

export type TUtilitiesRegionWithIDAndIndexPayload = {
  id: number;
  index: number | string;
};

export type TIUtilitiesRegionWithDataAndIndexPayload = {
  data: IUtilitiesRegion;
  index: number;
};

export type TUtilitiesRegionDataAndIndexPayload = {
  data: TUtilitiesRegionPayload;
  index: number;
};

//For Post and Put Payload
export type TUtilitiesRegionActionPayloadPostPut =
  PayloadAction<TUtilitiesRegionPayload>;
//For Post Success in Reducer
export type TIUtilitiesRegionActionPayloadPostSuccess =
  PayloadAction<IUtilitiesRegion>;
//For Selected Data And Put Success in Reducer
export type TIUtilitiesRegionActionPayloadSelectedPutSuccess =
  PayloadAction<TIUtilitiesRegionWithDataAndIndexPayload>;
//For Destroy in Reducer
export type TUtilitiesRegionDelete =
  PayloadAction<TUtilitiesRegionWithIDAndIndexPayload>;

