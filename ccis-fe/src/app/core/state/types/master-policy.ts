import { PayloadAction } from "@reduxjs/toolkit";
import { LoadingResult } from "@interface/common.interface";
import { IIssuanceInterface, IIssuance, IssuanceResponse } from "@interface/master-policy.interface";
import { ProposableTypes } from "@enums/enums";

// State type for managing Proposals
export type TIssuanceManagementState = {
  issuances: IIssuance[];
  issuance?: IIssuance;

  selectedIssuance: TIssuanceDataAndIndexPayload;
  getIssuance?: GetIssuance;
  getIssuances?: GetIssuance;
  postIssuance?: TIssuanceResponse;
  putIssuance?: TIssuanceResponse;
  destroyIssuance?: LoadingResult;
};

// For the Loading page
export type TIssuanceResponse = LoadingResult & {
  data?: IIssuanceInterface;
};

// Define the structure for Ticket data
export type TIssuancePayload = {
  id?: number;
  policyNumber: string;
  proposalNumber: string;
  mainCode: string;
  CoopCode: string;
  CoopName: string;
  CoopBranch: string;
  productName: string;
  date: string;
  status: string;
  proposalType: ProposableTypes;
};


// The new Get function for Issuance
export type GetIssuance = LoadingResult & {
  data?: IssuanceResponse;
};


// Payload type for deleting a Ticket
export type TIssuanceWithIDAndIndexPayload = {
  id: number;
  index: number | string;
};

export type TIIssuanceDataAndIndexPayload = {
  data: IIssuanceInterface;
  index: number;
};

export type TIssuanceDataAndIndexPayload = {
  data: TIssuancePayload;
  index: number;
};

export type TGetIssuanceWithFilterActionPayload = PayloadAction<{ filter?: string }>;
// Post and Put
export type TIssuanceActionPayloadPostPut = PayloadAction<TIssuancePayload>;
// Post Success
export type TIssuanceActionPayloadPostSuccess = PayloadAction<IIssuanceInterface>;
// Selected Data and Put Success
export type TIssuanceActionPayloadSelectedPutSuccess = PayloadAction<TIIssuanceDataAndIndexPayload>;
// Destroy
export type TIssuanceDelete = PayloadAction<TIssuanceWithIDAndIndexPayload>;


