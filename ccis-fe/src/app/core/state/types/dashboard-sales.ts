
import { LoadingResult } from "@interface/common.interface";
import { PayloadAction } from "@reduxjs/toolkit";
import { ISalesDashboard } from "@interface/dashboard-sales";

//For the Sales Dashboard
export type TSalesDashboard = {
  salesDashboard: ISalesDashboard;
  salesDashboardPayload: TSalesDashboardPostPayload;
  postSalesDashboard: LoadingResult;
}

export type TSalesDashboardPostPayload = {
  regionId?: number;
  province?: string;
  city?: string;
  cooperativeId?: number;
};

export type TSalesDashboardActionPayloadPost = PayloadAction<TSalesDashboardPostPayload>;

