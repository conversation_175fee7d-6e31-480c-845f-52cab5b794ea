import { PayloadAction } from "@reduxjs/toolkit"; // Importing PayloadAction type from redux toolkit
import { LoadingResult } from "."; // Importing LoadingResult type from the current module
import { IUser } from "@interface/user.interface"; // Importing IUser interface from user.interface module

// Defining the state type for users management
export type TUsersManagementState = {
  users: IUser[]; // State to set users record list
  selectedUser: TUserPayloadWithIndex; // State to set user selected
  getUsers?: LoadingResult; // State for get operation
  postUsers?: TUsersResponse; // State for post operation
  putUsers?: TUsersResponse; // State for put operation
  destroyUsers?: LoadingResult; // State for destroy operation
  loading?: boolean;
};

// Extending LoadingResult type to include a single IUser data
export type TUsersResponse = LoadingResult & {
  data?: IUser; // Optional user data
};

// Defining the payload type for post user action
/**
 * @type {object}
 * @property {number | string} [id] - Optional user ID, can be a number or a string
 * @property {string} firstname - User's first name
 * @property {string} [middlename] - Optional user's middle name
 * @property {string} lastname - User's last name
 * @property {string} email - User's email address
 * @property {string} contactNumber - User's contact number
 * @property {number[]} roles - Array of role IDs assigned to the user
 * @property {string} [status] - Optional status, useful for updates
 *
 * This payload type is use for post and put request
 */
export type TUserPayload = {
  id?: number | string;
  firstname: string;
  middlename?: string;
  lastname: string;
  email: string;
  contactNumber: string;
  roles?: string[] | number[];
  status?: string;
  positionId?: number | string;
  departmentId?: number | string;
  areaId?: number | string;
  companyId?: number | string;
  gender?: string;
  address?: string;
  // regionId?: number | string;
  // marketAreaId?: number | string;
  // regionIds?: (number | string)[];
  cooperativeId?: number | string;
};

// Defining the id of user as payload
export type TUsersIDAndIndexPayload = {
  id: number | string;
  index: number | string;
};

// Defining payload that handles IUser type as data
export type TIUsersWithIndexPayload = {
  data: IUser;
  index: number;
};

// Defining payload that handles TUserPayload type as data
export type TUserPayloadWithIndex = {
  data: TUserPayload;
  index: number;
};

// Defining the payload type for get user action with filter
export type TGetUserWithFilterActionPayload = PayloadAction<{
  filter?: string;
}>;

// Defining the payload type for post users action, extending PayloadAction
export type TUserActionPayload = PayloadAction<TUserPayload>;
export type TIUserActionPayload = PayloadAction<IUser>;

// Defining the payload type for put users action, extending PayloadAction
export type TUserWithIndexActionPayload = PayloadAction<TUserPayloadWithIndex>;
export type TIUserWithIndexActionPayload =
  PayloadAction<TIUsersWithIndexPayload>;

// Defining the payload type for get users action, extending PayloadAction
export type TDeleteUsersActionPayload = PayloadAction<TUsersIDAndIndexPayload>;
