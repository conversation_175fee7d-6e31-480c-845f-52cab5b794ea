import { PayloadAction } from "@reduxjs/toolkit";
import { LoadingResult } from "@interface/common.interface";
import { IAreaAdmin } from "@interface/form-inventory-utilities";

// State type for managing areaAdmins
export type TAreaAdminState = {
  areaAdmins: IAreaAdmin[];
  selectedAreaAdmin: TAreaAdminPayloadWithIndex;
  getAreaAdmins?: LoadingResult;
  getAreaAdmin?: LoadingResult;
  postAreaAdmin?: TAreaAdminResponse;
  putAreaAdmin?: TAreaAdminResponse;
  destroyAreaAdmin?: LoadingResult;
};

// Extending LoadingResult type to include a single IMasterlist data
export type TAreaAdminResponse = LoadingResult & {
  data?: IAreaAdmin; // Optional masterlist data
};

// Define the structure for AreaAdmin data
export type TAreaAdminPayload = {
  id: number;
  userAreaId: number;
  branchName: string;
  userId: number;
  adminName: string;
};

// Payload type for creating or updating a areaAdmin
export type IAreaAdminPayload = {
  id: number;
  userAreaId: number;
  branchName: string;
  userId: number;
  adminName: string;
};

// Payload type for deleting a areaAdmin
export type IAreaAdminWithIndexPayload = {
  id: number;
  index: number;
};

export type TGetAreaAdminsWithFilterActionPayload = PayloadAction<{
  filter: string;
  id?: number | string;
}>;

// Defining payload that handles TMasterlistPayload type as data
export type TAreaAdminPayloadWithIndex = {
  data: IAreaAdminPayload;
  index: number;
};

export type TIAreaAdminWithIndexPayload = {
  filter: string;
  id: number;
};
// Action payloads for areaAdmins
export type TAreaAdminPayloadAction = PayloadAction<TAreaAdminPayload>;
// export type TAreaAdminActionPayload = PayloadAction<{ data?: IAreaAdmin[] }>;
export type TIAreaAdminActionPayload = PayloadAction<IAreaAdmin>;
export type TAreaAdminActionPayloadIAreaAdmin = PayloadAction<TAreaAdminPayload>;

export type TAreaAdminWithIndexActionPayload = PayloadAction<TAreaAdminPayloadWithIndex>;
export type TAreaAdminIDPayloadActionPayload = PayloadAction<IAreaAdminWithIndexPayload>;
export type TGetAreaAdminWithFilterActionPayload = PayloadAction<{ filter?: string; id?: number }>;

// Payload for operations involving index and areaAdmin data
// export type TAreaAdminWithIndexActionPayload = PayloadAction<{ data: TAreaAdminPayload, index: number }>;
