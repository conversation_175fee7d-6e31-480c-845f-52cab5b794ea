import { IDefaultParams } from "@interface/common.interface";
import { PayloadAction } from "@reduxjs/toolkit";
import { takeLatest, call, put } from "redux-saga/effects";
import { AxiosResponse } from "axios";

//The setSelectedTicker, clearSelectedTicket, and destroyTicketReset variables are not
//included simply because they are Synchronous and can be done directly.
//If there is a need for that, just call me. -ITM

import {
  getTicket,
  getTicketSuccess,
  getTicketFailure,
  getPriorityTicket,
  getPriorityTicketSuccess,
  getPriorityTicketFailure,
  getGraphTicket,
  getGraphTicketSuccess,
  getGraphTicketFailure,
  getDueTicket,
  getDueTicketSuccess,
  getDueTicketFailure,
  postTicket,
  postTicketSuccess,
  postTicketFailure,
  // putTicketSuccess,
  destroyTicket,
  destroyTicketSuccess,
  destroyTicketFailure,
  getDepartmentUsersSuccess,
  getDepartmentU<PERSON>sFailure,
  getDepartmentUsers,
  postAssignTicketUserSuccess,
  postAssignTicketUserFailure,
  postAssignTicketUser,
  getTicketAssigneeDetailsSuccess,
  getTicketAssigneeDetailsFailure,
  getTicketAssigneeDetails,
  getTicketsInfoSuccess,
  getTicketsInfoFailure,
  getTicketsInfo,
  putTicketExtendDateSuccess,
  putTicketExtendDateFailure,
  putTicketExtendDate,
  postForwardTicketSuccess,
  postForwardTicketFailure,
  postForwardTicket,
  getDepartmentsSuccess,
  getDepartmentsFailure,
  getDepartments,
  putUpdateTicketStatusSuccess,
  putUpdateTicketStatusFailure,
  putUpdateTicketStatus,
  postTicketCommentSuccess,
  postTicketCommentFailure,
  postTicketComment,
  postTicketCommentAttachmentSuccess,
  postTicketCommentAttachmentFailure,
  postTicketCommentAttachment,
  postUpdateTicketApprovalSuccess,
  postUpdateTicketApprovalFailure,
  postUpdateTicketApproval,
  getExportReport,
  getExportReportSuccess,
  getExportReportFailure,
} from "@state/reducer/departmental-ticketing";
import {
  getTicketsService,
  getTicketService,
  getPriorityTicketsService,
  getGraphTicketsService,
  getDueTicketsService,
  postTicketService,
  destroyTicketService,
  getTicketDepartmentUsersService,
  postAssignTicketUserService,
  getTicketDetailsByIdService,
  getTicketsInfoService,
  putTicketExtendDateService,
  postForwardTicketService,
  getDepartmentsService,
  updateTicketStatusService,
  postTicketCommentService,
  postTicketCommentAttachmentService,
  updateTicketApprovalService,
  getExportReportService,
} from "@services/departmental-ticketing/departmental-ticketing.service.ts";
import { TAssignTicketUserPayload, TDepartmentalTicketActionPayloadPostPut, TDepartmentalTicketDelete, TTicketDepartmentUsersPayload } from "@state/types/departmental-ticketing";
import { handleServerException } from "@services/utils/utils.service";

import { toast } from "react-toastify";
import { IForwardTicketPayload, ISaveAttachmentsPayload, ITicketStatusUpdateResponse, ITicketUpdateStatusPayload, TITicketExtendDatePayload, IExportReportPayload } from "@interface/departmental-ticketing-interface";
import { UpdateTicketApprovalPayload } from "@interface/update-ticket-approval";


function* getTicketSaga(
  //required to use params for optimization
  actions: PayloadAction<{ params: IDefaultParams }>
) {
  try {
    const dataResponse: AxiosResponse = yield call(getTicketsService, actions.payload.params);
    //We may or may not need to add data into the dataResponse (dataResponse.data)
    yield put(getTicketSuccess(dataResponse));
  } catch (error) {
    yield call(handleServerException, error, getTicketFailure.type, true);
  }
}
function* getTicketsInfoSaga(
  //required to use params for optimization
  actions: PayloadAction<{ params: IDefaultParams }>
) {
  try {
    const dataResponse: AxiosResponse = yield call(getTicketsInfoService, actions.payload.params);
    //We may or may not need to add data into the dataResponse (dataResponse.data)
    yield put(getTicketsInfoSuccess(dataResponse));
  } catch (error) {
    yield call(handleServerException, error, getTicketsInfoFailure.type, true);
  }
}

function* getPriorityTicketSaga(
  //required to use params for optimization
  actions: PayloadAction<{ params: IDefaultParams }>
) {
  try {
    const dataResponse: AxiosResponse = yield call(getPriorityTicketsService, actions.payload.params);
    yield put(getPriorityTicketSuccess(dataResponse));
  } catch (error) {
    yield call(handleServerException, error, getPriorityTicketFailure.type, true);
  }
}
function* getDueTicketSaga(
  //required to use params for optimization
  actions: PayloadAction<{ params: IDefaultParams }>
) {
  try {
    const dataResponse: AxiosResponse = yield call(getDueTicketsService, actions.payload.params);
    yield put(getDueTicketSuccess(dataResponse));
  } catch (error) {
    yield call(handleServerException, error, getDueTicketFailure.type, true);
  }
}
function* getGraphTicketSaga(
  //required to use params for optimization
  actions: PayloadAction<{ params: IDefaultParams }>
) {
  try {
    const dataResponse: AxiosResponse = yield call(getGraphTicketsService, actions.payload.params);
    yield put(getGraphTicketSuccess(dataResponse));
  } catch (error) {
    yield call(handleServerException, error, getGraphTicketFailure.type, true);
  }
}

function* postTicketSaga(actions: TDepartmentalTicketActionPayloadPostPut) {
  try {
    //This will place the results of the post request
    // into the data variable
    const { data }: AxiosResponse = yield call(postTicketService, actions.payload);
    //Then the data from the get request will be placed into the
    // newRemittanceData variable
    const newTicket: AxiosResponse = yield call(getTicketService, data.id);
    //Acknowledge that the post is a success
    yield put(postTicketSuccess(newTicket?.data));
    toast.success("Successfully created new Ticket");
    //Call error if it isn't
  } catch (error) {
    yield call(handleServerException, error, postTicketFailure.type, true);
  }
}

function* destroyTicketSaga(actions: TDepartmentalTicketDelete) {
  try {
    yield call(destroyTicketService, actions.payload);
    yield put(destroyTicketSuccess(actions.payload.index));
    toast.success("Successfully deleted Ticket");
  } catch (error) {
    yield call(handleServerException, error, destroyTicketFailure.type, true);
  }
}

function* getDepartmentUsersSaga(action: PayloadAction<TTicketDepartmentUsersPayload>) {
  try {
    const result: AxiosResponse = yield call(getTicketDepartmentUsersService, action.payload);
    yield put(getDepartmentUsersSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getDepartmentUsersFailure.type, true);
  }
}

function* postAssignTicketUserSaga(action: PayloadAction<TAssignTicketUserPayload>) {
  try {
    const { data }: AxiosResponse = yield call(postAssignTicketUserService, action.payload);
    yield put(postAssignTicketUserSuccess(data));
  } catch (error) {
    yield call(handleServerException, error, postAssignTicketUserFailure.type, true);
  }
}

function* getTicketAssigneeDetailsSaga(action: PayloadAction<{ params: IDefaultParams }>) {
  try {
    const { params } = action.payload;

    const result: AxiosResponse = yield call(getTicketDetailsByIdService, params);
    yield put(getTicketAssigneeDetailsSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getTicketAssigneeDetailsFailure.type, true);
  }
}

function* putTicketExtendDateSaga(action: PayloadAction<{ params: TITicketExtendDatePayload }>) {
  try {
    const response: AxiosResponse = yield call(putTicketExtendDateService, action.payload.params);
    yield put(putTicketExtendDateSuccess(response.data));
  } catch (error) {
    yield call(handleServerException, error, putTicketExtendDateFailure.type, true);
  }
}

function* postForwardTicketSaga(actions: PayloadAction<IForwardTicketPayload>) {
  try {
    const result: AxiosResponse = yield call(postForwardTicketService, actions.payload); // Call the forwardTicketService service
    yield put(postForwardTicketSuccess(result.data)); // Dispatch forwardTicketSuccess action with the received data
  } catch (error) {
    yield call(handleServerException, error, postForwardTicketFailure.type, true); // Handle any errors using handleServerException utility
  }
}

function* getDepartmentsSaga() {
  try {
    const result: AxiosResponse = yield call(getDepartmentsService); // Call the forwardTicketService service
    yield put(getDepartmentsSuccess(result.data)); // Dispatch forwardTicketSuccess action with the received data
  } catch (error) {
    yield call(handleServerException, error, getDepartmentsFailure.type, true); // Handle any errors using handleServerException utility
  }
}



function* postUpdateTicketSaga(action: PayloadAction<{ id: number; status: string; remarks?: string }>) {
  try {
    const { id, status, remarks } = action.payload;
    const payload: ITicketUpdateStatusPayload = { status };
    if (remarks) {
      payload.remarks = remarks;
    }
    const result: AxiosResponse<ITicketStatusUpdateResponse> = yield call(updateTicketStatusService, id, payload);
    yield put(putUpdateTicketStatusSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, putUpdateTicketStatusFailure.type, true);
  }
}

function* postTicketCommentSaga(action: PayloadAction<{ id: number; comment: string }>) {
  try {
    const result: AxiosResponse = yield call(postTicketCommentService, action.payload);
    yield put(postTicketCommentSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, postTicketCommentFailure.type, true);
  }
}

function* postTicketCommentAttachmentSaga(action: PayloadAction<ISaveAttachmentsPayload>) {
  try {
    const result: AxiosResponse = yield call(postTicketCommentAttachmentService, action.payload);
    yield put(postTicketCommentAttachmentSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, postTicketCommentAttachmentFailure.type, true);
  }
}

function* postUpdateTicketApprovalSaga(action: PayloadAction<{ ticketId: number; payload: UpdateTicketApprovalPayload }>) {
  try {
    const { ticketId, payload } = action.payload;
    const result: AxiosResponse = yield call(updateTicketApprovalService, ticketId, payload);
    yield put(postUpdateTicketApprovalSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, postUpdateTicketApprovalFailure.type, true);
  }
}

function* getExportReportSaga(action: PayloadAction<IExportReportPayload>): Generator<any, void, any> {
  try {
    // Your existing service call that returns the blob
    const response: any = yield call(getExportReportService, action.payload);
    
    // Convert the blob response to a PDF blob
    const pdfBlob = new Blob([response], { type: 'application/pdf' });
    
    // Create a URL for the blob
    const pdfUrl = window.URL.createObjectURL(pdfBlob);
    
    // Open the PDF in a new tab
    window.open(pdfUrl, '_blank');
    
    // Clean up the URL after a short delay
    setTimeout(() => window.URL.revokeObjectURL(pdfUrl), 1000);
    
    yield put(getExportReportSuccess({ message: 'Report exported successfully' }));
    toast.success('Report exported successfully');
  } catch (error) {
    yield call(handleServerException, error, getExportReportFailure.type, true);
  }
}


export function* TicketSaga() {
  yield takeLatest(getTicket.type, getTicketSaga);
  yield takeLatest(getPriorityTicket.type, getPriorityTicketSaga);
  yield takeLatest(getGraphTicket.type, getGraphTicketSaga);
  yield takeLatest(getDueTicket.type, getDueTicketSaga);
  yield takeLatest(postTicket.type, postTicketSaga);
  yield takeLatest(destroyTicket.type, destroyTicketSaga);
  yield takeLatest(getDepartmentUsers.type, getDepartmentUsersSaga);
  yield takeLatest(postAssignTicketUser.type, postAssignTicketUserSaga);
  yield takeLatest(getTicketAssigneeDetails.type, getTicketAssigneeDetailsSaga);
  yield takeLatest(getTicketsInfo.type, getTicketsInfoSaga);
  yield takeLatest(putTicketExtendDate.type, putTicketExtendDateSaga);
  yield takeLatest(postForwardTicket.type, postForwardTicketSaga);
  yield takeLatest(getDepartments.type, getDepartmentsSaga);
  yield takeLatest(putUpdateTicketStatus.type, postUpdateTicketSaga);
  yield takeLatest(postTicketComment.type, postTicketCommentSaga);
  yield takeLatest(postTicketCommentAttachment.type, postTicketCommentAttachmentSaga);
  yield takeLatest(postUpdateTicketApproval.type, postUpdateTicketApprovalSaga);
  yield takeLatest(getExportReport.type, getExportReportSaga);
}
