import { takeLatest, call, put, all, takeEvery } from "redux-saga/effects";
import { PayloadAction } from "@reduxjs/toolkit";
import { IDefaultParams } from "@interface/common.interface";
import { TIssuancePayload, TIssuanceResponse } from "@state/types/master-policy";
import {
  getIssuanceService,
  getIssuanceServiceById,
  postIssuanceService,
  putIssuanceService,
  deleteIssuanceService
} from "@services/master-policy/master-policy.service.ts";
import {
  getIssuancesRequest,
  getIssuancesSuccess,
  getIssuancesFailure,
  getIssuanceRequest,
  getIssuanceSuccess,
  getIssuanceFailure,
  postIssuanceRequest,
  postIssuanceSuccess,
  postIssuanceFailure,
  putIssuanceRequest,
  putIssuanceSuccess,
  putIssuanceFailure,
  deleteIssuanceRequest,
  deleteIssuanceSuccess,
  deleteIssuanceFailure,
} from "@state/reducer/master-policy";

//TODO: Remove generator once we got Back-End online
// GET ALL PRODUCTS SAGA
function* getIssuancesSaga(action: PayloadAction<IDefaultParams>): Generator<any, void, any> {
  try {
    const response = yield call(getIssuanceService, action.payload);
    yield put(getIssuancesSuccess(response.data.data));
  } catch (error: any) {
    yield put(getIssuancesFailure(error.message || "Failed to fetch products"));
  }
}

// GET SINGLE PRODUCT SAGA
function* getIssuanceSaga(action: PayloadAction<number>): Generator<any, void, any> {
  try {
    const response = yield call(getIssuanceServiceById, action.payload);
    yield put(getIssuanceSuccess(response.data));
  } catch (error: any) {
    yield put(getIssuanceFailure(error.message || "Failed to fetch product"));
  }
}

// CREATE PRODUCT SAGA
function* postIssuanceSaga(action: PayloadAction<TIssuancePayload>): Generator<any, void, any>  {
  try {
    const response = yield call(postIssuanceService, action.payload);
    yield put(postIssuanceSuccess(response.data));
  } catch (error: any) {
    yield put(postIssuanceFailure(error.message || "Failed to create product"));
  }
}

// UPDATE PRODUCT SAGA
function* putIssuanceSaga(action: PayloadAction<TIssuanceResponse>): Generator<any, void, any> {
  try {
    const { data } = action.payload;
    const response = yield call(putIssuanceService, data.id, data);
    yield put(putIssuanceSuccess(response.data));
  } catch (error: any) {
    yield put(putIssuanceFailure(error.message || "Failed to update product"));
  }
}

// DELETE PRODUCT SAGA
function* deleteIssuanceSaga(action: PayloadAction<number>): Generator<any, void, any> {
  try {
    yield call(deleteIssuanceService, action.payload);
    yield put(deleteIssuanceSuccess(action.payload));
  } catch (error: any) {
    yield put(deleteIssuanceFailure(error.message || "Failed to delete product"));
  }
}

// WATCHER SAGA
export function* issuanceSaga() {
  yield all([
    takeLatest(getIssuancesRequest.type, getIssuancesSaga),
    takeLatest(getIssuanceRequest.type, getIssuanceSaga),
    takeLatest(postIssuanceRequest.type, postIssuanceSaga),
    takeEvery(putIssuanceRequest.type, putIssuanceSaga),
    takeLatest(deleteIssuanceRequest.type, deleteIssuanceSaga),
  ]);
}
