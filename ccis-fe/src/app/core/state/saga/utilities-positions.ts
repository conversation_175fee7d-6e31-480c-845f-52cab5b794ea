import { takeLatest, call, put } from "redux-saga/effects"; // Importing necessary functions from redux-saga
import { AxiosResponse } from "axios"; // Importing AxiosResponse type from axios
import {
  getPosition, // Action for initiating get request
  getPositionSuccess, // Action for successful get request
  getPositionFailure, // Action for failed get request
  postPosition, // Action for initiating post request
  postPositionSuccess, // Action for successful post request
  postPositionFailure, // Action for failed post request
  destroyPositionSuccess, // Action for successful delete request
  destroyPosition, // Action for initiating delete request
  destroyPositionFailure, //Action for failed put request
  putPosition as putInit, // Action for initiating put request
  putPositionSuccess, // Action for successful put request
  putPositionFailure, //Action for failed put request
} from "@state/reducer/utilities-positions"; // Importing actions from Positions-management reducer
import { getPositionService, postPositionsService, getPositionsService, destroyPositionsService, putPositionsService } from "@services/utilities-positions/utilities-positions.service"; // Importing service functions for position management
import { handleServerException } from "@services/utils/utils.service"; // Importing a utility function for handling server exceptions
import { TDeleteUtilitiesPositionsActionPayload, TUtilitiesPositionsActionPayload } from "@state/types/utilities-positions"; // Importing the type for post Positions action payload
import { PayloadAction } from "@reduxjs/toolkit";
import { IDefaultParams } from "@interface/common.interface";

// Saga for handling get Positions request
function* getPositionsSaga(actions: PayloadAction<{ params: IDefaultParams }>) {
  try {
    const { data }: AxiosResponse = yield call(getPositionsService, actions.payload.params); // Call the getPositions service and destructure data from the response
    yield put(getPositionSuccess(data)); // Dispatch getSuccess action with the received data
  } catch (error) {
    yield call(handleServerException, error, getPositionFailure.type, true); // Handle any errors using handleServerException utility
  }
}

// Saga for handling post Positions request
function* postPositionsSaga(actions: TUtilitiesPositionsActionPayload) {
  try {
    const { data }: AxiosResponse = yield call(postPositionsService, actions.payload); // Call the postPositions service with the action payload and destructure data from the response
    const newPosition: AxiosResponse = yield call(getPositionService, data.id); // Fetch the newly created position using getposition service
    yield put(postPositionSuccess(newPosition?.data)); // Dispatch postSuccess action with the new position's data
  } catch (error) {
    yield call(handleServerException, error, postPositionFailure.type, true); // Handle any errors using handleServerException utility
  }
}

// Saga for handling put Positions request
function* putPositionsSaga(actions: TUtilitiesPositionsActionPayload & { index: number }) {
  try {
    const { data }: AxiosResponse = yield call(putPositionsService, actions.payload); // Call the postPositions service with the action payload and destructure data from the response

    const newPosition: AxiosResponse = yield call(getPositionService, data.id); // Fetch the newly created position using getposition service
    yield put(putPositionSuccess({ data: newPosition?.data, index: actions.index })); // Dispatch postSuccess action with the new position's data
  } catch (error) {
    yield call(handleServerException, error, putPositionFailure.type, true); // Handle any errors using handleServerException utility
  }
}

// Saga to handle position deletion
function* destroyPositionsSaga(actions: TDeleteUtilitiesPositionsActionPayload) {
  try {
    // Call the destroy Positions function with the provided payload
    yield call(destroyPositionsService, actions.payload);
    // Dispatch success action if deletion is successful
    yield put(destroyPositionSuccess(actions.payload.index));
  } catch (error) {
    // Handle any errors that occur during the deletion process
    yield call(handleServerException, error, destroyPositionFailure.type, true);
  }
}

// Root saga to watch for get and post actions
export function* rootSaga() {
  yield takeLatest(getPosition.type, getPositionsSaga); // Watch for get action and run getPositionsSaga
  yield takeLatest(postPosition.type, postPositionsSaga); // Watch for post action and run postPositionsSaga
  yield takeLatest(putInit.type, putPositionsSaga); // Watch for post action and run putPositionsSage
  yield takeLatest(destroyPosition.type, destroyPositionsSaga); // Watch for destroy action and run destoryPositionsSaga
}
