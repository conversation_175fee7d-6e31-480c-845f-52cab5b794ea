import { IDefaultParams } from "@interface/common.interface";
import { PayloadAction } from "@reduxjs/toolkit";
import { 
  getRegionsService,
  getRegionService,
  postRegionService,
  putRegionService,
  destroyRegionService, 
} from "@services/utilities-region/utilities-region.service";
import { handleServerException } from "@services/utils/utils.service";

import { 
  getRegion, 
  getRegionFailure, 
  getRegionSuccess,
  getSingleRegion,
  getSingleRegionSuccess,
  getSingleRegionFailure,
  postRegion,
  postRegionFailure,
  postRegionSuccess,
  putRegion,
  putRegionFailure,
  putRegionSuccess,
  destroyRegion,
  destroyRegionFailure,
  destroyRegionSuccess,
} from "@state/reducer/utilities-region";
import{
  TUtilitiesRegionActionPayloadPostPut,
  TUtilitiesRegionDelete,
  TUtilitiesRegionWithIDAndIndexPayload,
} from "@state/types/utilities-region";

import { AxiosResponse } from "axios";
import { call, put, takeLatest } from "redux-saga/effects";

function* getRegionSaga(action: PayloadAction<{ params: IDefaultParams }>) {
  try {
    const result: AxiosResponse = yield call(getRegionsService, action.payload.params);
    yield put(getRegionSuccess(result));
  } catch (error) {
    yield call(handleServerException, error, getRegionFailure.type, true);
  }
}
function* getSingleRegionSaga(
  actions: PayloadAction<TUtilitiesRegionWithIDAndIndexPayload>
  ) {
  try {
    const result: AxiosResponse = yield call(getRegionService, actions.payload.id);
    yield put(getSingleRegionSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getSingleRegionFailure.type, true);
  }
}

function* postRegionSaga(actions: TUtilitiesRegionActionPayloadPostPut) {
  try {
    const result: AxiosResponse = yield call(postRegionService, actions.payload);
    yield put(postRegionSuccess(result.data));
    yield put(getRegion({ params: { filter: "" } }));
  } catch (error) {
    yield call(handleServerException, error, postRegionFailure.type, true);
  }
}

function* putRegionSaga(actions: TUtilitiesRegionActionPayloadPostPut & { index: number }) {
  try {
    const { data }: AxiosResponse = yield call(putRegionService, actions.payload);
    yield put(putRegionSuccess(data));
    yield put(getRegion({ params: { filter: "" } }));
  } catch (error) {
    yield call(handleServerException, error, putRegionFailure.type, true);
  }
}

function* destroyRegionSaga(actions: TUtilitiesRegionDelete) {
  try {
    yield call(destroyRegionService, actions.payload);
    yield put(destroyRegionSuccess(actions.payload.index));
    yield put(getRegion({ params: { filter: "" } }));
  } catch (error) {
    yield call(handleServerException, error, destroyRegionFailure.type, true);
  }
}

export function* rootWeeklyAccomplishmentReportSaga() {
  yield takeLatest(getRegion.type, getRegionSaga);
  yield takeLatest(getSingleRegion.type, getSingleRegionSaga);
  yield takeLatest(postRegion.type, postRegionSaga);
  yield takeLatest(putRegion.type, putRegionSaga);
  yield takeLatest(destroyRegion.type, destroyRegionSaga);
}
