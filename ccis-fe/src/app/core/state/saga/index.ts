import { all, fork } from "redux-saga/effects";
import * as authSaga from "./auth";
import * as permissionsSaga from "./permissions";
import * as productsSaga from "./products";
import * as productProposal from "./product-proposal";
import * as profileSaga from "./profile";
import * as rolesSaga from "./roles";
import * as usersSaga from "./users";
import * as usersManagementSaga from "./users-management";
import * as usersProductApproval from "./users-product-approval";
import * as ClspAER from "./actuary-clsp-aer";
import * as ClppAER from "./actuary-clpp-aer";
import * as FipAER from "./actuary-fip-aer";
import * as GyrtAER from "./actuary-gyrt-aer";
import * as utilitiesBenefitRate from "./actuary-utilities-benefit-rate";
import * as utilitiesDefaultNumberOfClaims from "./actuary-utilities-defaultnumberofclaims";
import * as utilitiesMortalityRate from "./actuary-utilities-mortality-rate";
import * as utilitiesRiskPremiumRate from "./actuary-utilities-risk-premium";
import * as actuaryUtilitiesClppRate from "./actuary-utilities-clpp-rate";
import * as commissionType from "./commision-type";
import * as productsForCompliance from "./compliance";
import * as contestability from "./contestability";
import * as cooperatives from "./cooperatives";
import * as cooperativesCda from "./cooperatives-cda";
import * as coverageType from "./coverage-type";
import * as departmentalTicketing from "./departmental-ticketing";
import * as departmentalTicketingApplication from "./departmental-ticketing-application";
import * as departmentalTicketingDeviceSystem from "./departmental-ticketing-device-system";
import * as departmentalTicketingOperatingSystem from "./departmental-ticketing-operating-system";
import * as departmentalTicketingRequestTypes from "./departmental-ticketing-request-types";
import * as formInventoryIncomingReceivedForms from "./form-inventory-incoming-received-form";
import * as formInventoryTransmittal from "./form-inventory-transmittal";
import * as formInventoryUtilitiesBanks from "./form-inventory-utilities-banks";
import * as formInventoryUtilitiesBankAccounts from "./form-inventory-utlities-bank-accounts";
import * as formInventoryUtilitiesDivisions from "./form-inventory-utilities-divisions";
import * as formInventoryUtilitiesFormTypes from "./form-inventory-utilities-form-types";
import * as formInventoryUtilitiesLocation from "./form-inventory-utilities-location";
import * as formInventoryUtilitiesMarketAreas from "./form-inventory-utilities-market-areas";
import * as formInventoryUtilitiesPaymentMethods from "./form-inventory-utilities-payment-methods";
import * as formInventoryUtilitiesReleasedMethods from "./form-inventory-utilities-released-methods";
import * as quotation from "./quotation";
import * as quotationSalesFipQuotation from "./quotation-sales-fip";
import * as shares from "./shares";
import * as sharesPayments from "./shares-payment";
import * as uatAnswersSaga from "./uat-answers";
import * as uatDetailsSaga from "./uat-details";
import * as utilitiesAdminExpense from "./utilities-admin-expense";
import * as utilitiesAffiliationSaga from "./utilities-affiliation";
import * as utilitiesAreas from "./utilities-areas";
import * as utilitiesCommissionAgeTypesSaga from "./utilities-commission-age-types";
import * as utilitiesCooperativeCategorySaga from "./utilities-cooperative-category";
import * as utilitiesCooperativeMembershipTypeSaga from "./utilities-cooperative-membership";
import * as utilitiesCooperativeRequirementSaga from "./utilities-cooperative-requirement";
import * as utilitiesCooperativeRequirementTemplateSaga from "./utilities-cooperative-requirement-template";
import * as utilitiesCooperativeShareTypeSaga from "./utilities-cooperative-sharetype";
import * as utilitiesCooperativeTypeSaga from "./utilities-cooperative-types";
import * as utilitiesDepartmentsSaga from "./utilities-departments";
import * as utilitiesPositionsSaga from "./utilities-positions";
import * as utilitiesProductBenefitsSaga from "./utilities-product-benefits";
import * as utilitiesProductCategorySaga from "./utilities-product-category";
import * as utilitiesProductCommisionSaga from "./utilities-product-commission";
import * as utilitiesProductHeadersSaga from "./utilities-product-headers";
import * as utilitiesProductTypeSaga from "./utilities-product-type";
import * as utilitiesSignatoryTemplateSaga from "./utilities-signatory-template";
import * as utilitiesSignatoryTypeSaga from "./utilities-signatory-type";
import * as utilitiesTargetMarketSaga from "./utilities-target-market";
import * as utilitiesRemittanceTypeSaga from "./utilities-remittance-types";
import * as utilitiesRemittanceDataSaga from "./utilities-remittance-data";
import * as managerIdSaga from "./manager-id";
import * as gamPadRequestSaga from "./gam-pad-request";
import * as systemSettingsIds from "./system-settings";
import * as formInventoryDashboards from "./form-inventory-dashboards";
import * as formInventoryPrIssuance from "./form-inventory-pr-issuance";
import * as globalSettings from "./global-settings";
import * as notification from "./notification";
import * as weeklyAccomplishmentReport from "./weekly-accomplishment-report";
import * as utilitiesRegion from "./utilities-region";
import * as masterPolicy from "./master-policy";
import * as dashboardSales from "./dashboard-sales";

export default function* root() {
  const sagas = [
    ...Object.values(authSaga),
    ...Object.values(usersSaga),
    ...Object.values(usersManagementSaga),
    ...Object.values(utilitiesDepartmentsSaga),
    ...Object.values(utilitiesPositionsSaga),
    ...Object.values(utilitiesSignatoryTypeSaga),
    ...Object.values(utilitiesSignatoryTemplateSaga),
    ...Object.values(utilitiesTargetMarketSaga),
    ...Object.values(utilitiesCommissionAgeTypesSaga),
    ...Object.values(rolesSaga),
    ...Object.values(profileSaga),
    ...Object.values(permissionsSaga),
    ...Object.values(utilitiesProductTypeSaga),
    ...Object.values(utilitiesProductCategorySaga),
    ...Object.values(utilitiesProductCommisionSaga),
    ...Object.values(utilitiesProductBenefitsSaga),
    ...Object.values(utilitiesProductHeadersSaga),
    ...Object.values(productsSaga),
    ...Object.values(usersProductApproval),
    ...Object.values(utilitiesAffiliationSaga),
    ...Object.values(utilitiesCooperativeCategorySaga),
    ...Object.values(utilitiesCooperativeTypeSaga),
    ...Object.values(utilitiesCooperativeRequirementSaga),
    ...Object.values(utilitiesCooperativeRequirementTemplateSaga),
    ...Object.values(uatDetailsSaga),
    ...Object.values(uatAnswersSaga),
    ...Object.values(utilitiesCooperativeMembershipTypeSaga),
    ...Object.values(cooperatives),
    ...Object.values(cooperativesCda),
    ...Object.values(productsForCompliance),
    ...Object.values(utilitiesAreas),
    ...Object.values(utilitiesCooperativeShareTypeSaga),
    ...Object.values(shares),
    ...Object.values(productProposal),
    ...Object.values(sharesPayments),
    ...Object.values(utilitiesMortalityRate),
    ...Object.values(utilitiesRiskPremiumRate),
    ...Object.values(utilitiesAdminExpense),
    ...Object.values(utilitiesBenefitRate),
    ...Object.values(utilitiesDefaultNumberOfClaims),
    ...Object.values(formInventoryUtilitiesDivisions),
    ...Object.values(formInventoryUtilitiesFormTypes),
    ...Object.values(formInventoryUtilitiesPaymentMethods),
    ...Object.values(formInventoryUtilitiesBanks),
    ...Object.values(formInventoryUtilitiesMarketAreas),
    ...Object.values(formInventoryUtilitiesBankAccounts),
    ...Object.values(formInventoryIncomingReceivedForms),
    ...Object.values(formInventoryUtilitiesReleasedMethods),
    ...Object.values(formInventoryUtilitiesLocation),
    ...Object.values(contestability),
    ...Object.values(commissionType),
    ...Object.values(quotation),
    ...Object.values(GyrtAER),
    ...Object.values(coverageType),
    ...Object.values(ClspAER),
    ...Object.values(ClppAER),
    ...Object.values(quotationSalesFipQuotation),
    ...Object.values(formInventoryTransmittal),
    ...Object.values(FipAER),
    ...Object.values(departmentalTicketing),
    ...Object.values(departmentalTicketingRequestTypes),
    ...Object.values(departmentalTicketingDeviceSystem),
    ...Object.values(departmentalTicketingApplication),
    ...Object.values(departmentalTicketingOperatingSystem),
    ...Object.values(actuaryUtilitiesClppRate),
    ...Object.values(utilitiesRemittanceTypeSaga),
    ...Object.values(utilitiesRemittanceDataSaga),
    ...Object.values(managerIdSaga),
    ...Object.values(gamPadRequestSaga),
    ...Object.values(systemSettingsIds),
    ...Object.values(formInventoryDashboards),
    ...Object.values(formInventoryPrIssuance),
    ...Object.values(globalSettings),
    ...Object.values(notification),
    ...Object.values(weeklyAccomplishmentReport),
    ...Object.values(utilitiesRegion),
    ...Object.values(masterPolicy),
    ...Object.values(utilitiesRemittanceDataSaga),
    ...Object.values(dashboardSales)
  ];
  yield all(sagas.map(fork));
}
