// Imports for the new GET function
import { PayloadAction } from "@reduxjs/toolkit";
import { TSalesDashboardPostPayload } from "@state/types/dashboard-sales";
import { ISalesDashboard } from "@interface/dashboard-sales";
import { postSalesDashboardData } from "@services/dashboard/sales.service";
import { handleServerException } from "@services/utils/utils.service";
import { postSalesDashboard, postSalesDashboardFailure, postSalesDashboardSuccess } from "@state/reducer/dashboard-sales";
import { call, put, takeLatest } from "redux-saga/effects";

function* postSalesDashboardSaga(actions: PayloadAction<TSalesDashboardPostPayload>) {
    try {
        const data: ISalesDashboard = yield call(postSalesDashboardData, actions.payload);
        yield put(postSalesDashboardSuccess(data));
    } catch (error) {
        yield call(handleServerException, error, postSalesDashboardFailure.type, true);
    }
}

export function* rootSaga() {
    yield takeLatest(postSalesDashboard.type, postSalesDashboardSaga);
}
