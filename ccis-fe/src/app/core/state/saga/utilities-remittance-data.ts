// Imports for the new GET function
import { IDefaultParams } from "@interface/common.interface";
import { PayloadAction } from "@reduxjs/toolkit";
// Importing AxiosResponse type from axios
import { AxiosResponse } from "axios";
// Importing necessary functions from redux-saga
import { call, put, takeLatest } from "redux-saga/effects";
// Import the Services declared earlier
import {
  destroyRemittanceDataService,
  getRemittanceDataService,
  getRemittanceDatasService,
  postRemittanceDataService,
  putRemittanceDataService,
} from "@services/utilities-remittance-data/utilities-remittance-data.service";

import { handleServerException } from "@services/utils/utils.service";
// All reducer types here
import {
  destroyRemittanceData,
  destroyRemittanceDataFailure,
  destroyRemittanceDataSuccess,
  getRemittanceData,
  getRemittanceDataFailure,
  getRemittanceDataSuccess,
  postRemittanceData,
  postRemittanceDataFailure,
  postRemittanceDataSuccess,
  putRemittanceData as putInit,
  putRemittanceDataFailure,
  putRemittanceDataSuccess,
} from "@state/reducer/utilities-remittance-data";
// Payload shells types here
import {
  TUtilitiesRemittanceDataActionPayloadPostPut,
  TUtilitiesRemittanceDataDelete,
} from "@state/types/utilities-remittance-data";

function* getRemittanceDataSaga(
  //required to use params for optimization
  actions: PayloadAction<{ params: IDefaultParams }>
) {
  try {
    const dataResponse : AxiosResponse = yield call(
      getRemittanceDatasService,
      actions.payload.params
    );
    yield put(getRemittanceDataSuccess(dataResponse));
  } catch (error) {
    yield call(
      handleServerException,
      error,
      getRemittanceDataFailure.type,
      true
    );
  }
}

function* postRemittanceDataSaga(
  actions: TUtilitiesRemittanceDataActionPayloadPostPut
) {
  try {
    //This will place the results of the post request 
    // into the data variable
    const { data }: AxiosResponse = yield call(
      postRemittanceDataService,
      actions.payload
    );
    //Then the data from the get request will be placed into the 
    // newRemittanceData variable
    const newRemittanceData: AxiosResponse = yield call(
      getRemittanceDataService,
      data.id
    );
    //Acknowledge that the post is a success 
    yield put(postRemittanceDataSuccess(newRemittanceData?.data));
    //Call error if it isn't
  } catch (error) {
    yield call(
      handleServerException,
      error,
      postRemittanceDataFailure.type,
      true
    );
  }
}

function* putRemittanceDataSaga(
  actions: TUtilitiesRemittanceDataActionPayloadPostPut & { index: number }
) {
  try {
    const { data }: AxiosResponse = yield call(
      putRemittanceDataService,
      actions.payload
    );
    
    const newRemittanceData: AxiosResponse = yield call(
      getRemittanceDataService,
      data.id
    );
    yield put(
      putRemittanceDataSuccess({
        data: newRemittanceData?.data,
        index: actions.index,
      })
    );    
  } catch (error) {
    yield call(
      handleServerException,
      error,
      putRemittanceDataFailure.type,
      true
    );
  }
}

function* destroyRemittanceDataSaga(actions: TUtilitiesRemittanceDataDelete) {
  try {
    yield call(destroyRemittanceDataService, actions.payload);
    yield put(destroyRemittanceDataSuccess(actions.payload.index));
  } catch (error) {
    yield call(
      handleServerException,
      error,
      destroyRemittanceDataFailure.type,
      true
    );
  }
}

export function* rootSaga() {
  yield takeLatest(getRemittanceData.type, getRemittanceDataSaga);
  yield takeLatest(postRemittanceData.type, postRemittanceDataSaga);
  yield takeLatest(putInit.type, putRemittanceDataSaga);
  yield takeLatest(destroyRemittanceData.type, destroyRemittanceDataSaga);
}
