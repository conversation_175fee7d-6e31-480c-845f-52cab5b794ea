import { takeLatest, call, put } from "redux-saga/effects"; // Importing necessary functions from redux-saga
import { AxiosResponse } from "axios"; // Importing AxiosResponse type from axios

import {
  getCooperatives,
  getCooperativesSuccess,
  getCooperativesFailure,
  postCooperatives,
  postCooperativesSuccess,
  postCooperativesFailure,
  putCooperatives as putInit,
  putCooperativesSuccess,
  putCooperativesFailure,
  destroyCooperatives,
  destroyCooperativesSuccess,
  destroyCooperativesFailure,
  getCooperativeByIdSuccess,
  getCooperativeByIdFailure,
  getCooperativeById,
} from "@state/reducer/cooperatives";
import { getCooperativessService, postCooperativesService, getCooperativesByIDService, destroyCooperativesService, putCooperativesService } from "@services/cooperatives/cooperatives.service";
import { TCooperativesDelete, TCooperativesActionPayloadPostPut, TGetCooperativeByIdPayload } from "@state/types/cooperatives";
import { handleServerException } from "@services/utils/utils.service";
import { PayloadAction } from "@reduxjs/toolkit";
import { IDefaultParams } from "@interface/common.interface";
// function* getQuotationsSaga(actions: PayloadAction<{ params: IDefaultParams }>) {
//   try {
//     const response: AxiosResponse = yield call(getQuotationsService, actions.payload.params);
//     yield put(getQuotationsSuccess(response));
//   } catch (error) {
//     yield call(handleServerException, error, getQuotationsFailure.type, true);
//   }
// }
// function* getCooperativessSaga(actions: PayloadAction<IDefaultParams>) {
function* getCooperativessSaga(actions: PayloadAction<{ payload: IDefaultParams }>) {
  try {
    const data: AxiosResponse = yield call(getCooperativessService, actions.payload?.payload);
    yield put(getCooperativesSuccess(data));
  } catch (error) {
    yield call(handleServerException, error, getCooperativesFailure.type, true);
  }
}

// New saga for getCooperativeById
function* getCooperativeByIdSaga(actions: TGetCooperativeByIdPayload) {
  try {
    const { data }: AxiosResponse = yield call(getCooperativesByIDService, actions.payload.id);
    yield put(getCooperativeByIdSuccess(data));
  } catch (error) {
    yield call(handleServerException, error, getCooperativeByIdFailure.type, true);
  }
}
function* postCooperativesSaga(actions: TCooperativesActionPayloadPostPut) {
  try {
    const { data }: AxiosResponse = yield call(postCooperativesService, actions.payload);
    const newCooperatives: AxiosResponse = yield call(getCooperativesByIDService, data.id);
    yield put(postCooperativesSuccess(newCooperatives?.data));
  } catch (error) {
    yield call(handleServerException, error, postCooperativesFailure.type, true);
  }
}
function* putCooperativesSaga(actions: TCooperativesActionPayloadPostPut & { index: number }) {
  try {
    const { data }: AxiosResponse = yield call(putCooperativesService, actions.payload);
    const newCooperatives: AxiosResponse = yield call(getCooperativesByIDService, data.id);
    yield put(
      putCooperativesSuccess({
        data: newCooperatives?.data,
        index: actions.index,
      })
    );
  } catch (error) {
    yield call(handleServerException, error, putCooperativesFailure.type, true);
  }
}

function* destroyCooperativesSaga(actions: TCooperativesDelete) {
  try {
    yield call(destroyCooperativesService, actions.payload);
    yield put(destroyCooperativesSuccess(actions.payload.index));
  } catch (error) {
    yield call(handleServerException, error, destroyCooperativesFailure.type, true);
  }
}

export function* rootSaga() {
  yield takeLatest(getCooperatives.type, getCooperativessSaga);
  yield takeLatest(getCooperativeById.type, getCooperativeByIdSaga);
  yield takeLatest(postCooperatives.type, postCooperativesSaga);
  yield takeLatest(putInit.type, putCooperativesSaga);
  yield takeLatest(destroyCooperatives.type, destroyCooperativesSaga);
}
