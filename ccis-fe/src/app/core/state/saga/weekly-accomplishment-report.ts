import { takeLatest, call, put } from "redux-saga/effects";
import { AxiosResponse } from "axios"; // Importing AxiosResponse type from axios
import { handleServerException } from "@services/utils/utils.service";
import { PayloadAction } from "@reduxjs/toolkit";
import { IDefaultParams } from "@interface/common.interface";
import {
  destroyAccomplishmentReport,
  destroyAccomplishmentReportFailure,
  destroyAccomplishmentReportSuccess,
  exportAccomplishmentReport,
  exportAccomplishmentReportFailure,
  exportAccomplishmentReportSuccess,
  getAccomplishmentReportById,
  getAccomplishmentReportByIdFailure,
  getAccomplishmentReportByIdSuccess,
  getAllAccomplishmentReport,
  getAllAccomplishmentReportFailure,
  getAllAccomplishmentReportSuccess,
  getIssueTypes,
  getIssueTypesFailure,
  getIssueTypesSuccess,
  getLoggedInUserAccomplishmentReport,
  getLoggedInUserAccomplishmentReportFailure,
  getLoggedInUserAccomplishmentReportSuccess,
  getTaskGroups,
  getTaskGroupsFailure,
  getTaskGroupsSuccess,
  getTaskTypes,
  getTaskTypesFailure,
  getTaskTypesSuccess,
  postAddAccomplishmentReport,
  postAddAccomplishmentReportFailure,
  postAddAccomplishmentReportSuccess,
  putUpdateAccomplishmentReport,
  putUpdateAccomplishmentReportFailure,
  putUpdateAccomplishmentReportSuccess,
} from "@state/reducer/weekly-accomplishment-report";
import {
  destroyAccomplishmentReportService,
  exportAccomplishmentReportService,
  getAllAccomplishmentReportService,
  getIssueTypeService,
  getLoggedInUserAccomplishmentReportService,
  getTaskGroupService,
  getTaskTypeService,
  postAddAccomplishmentReportService,
  putUpdateAccomplishmentReportService,
} from "@services/weekly-management-report/weekly-management-report.service";
import { IAccomplishmentReportPayload } from "@interface/weekly-accomplishment-report.interface";

function* getAllAccomplishmentReportSaga(action: PayloadAction<{ params: IDefaultParams }>) {
  try {
    const result : AxiosResponse = yield call(getAllAccomplishmentReportService, action.payload.params);
    yield put(getAllAccomplishmentReportSuccess(result));
  } catch (error) {
    yield call(handleServerException, error, getAllAccomplishmentReportFailure.type, true);
  }
}

function* getAccomplishmentReportByIdSaga(action: PayloadAction<IDefaultParams>) {
  try {
    const { data }: AxiosResponse = yield call(getAllAccomplishmentReportService, action.payload);
    yield put(getAccomplishmentReportByIdSuccess(data));
  } catch (error) {
    yield call(handleServerException, error, getAccomplishmentReportByIdFailure.type, true);
  }
}

function* postAddAccomplishmentReportSaga(action: PayloadAction<IAccomplishmentReportPayload>) {
  try {
    const { data }: AxiosResponse = yield call(postAddAccomplishmentReportService, action.payload);
    yield put(postAddAccomplishmentReportSuccess(data));
  } catch (error) {
    yield call(handleServerException, error, postAddAccomplishmentReportFailure.type, true);
  }
}

function* putUpdateAccomplishmentReportSaga(action: PayloadAction<{ accomplishmentId: number; payload: IAccomplishmentReportPayload }>) {
  try {
    const { accomplishmentId, payload } = action.payload;
    const { data }: AxiosResponse = yield call(putUpdateAccomplishmentReportService, accomplishmentId, payload);
    yield put(putUpdateAccomplishmentReportSuccess(data));
  } catch (error) {
    yield call(handleServerException, error, putUpdateAccomplishmentReportFailure.type, true);
  }
}

function* getIssueTypeSaga() {
  try {
    const { data }: AxiosResponse = yield call(getIssueTypeService);
    yield put(getIssueTypesSuccess(data));
  } catch (error) {
    yield call(handleServerException, error, getIssueTypesFailure.type, true);
  }
}

function* getTaskTypeSaga() {
  try {
    const { data }: AxiosResponse = yield call(getTaskTypeService);
    yield put(getTaskTypesSuccess(data));
  } catch (error) {
    yield call(handleServerException, error, getTaskTypesFailure.type, true);
  }
}

function* getTaskGroupSaga() {
  try {
    const { data }: AxiosResponse = yield call(getTaskGroupService);
    yield put(getTaskGroupsSuccess(data));
  } catch (error) {
    yield call(handleServerException, error, getTaskGroupsFailure.type, true);
  }
}

function* getLoggedInUserAccomplishmentReportSaga() {
  try {
    const { data }: AxiosResponse = yield call(getLoggedInUserAccomplishmentReportService);
    yield put(getLoggedInUserAccomplishmentReportSuccess(data));
  } catch (error) {
    yield call(handleServerException, error, getLoggedInUserAccomplishmentReportFailure.type, true);
  }
}

function* exportAccomplishmentReportSaga(action: PayloadAction<{ accomplishmentReportId: number }>) {
  try {
    const { accomplishmentReportId } = action.payload;
    const { data }: AxiosResponse = yield call(exportAccomplishmentReportService, accomplishmentReportId);
    yield put(exportAccomplishmentReportSuccess(data));
  } catch (error) {
    yield call(handleServerException, error, exportAccomplishmentReportFailure.type, true);
  }
}

function* destroyAccomplishmentReportSaga(action: PayloadAction<{ accomplishmentReportId: number }>) {
  try {
    const { accomplishmentReportId } = action.payload;
    const { data }: AxiosResponse = yield call(destroyAccomplishmentReportService, accomplishmentReportId);
    yield put(destroyAccomplishmentReportSuccess(data));
  } catch (error) {
    yield call(handleServerException, error, destroyAccomplishmentReportFailure.type, true);
  }
}

export function* rootWeeklyAccomplishmentReportSaga() {
  yield takeLatest(getAllAccomplishmentReport.type, getAllAccomplishmentReportSaga);
  yield takeLatest(getAccomplishmentReportById.type, getAccomplishmentReportByIdSaga);
  yield takeLatest(postAddAccomplishmentReport.type, postAddAccomplishmentReportSaga);
  yield takeLatest(getIssueTypes.type, getIssueTypeSaga);
  yield takeLatest(getTaskTypes.type, getTaskTypeSaga);
  yield takeLatest(getTaskGroups.type, getTaskGroupSaga);
  yield takeLatest(getLoggedInUserAccomplishmentReport.type, getLoggedInUserAccomplishmentReportSaga);
  yield takeLatest(putUpdateAccomplishmentReport.type, putUpdateAccomplishmentReportSaga);
  yield takeLatest(exportAccomplishmentReport.type, exportAccomplishmentReportSaga);
  yield takeLatest(destroyAccomplishmentReport.type, destroyAccomplishmentReportSaga);
}
