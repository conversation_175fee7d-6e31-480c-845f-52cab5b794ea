import { bindActionCreators, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { TAreaAdminState, IAreaAdminPayload, IAreaAdminWithIndexPayload, TGetAreaAdminsWithFilterActionPayload, TIAreaAdminActionPayload, TAreaAdminIDPayloadActionPayload, TAreaAdminWithIndexActionPayload } from "@state/types/form-inventory-utilities-area-admin";
import { useDispatch } from "react-redux";
import { showSuccess } from "@helpers/prompt";

const initialState: TAreaAdminState = {
    areaAdmins: [],
    selectedAreaAdmin: {
        index: 0,
        data: {
            id: 0,
            userAreaId: 0,
            branchName: 'string',
            userId: 0,
            adminName: 'string',
        }
    },
    getAreaAdmins: {
        loading: false,
        success: false,
        error: false
    },
    getAreaAdmin: {
        loading: false,
        success: false,
        error: false
    },
    postAreaAdmin: {
        loading: false,
        success: false,
        error: false
    },
    putAreaAdmin: {
        loading: false,
        success: false,
        error: false
    },
    destroyAreaAdmin: {
        loading: false,
        success: false,
        error: false
    }
}

const areaAdminSlice = createSlice({
    name: "areaAdminManagement",
    initialState,
    reducers: {
        setSelectedAreaAdmin( state, action: TAreaAdminWithIndexActionPayload) {
            state.selectedAreaAdmin = action.payload;
          },
          clearSelectedAreaAdmin(state) {
            state.selectedAreaAdmin = initialState.selectedAreaAdmin;
          },
    
        getAreaAdmins(state, _action: TGetAreaAdminsWithFilterActionPayload) {
            state.getAreaAdmins = {
                loading: true,
                success: false,
                error: false
            }
        },
        getAreaAdminsSuccess(state, action) {
            state.areaAdmins = [...action.payload].reverse();
            state.getAreaAdmins = {
                loading: false,
                success: true,
                error: false
            }
        },
        getAreaAdminsFailure(state) {
            state.getAreaAdmins = {
                loading: false,
                success: false,
                error: true
            }
        },

        getAreaAdmin(state, _action: TAreaAdminIDPayloadActionPayload) {
            state.getAreaAdmin = {
                loading: true,
                success: false,
                error: false
        }
        },
        getAreaAdminSuccess(state, action) {
            state.selectedAreaAdmin = action.payload;
            state.getAreaAdmin = {
                loading: false,
                success: true,
                error: false
            }
        },
        getAreaAdminFailure(state) {
            state.getAreaAdmin = {
                loading: false,
                success: false,
                error: true
            }
        },
        postAreaAdmin(state, _action: PayloadAction<IAreaAdminPayload>) {
            state.postAreaAdmin = {
                loading: true,
                success: false,
                error: false
            }
        },
        postAreaAdminSuccess(state, action: TIAreaAdminActionPayload) {
            state.areaAdmins.unshift(action.payload);
            state.postAreaAdmin = {
                loading: false,
                success: true,
                error: false
            }
            showSuccess();
        },
        postAreaAdminFailure(state) {
            state.postAreaAdmin = {
                loading: false,
                success: false,
                error: true
            }
        },
        putAreaAdmin(state, _action: PayloadAction<IAreaAdminPayload>) {
            state.putAreaAdmin = {
                loading: true,
                success: false,
                error: false
            }
        },
        putAreaAdminSuccess(state, action: TIAreaAdminActionPayload) {
            state.areaAdmins = state.areaAdmins.map((areaAdmin) =>
                areaAdmin.id === action.payload.id ? action.payload : areaAdmin
            );
            state.putAreaAdmin = {
                loading: false,
                success: true,
                error: false
            }
            showSuccess();
        },
        putAreaAdminFailure(state) {
            state.putAreaAdmin = {
                loading: false,
                success: false,
                error: true
            }
        },
        destroyAreaAdmin(state, _action: PayloadAction<IAreaAdminWithIndexPayload>) {
            state.destroyAreaAdmin = {
                loading: true,
                success: false,
                error: false
            }
        },
        destroyAreaAdminSuccess(state, action) {
            state.areaAdmins?.splice(action.payload, 1);
            state.destroyAreaAdmin = {
                loading: false,
                success: true,
                error: false
            }
            showSuccess("AreaAdmin deleted successfully.");
        },
        destroyAreaAdminFailure(state) {
            state.destroyAreaAdmin = {
                loading: false,
                success: false,
                error: true
            }
        },
        destroyAreaAdminReset(state) {
            state.destroyAreaAdmin = {
                loading: false,
                success: false,
                error: false
            }
        }
    }
})

export const {
    setSelectedAreaAdmin, 
    clearSelectedAreaAdmin,
    getAreaAdmins,
    getAreaAdminsSuccess,
    getAreaAdminsFailure,
    getAreaAdmin,
    getAreaAdminSuccess,
    getAreaAdminFailure,
    postAreaAdmin,
    postAreaAdminSuccess,
    postAreaAdminFailure,
    putAreaAdmin,
    putAreaAdminSuccess,
    putAreaAdminFailure,
    destroyAreaAdmin,
    destroyAreaAdminSuccess,
    destroyAreaAdminFailure,
    destroyAreaAdminReset
} = areaAdminSlice.actions

export const useAreaAdminActions = () => {
    return bindActionCreators(
        {
            setSelectedAreaAdmin,
            clearSelectedAreaAdmin,
            getAreaAdmins,
            getAreaAdmin,
            postAreaAdmin,
            putAreaAdmin,
            destroyAreaAdmin,
            destroyAreaAdminReset
        },
        useDispatch()
    );
};

export default areaAdminSlice.reducer;
