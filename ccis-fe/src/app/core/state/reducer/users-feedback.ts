import { createDynamicState } from "@helpers/array";
import { IDefaultParams } from "@interface/common.interface";
import { bindActionCreators, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { TUsersFeedbackState } from "@state/types/users-feedback";
import { useDispatch } from "react-redux";

const initialState: TUsersFeedbackState = {
  getFeedBackQuestions: createDynamicState(),
  getTransactionTypes: createDynamicState(),
  getUserFeedbacks: createDynamicState(),
  postSaveQuestions: createDynamicState(),
  postSaveChoice: createDynamicState(),
  postSaveTransactionType: createDynamicState(),
  postSaveFeedback: createDynamicState(),
  putUpdateTransactionType: createDynamicState(),
  putUpdateQuestion: createDynamicState(),
};

const usersFeedbackSlice = createSlice({
  name: "usersFeedback",
  initialState,
  reducers: {
    // Define your reducers here
    getFeedBackQuestions(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.getFeedBackQuestions = createDynamicState(["loading"]);
    },
    getFeedBackQuestionsSuccess(state, action) {
      state.getFeedBackQuestions = createDynamicState(["success"], action.payload);
    },
    getFeedBackQuestionsFailure(state) {
      state.getFeedBackQuestions = createDynamicState(["error"]);
    },
  },
});

export const {} = usersFeedbackSlice.actions;

export const useUsersFeedbackActions = () => {
  const dispatch = useDispatch();
  return bindActionCreators(
    {
      // Define your action creators here
    },
    dispatch
  );
};
