// Import dependencies
import { createDynamicState } from "@helpers/array";
import { showSuccess } from "@helpers/prompt";
import { bindActionCreators, createSlice } from "@reduxjs/toolkit";
// Import all the types that we would use for the operations here. 
import {
  TIUtilitiesRemittanceDataActionPayloadPostSuccess,
  TIUtilitiesRemittanceDataActionPayloadSelectedPutSuccess,
  TUtilitiesRemittanceDataActionPayloadPostPut,
  TUtilitiesRemittanceDataDelete,
  TUtilitiesRemittanceDataManagementState,
} from "@state/types/utilities-remittance-data";
import { useDispatch } from "react-redux";

//For Optimization of the Get
import { IDefaultParams } from "@interface/common.interface";
import { PayloadAction } from "@reduxjs/toolkit";

//Ensure that we use Datas, since we would be 
//getting that data for the table
const initialState: TUtilitiesRemittanceDataManagementState = {
  remittanceDatas: [],
  selectedRemittanceData: {
    index: 0,
    data: {
      periodFrom: "",
      periodTo: "",
      remittanceTypeId: "",
    },
  },
  getRemittanceData: createDynamicState(),
  postRemittanceData: createDynamicState(),
  putRemittanceData: createDynamicState(),
  destroyRemittanceData: createDynamicState(),
};

const utilitiesRemittanceDataManagementSlice = createSlice({
  name: "utilitiesRemittanceData",
  initialState,
  reducers: {
    setSelectedRemittanceData(
      state,
      action: TIUtilitiesRemittanceDataActionPayloadSelectedPutSuccess
    ) {
      state.selectedRemittanceData = action.payload;
    },
    clearSelectedRemittanceData(state) {
      state.selectedRemittanceData = initialState.selectedRemittanceData;
    },
    getRemittanceData(
      state,
      _action: PayloadAction<{ params: IDefaultParams }>
    ) {
      state.getRemittanceData = createDynamicState(["loading"]);
    },


    getRemittanceDataSuccess(state, action) {
      //Added .data in payload
      state.remittanceDatas = action.payload.data.reverse();
      state.getRemittanceData = createDynamicState(["success"], action.payload);
    },
    getRemittanceDataFailure(state) {
      state.getRemittanceData = createDynamicState(["error"]);
    },
    postRemittanceData(
      state,
      _action: TUtilitiesRemittanceDataActionPayloadPostPut
    ) {
      state.postRemittanceData = createDynamicState(["loading"]);
    },
    postRemittanceDataSuccess(
      state,
      action: TIUtilitiesRemittanceDataActionPayloadPostSuccess
    ) {
      state.remittanceDatas?.unshift(action.payload);
      state.postRemittanceData = createDynamicState(["success"]);
      showSuccess();
    },
    postRemittanceDataFailure(state) {
      state.postRemittanceData = createDynamicState(["error"]);
    },

    putRemittanceData(
      state,
      _actions: TUtilitiesRemittanceDataActionPayloadPostPut
    ) {
      state.putRemittanceData = createDynamicState(["loading"]);
    },
    putRemittanceDataSuccess(
      state,
      action: TIUtilitiesRemittanceDataActionPayloadSelectedPutSuccess
    ) {
      const prevState = state.remittanceDatas;
      prevState[state.selectedRemittanceData.index] = action.payload.data;
      state.remittanceDatas = prevState;
      state.putRemittanceData = createDynamicState(["success"]);
      showSuccess();
    },
    putRemittanceDataFailure(state) {
      state.putRemittanceData = createDynamicState(["error"]);
    },
    destroyRemittanceData(state, _action: TUtilitiesRemittanceDataDelete) {
      state.destroyRemittanceData = createDynamicState(["loading"]);
    },
    destroyRemittanceDataSuccess(state, action) {
      state.remittanceDatas?.splice(action.payload, 1);
      state.destroyRemittanceData = createDynamicState(["success"]);
      showSuccess();
    },
    destroyRemittanceDataFailure(state) {
      state.destroyRemittanceData = createDynamicState(["error"]);
    },
  },
});
export const {
  setSelectedRemittanceData,
  clearSelectedRemittanceData,
  getRemittanceData,
  getRemittanceDataSuccess,
  getRemittanceDataFailure,
  postRemittanceData,
  postRemittanceDataSuccess,
  postRemittanceDataFailure,
  putRemittanceData,
  putRemittanceDataSuccess,
  putRemittanceDataFailure,
  destroyRemittanceData,
  destroyRemittanceDataSuccess,
  destroyRemittanceDataFailure,
} = utilitiesRemittanceDataManagementSlice.actions;

export const useRemittanceDataManagementActions = () => {
  return bindActionCreators(
    {
      setSelectedRemittanceData,
      clearSelectedRemittanceData,
      getRemittanceData,
      postRemittanceData,
      putRemittanceData,
      destroyRemittanceData,
    },
    useDispatch()
  );
};
export default utilitiesRemittanceDataManagementSlice.reducer;
