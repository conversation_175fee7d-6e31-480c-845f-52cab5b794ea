// Import dependencies
import { createDynamicState } from "@helpers/array";
import { showSuccess } from "@helpers/prompt";
import { bindActionCreators, createSlice } from "@reduxjs/toolkit";
// Import all the types that we would use for the operations here. 
import {
  TIUtilitiesRemittanceTypeActionPayloadPostSuccess,
  TIUtilitiesRemittanceTypeActionPayloadSelectedPutSuccess,
  TUtilitiesRemittanceTypeActionPayloadPostPut,
  TUtilitiesRemittanceTypeDelete,
  TUtilitiesRemittanceTypeManagementState,
} from "@state/types/utilities-remittance-type";
import { useDispatch } from "react-redux";

//For Optimization of the Get
import { IDefaultParams } from "@interface/common.interface";
import { PayloadAction } from "@reduxjs/toolkit";

const initialState: TUtilitiesRemittanceTypeManagementState = {
  remittanceTypes: [],
  selectedRemittanceType: {
    index: 0,
    data: {
      remittanceTypeCode: "",
      remittanceTypeName: "",
      description: "",
    },
  },
  getRemittanceType: createDynamicState(),
  postRemittanceType: createDynamicState(),
  putRemittanceType: createDynamicState(),
  destroyRemittanceType: createDynamicState(),
};

const utilitiesRemittanceTypeManagementSlice = createSlice({
  name: "utilitiesRemittanceType",
  initialState,
  reducers: {
    setSelectedRemittanceType(
      state,
      action: TIUtilitiesRemittanceTypeActionPayloadSelectedPutSuccess
    ) {
      state.selectedRemittanceType = action.payload;
    },
    clearSelectedRemittanceType(state) {
      state.selectedRemittanceType = initialState.selectedRemittanceType;
    },
    getRemittanceType(
      state,
      _action: PayloadAction<{ params: IDefaultParams }>
    ) {
      state.getRemittanceType = createDynamicState(["loading"]);
    },


    getRemittanceTypeSuccess(state, action) {
      //Added .data in payload
      state.remittanceTypes = action.payload.data.reverse();
      state.getRemittanceType = createDynamicState(["success"], action.payload);
    },
    getRemittanceTypeFailure(state) {
      state.getRemittanceType = createDynamicState(["error"]);
    },
    postRemittanceType(
      state,
      _action: TUtilitiesRemittanceTypeActionPayloadPostPut
    ) {
      state.postRemittanceType = createDynamicState(["loading"]);
    },
    postRemittanceTypeSuccess(
      state,
      action: TIUtilitiesRemittanceTypeActionPayloadPostSuccess
    ) {
      state.remittanceTypes?.unshift(action.payload);
      state.postRemittanceType = createDynamicState(["success"]);
      showSuccess();
    },
    postRemittanceTypeFailure(state) {
      state.postRemittanceType = createDynamicState(["error"]);
    },

    putRemittanceType(
      state,
      _actions: TUtilitiesRemittanceTypeActionPayloadPostPut
    ) {
      state.putRemittanceType = createDynamicState(["loading"]);
    },
    putRemittanceTypeSuccess(
      state,
      action: TIUtilitiesRemittanceTypeActionPayloadSelectedPutSuccess
    ) {
      const prevState = state.remittanceTypes;
      prevState[state.selectedRemittanceType.index] = action.payload.data;
      state.remittanceTypes = prevState;
      state.putRemittanceType = createDynamicState(["success"]);
      showSuccess();
    },
    putRemittanceTypeFailure(state) {
      state.putRemittanceType = createDynamicState(["error"]);
    },
    destroyRemittanceType(state, _action: TUtilitiesRemittanceTypeDelete) {
      state.destroyRemittanceType = createDynamicState(["loading"]);
    },
    destroyRemittanceTypeSuccess(state, action) {
      state.remittanceTypes?.splice(action.payload, 1);
      state.destroyRemittanceType = createDynamicState(["success"]);
      showSuccess();
    },
    destroyRemittanceTypeFailure(state) {
      state.destroyRemittanceType = createDynamicState(["error"]);
    },
  },
});
export const {
  setSelectedRemittanceType,
  clearSelectedRemittanceType,
  getRemittanceType,
  getRemittanceTypeSuccess,
  getRemittanceTypeFailure,
  postRemittanceType,
  postRemittanceTypeSuccess,
  postRemittanceTypeFailure,
  putRemittanceType,
  putRemittanceTypeSuccess,
  putRemittanceTypeFailure,
  destroyRemittanceType,
  destroyRemittanceTypeSuccess,
  destroyRemittanceTypeFailure,
} = utilitiesRemittanceTypeManagementSlice.actions;

export const useRemittanceTypeManagementActions = () => {
  return bindActionCreators(
    {
      setSelectedRemittanceType,
      clearSelectedRemittanceType,
      getRemittanceType,
      postRemittanceType,
      putRemittanceType,
      destroyRemittanceType,
    },
    useDispatch()
  );
};
export default utilitiesRemittanceTypeManagementSlice.reducer;
