// Import dependencies
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { bindActionCreators } from "@reduxjs/toolkit";
import { useDispatch } from "react-redux";
import dayjs from "dayjs";
import {
  TCooperativesManagementState,
  TCooperativesActionPayloadPostPut,
  TICooperativesActionPayloadPostSuccess,
  TICooperativesActionPayloadSelectedPutSuccess,
  TCooperativesDelete,
  TGetCooperativeByIdPayload,
} from "@state/types/cooperatives";
import { ISharesCoopInformation as ISharesCoopInformationType } from "@interface/shares.interface";
import { createDynamicState } from "@helpers/array";
import { IDefaultParams } from "@interface/common.interface";

const initialState: TCooperativesManagementState = {
  cooperatives: [],
  selectedCooperatives: {
    index: 0,
    data: {
      coopCode: "",
      coopName: "",
      coopAcronym: "",
      streetAddress: "",
      barangay: "",
      city: "",
      province: "",
      zipCode: "",
      emailAddress: "",
      websiteAddress: "",
      telephoneNumber: "",
      cdaRegistrationNumber: "",
      cdaRegistrationDate: "",
      cdaCocNumber: "",
      cdaCocDate: "",
      taxIdNumber: "",
      taxIdDate: "",
      taxCteNumber: "",
      taxCteExpiryDate: "",
      coopBranchesCount: 0,
      coopMembersCount: 0,
      coopMembersMaleCount: 0,
      coopMembersFemaleCount: 0,
      coopTotalAssets: 0,
      status: "PENDING",
      mainBranchId: 0,
      cooperativeTypeId: 0,
      // cooperativeCategory: [] || "",
      cooperativeAffiliations: [],
      cooperativeOfficers: [],
      cooperativeCategoryId: 0,
      category: "",
      type: "",
    },
  },
  latestId: 0,
  getCooperatives: createDynamicState(),
  postCooperatives: createDynamicState(),
  putCooperatives: createDynamicState(),
  destroyCooperatives: createDynamicState(),
  getCooperativeById: createDynamicState(),
};

const utilitiesCooperativesManagementSlice = createSlice({
  name: "cooperatives",
  initialState,
  reducers: {
    setSelectedCooperatives(state, action: TICooperativesActionPayloadSelectedPutSuccess) {
      state.selectedCooperatives = action?.payload;
    },
    clearSelectedCooperatives(state) {
      state.selectedCooperatives = initialState.selectedCooperatives;
    },

    getCooperativeById(state, _action: TGetCooperativeByIdPayload) {
      state.getCooperativeById = createDynamicState(["loading"]);
    },
    getCooperativeByIdSuccess(state, action: TICooperativesActionPayloadPostSuccess) {
      const cooperativeData = action.payload;
      let globalIndex = 1;

      // Format officers with index and date
      const updatedOfficers =
        cooperativeData.cooperativeOfficers?.map(({ id, ...officer }) => ({
          ...officer,
          index: globalIndex++,
          effectivityDate: dayjs(officer.effectivityDate).format("YYYY-MM-DD"),
        })) || [];

      // Format affiliations with index and date
      const updatedAffiliations =
        cooperativeData.cooperativeAffiliations?.map((affiliation) => ({
          ...affiliation,
          index: globalIndex++,
          effectivityDate: dayjs(affiliation.effectivityDate).format("YYYY-MM-DD"),
        })) || [];

      const cooperativeCategoryId = cooperativeData.cooperativeCategory?.id ? Number(cooperativeData.cooperativeCategory.id) : null;

      const formattedData = {
        ...cooperativeData,
        cooperativeCategoryId,
        cooperativeOfficers: updatedOfficers,
        cooperativeAffiliations: updatedAffiliations,
      };

      state.getCooperativeById = createDynamicState(["success"], formattedData);
    },
    getCooperativeByIdFailure(state) {
      state.getCooperativeById = createDynamicState(["error"]);
    },
    getCooperatives(state, _action: PayloadAction<{ payload: IDefaultParams }>) {
      state.getCooperatives = createDynamicState(["loading"]);
    },
    getCooperativesSuccess(state, action) {
      // Extract the cooperatives data from the action payload
      const cooperativesData = action.payload.data.reverse();

      // Find the greatest id from the cooperatives
      const latestId = cooperativesData.length > 0 ? Math.max(...cooperativesData.map((coop: ISharesCoopInformationType) => coop.id || 0)) : 0;

      state.cooperatives = cooperativesData.map((coop: ISharesCoopInformationType) => {
        let globalIndex = 1; // Start a global index for both affiliations and officers

        // Add index to each cooperative officer
        const updatedOfficers = coop.cooperativeOfficers.map(({ id, ...officer }) => ({
          ...officer,
          index: globalIndex++, // Assign unique index across both lists
          effectivityDate: dayjs(officer.effectivityDate).format("YYYY-MM-DD"),
        }));

        // Format the effectivityDate in cooperativeAffiliations
        const updatedAffiliations = coop.cooperativeAffiliations.map((affiliation) => ({
          ...affiliation,
          index: globalIndex++, // Continue incrementing the global index
          effectivityDate: dayjs(affiliation.effectivityDate).format("YYYY-MM-DD"),
        }));
        // const cooperativeCategoryId = Number(coop.cooperativeCategory?.id);
        const cooperativeCategoryId = coop.cooperativeCategory?.id ? Number(coop.cooperativeCategory.id) : null; // Or a default value like 0
        return {
          ...coop,

          cooperativeCategoryId,
          cooperativeOfficers: updatedOfficers,
          cooperativeAffiliations: updatedAffiliations,
        };
      });

      // Store the latest id in the Redux state
      state.latestId = latestId;
      state.getCooperatives = createDynamicState(["success"], action.payload);
    },
    getCooperativesFailure(state) {
      state.getCooperatives = createDynamicState(["error"]);
    },

    postCooperatives(state, _action: TCooperativesActionPayloadPostPut) {
      state.postCooperatives = createDynamicState(["loading"]);
    },
    postCooperativesSuccess(state, action: TICooperativesActionPayloadPostSuccess) {
      state.cooperatives?.unshift(action.payload);

      state.postCooperatives = createDynamicState(["success"]);
      // showSuccess();
    },
    postCooperativesFailure(state) {
      state.postCooperatives = createDynamicState(["error"]);
    },

    putCooperatives(state, _actions: TCooperativesActionPayloadPostPut) {
      state.putCooperatives = createDynamicState(["loading"]);
    },
    putCooperativesSuccess(state, action: TICooperativesActionPayloadSelectedPutSuccess) {
      state.cooperatives = state.cooperatives.map((coop, index) => (index === state.selectedCooperatives.index ? action.payload.data : coop));

      state.putCooperatives = createDynamicState(["success"]);
      // showSuccess();
    },
    putCooperativesFailure(state) {
      state.putCooperatives = createDynamicState(["error"]);
    },
    destroyCooperatives(state, _action: TCooperativesDelete) {
      state.destroyCooperatives = createDynamicState(["loading"]);
    },
    destroyCooperativesSuccess(state, action) {
      state.cooperatives?.splice(action.payload, 1);
      state.destroyCooperatives = createDynamicState(["success"]);
      // showSuccess();
    },
    destroyCooperativesFailure(state) {
      state.destroyCooperatives = createDynamicState(["error"]);
    },
  },
});
export const {
  setSelectedCooperatives,
  clearSelectedCooperatives,
  getCooperatives,
  getCooperativesSuccess,
  getCooperativesFailure,
  postCooperatives,
  postCooperativesSuccess,
  postCooperativesFailure,
  putCooperatives,
  putCooperativesSuccess,
  putCooperativesFailure,
  destroyCooperatives,
  destroyCooperativesSuccess,
  destroyCooperativesFailure,
  getCooperativeById,
  getCooperativeByIdSuccess,
  getCooperativeByIdFailure,
} = utilitiesCooperativesManagementSlice.actions;

export const useCooperativesManagementActions = () => {
  return bindActionCreators(
    {
      setSelectedCooperatives,
      clearSelectedCooperatives,
      getCooperatives,
      postCooperatives,
      putCooperatives,
      destroyCooperatives,
      getCooperativeById,
    },
    useDispatch()
  );
};
export default utilitiesCooperativesManagementSlice.reducer;
