// Import dependencies
import { createDynamicState } from "@helpers/array";
import { showSuccess } from "@helpers/prompt";
import { bindActionCreators, createSlice } from "@reduxjs/toolkit";
// Import all the types that we would use for the operations here. 
import { 
  TUtilitiesRegionWithIDAndIndexPayload,
  TIUtilitiesRegionActionPayloadPostSuccess,
  TIUtilitiesRegionActionPayloadSelectedPutSuccess,
  TUtilitiesRegionActionPayloadPostPut,
  TUtilitiesRegionDelete,
  TUtilitiesRegionState 
} from "@state/types/utilities-region";
import { useDispatch } from "react-redux";

//For Optimization of the Get
import { IDefaultParams } from "@interface/common.interface";
import { PayloadAction } from "@reduxjs/toolkit";

//Getting that data for the table
const initialState: TUtilitiesRegionState = {
  regionTypes: [],
  selectedRegionType: {
    index: 0,
    data: {
      id: 0,
      regionName: "",
      createdAt: "",
      updatedAt: "",
      deletedAt: null,
    },
  },
  getRegion: createDynamicState(),
  getSingleRegion: createDynamicState(),
  postRegion: createDynamicState(),
  putRegion: createDynamicState(),
  destroyRegion: createDynamicState(),
};

const utilitiesRegionSlice = createSlice({
  name: "utilitiesRegion",
  initialState,
  reducers: {
    setSelectedRegion(
      state,
      action: TIUtilitiesRegionActionPayloadSelectedPutSuccess
    ) {
      state.selectedRegionType = action.payload;
    },
    clearSelectedRegion(state) {
      state.selectedRegionType = initialState.selectedRegionType;
    },
    getRegion(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.getRegion = createDynamicState(["loading"]);
    },
    getRegionSuccess(state, action) {
      state.regionTypes = action.payload.data;
      state.getRegion = createDynamicState(["success"], action.payload);
    },
    getRegionFailure(state) {
      state.getRegion = createDynamicState(["error"]);
    },
    getSingleRegion(state, _action: PayloadAction<TUtilitiesRegionWithIDAndIndexPayload>) {
      state.getSingleRegion = createDynamicState(["loading"]);
    },
    getSingleRegionSuccess(state, action) {
      state.selectedRegionType = action.payload;
      state.getSingleRegion = createDynamicState(["success"], action.payload);
    },
    getSingleRegionFailure(state) {
      state.getSingleRegion = createDynamicState(["error"]);
    },

    postRegion(state, _action: TUtilitiesRegionActionPayloadPostPut) {
      state.postRegion = createDynamicState(["loading"]);
      4;
    },
    postRegionSuccess(state, action: TIUtilitiesRegionActionPayloadPostSuccess) {
      state.regionTypes.unshift(action.payload);
      state.postRegion = createDynamicState(["success"]);
      showSuccess();
    },
    postRegionFailure(state) {
      state.postRegion = createDynamicState(["error"]);
    },

    putRegion(state, _action: TUtilitiesRegionActionPayloadPostPut) {
      state.putRegion = createDynamicState(["loading"]);
    },

    //*Delete this if this would break the region creation process.
    putRegionSuccess(state, action: TIUtilitiesRegionActionPayloadPostSuccess) {
      state.regionTypes = state.regionTypes.map((region) => (region.id === action.payload.id ? action.payload : region));
      state.putRegion = createDynamicState(["success"]);
      showSuccess();
    },
    
    putRegionFailure(state) {
      state.putRegion = createDynamicState(["error"]);
    },

    destroyRegion(state, _action: TUtilitiesRegionDelete) {
      state.destroyRegion = createDynamicState(["loading"]);
    },
    destroyRegionSuccess(state, action) {
      state.regionTypes?.splice(action.payload, 1);
      state.destroyRegion = createDynamicState(["success"]);
      showSuccess("Region deleted successfully.");
    },
    destroyRegionFailure(state) {
      state.destroyRegion = createDynamicState(["error"]);
    },
        
    
  },
});
export const { 
  setSelectedRegion,
  clearSelectedRegion,
  getRegion, 
  getRegionSuccess, 
  getRegionFailure,
  getSingleRegion,
  getSingleRegionSuccess,
  getSingleRegionFailure,
  postRegion,
  postRegionSuccess,
  postRegionFailure,
  putRegion,
  putRegionSuccess,
  putRegionFailure,
  destroyRegion,
  destroyRegionSuccess,
  destroyRegionFailure,
} = utilitiesRegionSlice.actions;

export const useUtilitiesRegionActions = () => {
  return bindActionCreators(
    {
      setSelectedRegion,
      clearSelectedRegion,
      getRegion,
      getSingleRegion,
      postRegion,
      putRegion,
      destroyRegion,
    },
    useDispatch()
  );
};

export default utilitiesRegionSlice.reducer;
