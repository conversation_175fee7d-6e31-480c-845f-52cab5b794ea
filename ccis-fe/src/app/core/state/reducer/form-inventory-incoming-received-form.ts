import { bindActionCreators, createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  TIncomingReceivedFormState,
  IIncomingReceivedFormPayload,
  IIncomingReceivedFormWithIndexPayload,
  TIIncomingReceivedFormActionPayload,
  TIncomingReceivedFormIDPayloadActionPayload,
  TIncomingReceivedFormWithIndexActionPayload,
  TUpdateFormStatusPayloadWithId,
} from "@state/types/form-inventory-incoming-received-form";
import { useDispatch } from "react-redux";
import { showSuccess } from "@helpers/prompt";
import { IDefaultParams } from "@interface/common.interface";
import { IPadAssignments } from "@interface/form-inventory.interface";
import { showProcessingToast } from "@modules/dashboard/RequestorDashboard/components/prompts/DepartmentalTicketingPrompts";
import Swal from "sweetalert2";

const initialState: TIncomingReceivedFormState = {
  incomingReceivedForms: [],
  selectedIncomingReceivedForm: {
    index: 0,
    data: {
      id: 0,
      divisionId: 0,
      formTypeId: 0,
      areaId: 0,
      receivedDate: "",
      seriesFrom: 0,
      seriesTo: 0,
      atpNumber: "",
      noPads: 0,
      padAssignments: [],
      attachments: [],
    },
  },
  getIncomingReceivedForms: {
    data: undefined,
    loading: false,
    success: false,
    error: false,
  },
  getIncomingReceivedForm: {
    loading: false,
    success: false,
    error: false,
  },
  postIncomingReceivedForm: {
    loading: false,
    success: false,
    error: false,
  },
  putIncomingReceivedForm: {
    loading: false,
    success: false,
    error: false,
  },
  destroyIncomingReceivedForm: {
    loading: false,
    success: false,
    error: false,
  },
  getPendingForms: {
    loading: false,
    success: false,
    error: false,
  },
  getCompletedPads: {
    loading: false,
    success: false,
    error: false,
  },
  selectedCompletedPads: [],
  transmittalStatus: {
    selectedRecord: undefined,
    tableData: {
      data: undefined,
      loading: false,
      success: false,
      error: false,
    },
  },
  putApprovalStatus: {
    loading: false,
    success: false,
    error: false,
  },
};

const incomingReceivedFormSlice = createSlice({
  name: "incomingReceivedFormManagement",
  initialState,
  reducers: {
    setSelectedIncomingReceivedForm(state, action: TIncomingReceivedFormWithIndexActionPayload) {
      state.selectedIncomingReceivedForm = action.payload;
    },
    clearSelectedIncomingReceivedForm(state) {
      state.selectedIncomingReceivedForm = initialState.selectedIncomingReceivedForm;
    },
    getPendingForms(state, _action: PayloadAction<IDefaultParams>) {
      state.getPendingForms = {
        loading: true,
        success: false,
        error: false,
      };
    },
    getPendingFormsSuccess(state, action) {
      state.incomingReceivedForms = [...action.payload].reverse();
      state.getPendingForms = {
        loading: false,
        success: true,
        error: false,
      };
    },
    getPendingFormsFailure(state) {
      state.getPendingForms = {
        loading: false,
        success: false,
        error: true,
      };
    },
    getIncomingReceivedForms(state, _action: PayloadAction<IDefaultParams>) {
      state.getIncomingReceivedForms = {
        data: undefined,
        loading: true,
        success: false,
        error: false,
      };
    },
    getIncomingReceivedFormsSuccess(state, action) {
      //   state.incomingReceivedForms = [...action.payload].reverse();
      state.getIncomingReceivedForms = {
        data: action.payload,
        loading: false,
        success: true,
        error: false,
      };
    },
    getIncomingReceivedFormsFailure(state) {
      state.getIncomingReceivedForms = {
        data: undefined,
        loading: false,
        success: false,
        error: true,
      };
    },

    getIncomingReceivedForm(state, _action: TIncomingReceivedFormIDPayloadActionPayload) {
      state.getIncomingReceivedForm = {
        loading: true,
        success: false,
        error: false,
      };
    },
    getIncomingReceivedFormSuccess(state, action) {
      state.selectedIncomingReceivedForm.data = action.payload;
      state.getIncomingReceivedForm = {
        loading: false,
        success: true,
        error: false,
      };
    },
    getIncomingReceivedFormFailure(state) {
      state.getIncomingReceivedForm = {
        loading: false,
        success: false,
        error: true,
      };
    },
    postIncomingReceivedForm(state, _action: PayloadAction<IIncomingReceivedFormPayload>) {
      state.postIncomingReceivedForm = {
        loading: true,
        success: false,
        error: false,
      };
      showProcessingToast("Processing...");
    },
    postIncomingReceivedFormSuccess(state, action: TIIncomingReceivedFormActionPayload) {
      state.incomingReceivedForms.unshift(action.payload);
      state.postIncomingReceivedForm = {
        loading: false,
        success: true,
        error: false,
      };
      showSuccess();
    },
    postIncomingReceivedFormFailure(state) {
      state.postIncomingReceivedForm = {
        loading: false,
        success: false,
        error: true,
      };
    },
    putIncomingReceivedForm(state, _action: PayloadAction<IIncomingReceivedFormPayload>) {
      state.putIncomingReceivedForm = {
        loading: true,
        success: false,
        error: false,
      };
    },
    putIncomingReceivedFormSuccess(state, action: TIIncomingReceivedFormActionPayload) {
      state.incomingReceivedForms = state.incomingReceivedForms.map((incomingReceivedForm) => (incomingReceivedForm.id === action.payload.id ? action.payload : incomingReceivedForm));
      state.putIncomingReceivedForm = {
        loading: false,
        success: true,
        error: false,
      };
      showSuccess();
    },
    putIncomingReceivedFormFailure(state) {
      state.putIncomingReceivedForm = {
        loading: false,
        success: false,
        error: true,
      };
      Swal.close();
    },
    destroyIncomingReceivedForm(state, _action: PayloadAction<IIncomingReceivedFormWithIndexPayload>) {
      state.destroyIncomingReceivedForm = {
        loading: true,
        success: false,
        error: false,
      };
    },
    destroyIncomingReceivedFormSuccess(state, action) {
      state.incomingReceivedForms?.splice(action.payload, 1);
      state.destroyIncomingReceivedForm = {
        loading: false,
        success: true,
        error: false,
      };
      showSuccess("IncomingReceivedForm deleted successfully.");
    },
    destroyIncomingReceivedFormFailure(state) {
      state.destroyIncomingReceivedForm = {
        loading: false,
        success: false,
        error: true,
      };
      Swal.close();
    },
    destroyIncomingReceivedFormReset(state) {
      state.destroyIncomingReceivedForm = {
        loading: false,
        success: false,
        error: false,
      };
    },
    removeAttachment(state, action: PayloadAction<number>) {
      state.incomingReceivedFormAttachment?.splice(action.payload, 1);
    },
    getCompletedPads(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.getCompletedPads = {
        loading: true,
        success: false,
        error: false,
        data: undefined,
      };
    },
    getCompletedPadsSuccess(state, action) {
      state.getCompletedPads = {
        loading: false,
        success: true,
        error: false,
        data: action.payload,
      };
    },
    getCompletedPadsFailure(state) {
      state.getCompletedPads = {
        loading: false,
        success: false,
        error: true,
        data: undefined,
      };
    },
    getTransmittalStatus(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.transmittalStatus.tableData = {
        loading: true,
        success: false,
        error: false,
        data: undefined,
      };
    },
    getTransmittalStatusSuccess(state, action) {
      state.transmittalStatus.tableData = {
        loading: false,
        success: true,
        error: false,
        data: action.payload,
      };
    },
    getTransmittalStatusFailure(state) {
      state.transmittalStatus.tableData = {
        loading: false,
        success: false,
        error: true,
        data: undefined,
      };
    },
    putApprovalStatus(state, _action: PayloadAction<TUpdateFormStatusPayloadWithId>) {
      state.putApprovalStatus = {
        loading: true,
        success: false,
        error: false,
        data: undefined,
      };
      showProcessingToast("Processing your request...");
    },
    putApprovalStatusSuccess(state, action) {
      state.putApprovalStatus = {
        loading: false,
        success: true,
        error: false,
        data: action.payload,
      };
      Swal.close();
    },
    putApprovalStatusFailure(state) {
      state.putApprovalStatus = {
        loading: false,
        success: false,
        error: true,
        data: undefined,
      };
      Swal.close();
    },
    bulkAddCompletedPads(state, action: PayloadAction<IPadAssignments[]>) {
      state.selectedCompletedPads = action.payload;
    },
    addCompletedPads(state, action: PayloadAction<IPadAssignments>) {
      state.selectedCompletedPads.push(action.payload);
    },
    removeCompletedPads(state, action: PayloadAction<IPadAssignments>) {
      state.selectedCompletedPads = state.selectedCompletedPads.filter((pad) => pad.id !== action.payload.id);
    },
    resetSelectedPads(state) {
      state.selectedCompletedPads = [];
    },
  },
});

export const {
  setSelectedIncomingReceivedForm,
  clearSelectedIncomingReceivedForm,
  getIncomingReceivedForms,
  getIncomingReceivedFormsSuccess,
  getIncomingReceivedFormsFailure,
  getIncomingReceivedForm,
  getIncomingReceivedFormSuccess,
  getIncomingReceivedFormFailure,
  postIncomingReceivedForm,
  postIncomingReceivedFormSuccess,
  postIncomingReceivedFormFailure,
  putIncomingReceivedForm,
  putIncomingReceivedFormSuccess,
  putIncomingReceivedFormFailure,
  destroyIncomingReceivedForm,
  destroyIncomingReceivedFormSuccess,
  destroyIncomingReceivedFormFailure,
  destroyIncomingReceivedFormReset,
  removeAttachment,
  getPendingForms,
  getPendingFormsSuccess,
  getPendingFormsFailure,
  getCompletedPads,
  getCompletedPadsSuccess,
  getCompletedPadsFailure,
  getTransmittalStatus,
  getTransmittalStatusSuccess,
  getTransmittalStatusFailure,
  putApprovalStatus,
  putApprovalStatusSuccess,
  putApprovalStatusFailure,
  bulkAddCompletedPads,
  addCompletedPads,
  removeCompletedPads,
  resetSelectedPads,
} = incomingReceivedFormSlice.actions;

export const useIncomingReceivedFormActions = () => {
  return bindActionCreators(
    {
      setSelectedIncomingReceivedForm,
      clearSelectedIncomingReceivedForm,
      getIncomingReceivedForms,
      getIncomingReceivedForm,
      postIncomingReceivedForm,
      putIncomingReceivedForm,
      destroyIncomingReceivedForm,
      destroyIncomingReceivedFormReset,
      removeAttachment,
      getPendingForms,
      getCompletedPads,
      getCompletedPadsSuccess,
      getCompletedPadsFailure,
      getTransmittalStatus,
      getTransmittalStatusSuccess,
      getTransmittalStatusFailure,
      putApprovalStatus,
      putApprovalStatusSuccess,
      putApprovalStatusFailure,
      bulkAddCompletedPads,
      addCompletedPads,
      removeCompletedPads,
      resetSelectedPads,
    },
    useDispatch()
  );
};

export default incomingReceivedFormSlice.reducer;
