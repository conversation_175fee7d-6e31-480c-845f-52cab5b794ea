import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { bindActionCreators } from "@reduxjs/toolkit";
import { useDispatch } from "react-redux";
import { IIssuanceInterface } from "@interface/master-policy.interface";
import { TIssuancePayload, TIssuanceResponse } from "@state/types/master-policy";
import { IDefaultParams } from "@interface/common.interface";
import { showSuccess } from "@helpers/prompt";

//Temporary state package, will delete once back-end is done. This is for the Issuance page.
interface IIssuanceState {
  issuances: IIssuanceInterface[];
  selectedIssuance: IIssuanceInterface | null;
  loading: boolean;
  error: string | null;
  createLoading: boolean;
  updateLoading: boolean;
  deleteLoading: boolean;
  getIssuance?: TIssuanceResponse;
  getIssuances?: TIssuanceResponse;
  postIssuance?: TIssuanceResponse;
  putIssuance?: TIssuanceResponse;
  destroyIssuance?: TIssuanceResponse;
}

const initialState: IIssuanceState = {
  issuances: [],
  selectedIssuance: null,
  loading: false,
  error: null,
  createLoading: false,
  updateLoading: false,
  deleteLoading: false,
  getIssuance: {
    loading: false,
    success: false,
    error: false,
  },
  getIssuances: {
    loading: false,
    success: false,
    error: false,
  },
  postIssuance: {
    loading: false,
    success: false,
    error: false,
  },
  putIssuance: {
    loading: false,
    success: false,
    error: false,
  },
  destroyIssuance: {
    loading: false,
    success: false,
    error: false,
  },
};

const issuanceSlice = createSlice({
  name: "issuance",
  initialState,
  reducers: {
    // GET ALL
    getIssuancesRequest: (state, _action: PayloadAction<IDefaultParams>) => {
      state.loading = true;
      state.error = null;
    },
    getIssuancesSuccess: (state, action: PayloadAction<IIssuanceInterface[]>) => {
      state.loading = false;
      state.issuances = action.payload;
    },
    getIssuancesFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    // GET BY ID
    getIssuanceRequest: (state, _action: PayloadAction<number>) => {
      state.loading = true;
      state.error = null;
    },
    getIssuanceSuccess: (state, action: PayloadAction<IIssuanceInterface>) => {
      state.loading = false;
      state.selectedIssuance = action.payload;
    },
    getIssuanceFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    // CREATE
    postIssuanceRequest: (state, _action: PayloadAction<TIssuancePayload>) => {
      state.createLoading = true;
      state.error = null;
    },
    postIssuanceSuccess: (state, action: PayloadAction<IIssuanceInterface>) => {
      state.createLoading = false;
      state.issuances.unshift(action.payload);
      showSuccess("Product created successfully!");
    },
    postIssuanceFailure: (state, action: PayloadAction<string>) => {
      state.createLoading = false;
      state.error = action.payload;
    },

    // UPDATE
    putIssuanceRequest: (state, _action: PayloadAction<TIssuanceResponse>) => {
      state.updateLoading = true;
      state.error = null;
    },
    putIssuanceSuccess: (state, action: PayloadAction<IIssuanceInterface>) => {
      state.updateLoading = false;
      const index = state.issuances.findIndex(p => p.id === action.payload.id);
      if (index !== -1) {
        state.issuances[index] = action.payload;
      }
      if (state.selectedIssuance?.id === action.payload.id) {
        state.selectedIssuance = action.payload;
      }
      showSuccess("Product updated successfully!");
    },
    putIssuanceFailure: (state, action: PayloadAction<string>) => {
      state.updateLoading = false;
      state.error = action.payload;
    },

    // DELETE
    deleteIssuanceRequest: (state, _action: PayloadAction<number>) => {
      state.deleteLoading = true;
      state.error = null;
    },
    deleteIssuanceSuccess: (state, action: PayloadAction<number>) => {
      state.deleteLoading = false;
      state.issuances = state.issuances.filter(p => p.id !== action.payload);
      if (state.selectedIssuance?.id === action.payload) {
        state.selectedIssuance = null;
      }
      showSuccess("Product deleted successfully!");
    },
    deleteIssuanceFailure: (state, action: PayloadAction<string>) => {
      state.deleteLoading = false;
      state.error = action.payload;
    },

    // UTILITY ACTIONS
    setSelectedIssuance: (state, action: PayloadAction<IIssuanceInterface | null>) => {
      state.selectedIssuance = action.payload;
    },
    clearIssuancesError: (state) => {
      state.error = null;
    },
    resetIssuancesState: (state) => {
      Object.assign(state, initialState);
    },
  },
});

export const {
  getIssuancesRequest,
  getIssuancesSuccess,
  getIssuancesFailure,
  getIssuanceRequest,
  getIssuanceSuccess,
  getIssuanceFailure,
  postIssuanceRequest,
  postIssuanceSuccess,
  postIssuanceFailure,
  putIssuanceRequest,
  putIssuanceSuccess,
  putIssuanceFailure,
  deleteIssuanceRequest,
  deleteIssuanceSuccess,
  deleteIssuanceFailure,
  setSelectedIssuance,
  clearIssuancesError,
  resetIssuancesState,
} = issuanceSlice.actions;

export const useIssuanceActions = () => {
  return bindActionCreators(
    {
      getIssuancesRequest,
      getIssuanceRequest,
      postIssuanceRequest,
      putIssuanceRequest,
      deleteIssuanceRequest,
      setSelectedIssuance,
      clearIssuancesError,
      resetIssuancesState,
    },
    useDispatch()
  );
};

export default issuanceSlice.reducer;