import { bindActionCreators, createSlice } from "@reduxjs/toolkit";
import { useDispatch } from "react-redux";
import { TWeeklyAccomplishmentReport } from "@state/types/weekly-accomplishment-report";
import { createDynamicState } from "@helpers/array";
import { PayloadAction } from "@reduxjs/toolkit";
import { IDefaultParams } from "@interface/common.interface";
import { IAccomplishmentReportPayload } from "@interface/weekly-accomplishment-report.interface";
import { toast } from "react-toastify";

const initialState: TWeeklyAccomplishmentReport = {
  getAllAccomplishmentReport: createDynamicState(),
  getAccomplishmentReportById: createDynamicState(),
  postAddAccomplishmentReport: createDynamicState(),
  putUpdateAccomplishmentReport: createDynamicState(),
  getIssueTypes: createDynamicState(),
  getTaskTypes: createDynamicState(),
  getTaskGroups: createDynamicState(),
  getLoggedInUserAccomplishmentReport: createDynamicState(),
  exportAccomplishmentReport: createDynamicState(),
  destroyAccomplishmentReport: createDynamicState(),
};

// Create a slice for coverage types
const weeklyAccomplishmentReportSlice = createSlice({
  name: "weeklyAccomplishmentReport",
  initialState,
  reducers: {
    getAllAccomplishmentReport(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.getAllAccomplishmentReport = createDynamicState(["loading"]);
    },
    getAllAccomplishmentReportSuccess(state, action) {
      state.getAllAccomplishmentReport = createDynamicState(["success"], action.payload);
    },
    getAllAccomplishmentReportFailure(state) {
      state.getAllAccomplishmentReport = createDynamicState(["error"]);
    },
    getAccomplishmentReportById(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.getAccomplishmentReportById = createDynamicState(["loading"]);
    },
    getAccomplishmentReportByIdSuccess(state, action) {
      state.getAccomplishmentReportById = createDynamicState(["success"], action.payload);
    },
    getAccomplishmentReportByIdFailure(state) {
      state.getAccomplishmentReportById = createDynamicState(["error"]);
    },
    clearPostAddAccomplishmentReport(state) {
      state.postAddAccomplishmentReport = createDynamicState();
    },
    postAddAccomplishmentReport(state, _action: PayloadAction<IAccomplishmentReportPayload>) {
      state.postAddAccomplishmentReport = createDynamicState(["loading"]);
    },
    postAddAccomplishmentReportSuccess(state, action) {
      state.postAddAccomplishmentReport = createDynamicState(["success"], action.payload);
      toast.success("Accomplishment Report created successfully");
    },
    postAddAccomplishmentReportFailure(state) {
      state.postAddAccomplishmentReport = createDynamicState(["error"]);
    },
    clearPutUpdateAccomplishmentReport(state) {
      state.putUpdateAccomplishmentReport = createDynamicState();
    },
    putUpdateAccomplishmentReport(state, _action: PayloadAction<{ accomplishmentId: number; payload: IAccomplishmentReportPayload }>) {
      state.putUpdateAccomplishmentReport = createDynamicState(["loading"]);
    },
    putUpdateAccomplishmentReportSuccess(state, action) {
      state.putUpdateAccomplishmentReport = createDynamicState(["success"], action.payload);
    },
    putUpdateAccomplishmentReportFailure(state) {
      state.putUpdateAccomplishmentReport = createDynamicState(["error"]);
    },

    getIssueTypes(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.getIssueTypes = createDynamicState(["loading"]);
    },
    getIssueTypesSuccess(state, action) {
      state.getIssueTypes = createDynamicState(["success"], action.payload);
    },
    getIssueTypesFailure(state) {
      state.getIssueTypes = createDynamicState(["error"]);
    },
    getTaskTypes(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.getTaskTypes = createDynamicState(["loading"]);
    },
    getTaskTypesSuccess(state, action) {
      state.getTaskTypes = createDynamicState(["success"], action.payload);
    },
    getTaskTypesFailure(state) {
      state.getTaskTypes = createDynamicState(["error"]);
    },
    getTaskGroups(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.getTaskGroups = createDynamicState(["loading"]);
    },
    getTaskGroupsSuccess(state, action) {
      state.getTaskGroups = createDynamicState(["success"], action.payload);
    },
    getTaskGroupsFailure(state) {
      state.getTaskGroups = createDynamicState(["error"]);
    },
    getLoggedInUserAccomplishmentReport(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.getLoggedInUserAccomplishmentReport = createDynamicState(["loading"]);
    },
    getLoggedInUserAccomplishmentReportSuccess(state, action) {
      state.getLoggedInUserAccomplishmentReport = createDynamicState(["success"], action.payload);
    },
    getLoggedInUserAccomplishmentReportFailure(state) {
      state.getLoggedInUserAccomplishmentReport = createDynamicState(["error"]);
    },

    exportAccomplishmentReport(state, _action: PayloadAction<{ accomplishmentReportId: number }>) {
      state.exportAccomplishmentReport = createDynamicState(["loading"]);
    },
    exportAccomplishmentReportSuccess(state, action) {
      state.exportAccomplishmentReport = createDynamicState(["success"], action.payload);
    },
    exportAccomplishmentReportFailure(state) {
      state.exportAccomplishmentReport = createDynamicState(["error"]);
    },
    destroyAccomplishmentReport(state, _action: PayloadAction<{ accomplishmentReportId: number }>) {
      state.destroyAccomplishmentReport = createDynamicState(["loading"]);
    },
    destroyAccomplishmentReportSuccess(state, action) {
      state.destroyAccomplishmentReport = createDynamicState(["success"], action.payload);
      toast.success("Accomplishment Report deleted successfully");
    },
    destroyAccomplishmentReportFailure(state) {
      state.destroyAccomplishmentReport = createDynamicState(["error"]);
    },
    clearDeleteAccomplishmentReport(state) {
      state.destroyAccomplishmentReport = createDynamicState();
    },
  },
});

// Export actions
export const {
  getAllAccomplishmentReport,
  getAllAccomplishmentReportSuccess,
  getAllAccomplishmentReportFailure,
  getAccomplishmentReportById,
  getAccomplishmentReportByIdSuccess,
  getAccomplishmentReportByIdFailure,
  postAddAccomplishmentReport,
  postAddAccomplishmentReportSuccess,
  postAddAccomplishmentReportFailure,
  putUpdateAccomplishmentReport,
  putUpdateAccomplishmentReportSuccess,
  putUpdateAccomplishmentReportFailure,
  destroyAccomplishmentReport,
  destroyAccomplishmentReportSuccess,
  destroyAccomplishmentReportFailure,
  getIssueTypes,
  getIssueTypesSuccess,
  getIssueTypesFailure,
  getTaskTypes,
  getTaskTypesSuccess,
  getTaskTypesFailure,
  getTaskGroups,
  getTaskGroupsSuccess,
  getTaskGroupsFailure,
  getLoggedInUserAccomplishmentReport,
  getLoggedInUserAccomplishmentReportSuccess,
  getLoggedInUserAccomplishmentReportFailure,
  exportAccomplishmentReport,
  exportAccomplishmentReportSuccess,
  exportAccomplishmentReportFailure,
  clearPostAddAccomplishmentReport,
  clearDeleteAccomplishmentReport,
  clearPutUpdateAccomplishmentReport,
} = weeklyAccomplishmentReportSlice.actions;

// Export the reducer
export const useWeeklyAccomplishmentReportActions = () => {
  const dispatch = useDispatch();
  return bindActionCreators(
    {
      getAllAccomplishmentReport,
      getAccomplishmentReportById,
      postAddAccomplishmentReport,
      clearPostAddAccomplishmentReport,
      putUpdateAccomplishmentReport,
      destroyAccomplishmentReport,
      clearDeleteAccomplishmentReport,
      clearPutUpdateAccomplishmentReport,
      getIssueTypes,
      getTaskTypes,
      getTaskGroups,
      getLoggedInUserAccomplishmentReport,
      exportAccomplishmentReport,
    },
    dispatch
  );
};

// Export reducer
export default weeklyAccomplishmentReportSlice.reducer;
