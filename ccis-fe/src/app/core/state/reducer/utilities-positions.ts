// Import dependencies
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { bindActionCreators } from "@reduxjs/toolkit";
import { useDispatch } from "react-redux";
import {
  TDeleteUtilitiesPositionsActionPayload,
  TUtilitiesPositionsActionPayload,
  TIUtilitiesPositionsActionPayload,
  TUtilitiesPositionsManagementState,
  TIUtilitiesPositionsWithIndexActionPayload,
  TUtilitiesPositionsPayloadWithIndex,
} from "@state/types/utilities-positions";
import { IDefaultParams } from "@interface/common.interface";

// Define initial state for user management slice
const initialState: TUtilitiesPositionsManagementState = {
  positions: [],
  selectedPosition: {
    index: 0,
    data: {
      positionCode: "",
      positionName: "",
      description: "",
    },
  },
  getPosition: {
    data: undefined,
    success: false,
    loading: false,
    error: false,
  },
  postPosition: {
    data: undefined,
    success: false,
    loading: false,
    error: false,
  },
  putPosition: {
    data: undefined,
    success: false,
    loading: false,
    error: false,
  },
  destroyPosition: {
    success: false,
    loading: false,
    error: false,
  },
};

// Create a Redux slice for user management with actions and reducer
const utilitiesPositionsManagementSlice = createSlice({
  name: "utilitiesPositions", // Slice name
  initialState, // Initial state defined above
  reducers: {
    setSelectedPosition(state, action: PayloadAction<TUtilitiesPositionsPayloadWithIndex>) {
      state.selectedPosition = action.payload;
    },
    clearSelectedPosition(state) {
      state.selectedPosition = initialState.selectedPosition;
    },
    // Action for initiating a GET request
    getPosition(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.getPosition = {
        data: undefined,
        loading: true,
        success: false,
        error: false,
      };
    },
    // Action for successful GET request
    getPositionSuccess(state, _action) {
      // latest to old
      // state.positions = action.payload.reverse();
      state.getPosition = {
        data: _action.payload,
        loading: false,
        success: true,
        error: false,
      };
    },
    // Action for failed GET request
    getPositionFailure(state) {
      state.getPosition = {
        data: undefined,
        loading: false,
        success: false,
        error: true,
      };
    },
    // Action for initiating a POST request
    postPosition(state, _actions: TUtilitiesPositionsActionPayload) {
      state.postPosition = {
        loading: true,
        success: false,
        error: false,
      };
    },
    // Action for successful POST request
    postPositionSuccess(state, actions: TIUtilitiesPositionsActionPayload) {
      state.positions?.unshift(actions.payload);
      state.postPosition = {
        loading: false,
        success: true,
        error: false,
      };
    },
    // Action for failed POST request
    postPositionFailure(state) {
      state.postPosition = {
        loading: false,
        success: false,
        error: true,
      };
    },
    // Action for initiating a PUT request
    putPosition(state, _actions: TUtilitiesPositionsActionPayload) {
      state.putPosition = {
        loading: true,
        success: false,
        error: false,
      };
    },
    // Action for successful PUT request
    putPositionSuccess(state, actions: TIUtilitiesPositionsWithIndexActionPayload) {
      // indirect mutation
      const prevState = state.positions;
      // update the value at specific index
      prevState[state.selectedPosition.index] = actions.payload.data;

      // update state
      state.positions = prevState;

      state.putPosition = {
        loading: false,
        success: true,
        error: false,
      };
    },
    // Action for failed PUT request
    putPositionFailure(state) {
      state.putPosition = {
        loading: false,
        success: false,
        error: true,
      };
    },
    // Action for initiating a DELETE request
    destroyPosition(state, _actions: TDeleteUtilitiesPositionsActionPayload) {
      state.destroyPosition = {
        loading: true,
        success: false,
        error: false,
      };
    },
    // Action for successful DELETE request
    destroyPositionSuccess(state, actions) {
      state.positions?.splice(actions.payload, 1); // remove the target element of users state
      state.destroyPosition = {
        loading: false,
        success: true,
        error: false,
      };
    },
    // Action for failed DELETE request
    destroyPositionFailure(state) {
      state.destroyPosition = {
        loading: false,
        success: false,
        error: true,
      };
    },
  },
});

// Export individual action creators from the slice
export const {
  setSelectedPosition,
  clearSelectedPosition,
  getPosition,
  getPositionSuccess,
  getPositionFailure,
  postPosition,
  postPositionSuccess,
  postPositionFailure,
  putPosition,
  putPositionSuccess,
  putPositionFailure,
  destroyPosition,
  destroyPositionSuccess,
  destroyPositionFailure,
} = utilitiesPositionsManagementSlice.actions;

// Custom hook to bind action creators to dispatch
export const usePositionsManagementActions = () => {
  return bindActionCreators(
    {
      setSelectedPosition,
      clearSelectedPosition,
      getPosition,
      postPosition,
      putPosition,
      destroyPosition,
    },
    useDispatch()
  );
};

// Export the reducer function from the slice
export default utilitiesPositionsManagementSlice.reducer;
