import { combineReducers } from "@reduxjs/toolkit";
import { persistReducer } from "redux-persist";
import storage from "redux-persist/lib/storage";
import auth from "./auth";
import commissionStructure from "./commission-structure";
import guidelines from "./guidelines";
import permissions from "./permissions";
import products from "./products";
import profile from "./profile";
import roles from "./roles";
import stepper from "./stepper";
import users from "./users";
import usersManagement from "./users-management";
import usersProductApproval from "./users-product-approval";
import utilitiesAffiliation from "./utilities-affiliation";
import utilitiesCommissionsAgeTypes from "./utilities-commission-age-types";
import utilitiesCooperativeCategory from "./utilities-cooperative-category";
import utilitiesCooperativeRequirement from "./utilities-cooperative-requirement";
import utilitiesCooperativeRequirementTemplate from "./utilities-cooperative-requirement-template";
import utilitiesCooperativeType from "./utilities-cooperative-types";
import utilitiesDepartments from "./utilities-departments";
import utilitiesPositions from "./utilities-positions";
import utilitiesProductBenefits from "./utilities-product-benefits";
import utilitiesProductCategory from "./utilities-product-category";
import utilitiesProductCommission from "./utilities-product-commission";
import utilitiesProductHeaders from "./utilities-product-headers";
import utilitiesProductType from "./utilities-product-type";
import utilitiesSignatoryTemplate from "./utilities-signatory-template";
import utilitiesSignatoryType from "./utilities-signatory-type";
import utilitiesTargetMarket from "./utilities-target-market";
//Add the import and reference in Index.ts in Reducers
import clspAER from "./actuary-clsp-aer";
import fipAER from "./actuary-fip-aer";
import gyrtAER from "./actuary-gyrt-aer";
import clppAER from "./actuary-clpp-aer";
import utilitiesBenefitRate from "./actuary-utilities-benefit-rate";
import utilitiesMortalityRate from "./actuary-utilities-mortality-rate";
import utilitiesRiskPremiumRate from "./actuary-utilities-risk-premium";
import utilitiesDefaultNumberOfClaims from "./auctuary-utilities-defaultnumberofclaims";
import actuaryUtilitiesClppRate from "./actuary-utilities-clpp-rate";
import commissionType from "./commision-type";
import productsForCompliance from "./compliance";
import contestability from "./contestability";
import cooperatives from "./cooperatives";
import cooperativesCda from "./cooperatives-cda";
import coverageType from "./coverage-type";
import departmentalTicketing from "./departmental-ticketing";
import departmentalTicketingApplication from "./departmental-ticketing-application";
import departmentalTicketingDeviceSystem from "./departmental-ticketing-device-system";
import departmentalTicketingOperatingSystem from "./departmental-ticketing-operating-system";
import departmentalTicketingRequestType from "./departmental-ticketing-request-type";
import formInventoryIncomingReceivedForms from "./form-inventory-incoming-received-form";
import formInventoryTransmittal from "./form-inventory-transmittal";
import formInventoryUtilitiesBankAccounts from "./form-inventory-utilities-bank-accounts";
import formInventoryUtilitiesBanks from "./form-inventory-utilities-banks";
import formInventoryUtilitiesDivisions from "./form-inventory-utilities-divisions";
import formInventoryUtilitiesAreaAdmins from "./form-inventory-utilities-area-admins";
import formInventoryUtilitiesFormTypes from "./form-inventory-utilities-form-types";
import formInventoryUtilitiesLocation from "./form-inventory-utilities-location";
import formInventoryUtilitiesMarketAreas from "./form-inventory-utilities-market-areas";
import formInventoryUtilitiesPaymentMethods from "./form-inventory-utilities-payment-methods";
import formInventoryUtilitiesReleasedMethods from "./form-inventory-utilities-released-methods";
import productProposal from "./product-proposal";
import quotation from "./quotations";
import salesFipQuotation from "./quotation-sales-fip";
import sharesPayments from "./share-payments";
import shares from "./shares";
import uatAnswers from "./uat-answers";
import uatDetails from "./uat-details";
import utilitiesAdminExpense from "./utilities-admin-expense";
import utilitiesAreas from "./utilities-areas";
import utilitiesCooperativeMembershipType from "./utilities-cooperative-membership";
import cooperativesShareType from "./utilities-cooperative-sharetypes";
import utilitiesRemittanceData from "./utilities-remittance-data";
import utilitiesRemittanceTypes from "./utilities-remittance-type";
import notification from "./notification";
import managerIds from "./manager-id";
import gamPadRequest from "./gam-pad-request";
import systemSettingsId from "./system-settings-ids";
import globalSettings from "./global-settings";
import fromInventoryDashboards from "./form-inventory-dashboards";
import prIssuanceManagement from "./form-inventory-pr-issuance";
import weeklyAccomplishmentReport from "./weekly-accomplishment-report";
import utilitiesRegion from "./utilities-region";
import issuance from "./master-policy";
import salesDashboard from "./dashboard-sales";

const persistConfig = {
  key: "auth",
  storage,
};

const globalSettingsConfig = {
  key: "globalSettings",
  storage,
};

const rootReducer = combineReducers({
  auth: persistReducer(persistConfig, auth),
  users,
  usersManagement,
  utilitiesDepartments,
  utilitiesPositions,
  utilitiesSignatoryType,
  utilitiesTargetMarket,
  utilitiesSignatoryTemplate,
  utilitiesCommissionsAgeTypes,
  roles,
  profile,
  permissions,
  products,
  productProposal,
  guidelines,
  utilitiesProductType,
  utilitiesProductCategory,
  utilitiesProductCommission,
  utilitiesProductBenefits,
  utilitiesProductHeaders,
  stepper,
  commissionStructure,
  usersProductApproval,
  utilitiesAffiliation,
  utilitiesCooperativeCategory,
  utilitiesCooperativeType,
  utilitiesCooperativeRequirement,
  utilitiesCooperativeRequirementTemplate,
  utilitiesAreas,
  uatDetails,
  uatAnswers,
  utilitiesCooperativeMembershipType,
  productsForCompliance,
  contestability,
  commissionType,
  cooperatives,
  cooperativesCda,
  cooperativesShareType,
  quotation,
  shares,
  sharesPayments,
  utilitiesMortalityRate,
  utilitiesRiskPremiumRate,
  utilitiesAdminExpense,
  utilitiesBenefitRate,
  utilitiesDefaultNumberOfClaims,
  formInventoryUtilitiesDivisions,
  formInventoryUtilitiesFormTypes,
  formInventoryUtilitiesAreaAdmins,
  formInventoryUtilitiesPaymentMethods,
  formInventoryUtilitiesBanks,
  formInventoryUtilitiesMarketAreas,
  formInventoryUtilitiesBankAccounts,
  formInventoryIncomingReceivedForms,
  formInventoryUtilitiesReleasedMethods,
  gyrtAER,
  coverageType,
  clppAER,
  clspAER,
  salesFipQuotation,
  formInventoryTransmittal,
  fipAER,
  departmentalTicketing,
  departmentalTicketingRequestType,
  departmentalTicketingApplication,
  departmentalTicketingOperatingSystem,
  departmentalTicketingDeviceSystem,
  actuaryUtilitiesClppRate,
  formInventoryUtilitiesLocation,
  utilitiesRemittanceData,
  utilitiesRemittanceTypes,
  managerIds,
  notification,
  gamPadRequest,
  systemSettingsId,
  globalSettings: persistReducer(globalSettingsConfig, globalSettings),
  fromInventoryDashboards,
  prIssuanceManagement,
  weeklyAccomplishmentReport,
  utilitiesRegion,
  issuance,
  salesDashboard,
});

export default rootReducer;
export type RootState = ReturnType<typeof rootReducer>;
