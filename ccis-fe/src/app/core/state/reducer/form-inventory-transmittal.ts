import { bindActionCreators, createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  TTransmittalFormIDPayloadActionPayload,
  TTransmittalFormWithIndexActionPayload,
  TIFormTransmittalActionPayload,
  IFormTransmittalPayload,
  TFormTransmittalOutgoingPayloadPostPut,
  TIFormTransmittalOutgoingPayloadPostSuccess,
  TTransmittalFormState,
  TTransmittalFormToClifsaPrintWithIdActionPayload,
  TCreateReturnedPayloadAction,
  TCancelReceiptWithActionPayload,
  TIssueReceiptWithActionPayload,
  TUpdateTransmitalStatusWithActionPayload,
} from "@state/types/form-inventory-transmittal";
import { useDispatch } from "react-redux";
import { showSuccess } from "@helpers/prompt";
import { IFormTransmittal } from "@interface/form-inventory.interface";
import { createDynamicState } from "@helpers/array";
import { TIDefaultParamsActionPayload } from "@state/types/common-type";
import { IDefaultParams } from "@interface/common.interface";
import { showProcessingToast } from "@modules/dashboard/RequestorDashboard/components/prompts/DepartmentalTicketingPrompts";
import Swal from "sweetalert2";

const initialState: TTransmittalFormState = {
  transmittalForms: [],
  selectedTransmittalForm: {
    index: 0,
    data: {} as IFormTransmittal,
  },
  getTransmittalForms: {
    data: undefined,
    loading: false,
    success: false,
    error: false,
  },
  getTransmittalForm: {
    loading: false,
    success: false,
    error: false,
  },
  postTransmittalForm: {
    loading: false,
    success: false,
    error: false,
  },
  getCurrentUserFormTransmittalTrail: {
    data: undefined,
    loading: false,
    success: false,
    error: false,
  },
  getLatestTransmittalForm: {
    loading: false,
    success: false,
    error: false,
  },
  postFormTransmittalTrail: createDynamicState(),
  getTransmittalTrailActivityLog: createDynamicState(),
  getFormActivityLogs: createDynamicState(),
  getTransmittalFormTrail: createDynamicState(),
  getTransmittalFormToClifsaPrint: {
    loading: false,
    success: false,
    error: false,
    pdfUrl: null,
  },
  postReturnedPads: {
    loading: false,
    success: false,
    error: false,
  },
  getReturnedPads: createDynamicState(),
  getReturnedTransmittalTrail: createDynamicState(),
  getPadAssignments: {
    data: undefined,
    loading: false,
    success: false,
    error: false,
  },
  userPadAssignment: {
    selectedRecord: undefined,
    tableData: {
      data: undefined,
      loading: false,
      success: false,
      error: false,
    },
  },
  userPadAssignmentById: {
    data: undefined,
    loading: false,
    success: false,
    error: false,
  },
  userPadSeriesDetails: {
    data: undefined,
    loading: false,
    success: false,
    error: false,
  },
  putCancelReceipt: {
    loading: false,
    success: false,
    error: false,
  },
  putIssueReceipt: {
    loading: false,
    success: false,
    error: false,
  },
  putTransmittalTrail: {
    loading: false,
    success: false,
    error: false,
  },
  putFormTransmittalTrail: createDynamicState(),
};

const transmittalFormSlice = createSlice({
  name: "transmittalFormManagement",
  initialState,
  reducers: {
    setSelectedTransmittalForm(state, action: TTransmittalFormWithIndexActionPayload) {
      state.selectedTransmittalForm = action.payload;
    },
    clearSelectedTransmittalForm(state) {
      state.selectedTransmittalForm = initialState.selectedTransmittalForm;
      state.postFormTransmittalTrail = createDynamicState();
      state.getTransmittalTrailActivityLog = createDynamicState();
      state.getTransmittalFormTrail = createDynamicState();
    },
    clearTransmittalFormToClifsaPrint(state) {
      state.getTransmittalFormToClifsaPrint = {
        loading: false,
        success: false,
        error: false,
        pdfUrl: null,
      };
    },
    getTransmittalForms(state, _action: TIDefaultParamsActionPayload) {
      state.getTransmittalForms = {
        data: undefined,
        loading: true,
        success: false,
        error: false,
      };
    },
    getTransmittalFormsSuccess(state, action) {
      state.getTransmittalForms = {
        data: action.payload,
        loading: false,
        success: true,
        error: false,
      };
    },
    getTransmittalFormsFailure(state) {
      state.getTransmittalForms = {
        data: undefined,
        loading: false,
        success: false,
        error: true,
      };
    },
    getTransmittalForm(state, _action: TTransmittalFormIDPayloadActionPayload) {
      state.getTransmittalForm = {
        loading: true,
        success: false,
        error: false,
      };
    },
    getTransmittalFormSuccess(state, action) {
      state.selectedTransmittalForm = action.payload;
      state.getTransmittalForm = {
        data: action.payload,
        loading: false,
        success: true,
        error: false,
      };
    },
    getTransmittalFormFailure(state) {
      state.getTransmittalForm = {
        loading: false,
        success: false,
        error: true,
      };
    },
    getTransmittalFormTrail(state, _action: TIDefaultParamsActionPayload) {
      state.getTransmittalFormTrail = createDynamicState(["loading"]);
    },
    getTransmittalFormTrailSuccess(state, action) {
      state.getTransmittalFormTrail = createDynamicState(["success"], action.payload);
    },
    getTransmittalFormTrailFailure(state) {
      state.getTransmittalFormTrail = createDynamicState(["error"]);
    },
    getTransmittalFormsTrail(state, _action: TIDefaultParamsActionPayload) {
      state.getTransmittalForms = {
        data: undefined,
        loading: true,
        success: false,
        error: false,
      };
    },
    getTransmittalFormsTrailSuccess(state, action) {
      state.getTransmittalForms = {
        data: action.payload,
        loading: false,
        success: true,
        error: false,
      };
    },
    getTransmittalFormsTrailFailure(state) {
      state.getTransmittalForms = {
        data: undefined,
        loading: false,
        success: false,
        error: true,
      };
    },
    getCurrentUserFormTransmittalTrail(state, _action: TIDefaultParamsActionPayload) {
      state.getCurrentUserFormTransmittalTrail = {
        data: undefined,
        loading: true,
        success: false,
        error: false,
      };
    },
    getCurrentUserFormTransmittalTrailSuccess(state, action) {
      state.getCurrentUserFormTransmittalTrail = {
        data: action.payload,
        loading: false,
        success: true,
        error: false,
      };
    },
    getCurrentUserFormTransmittalTrailFailure(state) {
      state.getCurrentUserFormTransmittalTrail = {
        data: undefined,
        loading: false,
        success: false,
        error: true,
      };
    },
    getLatestTransmittalForm(state, _action: TIDefaultParamsActionPayload) {
      state.getLatestTransmittalForm = {
        loading: true,
        success: false,
        error: false,
      };
    },
    getLatestTransmittalFormSuccess(state, action) {
      state.transmittalForms = action.payload;
      state.getLatestTransmittalForm = {
        loading: false,
        success: true,
        error: false,
      };
    },
    getLatestTransmittalFormFailure(state) {
      state.getLatestTransmittalForm = {
        loading: false,
        success: false,
        error: true,
      };
    },
    postTransmittalForm(state, _action: PayloadAction<IFormTransmittalPayload>) {
      state.postTransmittalForm = {
        loading: true,
        success: false,
        error: false,
      };
      showProcessingToast("Processing...");
    },
    postTransmittalFormSuccess(state, action: TIFormTransmittalActionPayload) {
      state.transmittalForms.unshift(action.payload);
      state.postTransmittalForm = {
        loading: false,
        success: true,
        error: false,
      };
      showSuccess();
    },
    postTransmittalFormFailure(state) {
      state.postTransmittalForm = {
        loading: false,
        success: false,
        error: true,
      };
      Swal.close();
    },
    postFormTransmittalTrail(state, _action: TFormTransmittalOutgoingPayloadPostPut) {
      state.postFormTransmittalTrail = createDynamicState(["loading"]);
      showProcessingToast("Processing...");
    },
    postFormTransmittalTrailSuccess(state, action: TIFormTransmittalOutgoingPayloadPostSuccess) {
      state.postFormTransmittalTrail = createDynamicState(["success"], action.payload);
      Swal.close();
    },
    postFormTransmittalTrailFailure(state) {
      state.postFormTransmittalTrail = createDynamicState(["error"]);
      Swal.close();
    },
    getTransmittalTrailActivityLog(state, _action: TIDefaultParamsActionPayload) {
      state.getTransmittalTrailActivityLog = createDynamicState(["loading"]);
    },
    getTransmittalTrailActivityLogSuccess(state, action) {
      state.getTransmittalTrailActivityLog = createDynamicState(["success"], action.payload);
    },
    getTransmittalTrailActivityLogFailure(state) {
      state.getTransmittalTrailActivityLog = createDynamicState(["error"]);
    },


    getFormActivityLogs(state, _action: TIDefaultParamsActionPayload) {
      state.getFormActivityLogs = createDynamicState(["loading"]);
    },
    getFormActivityLogsSuccess(state, action) {
      state.getFormActivityLogs = createDynamicState(["success"], action.payload);
    },
    getFormActivityLogsFailure(state) {
      state.getFormActivityLogs = createDynamicState(["error"]);
    },
    getTransmittalFormToClifsaPrint(state, _action: TTransmittalFormToClifsaPrintWithIdActionPayload) {
      state.getTransmittalFormToClifsaPrint = {
        loading: true,
        success: false,
        error: false,
        pdfUrl: null,
      };
    },
    getTransmittalFormToClifsaPrintSuccess(state, action) {
      state.getTransmittalFormToClifsaPrint = {
        loading: false,
        success: true,
        error: false,
        pdfUrl: action.payload,
      };
    },
    getTransmittalFormToClifsaPrintFailure(state) {
      state.getTransmittalFormToClifsaPrint = {
        loading: false,
        success: false,
        error: true,
        pdfUrl: null,
      };
    },
    getCurrentUserOldestFormTransmittalTrail(state, _action: TIDefaultParamsActionPayload) {
      state.getCurrentUserFormTransmittalTrail = {
        data: undefined,
        loading: true,
        success: false,
        error: false,
      };
    },
    getCurrentUserOldestFormTransmittalTrailSuccess(state, action) {
      state.getCurrentUserFormTransmittalTrail = {
        data: action.payload,
        loading: false,
        success: true,
        error: false,
      };
    },
    getCurrentUserOldestFormTransmittalTrailFailure(state) {
      state.getCurrentUserFormTransmittalTrail = {
        data: undefined,
        loading: false,
        success: false,
        error: true,
      };
    },
    postReturnedPads(state, _action: TCreateReturnedPayloadAction) {
      state.postReturnedPads = {
        data: undefined,
        loading: true,
        success: false,
        error: false,
      };
      showProcessingToast("Processing...");
    },
    postReturnedPadsSuccess(state, action) {
      state.postReturnedPads = {
        data: action.payload,
        loading: false,
        success: true,
        error: false,
      };
      Swal.close();
    },
    postReturnedPadsFailure(state) {
      state.postReturnedPads = {
        data: undefined,
        loading: false,
        success: false,
        error: true,
      };
      Swal.close();
    },
    resetPostReturnedPads(state) {
      state.postReturnedPads = {
        data: undefined,
        loading: false,
        success: false,
        error: false,
      };
    },
    getPadAssignments(state, _action: TIDefaultParamsActionPayload) {
      state.getPadAssignments = {
        data: undefined,
        loading: true,
        success: false,
        error: false,
      };
    },
    getPadAssignmentsSuccess(state, action) {
      state.getPadAssignments = {
        data: action.payload,
        loading: false,
        success: true,
        error: false,
      };
    },
    getPadAssignmentsFailure(state) {
      state.getPadAssignments = {
        loading: false,
        success: false,
        error: true,
      };
    },
    getUserPadAssignment(state, _action: TIDefaultParamsActionPayload) {
      state.userPadAssignment.tableData = {
        data: undefined,
        loading: true,
        success: false,
        error: false,
      };
    },
    getUserPadAssignmentSuccess(state, action) {
      state.userPadAssignment.tableData = {
        data: action.payload,
        loading: false,
        success: true,
        error: false,
      };
    },
    getUserPadAssignmentFailure(state) {
      state.userPadAssignment.tableData = {
        loading: false,
        success: false,
        error: true,
      };
    },
    getUserPadAssignmentById(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.userPadAssignmentById = {
        data: undefined,
        loading: true,
        success: false,
        error: false,
      };
    },
    getUserPadAssignmentByIdSuccess(state, action) {
      state.userPadAssignmentById = {
        data: action.payload,
        loading: false,
        success: true,
        error: false,
      };
    },
    getUserPadAssignmentByIdFailure(state) {
      state.userPadAssignmentById = {
        loading: false,
        success: false,
        error: true,
      };
    },
    getUserPadSeriesDetails(state, _action: PayloadAction<{ id: number }>) {
      state.userPadSeriesDetails = {
        data: undefined,
        loading: true,
        success: false,
        error: false,
      };
    },
    getUserPadSeriesDetailsSuccess(state, action) {
      state.userPadSeriesDetails = {
        data: action.payload,
        loading: false,
        success: true,
        error: false,
      };
    },
    getUserPadSeriesDetailsFailure(state) {
      state.userPadSeriesDetails = {
        loading: false,
        success: false,
        error: true,
      };
    },
    putCancelReceipt(state, _action: TCancelReceiptWithActionPayload) {
      state.putCancelReceipt = {
        loading: true,
        success: false,
        error: false,
      };
    },
    putCancelReceiptSuccess(state) {
      state.putCancelReceipt = {
        loading: false,
        success: true,
        error: false,
      };
      showSuccess("Receipt cancelled successfully.");
    },
    putCancelReceiptFailure(state) {
      state.putCancelReceipt = {
        loading: false,
        success: false,
        error: true,
        data: undefined,
      };
      Swal.close();
    },
    putIssueReceipt(state, _action: TIssueReceiptWithActionPayload) {
      state.putIssueReceipt = {
        loading: true,
        success: false,
        error: false,
      };
      showProcessingToast("Processing receipt issue...");
    },
    putIssueReceiptSuccess(state) {
      state.putIssueReceipt = {
        loading: false,
        success: true,
        error: false,
      };
      Swal.close();
      showSuccess("Receipt Issued successfully.");
    },
    putIssueReceiptFailure(state) {
      state.putIssueReceipt = {
        loading: false,
        success: false,
        error: true,
        data: undefined,
      };
      Swal.close();
    },
    resetIssueForm(state) {
      state.putIssueReceipt = {
        loading: false,
        success: false,
        error: false,
      };
      state.putCancelReceipt = {
        loading: false,
        success: false,
        error: false,
      };
    },

    // Return Padsstate, _action: TIDefaultParamsActionPayload) {
    getReturnedPads(state, _action: TIDefaultParamsActionPayload) {
      state.getReturnedPads = createDynamicState(["loading"]);
    },
    getReturnedPadsSuccess(state, action) {
      state.getReturnedPads = {
        data: action.payload,
        loading: false,
        success: true,
        error: false,
      };
    },
    getReturnedPadsFailure(state) {
      state.getReturnedPads = createDynamicState(["error"]);
    },

    getReturnedTransmittalTrail(state, _action: TIDefaultParamsActionPayload) {
      state.getReturnedTransmittalTrail = createDynamicState(["loading"]);
    },

    getReturnedTransmittalTrailSuccess(state, action) {
      state.getReturnedTransmittalTrail = {
        data: action.payload,
        loading: false,
        success: true,
        error: false,
      };
    },
    getReturnedTransmittalTrailFailure(state) {
      state.getReturnedTransmittalTrail = createDynamicState(["error"]);
    },

    putTransmittalTrail(state, _action: TUpdateTransmitalStatusWithActionPayload) {
      state.putIssueReceipt = {
        loading: true,
        success: false,
        error: false,
      };
    },
    putTransmittalTrailSuccess(state) {
      state.putTransmittalTrail = {
        loading: false,
        success: true,
        error: false,
      };
      showSuccess("Receipt Issued successfully.");
    },
    putTransmittalTrailFailure(state) {
      state.putTransmittalTrail = {
        loading: false,
        success: false,
        error: true,
        data: undefined,
      };
    },
    resetTransmittalForm(state) {
      state.postTransmittalForm = {
        loading: false,
        success: false,
        error: false,
      };
    },
    putFormTransmittalTrail(state, _action: TUpdateTransmitalStatusWithActionPayload) {
      state.putFormTransmittalTrail = createDynamicState(["loading"]);
      showProcessingToast("Processing...");
    },
    putFormTransmittalTrailSuccess(state, action) {
      state.putFormTransmittalTrail = createDynamicState(["success"], action.payload);
      Swal.close();
    },
    putFormTransmittalTrailFailure(state) {
      state.putFormTransmittalTrail = createDynamicState(["error"]);
      Swal.close();
    },
  },
});

export const {
  setSelectedTransmittalForm,
  clearSelectedTransmittalForm,
  getTransmittalForms,
  getTransmittalFormsSuccess,
  getTransmittalFormsFailure,
  getTransmittalFormTrail,
  getTransmittalFormTrailSuccess,
  getTransmittalFormTrailFailure,
  getTransmittalForm,
  getTransmittalFormSuccess,
  getTransmittalFormFailure,
  getTransmittalFormsTrail,
  getTransmittalFormsTrailSuccess,
  getTransmittalFormsTrailFailure,
  postTransmittalForm,
  postTransmittalFormSuccess,
  postTransmittalFormFailure,
  getCurrentUserFormTransmittalTrail,
  getCurrentUserFormTransmittalTrailSuccess,
  getCurrentUserFormTransmittalTrailFailure,
  getLatestTransmittalForm,
  getLatestTransmittalFormSuccess,
  getLatestTransmittalFormFailure,
  postFormTransmittalTrail,
  postFormTransmittalTrailSuccess,
  postFormTransmittalTrailFailure,
  getTransmittalTrailActivityLog,
  getTransmittalTrailActivityLogSuccess,
  getTransmittalTrailActivityLogFailure,
  getFormActivityLogs,
  getFormActivityLogsSuccess,
  getFormActivityLogsFailure,
  getTransmittalFormToClifsaPrint,
  getTransmittalFormToClifsaPrintSuccess,
  getTransmittalFormToClifsaPrintFailure,
  clearTransmittalFormToClifsaPrint,
  getCurrentUserOldestFormTransmittalTrail,
  getCurrentUserOldestFormTransmittalTrailSuccess,
  getCurrentUserOldestFormTransmittalTrailFailure,
  postReturnedPads,
  postReturnedPadsSuccess,
  postReturnedPadsFailure,
  resetPostReturnedPads,
  getPadAssignments,
  getPadAssignmentsSuccess,
  getPadAssignmentsFailure,
  getUserPadAssignment,
  getUserPadAssignmentSuccess,
  getUserPadAssignmentFailure,
  getUserPadAssignmentById,
  getUserPadAssignmentByIdSuccess,
  getUserPadAssignmentByIdFailure,
  getUserPadSeriesDetails,
  getUserPadSeriesDetailsSuccess,
  getUserPadSeriesDetailsFailure,
  putCancelReceipt,
  putCancelReceiptSuccess,
  putCancelReceiptFailure,
  putIssueReceipt,
  putIssueReceiptSuccess,
  putIssueReceiptFailure,
  resetIssueForm,
  getReturnedPads,
  getReturnedPadsSuccess,
  getReturnedPadsFailure,
  getReturnedTransmittalTrail,
  getReturnedTransmittalTrailSuccess,
  getReturnedTransmittalTrailFailure,
  putTransmittalTrail,
  putTransmittalTrailSuccess,
  putTransmittalTrailFailure,
  resetTransmittalForm,
  putFormTransmittalTrail,
  putFormTransmittalTrailSuccess,
  putFormTransmittalTrailFailure,
} = transmittalFormSlice.actions;

export const useTransmittalFormActions = () => {
  return bindActionCreators(
    {
      setSelectedTransmittalForm,
      clearSelectedTransmittalForm,
      getTransmittalForms,
      getTransmittalForm,
      getTransmittalFormTrail,
      getTransmittalFormsTrail,
      postTransmittalForm,
      getCurrentUserFormTransmittalTrail,
      getLatestTransmittalForm,
      postFormTransmittalTrail,
      getTransmittalTrailActivityLog,
      getTransmittalTrailActivityLogSuccess,
      getTransmittalTrailActivityLogFailure,
      getFormActivityLogs,
      getFormActivityLogsSuccess,
      getFormActivityLogsFailure,
      getTransmittalFormToClifsaPrint,
      clearTransmittalFormToClifsaPrint,
      getCurrentUserOldestFormTransmittalTrail,
      getCurrentUserOldestFormTransmittalTrailSuccess,
      getCurrentUserOldestFormTransmittalTrailFailure,
      postReturnedPads,
      postReturnedPadsSuccess,
      postReturnedPadsFailure,
      resetPostReturnedPads,
      getPadAssignments,
      getPadAssignmentsSuccess,
      getPadAssignmentsFailure,
      getUserPadAssignment,
      getUserPadAssignmentSuccess,
      getUserPadAssignmentFailure,
      getUserPadAssignmentById,
      getUserPadAssignmentByIdSuccess,
      getUserPadAssignmentByIdFailure,
      getUserPadSeriesDetails,
      getUserPadSeriesDetailsSuccess,
      getUserPadSeriesDetailsFailure,
      putCancelReceipt,
      putCancelReceiptSuccess,
      putCancelReceiptFailure,
      putIssueReceipt,
      putIssueReceiptSuccess,
      putIssueReceiptFailure,
      resetIssueForm,
      getReturnedPads,
      getReturnedPadsSuccess,
      getReturnedPadsFailure,
      getReturnedTransmittalTrail,
      getReturnedTransmittalTrailSuccess,
      getReturnedTransmittalTrailFailure,
      putTransmittalTrail,
      putTransmittalTrailSuccess,
      putTransmittalTrailFailure,
      resetTransmittalForm,
      putFormTransmittalTrail,
      putFormTransmittalTrailSuccess,
      putFormTransmittalTrailFailure,
    },
    useDispatch()
  );
};

export default transmittalFormSlice.reducer;
