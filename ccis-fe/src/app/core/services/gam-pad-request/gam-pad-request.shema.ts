import * as Yup from "yup";

export const RequestPadValidation = Yup.object().shape({
  divisionId: Yup.number().notOneOf([0], "Please select a valid division").required("Division is required"),
  formTypeId: Yup.number().notOneOf([0], "Please select a valid form").required("Form Type is required"),
  areaId: Yup.number().notOneOf([0], "Please select a valid area").required("Area is required"),
  releasedTo: Yup.number().notOneOf([0], "Please select a valid User").required("Released To is required"),
  numberOfPads: Yup.number().min(1, "The minimum pad is one").required("Number of Pads is required"),
  seriesFrom: Yup.number().min(1, "Series From should not start from 0").required("Number of Pads is required"),
  seriesTo: Yup.number().min(1, "Series To should not start from 0").required("Number of Pads is required"),
});
