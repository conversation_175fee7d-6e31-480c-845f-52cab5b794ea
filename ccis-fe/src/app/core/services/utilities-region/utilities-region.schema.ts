import * as Yup from 'yup';


export const CreateRegionSchema = Yup.object().shape({
    regionName: Yup.string()
      .required('Region Name is required')
      .max(50, 'Region Name must be at most 50 characters long'),
  });
  
  export const EditRegionSchema = Yup.object().shape({
    regionName: Yup.string()
      .required('Region Name is required')
      .max(50, 'Region Name must be at most 50 characters long'),
  });
  