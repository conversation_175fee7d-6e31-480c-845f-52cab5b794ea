import httpClient from "@clients/httpClient";
//Imports for the payload
import {
  TUtilitiesRemittanceDataPayload,
  TUtilitiesRemittanceDataWithIDAndIndexPayload,
} from "@state/types/utilities-remittance-data";
//For new GET
import { IDefaultParams } from "@interface/common.interface";
//For grabbing the current type being used
const apiResource = "remittance";

//For new GET feature
export const getRemittanceDatasService = async (params: IDefaultParams) => {
  
  //Use the relation in the API for this:
  let queryParams = "relations=remittanceType";
  
  //For pagination
  if (params.page) {
    queryParams += `&pageSize=${params.page}`;
  }
  if (params.pageSize) {
    queryParams += `&pageSize=${params.pageSize}`;
  }
  
  //Filters:
  
  //For filtering the Remittance Type name
  if (params.filter) {
    queryParams += `&remittanceType.remittanceTypeName[like]=${params.filter}`;
  }
  
  //Filter by date for period from and to for Remittance Data
  if (params?.dateFrom && params?.dateTo) {
    queryParams += `&periodFrom[between]=${params.dateFrom},${params.dateTo}`;
  }
  if (params?.dateFrom && params?.dateTo) {
    queryParams += `&periodTo[between]=${params.dateFrom},${params.dateTo}`;
  }
  return httpClient.get(`${apiResource}?${queryParams}`);
};

//For fetching a specific ID
export const getRemittanceDataService = async (id: number) => {
  return httpClient.get(`${apiResource}/${id}`);
};

//For creating a new Remittance Data
export const postRemittanceDataService = async (
  payload: TUtilitiesRemittanceDataPayload
) => {
  return httpClient.post(`${apiResource}`, payload);
};

//For updating a Remittance Data
export const putRemittanceDataService = async (
  payload: TUtilitiesRemittanceDataPayload
) => {
  return httpClient.put(`${apiResource}/${payload.id}`, payload);
};

//For deleting a Remittance Data
export const destroyRemittanceDataService = async (
  payload: TUtilitiesRemittanceDataWithIDAndIndexPayload
) => {
  return httpClient.delete(`${apiResource}/${payload.id}`);
};
