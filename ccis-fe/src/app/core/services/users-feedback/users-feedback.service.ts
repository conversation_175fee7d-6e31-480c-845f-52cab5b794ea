import httpClient from "@clients/httpClient";
import { IDefaultParams } from "@interface/common.interface";
import { IQuestion, ISaveFeedbackPayload } from "@interface/users-feedback.interface";

const apiResource = "/feedback";

export const getFeedbackQuestionsService = (params: IDefaultParams) => {
  let queryParams = "";
  if (params.relations) {
    queryParams += `relations=${params.relations}`;
  }
  if (params.condition) {
    queryParams += `&condition=${params.condition}`;
  }
  return httpClient.get(`${apiResource}/question/?${queryParams}`);
};

export const getTransactionTypesService = (params: IDefaultParams) => {
  let queryParams = "";
  if (params.relations) {
    queryParams += `relations=${params.relations}`;
  }
  if (params.condition) {
    queryParams += `&condition=${params.condition}`;
  }
  return httpClient.get(`${apiResource}/transaction-type/?${queryParams}`);
};

export const getUserFeedbacksService = (params: IDefaultParams) => {
  let queryParams = "";
  if (params.relations) {
    queryParams += `relations=${params.relations}`;
  }
  if (params.condition) {
    queryParams += `&condition=${params.condition}`;
  }
  return httpClient.get(`${apiResource}/user-feedback/?${queryParams}`);
};

export const postSaveQuestionsService = (question: IQuestion, status: number) => {
  const formData = new FormData();
  formData.append("question", question.question);
  formData.append("status", status.toString());

  return httpClient.post(`${apiResource}/question/save-question`, formData);
};

export const postSaveChoiceService = async (questionId: number, choiceName: string): Promise<void> => {
  const formData = new FormData();
  formData.append("questionId", questionId.toString());
  formData.append("name", choiceName);

  return httpClient.post(`${apiResource}/choice/save-choice`, formData);
};

export const postSaveTransactionTypeService = async (transactionTypeName: string): Promise<void> => {
  const formData = new FormData();
  formData.append("name", transactionTypeName);

  return httpClient.post(`${apiResource}/transaction-type/save-transaction`, formData);
};

export const postSaveFeedbackService = (payload: ISaveFeedbackPayload) => {
  return httpClient.post(`${apiResource}/user/save-feedback`, payload);
};

export const putUpdateTransactionTypeService = (transactionTypeId: number, transactionTypeName: string) => {
  const payload = {
    name: transactionTypeName,
  };

  return httpClient.put(`${apiResource}/transaction-type/update-transaction/${transactionTypeId}`, payload);
};

export const putUpdateQuestionService = (
  questionId: number,
  payload: {
    id: number;
    question: string;
    status: number;
    feedbackChoices: { questionId: number; name: string }[];
  }
) => {
  return httpClient.put(`${apiResource}/question/feedback-questions/${questionId}`, payload);
};
