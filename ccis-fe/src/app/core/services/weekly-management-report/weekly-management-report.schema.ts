import * as Yup from "yup";
// Validation schema for the AddTaskForm

export const CreateTaskFormSchema = Yup.object().shape({
  weekNo: Yup.string().required("Week number is required").matches(/^\d+$/, "Week number must be a valid number"),

  periodFrom: Yup.date().required("Period from date is required").typeError("Period from must be a valid date"),

  periodTo: Yup.date().required("Period to date is required").typeError("Period to must be a valid date").min(Yup.ref("periodFrom"), "Period to must be after period from"),

  targetTasks: Yup.array().of(
    Yup.object()
      .shape({
        ticketNo: Yup.string(),
        description: Yup.string(),
        taskType: Yup.string(),
        hours: Yup.string(),
      })
      .test("at-least-one-filled", "All fields (Ticket number, Description, Issue type, Hours) must be filled if any one is provided", (value) => {
        const { ticketNo, description, taskType, hours } = value || {};
        const isAnyFilled = ticketNo || description || taskType || hours;
        const isAllFilled = ticketNo && description && taskType && hours;

        if (isAnyFilled && !isAllFilled) {
          return false; // If any field is filled, all must be filled
        }

        // If hours is provided, ensure it's a positive number
        if (hours) {
          const num = parseFloat(hours);
          return num > 0;
        }

        return true; // Valid if no fields are filled or all are filled correctly
      })
  ),

  actualTasks: Yup.array().of(
    Yup.object()
      .shape({
        ticketNo: Yup.string(),
        description: Yup.string(),
        taskType: Yup.string(),
        hours: Yup.string(),
      })
      .test("at-least-one-filled", "All fields (Ticket number, Description, Issue type, Hours) must be filled if any one is provided", (value) => {
        const { ticketNo, description, taskType, hours } = value || {};
        const isAnyFilled = ticketNo || description || taskType || hours;
        const isAllFilled = ticketNo && description && taskType && hours;

        if (isAnyFilled && !isAllFilled) {
          return false; // If any field is filled, all must be filled
        }

        // If hours is provided, ensure it's a positive number
        if (hours) {
          const num = parseFloat(hours);
          return num > 0;
        }

        return true; // Valid if no fields are filled or all are filled correctly
      })
  ),
});

// Helper function to get field error for nested arrays
export const getFieldError = (errors: any, touched: any, arrayName: "targetTasks" | "actualTasks", index: number, fieldName: string) => {
  return errors?.[arrayName]?.[index]?.[fieldName] && touched?.[arrayName]?.[index]?.[fieldName];
};

// Helper function to get error message for nested arrays
export const getFieldErrorMessage = (errors: any, arrayName: "targetTasks" | "actualTasks", index: number, fieldName: string) => {
  return errors?.[arrayName]?.[index]?.[fieldName];
};
// Keep the existing UpdateTaskFormSchema for the main form that still uses the old structure
export const UpdateTaskFormSchema = Yup.object().shape({
  employeeName: Yup.string().optional(),
  position: Yup.string().optional(),
  monthYear: Yup.string().required("Month/Year is required"),
  weekNo: Yup.string()
    .required("Week number is required")
    .matches(/^[1-9][0-9]?$|^52$/, "Week number must be between 1 and 52"),
  periodFrom: Yup.string().required("Period from date is required"),
  periodTo: Yup.string()
    .required("Period to date is required")
    .test("date-after", "Period to must be after period from", function (value) {
      const { periodFrom } = this.parent;
      if (!periodFrom || !value) return true;
      return new Date(value) >= new Date(periodFrom);
    }),

  targetTasks: Yup.array()
    .of(
      Yup.object()
        .shape({
          id: Yup.number().required(),
          ticketNo: Yup.string(),
          description: Yup.string(),
          taskType: Yup.string(),
          hours: Yup.string(),
        })
        .test("task-validation", "Invalid task data", function (value) {
          const { ticketNo, description, taskType, hours } = value || {};
          const isAnyFilled = ticketNo || description || taskType || hours;
          const isAllFilled = ticketNo && description && taskType && hours;

          if (isAnyFilled && !isAllFilled) {
            if (!ticketNo) {
              return this.createError({
                path: `${this.path}.ticketNo`,
                message: "Ticket number is required when other fields are filled",
              });
            }
            if (!description) {
              return this.createError({
                path: `${this.path}.description`,
                message: "Description is required when other fields are filled",
              });
            }
            if (!taskType) {
              return this.createError({
                path: `${this.path}.taskType`,
                message: "Task type is required when other fields are filled",
              });
            }
            if (!hours) {
              return this.createError({
                path: `${this.path}.hours`,
                message: "Hours is required when other fields are filled",
              });
            }
          }

          if (hours) {
            const num = parseFloat(hours);
            if (isNaN(num) || num <= 0) {
              return this.createError({
                path: `${this.path}.hours`,
                message: "Hours must be a positive number",
              });
            }
          }

          return true;
        })
    )
    .test("at-least-one-complete", "At least one complete target task is required", function (tasks) {
      const hasCompleteTask = tasks?.some((task) => task.ticketNo && task.description && task.taskType && task.hours);

      if (!hasCompleteTask) {
        return this.createError({
          message: "At least one complete target task is required",
          path: this.path,
        });
      }

      return true;
    }),

  actualTasks: Yup.array()
    .of(
      Yup.object()
        .shape({
          id: Yup.number().required(),
          ticketNo: Yup.string(),
          description: Yup.string(),
          taskType: Yup.string(),
          hours: Yup.string(),
        })
        .test("task-validation", "Invalid task data", function (value) {
          const { ticketNo, description, taskType, hours } = value || {};
          const isAnyFilled = ticketNo || description || taskType || hours;
          const isAllFilled = ticketNo && description && taskType && hours;

          if (isAnyFilled && !isAllFilled) {
            if (!ticketNo) {
              return this.createError({
                path: `${this.path}.ticketNo`,
                message: "Ticket number is required when other fields are filled",
              });
            }
            if (!description) {
              return this.createError({
                path: `${this.path}.description`,
                message: "Description is required when other fields are filled",
              });
            }
            if (!taskType) {
              return this.createError({
                path: `${this.path}.taskType`,
                message: "Task type is required when other fields are filled",
              });
            }
            if (!hours) {
              return this.createError({
                path: `${this.path}.hours`,
                message: "Hours is required when other fields are filled",
              });
            }
          }

          if (hours) {
            const num = parseFloat(hours);
            if (isNaN(num) || num <= 0) {
              return this.createError({
                path: `${this.path}.hours`,
                message: "Hours must be a positive number",
              });
            }
          }

          return true;
        })
    )
    .test("at-least-one-complete", "At least one complete actual task is required", function (tasks) {
      const hasCompleteTask = tasks?.some((task) => task.ticketNo && task.description && task.taskType && task.hours);

      if (!hasCompleteTask) {
        return this.createError({
          message: "At least one complete actual task is required",
          path: this.path,
        });
      }

      return true;
    }),
});
