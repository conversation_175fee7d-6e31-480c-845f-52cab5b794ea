import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

import RequestTypes from "@modules/admin/departmental-ticketing-utilities/request-types";
import ApplicationUtility from "@modules/admin/departmental-ticketing-utilities/applications";
import DevicesSystems from "@modules/admin/departmental-ticketing-utilities/devices-systems";
import OperatingSystems from "@modules/admin/departmental-ticketing-utilities/operating-systems";
import TicketUtilities from "@modules/admin/departmental-ticketing-utilities";
import { HiChevronRight } from "react-icons/hi2";
import { FaBookmark } from "react-icons/fa";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.dashboard.key,
  path: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.oicOperationsManagerNonLife],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.requestDashboard.key,
  path: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.oicOperationsManagerNonLife],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.requestForm.key,
  path: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.oicOperationsManagerNonLife],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.viewRequestForm.key,
  path: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.oicOperationsManagerNonLife],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.notification.key,
  path: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.oicOperationsManagerNonLife],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.profile.key,
  path: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.oicOperationsManagerNonLife],
};

export const requestTypeUtilities: RouteItem = {
  name: "Request Type",
  id: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.requestTypesUtility.key,
  path: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.requestTypesUtility.key,
  component: RequestTypes,
  guard: AuthGuard,
  roles: [UserRoles.oicOperationsManagerNonLife],
  icon: HiChevronRight,
  isSidebar: false,
};

export const applicationUtilities: RouteItem = {
  name: "Applications",
  id: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.applicationsUtility.key,
  path: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.applicationsUtility.key,
  component: ApplicationUtility,
  guard: AuthGuard,
  roles: [UserRoles.oicOperationsManagerNonLife],
  icon: HiChevronRight,
  isSidebar: false,
};

export const devicesUtilities: RouteItem = {
  name: "Devices/Systems",
  id: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.devicesSystemUtility.key,
  path: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.devicesSystemUtility.key,
  component: DevicesSystems,
  guard: AuthGuard,
  roles: [UserRoles.oicOperationsManagerNonLife],
  icon: HiChevronRight,
  isSidebar: false,
};

export const operatingSystemsUtilities: RouteItem = {
  name: "Operating Systems",
  id: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.operatingSystemsUtility.key,
  path: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.operatingSystemsUtility.key,
  component: OperatingSystems,
  guard: AuthGuard,
  roles: [UserRoles.oicOperationsManagerNonLife],
  icon: HiChevronRight,
  isSidebar: false,
};

export const ticketUtilities: RouteItem = {
  name: "Ticket Utilities",
  id: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.ticketUtilities.key,
  path: ROUTES.OIC_OPERATIONS_MANAGER_NON_LIFE.ticketUtilities.key,
  component: TicketUtilities,
  guard: AuthGuard,
  roles: [UserRoles.oicOperationsManagerNonLife],
  icon: FaBookmark,
  isSidebar: true,
  children: [requestTypeUtilities, applicationUtilities, devicesUtilities, operatingSystemsUtilities],
};

export const oicOperationsManagerNonLifeRoutes = [
  overview,
  requestDashboard,
  requestForm,
  viewRequest,
  notification,
  profile,
  ticketUtilities,
  requestTypeUtilities,
  applicationUtilities,
  devicesUtilities,
  operatingSystemsUtilities,
];
