import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.AVP_ADMIN_CORPLAN.dashboard.key,
  path: ROUTES.AVP_ADMIN_CORPLAN.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.avpAdminCorplan],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.AVP_ADMIN_CORPLAN.requestDashboard.key,
  path: ROUTES.AVP_ADMIN_CORPLAN.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.avpAdminCorplan],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.AVP_ADMIN_CORPLAN.requestForm.key,
  path: ROUTES.AVP_ADMIN_CORPLAN.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.avpAdminCorplan],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.AVP_ADMIN_CORPLAN.viewRequestForm.key,
  path: ROUTES.AVP_ADMIN_CORPLAN.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.avpAdminCorplan],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.AVP_ADMIN_CORPLAN.notification.key,
  path: ROUTES.AVP_ADMIN_CORPLAN.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.avpAdminCorplan],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.AVP_ADMIN_CORPLAN.profile.key,
  path: ROUTES.AVP_ADMIN_CORPLAN.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.avpAdminCorplan],
};

export const avpAdminCorplanRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];