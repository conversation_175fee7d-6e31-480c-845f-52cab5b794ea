import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.CLAIMS_EVALUATOR_PROCESSOR.dashboard.key,
  path: ROUTES.CLAIMS_EVALUATOR_PROCESSOR.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.claimsEvaluatorProcessor],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.CLAIMS_EVALUATOR_PROCESSOR.requestDashboard.key,
  path: ROUTES.CLAIMS_EVALUATOR_PROCESSOR.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.claimsEvaluatorProcessor],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.CLAIMS_EVALUATOR_PROCESSOR.requestForm.key,
  path: ROUTES.CLAIMS_EVALUATOR_PROCESSOR.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.claimsEvaluatorProcessor],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.CLAIMS_EVALUATOR_PROCESSOR.viewRequestForm.key,
  path: ROUTES.CLAIMS_EVALUATOR_PROCESSOR.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.claimsEvaluatorProcessor],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.CLAIMS_EVALUATOR_PROCESSOR.notification.key,
  path: ROUTES.CLAIMS_EVALUATOR_PROCESSOR.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.claimsEvaluatorProcessor],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.CLAIMS_EVALUATOR_PROCESSOR.profile.key,
  path: ROUTES.CLAIMS_EVALUATOR_PROCESSOR.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.claimsEvaluatorProcessor],
};

export const claimsEvaluatorProcessorRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];