import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.ACTUARY_ASSISTANT_RELIEVER.dashboard.key,
  path: ROUTES.ACTUARY_ASSISTANT_RELIEVER.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAssistantReliever],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.ACTUARY_ASSISTANT_RELIEVER.requestDashboard.key,
  path: ROUTES.ACTUARY_ASSISTANT_RELIEVER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAssistantReliever],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.ACTUARY_ASSISTANT_RELIEVER.requestForm.key,
  path: ROUTES.ACTUARY_ASSISTANT_RELIEVER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAssistantReliever],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.ACTUARY_ASSISTANT_RELIEVER.viewRequestForm.key,
  path: ROUTES.ACTUARY_ASSISTANT_RELIEVER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAssistantReliever],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.ACTUARY_ASSISTANT_RELIEVER.notification.key,
  path: ROUTES.ACTUARY_ASSISTANT_RELIEVER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAssistantReliever],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.ACTUARY_ASSISTANT_RELIEVER.profile.key,
  path: ROUTES.ACTUARY_ASSISTANT_RELIEVER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAssistantReliever],
};

export const actuaryAssistantRelieverRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];