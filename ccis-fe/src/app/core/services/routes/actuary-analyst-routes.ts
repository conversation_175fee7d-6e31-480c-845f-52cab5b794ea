import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.ACTUARY_ANALYST.dashboard.key,
  path: ROUTES.ACTUARY_ANALYST.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAnalyst],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.ACTUARY_ANALYST.requestDashboard.key,
  path: ROUTES.ACTUARY_ANALYST.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAnalyst],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.ACTUARY_ANALYST.requestForm.key,
  path: ROUTES.ACTUARY_ANALYST.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAnalyst],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.ACTUARY_ANALYST.viewRequestForm.key,
  path: ROUTES.ACTUARY_ANALYST.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAnalyst],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.ACTUARY_ANALYST.notification.key,
  path: ROUTES.ACTUARY_ANALYST.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAnalyst],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.ACTUARY_ANALYST.profile.key,
  path: ROUTES.ACTUARY_ANALYST.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAnalyst],
};

export const actuaryAnalystRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];