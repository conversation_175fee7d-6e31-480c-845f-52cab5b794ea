import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.ADMIN_ASSISTANT_VP_FINANCE.dashboard.key,
  path: ROUTES.ADMIN_ASSISTANT_VP_FINANCE.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.adminAssistantVpFinance],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.ADMIN_ASSISTANT_VP_FINANCE.requestDashboard.key,
  path: ROUTES.ADMIN_ASSISTANT_VP_FINANCE.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.adminAssistantVpFinance],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.ADMIN_ASSISTANT_VP_FINANCE.requestForm.key,
  path: ROUTES.ADMIN_ASSISTANT_VP_FINANCE.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.adminAssistantVpFinance],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.ADMIN_ASSISTANT_VP_FINANCE.viewRequestForm.key,
  path: ROUTES.ADMIN_ASSISTANT_VP_FINANCE.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.adminAssistantVpFinance],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.ADMIN_ASSISTANT_VP_FINANCE.notification.key,
  path: ROUTES.ADMIN_ASSISTANT_VP_FINANCE.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.adminAssistantVpFinance],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.ADMIN_ASSISTANT_VP_FINANCE.profile.key,
  path: ROUTES.ADMIN_ASSISTANT_VP_FINANCE.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.adminAssistantVpFinance],
};

export const adminAssistantVpFinanceRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];