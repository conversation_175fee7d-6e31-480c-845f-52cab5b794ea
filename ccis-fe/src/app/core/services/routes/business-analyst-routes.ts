import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.BUSINESS_ANALYST.dashboard.key,
  path: ROUTES.BUSINESS_ANALYST.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.businessAnalyst],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.BUSINESS_ANALYST.requestDashboard.key,
  path: ROUTES.BUSINESS_ANALYST.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.businessAnalyst],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.BUSINESS_ANALYST.requestForm.key,
  path: ROUTES.BUSINESS_ANALYST.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.businessAnalyst],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.BUSINESS_ANALYST.viewRequestForm.key,
  path: ROUTES.BUSINESS_ANALYST.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.businessAnalyst],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.BUSINESS_ANALYST.notification.key,
  path: ROUTES.BUSINESS_ANALYST.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.businessAnalyst],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.BUSINESS_ANALYST.profile.key,
  path: ROUTES.BUSINESS_ANALYST.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.businessAnalyst],
};

export const businessAnalystRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];