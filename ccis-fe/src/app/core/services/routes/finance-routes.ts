import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";
import { PiHandCoinsBold } from "react-icons/pi";
import ChartOfAccounts from "@modules/chart-of-accounts/components/Tables/ChartOfAccounts";
import ChartOfAccountsUtility from "@modules/chart-of-accounts/components/Tables/ChartOfAccountsUtility";
import EntityTypes from "@modules/chart-of-accounts/components/Tables/EntityTypes";
import { <PERSON><PERSON>oop } from "react-icons/si";
import { LuLayoutDashboard } from "react-icons/lu";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.FINANCE.dashboard.key,
  path: ROUTES.FINANCE.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.finance],
  icon: MdDashboard,
  isSidebar: false,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.FINANCE.requestDashboard.key,
  path: ROUTES.FINANCE.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.finance],
  icon: MdDashboard,
  isSidebar: false,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.FINANCE.requestForm.key,
  path: ROUTES.FINANCE.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.finance],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.FINANCE.viewRequestForm.key,
  path: ROUTES.FINANCE.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.finance],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.FINANCE.notification.key,
  path: ROUTES.FINANCE.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.finance],
  icon: MdDashboard,
};

export const chartOfAccounts: RouteItem = {
  name: "Chart of Accounts ",
  id: ROUTES.FINANCE.chartOfAccounts.key,
  path: ROUTES.FINANCE.chartOfAccounts.key,
  component: ChartOfAccounts,
  guard: AuthGuard,
  roles: [UserRoles.finance],
  icon: LuLayoutDashboard,
  isSidebar: true,
};

export const chartOfAccountsUtility: RouteItem = {
  name: "Chart of Accounts Utility",
  id: ROUTES.FINANCE.chartOfAccountsUtility.key,
  path: ROUTES.FINANCE.chartOfAccountsUtility.key,
  component: ChartOfAccountsUtility,
  guard: AuthGuard,
  roles: [UserRoles.finance],
  icon: PiHandCoinsBold,
  isSidebar: true,
};

export const entityType: RouteItem = {
  name: "Entity Types",
  id: ROUTES.FINANCE.entityTypes.key,
  path: ROUTES.FINANCE.entityTypes.key,
  component: EntityTypes,
  guard: AuthGuard,
  roles: [UserRoles.finance],
  icon: SiCoop,
  isSidebar: true,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.FINANCE.profile.key,
  path: ROUTES.FINANCE.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.finance],
};

export const financeRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile, chartOfAccounts, chartOfAccountsUtility, entityType];
