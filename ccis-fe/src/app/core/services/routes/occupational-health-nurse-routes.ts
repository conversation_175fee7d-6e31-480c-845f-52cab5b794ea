import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.OCCUPATIONAL_HEALTH_NURSE.dashboard.key,
  path: ROUTES.OCCUPATIONAL_HEALTH_NURSE.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.occupationalHealthNurse],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.OCCUPATIONAL_HEALTH_NURSE.requestDashboard.key,
  path: ROUTES.OCCUPATIONAL_HEALTH_NURSE.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.occupationalHealthNurse],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.OCCUPATIONAL_HEALTH_NURSE.requestForm.key,
  path: ROUTES.OCCUPATIONAL_HEALTH_NURSE.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.occupationalHealthNurse],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.OCCUPATIONAL_HEALTH_NURSE.viewRequestForm.key,
  path: ROUTES.OCCUPATIONAL_HEALTH_NURSE.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.occupationalHealthNurse],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.OCCUPATIONAL_HEALTH_NURSE.notification.key,
  path: ROUTES.OCCUPATIONAL_HEALTH_NURSE.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.occupationalHealthNurse],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.OCCUPATIONAL_HEALTH_NURSE.profile.key,
  path: ROUTES.OCCUPATIONAL_HEALTH_NURSE.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.occupationalHealthNurse],
};

export const occupationalHealthNurseRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];