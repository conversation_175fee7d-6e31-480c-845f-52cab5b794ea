import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.BANK_RECONCILIATION_ASSISTANT_1_LIFE.dashboard.key,
  path: ROUTES.BANK_RECONCILIATION_ASSISTANT_1_LIFE.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.bankReconciliationAssistant1Life],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.BANK_RECONCILIATION_ASSISTANT_1_LIFE.requestDashboard.key,
  path: ROUTES.BANK_RECONCILIATION_ASSISTANT_1_LIFE.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.bankReconciliationAssistant1Life],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.BANK_RECONCILIATION_ASSISTANT_1_LIFE.requestForm.key,
  path: ROUTES.BANK_RECONCILIATION_ASSISTANT_1_LIFE.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.bankReconciliationAssistant1Life],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.BANK_RECONCILIATION_ASSISTANT_1_LIFE.viewRequestForm.key,
  path: ROUTES.BANK_RECONCILIATION_ASSISTANT_1_LIFE.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.bankReconciliationAssistant1Life],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.BANK_RECONCILIATION_ASSISTANT_1_LIFE.notification.key,
  path: ROUTES.BANK_RECONCILIATION_ASSISTANT_1_LIFE.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.bankReconciliationAssistant1Life],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.BANK_RECONCILIATION_ASSISTANT_1_LIFE.profile.key,
  path: ROUTES.BANK_RECONCILIATION_ASSISTANT_1_LIFE.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.bankReconciliationAssistant1Life],
};

export const bankReconciliationAssistant1LifeRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];