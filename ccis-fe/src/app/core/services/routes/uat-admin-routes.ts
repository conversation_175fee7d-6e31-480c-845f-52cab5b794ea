import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles} from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import UATManagement from "@modules/admin/user-acceptance-test";
import { FaRegHandPaper } from "react-icons/fa";
import ViewUATResults from "@modules/admin/user-acceptance-test/Pages/ViewUAT";
import NotificationPage from "@modules/shared/notification";
import { MdDashboard } from "react-icons/md";
import Profile from "@modules/shared/profile";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";

export const uatManagement: RouteItem = {
    name: "UAT Management",
    id: ROUTES.UATADMIN.uatManagement.key,
    path: ROUTES.UATADMIN.uatManagement.key,
    component: UATManagement,
    guard: AuthGuard,
    roles: [UserRoles.uatadmin],
    icon: FaRegHandPaper,
    isSidebar: true,
  };
  
  export const uatResults: RouteItem = {
    name: "UAT Results",
    id: ROUTES.UATADMIN.uatResults.key,
    path: ROUTES.UATADMIN.uatResults.key,
    component: ViewUATResults,
    guard: AuthGuard,
    roles: [UserRoles.uatadmin],
  };

  export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.UATADMIN.notification.key,
  path: ROUTES.UATADMIN.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.uatadmin],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.UATADMIN.profile.key,
  path: ROUTES.UATADMIN.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.uatadmin],
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.UATADMIN.requestForm.key,
  path: ROUTES.UATADMIN.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.uatadmin],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.UATADMIN.viewRequestForm.key,
  path: ROUTES.UATADMIN.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.uatadmin],
  isSidebar: false,
};
export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.UATADMIN.requestDashboard.key,
  path: ROUTES.UATADMIN.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.uatadmin],
  icon: MdDashboard,
  isSidebar: true,
};

export const uatAdminRoutes = [uatManagement, uatResults, notification, profile, requestDashboard, requestForm, viewRequest];
