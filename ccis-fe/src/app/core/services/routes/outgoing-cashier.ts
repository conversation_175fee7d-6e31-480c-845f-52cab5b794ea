import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import IncomingOutgoingCashierDashboard from "@modules/dashboard/IncomingOutgoingCashierDashboard.tsx";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
// import { PiCertificate } from "react-icons/pi";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.OUTGOINGCASHIER.outgoingCashierDashboard.key,
  path: ROUTES.OUTGOINGCASHIER.outgoingCashierDashboard.key,
  component: IncomingOutgoingCashierDashboard,
  guard: AuthGuard,
  roles: [UserRoles.incomingCashier],
  icon: MdDashboard,
  isSidebar: true,
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.OUTGOINGCASHIER.notification.key,
  path: ROUTES.OUTGOINGCASHIER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.outgoingCashier],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.OUTGOINGCASHIER.profile.key,
  path: ROUTES.OUTGOINGCASHIER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.outgoingCashier],
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.OUTGOINGCASHIER.requestForm.key,
  path: ROUTES.OUTGOINGCASHIER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.outgoingCashier],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.OUTGOINGCASHIER.viewRequestForm.key,
  path: ROUTES.OUTGOINGCASHIER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.outgoingCashier],
  isSidebar: false,
};
export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.OUTGOINGCASHIER.requestDashboard.key,
  path: ROUTES.OUTGOINGCASHIER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.outgoingCashier],
  icon: MdDashboard,
  isSidebar: true,
};

export const outgoingCashierRoutes = [overview, notification, profile, requestDashboard, requestForm, viewRequest];
