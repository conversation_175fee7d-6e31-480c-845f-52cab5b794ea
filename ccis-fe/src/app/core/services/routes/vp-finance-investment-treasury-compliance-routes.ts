import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.VP_FINANCE_INVESTMENT_TREASURY_COMPLIANCE.dashboard.key,
  path: ROUTES.VP_FINANCE_INVESTMENT_TREASURY_COMPLIANCE.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.vpFinanceInvestmentTreasuryCompliance],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.VP_FINANCE_INVESTMENT_TREASURY_COMPLIANCE.requestDashboard.key,
  path: ROUTES.VP_FINANCE_INVESTMENT_TREASURY_COMPLIANCE.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.vpFinanceInvestmentTreasuryCompliance],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.VP_FINANCE_INVESTMENT_TREASURY_COMPLIANCE.requestForm.key,
  path: ROUTES.VP_FINANCE_INVESTMENT_TREASURY_COMPLIANCE.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.vpFinanceInvestmentTreasuryCompliance],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.VP_FINANCE_INVESTMENT_TREASURY_COMPLIANCE.viewRequestForm.key,
  path: ROUTES.VP_FINANCE_INVESTMENT_TREASURY_COMPLIANCE.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.vpFinanceInvestmentTreasuryCompliance],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.VP_FINANCE_INVESTMENT_TREASURY_COMPLIANCE.notification.key,
  path: ROUTES.VP_FINANCE_INVESTMENT_TREASURY_COMPLIANCE.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.vpFinanceInvestmentTreasuryCompliance],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.VP_FINANCE_INVESTMENT_TREASURY_COMPLIANCE.profile.key,
  path: ROUTES.VP_FINANCE_INVESTMENT_TREASURY_COMPLIANCE.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.vpFinanceInvestmentTreasuryCompliance],
};

export const vpFinanceInvestmentTreasuryComplianceRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];