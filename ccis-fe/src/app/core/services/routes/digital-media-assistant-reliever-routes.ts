import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.DIGITAL_MEDIA_ASSISTANT_RELIEVER.dashboard.key,
  path: ROUTES.DIGITAL_MEDIA_ASSISTANT_RELIEVER.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.digitalMediaAssistantReliever],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.DIGITAL_MEDIA_ASSISTANT_RELIEVER.requestDashboard.key,
  path: ROUTES.DIGITAL_MEDIA_ASSISTANT_RELIEVER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.digitalMediaAssistantReliever],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.DIGITAL_MEDIA_ASSISTANT_RELIEVER.requestForm.key,
  path: ROUTES.DIGITAL_MEDIA_ASSISTANT_RELIEVER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.digitalMediaAssistantReliever],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.DIGITAL_MEDIA_ASSISTANT_RELIEVER.viewRequestForm.key,
  path: ROUTES.DIGITAL_MEDIA_ASSISTANT_RELIEVER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.digitalMediaAssistantReliever],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.DIGITAL_MEDIA_ASSISTANT_RELIEVER.notification.key,
  path: ROUTES.DIGITAL_MEDIA_ASSISTANT_RELIEVER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.digitalMediaAssistantReliever],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.DIGITAL_MEDIA_ASSISTANT_RELIEVER.profile.key,
  path: ROUTES.DIGITAL_MEDIA_ASSISTANT_RELIEVER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.digitalMediaAssistantReliever],
};

export const digitalMediaAssistantRelieverRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];