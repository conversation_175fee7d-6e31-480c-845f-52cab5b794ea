import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.DATA_ENCODER.dashboard.key,
  path: ROUTES.DATA_ENCODER.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.dataEncoder],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.DATA_ENCODER.requestDashboard.key,
  path: ROUTES.DATA_ENCODER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.dataEncoder],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.DATA_ENCODER.requestForm.key,
  path: ROUTES.DATA_ENCODER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.dataEncoder],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.DATA_ENCODER.viewRequestForm.key,
  path: ROUTES.DATA_ENCODER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.dataEncoder],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.DATA_ENCODER.notification.key,
  path: ROUTES.DATA_ENCODER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.dataEncoder],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.DATA_ENCODER.profile.key,
  path: ROUTES.DATA_ENCODER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.dataEncoder],
};

export const dataEncoderRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];