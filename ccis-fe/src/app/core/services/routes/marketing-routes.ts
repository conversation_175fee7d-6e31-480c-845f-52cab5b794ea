import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import { HiChevronRight } from "react-icons/hi";
import { MdOutlineFactCheck } from "react-icons/md";
import MarketingDashboard from "@modules/marketing/dashboard";
import Validation from "@modules/marketing/validation";
import CommissionAndRequirements from "@modules/marketing/validation/commission-and-requirements";
import Approval from "@modules/marketing/validation/commission-and-requirements/Approval";
import Shares from "@modules/admin/shares";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import ProductProposal from "@modules/sales/product-proposal";
import { GiNotebook } from "react-icons/gi";
import ProposalView from "@modules/sales/product-proposal/Approval";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import Utilities from "@modules/sales/utilities/cooperatives/index";
import CooperativeInformationForm from "@modules/sales/utilities/cooperatives/CooperativeInformationForm";
import CooperativesTable from "@modules/sales/utilities/cooperatives/CooperativesTable";
import { SiCoop } from "react-icons/si";
export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.MARKETING.marketingDashboard.key,
  path: ROUTES.MARKETING.marketingDashboard.key,
  component: MarketingDashboard,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
  icon: MdDashboard,
  isSidebar: true,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.MARKETING.notification.key,
  path: ROUTES.MARKETING.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.user],
  icon: MdDashboard,
  //   isSidebar: true,
};

export const commissionAndRequirements: RouteItem = {
  name: "Commission and Requirements",
  id: ROUTES.MARKETING.commissionAndRequirements.key,
  path: ROUTES.MARKETING.commissionAndRequirements.key,
  component: CommissionAndRequirements,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
  icon: HiChevronRight,
};

function capitalizeFirstLetter(str: string): string {
  return str.slice(1).replace(/\b\w/g, (char) => char.toUpperCase());
}
const pageLocation = capitalizeFirstLetter(`${ROUTES.MARKETING.shares.key}`);
export const shares: RouteItem = {
  name: "Shares",
  id: ROUTES.MARKETING.shares.key,
  path: ROUTES.MARKETING.shares.key,
  component: Shares,
  props: { pageLocation },

  guard: AuthGuard,
  roles: [UserRoles.marketing],
  icon: HiChevronRight,
};

export const validation: RouteItem = {
  name: "Validation",
  id: ROUTES.MARKETING.validation.key,
  path: ROUTES.MARKETING.validation.key,
  component: Validation,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
  icon: MdOutlineFactCheck,
  isSidebar: true,
  children: [commissionAndRequirements, shares],
};

export const approval: RouteItem = {
  name: "Approval",
  id: ROUTES.MARKETING.viewProductProposal.key,
  path: ROUTES.MARKETING.viewProductProposal.key,
  component: Approval,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
  isSidebar: false,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.MARKETING.profile.key,
  path: ROUTES.MARKETING.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
};

export const proposal: RouteItem = {
  name: "Proposal",
  id: ROUTES.MARKETING.productProposal.key,
  path: ROUTES.MARKETING.productProposal.key,
  component: ProductProposal,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
  icon: GiNotebook,
  isSidebar: true,
};
export const viewProductProposalSales: RouteItem = {
  name: "View Proposal",
  id: ROUTES.MARKETING.viewProductProposalSales.key,
  path: ROUTES.MARKETING.viewProductProposalSales.key,
  component: ProposalView,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
  icon: GiNotebook,
  isSidebar: false,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.MARKETING.requestDashboard.key,
  path: ROUTES.MARKETING.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.MARKETING.requestForm.key,
  path: ROUTES.MARKETING.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.MARKETING.viewRequestForm.key,
  path: ROUTES.MARKETING.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
  isSidebar: false,
};
export const cooperative: RouteItem = {
  name: "Create Cooperative",
  id: ROUTES.MARKETING.cooperativesCreate.key,
  path: ROUTES.MARKETING.cooperativesCreate.key,
  component: CooperativeInformationForm,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
  icon: HiChevronRight,
  isSidebar: false,
};
export const cooperativeEdit: RouteItem = {
  name: "Edit Cooperative",
  id: ROUTES.MARKETING.cooperativesEdit.key,
  path: ROUTES.MARKETING.cooperativesEdit.key,
  component: CooperativeInformationForm,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
  icon: HiChevronRight,
  isSidebar: false,
};
export const cooperativeTable: RouteItem = {
  name: "Cooperatives",
  id: ROUTES.MARKETING.cooperativesTable.key,
  path: ROUTES.MARKETING.cooperativesTable.key,
  component: CooperativesTable,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
  icon: HiChevronRight,
  isSidebar: false,
};
export const coopUtilities: RouteItem = {
  name: "Coop Utilities",
  id: ROUTES.MARKETING.utilities.key,
  path: ROUTES.MARKETING.utilities.key,
  component: Utilities,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
  icon: SiCoop,
  isSidebar: true,
  children: [cooperativeTable],
};
export const marketingRoutes = [
  overview,
  validation,
  commissionAndRequirements,
  approval,
  shares,
  notification,
  profile,
  proposal,
  viewProductProposalSales,
  requestDashboard,
  requestForm,
  viewRequest,
  coopUtilities,
  cooperative,
  cooperativeTable,
  cooperativeEdit,
];
