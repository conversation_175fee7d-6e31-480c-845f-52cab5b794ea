import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import UATGuard from "@layouts/AuthGuard";
import ParentComponent from "@modules/admin/user-acceptance-test/uat-users/Components/parent";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import { MdDashboard } from "react-icons/md";

export const uatUser: RouteItem = {
  name: "uatUser",
  id: ROUTES.UATUSERS.uatUser.key,
  path: ROUTES.UATUSERS.uatUser.key,
  component: ParentComponent,
  guard: UATGuard,
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.UATUSERS.notification.key,
  path: ROUTES.UATUSERS.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.user],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.UATUSERS.profile.key,
  path: ROUTES.UATUSERS.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.user],
};
export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.UATUSERS.requestDashboard.key,
  path: ROUTES.UATUSERS.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.user],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.UATUSERS.requestForm.key,
  path: ROUTES.UATUSERS.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.user],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.UATUSERS.viewRequestForm.key,
  path: ROUTES.UATUSERS.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.user],
  isSidebar: false,
};

export const uatUserRoutes = [uatUser, notification, profile, requestDashboard, requestForm, viewRequest];
