import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.LEGAL_COUNSEL_IN_HOUSE.dashboard.key,
  path: ROUTES.LEGAL_COUNSEL_IN_HOUSE.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.legalCounselInHouse],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.LEGAL_COUNSEL_IN_HOUSE.requestDashboard.key,
  path: ROUTES.LEGAL_COUNSEL_IN_HOUSE.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.legalCounselInHouse],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.LEGAL_COUNSEL_IN_HOUSE.requestForm.key,
  path: ROUTES.LEGAL_COUNSEL_IN_HOUSE.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.legalCounselInHouse],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.LEGAL_COUNSEL_IN_HOUSE.viewRequestForm.key,
  path: ROUTES.LEGAL_COUNSEL_IN_HOUSE.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.legalCounselInHouse],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.LEGAL_COUNSEL_IN_HOUSE.notification.key,
  path: ROUTES.LEGAL_COUNSEL_IN_HOUSE.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.legalCounselInHouse],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.LEGAL_COUNSEL_IN_HOUSE.profile.key,
  path: ROUTES.LEGAL_COUNSEL_IN_HOUSE.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.legalCounselInHouse],
};

export const legalCounselInHouseRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];