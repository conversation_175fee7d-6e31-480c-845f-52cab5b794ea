import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.POLICY_ISSUANCE_CLERK.dashboard.key,
  path: ROUTES.POLICY_ISSUANCE_CLERK.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.policyIssuanceClerk],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.POLICY_ISSUANCE_CLERK.requestDashboard.key,
  path: ROUTES.POLICY_ISSUANCE_CLERK.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.policyIssuanceClerk],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.POLICY_ISSUANCE_CLERK.requestForm.key,
  path: ROUTES.POLICY_ISSUANCE_CLERK.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.policyIssuanceClerk],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.POLICY_ISSUANCE_CLERK.viewRequestForm.key,
  path: ROUTES.POLICY_ISSUANCE_CLERK.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.policyIssuanceClerk],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.POLICY_ISSUANCE_CLERK.notification.key,
  path: ROUTES.POLICY_ISSUANCE_CLERK.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.policyIssuanceClerk],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.POLICY_ISSUANCE_CLERK.profile.key,
  path: ROUTES.POLICY_ISSUANCE_CLERK.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.policyIssuanceClerk],
};

export const policyIssuanceClerkRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];