import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.MEMBER_RELATIONS_ASSISTANT_2_LIFE.dashboard.key,
  path: ROUTES.MEMBER_RELATIONS_ASSISTANT_2_LIFE.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.memberRelationsAssistant2Life],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.MEMBER_RELATIONS_ASSISTANT_2_LIFE.requestDashboard.key,
  path: ROUTES.MEMBER_RELATIONS_ASSISTANT_2_LIFE.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.memberRelationsAssistant2Life],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.MEMBER_RELATIONS_ASSISTANT_2_LIFE.requestForm.key,
  path: ROUTES.MEMBER_RELATIONS_ASSISTANT_2_LIFE.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.memberRelationsAssistant2Life],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.MEMBER_RELATIONS_ASSISTANT_2_LIFE.viewRequestForm.key,
  path: ROUTES.MEMBER_RELATIONS_ASSISTANT_2_LIFE.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.memberRelationsAssistant2Life],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.MEMBER_RELATIONS_ASSISTANT_2_LIFE.notification.key,
  path: ROUTES.MEMBER_RELATIONS_ASSISTANT_2_LIFE.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.memberRelationsAssistant2Life],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.MEMBER_RELATIONS_ASSISTANT_2_LIFE.profile.key,
  path: ROUTES.MEMBER_RELATIONS_ASSISTANT_2_LIFE.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.memberRelationsAssistant2Life],
};

export const memberRelationsAssistant2LifeRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];