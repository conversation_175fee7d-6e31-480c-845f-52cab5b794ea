import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.MANAGER_AGENCY_DISTRIBUTION_CHANNEL.dashboard.key,
  path: ROUTES.MANAGER_AGENCY_DISTRIBUTION_CHANNEL.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.managerAgencyDistributionChannel],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.MANAGER_AGENCY_DISTRIBUTION_CHANNEL.requestDashboard.key,
  path: ROUTES.MANAGER_AGENCY_DISTRIBUTION_CHANNEL.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.managerAgencyDistributionChannel],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.MANAGER_AGENCY_DISTRIBUTION_CHANNEL.requestForm.key,
  path: ROUTES.MANAGER_AGENCY_DISTRIBUTION_CHANNEL.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.managerAgencyDistributionChannel],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.MANAGER_AGENCY_DISTRIBUTION_CHANNEL.viewRequestForm.key,
  path: ROUTES.MANAGER_AGENCY_DISTRIBUTION_CHANNEL.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.managerAgencyDistributionChannel],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.MANAGER_AGENCY_DISTRIBUTION_CHANNEL.notification.key,
  path: ROUTES.MANAGER_AGENCY_DISTRIBUTION_CHANNEL.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.managerAgencyDistributionChannel],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.MANAGER_AGENCY_DISTRIBUTION_CHANNEL.profile.key,
  path: ROUTES.MANAGER_AGENCY_DISTRIBUTION_CHANNEL.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.managerAgencyDistributionChannel],
};

export const managerAgencyDistributionChannelRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];