import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import Revisions from "@modules/admin/product-revisions";
import ReviewRevision from "@modules/admin/product-revisions/components/Review/ReviewRevision";
import Products from "@modules/admin/products";
import CreateProductForm from "@modules/admin/products/components/Forms/CreateProductForm";
import EditProductForm from "@modules/admin/products/components/Forms/EditProductForm";
import EditProductRevision from "@modules/admin/products/components/Forms/EditProductRevision";
import RNDDashboard from "@modules/dashboard/RNDDashboard";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import { LuFileSpreadsheet } from "react-icons/lu";
import { MdDashboard } from "react-icons/md";
import ProductProposal from "@modules/sales/product-proposal";
import { GiNotebook } from "react-icons/gi";
import Approval from "@modules/sales/product-proposal/Approval";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.RESEARCHANDDEVELOPMENT.dashboard.key,
  path: ROUTES.RESEARCHANDDEVELOPMENT.dashboard.key,
  component: RNDDashboard,
  guard: AuthGuard,
  roles: [UserRoles.rnd],
  icon: MdDashboard,
  isSidebar: true,
};

export const products: RouteItem = {
  name: "Products",
  id: ROUTES.RESEARCHANDDEVELOPMENT.products.key,
  path: ROUTES.RESEARCHANDDEVELOPMENT.products.key,
  component: Products,
  guard: AuthGuard,
  roles: [UserRoles.rnd],
  icon: LuFileSpreadsheet,
  isSidebar: true,
};

export const revisions: RouteItem = {
  name: "Product Revisions",
  id: ROUTES.RESEARCHANDDEVELOPMENT.revisions.key,
  path: ROUTES.RESEARCHANDDEVELOPMENT.revisions.key,
  component: Revisions,
  guard: AuthGuard,
  roles: [UserRoles.rnd],
  icon: LuFileSpreadsheet,
};
export const proposal: RouteItem = {
  name: "Proposal",
  id: ROUTES.RESEARCHANDDEVELOPMENT.productProposal.key,
  path: ROUTES.RESEARCHANDDEVELOPMENT.productProposal.key,
  component: ProductProposal,
  guard: AuthGuard,
  roles: [UserRoles.rnd],
  icon: GiNotebook,
  isSidebar: true,
};
export const viewProposal: RouteItem = {
  name: "View Proposal",
  id: ROUTES.RESEARCHANDDEVELOPMENT.viewProductProposal.key,
  path: ROUTES.RESEARCHANDDEVELOPMENT.viewProductProposal.key,
  component: Approval,
  guard: AuthGuard,
  roles: [UserRoles.rnd],
  icon: GiNotebook,
  isSidebar: false,
};

export const reviewRevisions: RouteItem = {
  name: "Review Product Revisions",
  id: ROUTES.RESEARCHANDDEVELOPMENT.reviewRevisions.key,
  path: ROUTES.RESEARCHANDDEVELOPMENT.reviewRevisions.key,
  component: ReviewRevision,
  guard: AuthGuard,
  roles: [UserRoles.rnd],
  icon: LuFileSpreadsheet,
};

export const editProductRevision: RouteItem = {
  name: "Edit Product Revision",
  id: ROUTES.RESEARCHANDDEVELOPMENT.editRevision.key,
  path: ROUTES.RESEARCHANDDEVELOPMENT.editRevision.key,
  component: EditProductRevision,
  guard: AuthGuard,
  roles: [UserRoles.rnd],
  isSidebar: false,
};

export const cloneProductGuideline: RouteItem = {
  name: "Clone Product Guidelines",
  id: ROUTES.RESEARCHANDDEVELOPMENT.cloneProductGuideline.key,
  path: ROUTES.RESEARCHANDDEVELOPMENT.cloneProductGuideline.key,
  component: EditProductRevision,
  guard: AuthGuard,
  roles: [UserRoles.rnd],
  isSidebar: false,
};

export const editGuidelines: RouteItem = {
  name: "Edit Product Guidelines",
  id: ROUTES.RESEARCHANDDEVELOPMENT.editGuidelines.key,
  path: ROUTES.RESEARCHANDDEVELOPMENT.editGuidelines.key,
  component: EditProductForm,
  guard: AuthGuard,
  roles: [UserRoles.rnd],
  isSidebar: false,
};
export const createGuidelines: RouteItem = {
  name: "Create Product Guidelines",
  id: ROUTES.RESEARCHANDDEVELOPMENT.createGuidelines.key,
  path: ROUTES.RESEARCHANDDEVELOPMENT.createGuidelines.key,
  component: CreateProductForm,
  guard: AuthGuard,
  roles: [UserRoles.rnd],
  isSidebar: false,
};

export const cloneProduct: RouteItem = {
  name: "Clone Product",
  id: ROUTES.RESEARCHANDDEVELOPMENT.cloneProduct.key,
  path: ROUTES.RESEARCHANDDEVELOPMENT.cloneProduct.key,
  component: CreateProductForm,
  guard: AuthGuard,
  roles: [UserRoles.rnd],
  isSidebar: false,
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.RESEARCHANDDEVELOPMENT.notification.key,
  path: ROUTES.RESEARCHANDDEVELOPMENT.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.rnd],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.RESEARCHANDDEVELOPMENT.profile.key,
  path: ROUTES.RESEARCHANDDEVELOPMENT.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.rnd],
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.RESEARCHANDDEVELOPMENT.requestForm.key,
  path: ROUTES.RESEARCHANDDEVELOPMENT.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.rnd],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.RESEARCHANDDEVELOPMENT.viewRequestForm.key,
  path: ROUTES.RESEARCHANDDEVELOPMENT.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.rnd],
  isSidebar: false,
};
export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.RESEARCHANDDEVELOPMENT.requestDashboard.key,
  path: ROUTES.RESEARCHANDDEVELOPMENT.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.rnd],
  icon: MdDashboard,
  isSidebar: true,
};

export const rndRoutes = [
  overview,
  products,
  revisions,
  reviewRevisions,
  editProductRevision,
  cloneProductGuideline,
  cloneProduct,
  editGuidelines,
  createGuidelines,
  notification,
  proposal,
  viewProposal,
  profile,
  requestForm,
  viewRequest,
  requestDashboard,
];
