import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.INTERNAL_AUDITOR.dashboard.key,
  path: ROUTES.INTERNAL_AUDITOR.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.internalAuditor],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.INTERNAL_AUDITOR.requestDashboard.key,
  path: ROUTES.INTERNAL_AUDITOR.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.internalAuditor],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.INTERNAL_AUDITOR.requestForm.key,
  path: ROUTES.INTERNAL_AUDITOR.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.internalAuditor],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.INTERNAL_AUDITOR.viewRequestForm.key,
  path: ROUTES.INTERNAL_AUDITOR.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.internalAuditor],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.INTERNAL_AUDITOR.notification.key,
  path: ROUTES.INTERNAL_AUDITOR.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.internalAuditor],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.INTERNAL_AUDITOR.profile.key,
  path: ROUTES.INTERNAL_AUDITOR.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.internalAuditor],
};

export const internalAuditorRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];