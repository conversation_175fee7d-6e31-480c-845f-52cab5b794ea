import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.FIRE_MARSHALL.dashboard.key,
  path: ROUTES.FIRE_MARSHALL.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.fireMarshall],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.FIRE_MARSHALL.requestDashboard.key,
  path: ROUTES.FIRE_MARSHALL.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.fireMarshall],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.FIRE_MARSHALL.requestForm.key,
  path: ROUTES.FIRE_MARSHALL.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.fireMarshall],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.FIRE_MARSHALL.viewRequestForm.key,
  path: ROUTES.FIRE_MARSHALL.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.fireMarshall],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.FIRE_MARSHALL.notification.key,
  path: ROUTES.FIRE_MARSHALL.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.fireMarshall],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.FIRE_MARSHALL.profile.key,
  path: ROUTES.FIRE_MARSHALL.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.fireMarshall],
};

export const fireMarshallRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];