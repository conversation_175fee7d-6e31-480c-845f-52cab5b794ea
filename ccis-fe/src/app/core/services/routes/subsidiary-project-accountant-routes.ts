import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.SUBSIDIARY_PROJECT_ACCOUNTANT.dashboard.key,
  path: ROUTES.SUBSIDIARY_PROJECT_ACCOUNTANT.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.subsidiaryProjectAccountant],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.SUBSIDIARY_PROJECT_ACCOUNTANT.requestDashboard.key,
  path: ROUTES.SUBSIDIARY_PROJECT_ACCOUNTANT.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.subsidiaryProjectAccountant],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.SUBSIDIARY_PROJECT_ACCOUNTANT.requestForm.key,
  path: ROUTES.SUBSIDIARY_PROJECT_ACCOUNTANT.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.subsidiaryProjectAccountant],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.SUBSIDIARY_PROJECT_ACCOUNTANT.viewRequestForm.key,
  path: ROUTES.SUBSIDIARY_PROJECT_ACCOUNTANT.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.subsidiaryProjectAccountant],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.SUBSIDIARY_PROJECT_ACCOUNTANT.notification.key,
  path: ROUTES.SUBSIDIARY_PROJECT_ACCOUNTANT.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.subsidiaryProjectAccountant],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.SUBSIDIARY_PROJECT_ACCOUNTANT.profile.key,
  path: ROUTES.SUBSIDIARY_PROJECT_ACCOUNTANT.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.subsidiaryProjectAccountant],
};

export const subsidiaryProjectAccountantRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];