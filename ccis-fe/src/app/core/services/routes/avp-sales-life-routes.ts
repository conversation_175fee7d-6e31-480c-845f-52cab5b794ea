import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.AVP_SALES_LIFE.dashboard.key,
  path: ROUTES.AVP_SALES_LIFE.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.avpSalesLife],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.AVP_SALES_LIFE.requestDashboard.key,
  path: ROUTES.AVP_SALES_LIFE.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.avpSalesLife],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.AVP_SALES_LIFE.requestForm.key,
  path: ROUTES.AVP_SALES_LIFE.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.avpSalesLife],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.AVP_SALES_LIFE.viewRequestForm.key,
  path: ROUTES.AVP_SALES_LIFE.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.avpSalesLife],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.AVP_SALES_LIFE.notification.key,
  path: ROUTES.AVP_SALES_LIFE.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.avpSalesLife],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.AVP_SALES_LIFE.profile.key,
  path: ROUTES.AVP_SALES_LIFE.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.avpSalesLife],
};

export const avpSalesLifeRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];