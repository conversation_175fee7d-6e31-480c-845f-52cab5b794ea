import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import IncomingOutgoingCashierDashboard from "@modules/dashboard/IncomingOutgoingCashierDashboard.tsx";
import { FaClipboardList, FaFile } from "react-icons/fa";
import { TbMessageForward } from "react-icons/tb";
import CACNewForms from "@modules/cac";
import ViewFormReceiving from "@modules/cac/components/viewFormReceiving";
import RequestPads from "@modules/cac/request-pads";
import InventoryTab from "@modules/cac/inventory";
import TransmittalReturnedForm from "@modules/cac/components/form/transmittal-returned-form";
import PRTable from "@modules/cac/components/PRTable";
import IssuePRForm from "@modules/cac/components/form/issue-pr-form";
import ViewPR from "@modules/cac/components/form/view-pr";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import ViewReturnedForm from "@components/template/Forms/UsedForms/ViewReturnedForm";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.CAC.cacDashboard.key,
  path: ROUTES.CAC.cacDashboard.key,
  component: IncomingOutgoingCashierDashboard,
  guard: AuthGuard,
  roles: [UserRoles.cac],
  icon: MdDashboard,
  isSidebar: true,
};
export const newForm: RouteItem = {
  name: "New Form",
  id: ROUTES.CAC.cacNewForm.key,
  path: ROUTES.CAC.cacNewForm.key,
  component: CACNewForms,
  guard: AuthGuard,
  roles: [UserRoles.cac],
  icon: FaFile,
  isSidebar: true,
};

export const inventory: RouteItem = {
  name: "Inventory",
  id: ROUTES.CAC.cacInventory.key,
  path: ROUTES.CAC.cacInventory.key,
  component: InventoryTab,
  guard: AuthGuard,
  roles: [UserRoles.cac],
  icon: FaClipboardList,
  isSidebar: true,
};

export const viewForReceivingForm: RouteItem = {
  name: "View For Receiving Form",
  id: ROUTES.CAC.viewForReceivingForm.key,
  path: ROUTES.CAC.viewForReceivingForm.key,
  component: ViewFormReceiving,
  guard: AuthGuard,
  roles: [UserRoles.cac],
  isSidebar: false,
};

export const requestPads: RouteItem = {
  name: "Request Pads",
  id: ROUTES.CAC.requestPads.key,
  path: ROUTES.CAC.requestPads.key,
  component: RequestPads,
  guard: AuthGuard,
  roles: [UserRoles.cac],
  icon: TbMessageForward,
  isSidebar: true,
};

export const transmittalReturnedForm: RouteItem = {
  name: "Request Pads",
  id: ROUTES.CAC.transmittalReturnedForm.key,
  path: ROUTES.CAC.transmittalReturnedForm.key,
  component: TransmittalReturnedForm,
  guard: AuthGuard,
  roles: [UserRoles.cac],
};

export const viewPrTable: RouteItem = {
  name: "View PR Table",
  id: ROUTES.CAC.viewPrTable.key,
  path: ROUTES.CAC.viewPrTable.key,
  component: PRTable,
  guard: AuthGuard,
  roles: [UserRoles.cac],
  isSidebar: false,
};

export const issuePRForm: RouteItem = {
  name: "Issue PR Form",
  id: ROUTES.CAC.issuePRForm.key,
  path: ROUTES.CAC.issuePRForm.key,
  component: IssuePRForm,
  guard: AuthGuard,
  roles: [UserRoles.cac],
  isSidebar: false,
};

export const viewPR: RouteItem = {
  name: "View Issued/Cancelled PR",
  id: ROUTES.CAC.viewPR.key,
  path: ROUTES.CAC.viewPR.key,
  component: ViewPR,
  guard: AuthGuard,
  roles: [UserRoles.cac],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.CAC.notification.key,
  path: ROUTES.CAC.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.cac],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.CAC.profile.key,
  path: ROUTES.CAC.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.cac],
};

export const viewReleasedForm: RouteItem = {
  name: "View Returned Form",
  id: ROUTES.CAC.viewReturnedForm.key,
  path: ROUTES.CAC.viewReturnedForm.key,
  component: ViewReturnedForm,
  guard: AuthGuard,
  roles: [UserRoles.cac],
  isSidebar: false,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.CAC.requestForm.key,
  path: ROUTES.CAC.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.cac],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.CAC.viewRequestForm.key,
  path: ROUTES.CAC.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.cac],
  isSidebar: false,
};
export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.CAC.requestDashboard.key,
  path: ROUTES.CAC.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.cac],
  icon: MdDashboard,
  isSidebar: true,
};

export const cacRoutes = [
  overview,
  newForm,
  inventory,
  viewForReceivingForm,
  requestPads,
  transmittalReturnedForm,
  viewPrTable,
  issuePRForm,
  viewPR,
  notification,
  viewReleasedForm,
  profile,
  requestForm,
  viewRequest,
  requestDashboard,
];
