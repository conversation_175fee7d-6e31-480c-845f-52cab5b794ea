import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.PROPERTY_CUSTODIAN.dashboard.key,
  path: ROUTES.PROPERTY_CUSTODIAN.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.propertyCustodian],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.PROPERTY_CUSTODIAN.requestDashboard.key,
  path: ROUTES.PROPERTY_CUSTODIAN.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.propertyCustodian],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.PROPERTY_CUSTODIAN.requestForm.key,
  path: ROUTES.PROPERTY_CUSTODIAN.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.propertyCustodian],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.PROPERTY_CUSTODIAN.viewRequestForm.key,
  path: ROUTES.PROPERTY_CUSTODIAN.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.propertyCustodian],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.PROPERTY_CUSTODIAN.notification.key,
  path: ROUTES.PROPERTY_CUSTODIAN.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.propertyCustodian],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.PROPERTY_CUSTODIAN.profile.key,
  path: ROUTES.PROPERTY_CUSTODIAN.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.propertyCustodian],
};

export const propertyCustodianRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];