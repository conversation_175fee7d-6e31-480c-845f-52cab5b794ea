import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.VP_AGENCY_DISTRIBUTION_CHANNEL_MANAGEMENT.dashboard.key,
  path: ROUTES.VP_AGENCY_DISTRIBUTION_CHANNEL_MANAGEMENT.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.vpAgencyDistributionChannelManagement],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.VP_AGENCY_DISTRIBUTION_CHANNEL_MANAGEMENT.requestDashboard.key,
  path: ROUTES.VP_AGENCY_DISTRIBUTION_CHANNEL_MANAGEMENT.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.vpAgencyDistributionChannelManagement],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.VP_AGENCY_DISTRIBUTION_CHANNEL_MANAGEMENT.requestForm.key,
  path: ROUTES.VP_AGENCY_DISTRIBUTION_CHANNEL_MANAGEMENT.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.vpAgencyDistributionChannelManagement],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.VP_AGENCY_DISTRIBUTION_CHANNEL_MANAGEMENT.viewRequestForm.key,
  path: ROUTES.VP_AGENCY_DISTRIBUTION_CHANNEL_MANAGEMENT.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.vpAgencyDistributionChannelManagement],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.VP_AGENCY_DISTRIBUTION_CHANNEL_MANAGEMENT.notification.key,
  path: ROUTES.VP_AGENCY_DISTRIBUTION_CHANNEL_MANAGEMENT.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.vpAgencyDistributionChannelManagement],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.VP_AGENCY_DISTRIBUTION_CHANNEL_MANAGEMENT.profile.key,
  path: ROUTES.VP_AGENCY_DISTRIBUTION_CHANNEL_MANAGEMENT.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.vpAgencyDistributionChannelManagement],
};

export const vpAgencyDistributionChannelManagementRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];