import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import IncomingCashierDashboard from "@modules/dashboard/IncomingOutgoingCashierDashboard.tsx";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
// import { PiCertificate } from "react-icons/pi";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.INCOMINGCASHIER.incomingCashierDashboard.key,
  path: ROUTES.INCOMINGCASHIER.incomingCashierDashboard.key,
  component: IncomingCashierDashboard,
  guard: AuthGuard,
  roles: [UserRoles.incomingCashier],
  icon: MdDashboard,
  isSidebar: true,
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.INCOMINGCASHIER.notification.key,
  path: ROUTES.INCOMINGCASHIER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.incomingCashier],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.INCOMINGCASHIER.profile.key,
  path: ROUTES.INCOMINGCASHIER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.incomingCashier],
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.INCOMINGCASHIER.requestForm.key,
  path: ROUTES.INCOMINGCASHIER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.accounting],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.INCOMINGCASHIER.viewRequestForm.key,
  path: ROUTES.INCOMINGCASHIER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.accounting],
  isSidebar: false,
};
export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.INCOMINGCASHIER.requestDashboard.key,
  path: ROUTES.INCOMINGCASHIER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.accounting],
  icon: MdDashboard,
  isSidebar: true,
};

export const IncomingCashierRoutes = [overview, notification, profile, requestDashboard, requestForm, viewRequest];
