import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import IncomingOutgoingCashierDashboard from "@modules/dashboard/IncomingOutgoingCashierDashboard.tsx";
import { FaClipboardList, FaFile } from "react-icons/fa";
import { TbMessageForward } from "react-icons/tb";
import GamNewForms from "@modules/gam";
import ViewFormReceiving from "@modules/gam/components/viewFormReceiving";
import RequestPads from "@modules/gam/request-pads";
import InventoryTab from "@modules/gam/inventory";
import TransmittalReturnedForm from "@modules/gam/components/form/transmittal-returned-form";
import PRTable from "@modules/gam/components/PRTable";
import IssuePRForm from "@modules/gam/components/form/issue-pr-form";
import ViewPR from "@modules/gam/components/form/view-pr";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import ViewReturnedForm from "@components/template/Forms/UsedForms/ViewReturnedForm";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.GAM.gamDashboard.key,
  path: ROUTES.GAM.gamDashboard.key,
  component: IncomingOutgoingCashierDashboard,
  guard: AuthGuard,
  roles: [UserRoles.gam],
  icon: MdDashboard,
  isSidebar: true,
};
export const newForm: RouteItem = {
  name: "New Form",
  id: ROUTES.GAM.gamAdminNewForm.key,
  path: ROUTES.GAM.gamAdminNewForm.key,
  component: GamNewForms,
  guard: AuthGuard,
  roles: [UserRoles.gam],
  icon: FaFile,
  isSidebar: true,
};

export const inventory: RouteItem = {
  name: "Inventory",
  id: ROUTES.GAM.gamInventory.key,
  path: ROUTES.GAM.gamInventory.key,
  component: InventoryTab,
  guard: AuthGuard,
  roles: [UserRoles.gam],
  icon: FaClipboardList,
  isSidebar: true,
};

export const viewForReceivingForm: RouteItem = {
  name: "View For Receiving Form",
  id: ROUTES.GAM.viewForReceivingForm.key,
  path: ROUTES.GAM.viewForReceivingForm.key,
  component: ViewFormReceiving,
  guard: AuthGuard,
  roles: [UserRoles.gam],
  isSidebar: false,
};

export const requestPads: RouteItem = {
  name: "Request Pads",
  id: ROUTES.GAM.requestPads.key,
  path: ROUTES.GAM.requestPads.key,
  component: RequestPads,
  guard: AuthGuard,
  roles: [UserRoles.gam],
  icon: TbMessageForward,
  isSidebar: true,
};

export const transmittalReturnedForm: RouteItem = {
  name: "Request Pads",
  id: ROUTES.GAM.transmittalReturnedForm.key,
  path: ROUTES.GAM.transmittalReturnedForm.key,
  component: TransmittalReturnedForm,
  guard: AuthGuard,
  roles: [UserRoles.gam],
};

export const viewPrTable: RouteItem = {
  name: "View PR Table",
  id: ROUTES.GAM.viewPrTable.key,
  path: ROUTES.GAM.viewPrTable.key,
  component: PRTable,
  guard: AuthGuard,
  roles: [UserRoles.gam],
  isSidebar: false,
};

export const issuePRForm: RouteItem = {
  name: "Issue PR Form",
  id: ROUTES.GAM.issuePRForm.key,
  path: ROUTES.GAM.issuePRForm.key,
  component: IssuePRForm,
  guard: AuthGuard,
  roles: [UserRoles.gam],
  isSidebar: false,
};

export const viewPR: RouteItem = {
  name: "View Issued/Cancelled PR",
  id: ROUTES.GAM.viewPR.key,
  path: ROUTES.GAM.viewPR.key,
  component: ViewPR,
  guard: AuthGuard,
  roles: [UserRoles.gam],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.GAM.notification.key,
  path: ROUTES.GAM.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.gam],
  icon: MdDashboard,
  //   isSidebar: true,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.GAM.profile.key,
  path: ROUTES.GAM.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.gam],
};

export const viewReleasedForm: RouteItem = {
  name: "View Returned Form",
  id: ROUTES.GAM.viewReturnedForm.key,
  path: ROUTES.GAM.viewReturnedForm.key,
  component: ViewReturnedForm,
  guard: AuthGuard,
  roles: [UserRoles.gam],
  isSidebar: false,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.GAM.requestForm.key,
  path: ROUTES.GAM.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.gam],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.GAM.viewRequestForm.key,
  path: ROUTES.GAM.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.gam],
  isSidebar: false,
};
export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.GAM.requestDashboard.key,
  path: ROUTES.GAM.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.gam],
  icon: MdDashboard,
  isSidebar: true,
};

export const gamRoutes = [overview, newForm, inventory, viewForReceivingForm, requestPads, transmittalReturnedForm, viewPrTable, issuePRForm, viewPR, notification, viewReleasedForm, profile, requestForm, viewRequest, requestDashboard];
