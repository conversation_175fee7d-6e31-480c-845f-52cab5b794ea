import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.ADMIN_ASST_OUTGOING.dashboard.key,
  path: ROUTES.ADMIN_ASST_OUTGOING.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.adminAsstOutgoing],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.ADMIN_ASST_OUTGOING.requestDashboard.key,
  path: ROUTES.ADMIN_ASST_OUTGOING.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.adminAsstOutgoing],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.ADMIN_ASST_OUTGOING.requestForm.key,
  path: ROUTES.ADMIN_ASST_OUTGOING.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.adminAsstOutgoing],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.ADMIN_ASST_OUTGOING.viewRequestForm.key,
  path: ROUTES.ADMIN_ASST_OUTGOING.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.adminAsstOutgoing],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.ADMIN_ASST_OUTGOING.notification.key,
  path: ROUTES.ADMIN_ASST_OUTGOING.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.adminAsstOutgoing],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.ADMIN_ASST_OUTGOING.profile.key,
  path: ROUTES.ADMIN_ASST_OUTGOING.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.adminAsstOutgoing],
};

export const adminAsstOutgoingRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];