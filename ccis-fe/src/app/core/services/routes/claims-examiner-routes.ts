import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.CLAIMS_EXAMINER.dashboard.key,
  path: ROUTES.CLAIMS_EXAMINER.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.claimsExaminer],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.CLAIMS_EXAMINER.requestDashboard.key,
  path: ROUTES.CLAIMS_EXAMINER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.claimsExaminer],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.CLAIMS_EXAMINER.requestForm.key,
  path: ROUTES.CLAIMS_EXAMINER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.claimsExaminer],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.CLAIMS_EXAMINER.viewRequestForm.key,
  path: ROUTES.CLAIMS_EXAMINER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.claimsExaminer],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.CLAIMS_EXAMINER.notification.key,
  path: ROUTES.CLAIMS_EXAMINER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.claimsExaminer],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.CLAIMS_EXAMINER.profile.key,
  path: ROUTES.CLAIMS_EXAMINER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.claimsExaminer],
};

export const claimsExaminerRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];