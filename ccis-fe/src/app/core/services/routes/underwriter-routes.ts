import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
// import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import IssuanceViewProposal from "@modules/underwriting/master-policy/issuance/components/Approval";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.UNDERWRITER.dashboard.key,
  path: ROUTES.UNDERWRITER.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.underwriter],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.UNDERWRITER.requestDashboard.key,
  path: ROUTES.UNDERWRITER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.underwriter],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.UNDERWRITER.requestForm.key,
  path: ROUTES.UNDERWRITER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.underwriter],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.UNDERWRITER.viewRequestForm.key,
  path: ROUTES.UNDERWRITER.viewRequestForm.key,
  component: IssuanceViewProposal,
  guard: AuthGuard,
  roles: [UserRoles.underwriter],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.UNDERWRITER.notification.key,
  path: ROUTES.UNDERWRITER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.underwriter],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.UNDERWRITER.profile.key,
  path: ROUTES.UNDERWRITER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.underwriter],
};

export const underwriterRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];