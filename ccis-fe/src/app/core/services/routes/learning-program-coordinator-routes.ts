import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.LEARNING_PROGRAM_COORDINATOR.dashboard.key,
  path: ROUTES.LEARNING_PROGRAM_COORDINATOR.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.learningProgramCoordinator],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.LEARNING_PROGRAM_COORDINATOR.requestDashboard.key,
  path: ROUTES.LEARNING_PROGRAM_COORDINATOR.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.learningProgramCoordinator],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.LEARNING_PROGRAM_COORDINATOR.requestForm.key,
  path: ROUTES.LEARNING_PROGRAM_COORDINATOR.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.learningProgramCoordinator],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.LEARNING_PROGRAM_COORDINATOR.viewRequestForm.key,
  path: ROUTES.LEARNING_PROGRAM_COORDINATOR.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.learningProgramCoordinator],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.LEARNING_PROGRAM_COORDINATOR.notification.key,
  path: ROUTES.LEARNING_PROGRAM_COORDINATOR.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.learningProgramCoordinator],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.LEARNING_PROGRAM_COORDINATOR.profile.key,
  path: ROUTES.LEARNING_PROGRAM_COORDINATOR.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.learningProgramCoordinator],
};

export const learningProgramCoordinatorRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];