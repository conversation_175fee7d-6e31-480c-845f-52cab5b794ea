import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

import RequestTypes from "@modules/admin/departmental-ticketing-utilities/request-types";
import ApplicationUtility from "@modules/admin/departmental-ticketing-utilities/applications";
import DevicesSystems from "@modules/admin/departmental-ticketing-utilities/devices-systems";
import OperatingSystems from "@modules/admin/departmental-ticketing-utilities/operating-systems";
import TicketUtilities from "@modules/admin/departmental-ticketing-utilities";
import { HiChevronRight } from "react-icons/hi2";
import { FaBookmark } from "react-icons/fa";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.COMPLIANCE_MANAGER_NONLIFE.dashboard.key,
  path: ROUTES.COMPLIANCE_MANAGER_NONLIFE.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.complianceManagerNonlife],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.COMPLIANCE_MANAGER_NONLIFE.requestDashboard.key,
  path: ROUTES.COMPLIANCE_MANAGER_NONLIFE.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.complianceManagerNonlife],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.COMPLIANCE_MANAGER_NONLIFE.requestForm.key,
  path: ROUTES.COMPLIANCE_MANAGER_NONLIFE.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.complianceManagerNonlife],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.COMPLIANCE_MANAGER_NONLIFE.viewRequestForm.key,
  path: ROUTES.COMPLIANCE_MANAGER_NONLIFE.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.complianceManagerNonlife],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.COMPLIANCE_MANAGER_NONLIFE.notification.key,
  path: ROUTES.COMPLIANCE_MANAGER_NONLIFE.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.complianceManagerNonlife],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.COMPLIANCE_MANAGER_NONLIFE.profile.key,
  path: ROUTES.COMPLIANCE_MANAGER_NONLIFE.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.complianceManagerNonlife],
};

export const requestTypeUtilities: RouteItem = {
  name: "Request Type",
  id: ROUTES.COMPLIANCE_MANAGER_NONLIFE.requestTypesUtility.key,
  path: ROUTES.COMPLIANCE_MANAGER_NONLIFE.requestTypesUtility.key,
  component: RequestTypes,
  guard: AuthGuard,
  roles: [UserRoles.complianceManagerNonlife],
  icon: HiChevronRight,
  isSidebar: false,
};

export const applicationUtilities: RouteItem = {
  name: "Applications",
  id: ROUTES.COMPLIANCE_MANAGER_NONLIFE.applicationsUtility.key,
  path: ROUTES.COMPLIANCE_MANAGER_NONLIFE.applicationsUtility.key,
  component: ApplicationUtility,
  guard: AuthGuard,
  roles: [UserRoles.complianceManagerNonlife],
  icon: HiChevronRight,
  isSidebar: false,
};

export const devicesUtilities: RouteItem = {
  name: "Devices/Systems",
  id: ROUTES.COMPLIANCE_MANAGER_NONLIFE.devicesSystemUtility.key,
  path: ROUTES.COMPLIANCE_MANAGER_NONLIFE.devicesSystemUtility.key,
  component: DevicesSystems,
  guard: AuthGuard,
  roles: [UserRoles.complianceManagerNonlife],
  icon: HiChevronRight,
  isSidebar: false,
};

export const operatingSystemsUtilities: RouteItem = {
  name: "Operating Systems",
  id: ROUTES.COMPLIANCE_MANAGER_NONLIFE.operatingSystemsUtility.key,
  path: ROUTES.COMPLIANCE_MANAGER_NONLIFE.operatingSystemsUtility.key,
  component: OperatingSystems,
  guard: AuthGuard,
  roles: [UserRoles.complianceManagerNonlife],
  icon: HiChevronRight,
  isSidebar: false,
};

export const ticketUtilities: RouteItem = {
  name: "Ticket Utilities",
  id: ROUTES.COMPLIANCE_MANAGER_NONLIFE.ticketUtilities.key,
  path: ROUTES.COMPLIANCE_MANAGER_NONLIFE.ticketUtilities.key,
  component: TicketUtilities,
  guard: AuthGuard,
  roles: [UserRoles.complianceManagerNonlife],
  icon: FaBookmark,
  isSidebar: true,
  children: [requestTypeUtilities, applicationUtilities, devicesUtilities, operatingSystemsUtilities],
};

export const complianceManagerNonlifeRoutes = [
  overview,
  requestDashboard,
  requestForm,
  viewRequest,
  notification,
  profile,
  ticketUtilities,
  requestTypeUtilities,
  applicationUtilities,
  devicesUtilities,
  operatingSystemsUtilities,
];
