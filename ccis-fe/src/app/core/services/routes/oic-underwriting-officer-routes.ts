import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.OIC_UNDERWRITING_OFFICER.dashboard.key,
  path: ROUTES.OIC_UNDERWRITING_OFFICER.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.oicUnderwritingOfficer],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.OIC_UNDERWRITING_OFFICER.requestDashboard.key,
  path: ROUTES.OIC_UNDERWRITING_OFFICER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.oicUnderwritingOfficer],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.OIC_UNDERWRITING_OFFICER.requestForm.key,
  path: ROUTES.OIC_UNDERWRITING_OFFICER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.oicUnderwritingOfficer],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.OIC_UNDERWRITING_OFFICER.viewRequestForm.key,
  path: ROUTES.OIC_UNDERWRITING_OFFICER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.oicUnderwritingOfficer],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.OIC_UNDERWRITING_OFFICER.notification.key,
  path: ROUTES.OIC_UNDERWRITING_OFFICER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.oicUnderwritingOfficer],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.OIC_UNDERWRITING_OFFICER.profile.key,
  path: ROUTES.OIC_UNDERWRITING_OFFICER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.oicUnderwritingOfficer],
};

export const oicUnderwritingOfficerRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];