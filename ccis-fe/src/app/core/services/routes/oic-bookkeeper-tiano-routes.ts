import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.OIC_BOOKKEEPER_TIANO.dashboard.key,
  path: ROUTES.OIC_BOOKKEEPER_TIANO.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.oicBookkeeperTiano],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.OIC_BOOKKEEPER_TIANO.requestDashboard.key,
  path: ROUTES.OIC_BOOKKEEPER_TIANO.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.oicBookkeeperTiano],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.OIC_BOOKKEEPER_TIANO.requestForm.key,
  path: ROUTES.OIC_BOOKKEEPER_TIANO.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.oicBookkeeperTiano],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.OIC_BOOKKEEPER_TIANO.viewRequestForm.key,
  path: ROUTES.OIC_BOOKKEEPER_TIANO.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.oicBookkeeperTiano],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.OIC_BOOKKEEPER_TIANO.notification.key,
  path: ROUTES.OIC_BOOKKEEPER_TIANO.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.oicBookkeeperTiano],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.OIC_BOOKKEEPER_TIANO.profile.key,
  path: ROUTES.OIC_BOOKKEEPER_TIANO.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.oicBookkeeperTiano],
};

export const oicBookkeeperTianoRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];