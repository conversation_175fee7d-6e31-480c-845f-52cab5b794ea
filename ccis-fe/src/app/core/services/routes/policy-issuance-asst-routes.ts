import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.POLICY_ISSUANCE_ASST.dashboard.key,
  path: ROUTES.POLICY_ISSUANCE_ASST.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.policyIssuanceAsst],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.POLICY_ISSUANCE_ASST.requestDashboard.key,
  path: ROUTES.POLICY_ISSUANCE_ASST.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.policyIssuanceAsst],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.POLICY_ISSUANCE_ASST.requestForm.key,
  path: ROUTES.POLICY_ISSUANCE_ASST.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.policyIssuanceAsst],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.POLICY_ISSUANCE_ASST.viewRequestForm.key,
  path: ROUTES.POLICY_ISSUANCE_ASST.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.policyIssuanceAsst],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.POLICY_ISSUANCE_ASST.notification.key,
  path: ROUTES.POLICY_ISSUANCE_ASST.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.policyIssuanceAsst],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.POLICY_ISSUANCE_ASST.profile.key,
  path: ROUTES.POLICY_ISSUANCE_ASST.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.policyIssuanceAsst],
};

export const policyIssuanceAsstRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];