import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.BOOKKEEPER_NONLIFE.dashboard.key,
  path: ROUTES.BOOKKEEPER_NONLIFE.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.bookkeeperNonlife],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.BOOKKEEPER_NONLIFE.requestDashboard.key,
  path: ROUTES.BOOKKEEPER_NONLIFE.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.bookkeeperNonlife],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.BOOKKEEPER_NONLIFE.requestForm.key,
  path: ROUTES.BOOKKEEPER_NONLIFE.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.bookkeeperNonlife],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.BOOKKEEPER_NONLIFE.viewRequestForm.key,
  path: ROUTES.BOOKKEEPER_NONLIFE.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.bookkeeperNonlife],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.BOOKKEEPER_NONLIFE.notification.key,
  path: ROUTES.BOOKKEEPER_NONLIFE.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.bookkeeperNonlife],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.BOOKKEEPER_NONLIFE.profile.key,
  path: ROUTES.BOOKKEEPER_NONLIFE.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.bookkeeperNonlife],
};

export const bookkeeperNonlifeRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];