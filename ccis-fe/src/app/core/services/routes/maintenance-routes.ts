import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.MAINTENANCE.dashboard.key,
  path: ROUTES.MAINTENANCE.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.maintenance],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.MAINTENANCE.requestDashboard.key,
  path: ROUTES.MAINTENANCE.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.maintenance],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.MAINTENANCE.requestForm.key,
  path: ROUTES.MAINTENANCE.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.maintenance],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.MAINTENANCE.viewRequestForm.key,
  path: ROUTES.MAINTENANCE.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.maintenance],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.MAINTENANCE.notification.key,
  path: ROUTES.MAINTENANCE.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.maintenance],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.MAINTENANCE.profile.key,
  path: ROUTES.MAINTENANCE.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.maintenance],
};

export const maintenanceRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];