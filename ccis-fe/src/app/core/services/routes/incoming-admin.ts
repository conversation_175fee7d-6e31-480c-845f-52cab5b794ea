import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import IncomingOutgoingCashierDashboard from "@modules/dashboard/IncomingOutgoingCashierDashboard.tsx";
import NewForms from "@modules/admin-incoming/fis/new-forms/index.tsx";
import { RiFile2Line } from "react-icons/ri";
import { HiChevronRight } from "react-icons/hi2";
import IncomingForm from "@modules/admin-incoming/fis/new-forms/index";
// import ViewFormReceivingAdminIncoming from "@modules/admin-incoming/fis/components/ViewIncomingAdminReceiveForm";
import ViewIncomingAdminTransmittalForm from "@modules/admin-incoming/fis/components/ViewIncomingAdminTransmittalForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import ViewReturnedFormReceiving from "@components/template/Forms/UsedForms/ViewReturnedForReceivingForm";
import ViewReturnedForm from "@components/template/Forms/UsedForms/ViewReturnedForm";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.INCOMINGADMIN.incomingAdminDashboard.key,
  path: ROUTES.INCOMINGADMIN.incomingAdminDashboard.key,
  component: IncomingOutgoingCashierDashboard,
  guard: AuthGuard,
  roles: [UserRoles.incomingAdmin],
  icon: MdDashboard,
  isSidebar: true,
};
export const newFormsIncoming: RouteItem = {
  name: "Incoming",
  id: ROUTES.INCOMINGADMIN.incomingAdminNewFormsIncoming.key,
  path: ROUTES.INCOMINGADMIN.incomingAdminNewFormsIncoming.key,
  component: IncomingForm,
  guard: AuthGuard,
  roles: [UserRoles.incomingAdmin],
  icon: HiChevronRight,
  // isSidebar: true,
};
export const newForms: RouteItem = {
  name: "Used Forms",
  id: ROUTES.INCOMINGADMIN.incomingAdminNewForms.key,
  path: ROUTES.INCOMINGADMIN.incomingAdminNewForms.key,
  component: NewForms,
  guard: AuthGuard,
  roles: [UserRoles.incomingAdmin],
  icon: RiFile2Line,
  children: [newFormsIncoming],
  isSidebar: true,
};
export const forReceivingFormAdminIncoming: RouteItem = {
  name: "For Receiving Form",
  id: ROUTES.INCOMINGADMIN.forIncomingAdminReceivingForm.key,
  path: ROUTES.INCOMINGADMIN.forIncomingAdminReceivingForm.key,
  component: ViewReturnedFormReceiving,
  guard: AuthGuard,
  roles: [UserRoles.incomingAdmin],
  isSidebar: false,
};
export const viewIncomingAdminTransmittal: RouteItem = {
  name: "View New Transmittal Form",
  id: ROUTES.INCOMINGADMIN.viewIncomingAdminTransmittal.key,
  path: ROUTES.INCOMINGADMIN.viewIncomingAdminTransmittal.key,
  component: ViewIncomingAdminTransmittalForm,
  guard: AuthGuard,
  roles: [UserRoles.incomingAdmin],
  isSidebar: false,
};
export const viewIncomingAdminTransmittalTrail: RouteItem = {
  name: "View New Transmittal Trail",
  id: ROUTES.INCOMINGADMIN.viewIncomingAdminTransmittalTrail.key,
  path: ROUTES.INCOMINGADMIN.viewIncomingAdminTransmittalTrail.key,
  component: ViewReturnedForm,
  guard: AuthGuard,
  roles: [UserRoles.incomingAdmin],
  isSidebar: false,
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.INCOMINGADMIN.notification.key,
  path: ROUTES.INCOMINGADMIN.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.incomingAdmin],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.INCOMINGADMIN.profile.key,
  path: ROUTES.INCOMINGADMIN.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.incomingAdmin],
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.INCOMINGADMIN.requestForm.key,
  path: ROUTES.INCOMINGADMIN.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.incomingAdmin],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.INCOMINGADMIN.viewRequestForm.key,
  path: ROUTES.INCOMINGADMIN.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.incomingAdmin],
  isSidebar: false,
};
export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.INCOMINGADMIN.requestDashboard.key,
  path: ROUTES.INCOMINGADMIN.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.incomingAdmin],
  icon: MdDashboard,
  isSidebar: true,
};

export const incomingAdminRoutes = [overview, newForms, newFormsIncoming, forReceivingFormAdminIncoming, viewIncomingAdminTransmittal, viewIncomingAdminTransmittalTrail, notification, profile, requestDashboard, requestForm, viewRequest];
