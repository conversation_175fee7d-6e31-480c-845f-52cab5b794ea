import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.COLLECTION_ANALYST.dashboard.key,
  path: ROUTES.COLLECTION_ANALYST.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.collectionAnalyst],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.COLLECTION_ANALYST.requestDashboard.key,
  path: ROUTES.COLLECTION_ANALYST.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.collectionAnalyst],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.COLLECTION_ANALYST.requestForm.key,
  path: ROUTES.COLLECTION_ANALYST.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.collectionAnalyst],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.COLLECTION_ANALYST.viewRequestForm.key,
  path: ROUTES.COLLECTION_ANALYST.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.collectionAnalyst],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.COLLECTION_ANALYST.notification.key,
  path: ROUTES.COLLECTION_ANALYST.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.collectionAnalyst],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.COLLECTION_ANALYST.profile.key,
  path: ROUTES.COLLECTION_ANALYST.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.collectionAnalyst],
};

export const collectionAnalystRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];