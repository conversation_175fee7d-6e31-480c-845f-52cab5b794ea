import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.VP_ADMIN_CORPLAN_CEO_PRINCIPAL_CCP.dashboard.key,
  path: ROUTES.VP_ADMIN_CORPLAN_CEO_PRINCIPAL_CCP.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.vpAdminCorplanCeoPrincipalCcp],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.VP_ADMIN_CORPLA<PERSON>_CEO_PRINCIPAL_CCP.requestDashboard.key,
  path: ROUTES.VP_ADMIN_CORPLAN_CEO_PRINCIPAL_CCP.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.vpAdminCorplanCeoPrincipalCcp],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.VP_ADMIN_CORPLAN_CEO_PRINCIPAL_CCP.requestForm.key,
  path: ROUTES.VP_ADMIN_CORPLAN_CEO_PRINCIPAL_CCP.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.vpAdminCorplanCeoPrincipalCcp],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.VP_ADMIN_CORPLAN_CEO_PRINCIPAL_CCP.viewRequestForm.key,
  path: ROUTES.VP_ADMIN_CORPLAN_CEO_PRINCIPAL_CCP.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.vpAdminCorplanCeoPrincipalCcp],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.VP_ADMIN_CORPLAN_CEO_PRINCIPAL_CCP.notification.key,
  path: ROUTES.VP_ADMIN_CORPLAN_CEO_PRINCIPAL_CCP.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.vpAdminCorplanCeoPrincipalCcp],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.VP_ADMIN_CORPLAN_CEO_PRINCIPAL_CCP.profile.key,
  path: ROUTES.VP_ADMIN_CORPLAN_CEO_PRINCIPAL_CCP.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.vpAdminCorplanCeoPrincipalCcp],
};

export const vpAdminCorplanCeoPrincipalCcpRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];