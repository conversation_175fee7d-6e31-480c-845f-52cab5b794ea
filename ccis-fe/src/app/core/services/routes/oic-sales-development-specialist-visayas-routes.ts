import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.OIC_SALES_DEVELOPMENT_SPECIALIST_VISAYAS.dashboard.key,
  path: ROUTES.OIC_SALES_DEVELOPMENT_SPECIALIST_VISAYAS.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.oicSalesDevelopmentSpecialistVisayas],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.OIC_SALES_DEVELOPMENT_SPECIALIST_VISAYAS.requestDashboard.key,
  path: ROUTES.OIC_SALES_DEVELOPMENT_SPECIALIST_VISAYAS.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.oicSalesDevelopmentSpecialistVisayas],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.OIC_SALES_DEVELOPMENT_SPECIALIST_VISAYAS.requestForm.key,
  path: ROUTES.OIC_SALES_DEVELOPMENT_SPECIALIST_VISAYAS.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.oicSalesDevelopmentSpecialistVisayas],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.OIC_SALES_DEVELOPMENT_SPECIALIST_VISAYAS.viewRequestForm.key,
  path: ROUTES.OIC_SALES_DEVELOPMENT_SPECIALIST_VISAYAS.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.oicSalesDevelopmentSpecialistVisayas],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.OIC_SALES_DEVELOPMENT_SPECIALIST_VISAYAS.notification.key,
  path: ROUTES.OIC_SALES_DEVELOPMENT_SPECIALIST_VISAYAS.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.oicSalesDevelopmentSpecialistVisayas],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.OIC_SALES_DEVELOPMENT_SPECIALIST_VISAYAS.profile.key,
  path: ROUTES.OIC_SALES_DEVELOPMENT_SPECIALIST_VISAYAS.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.oicSalesDevelopmentSpecialistVisayas],
};

export const oicSalesDevelopmentSpecialistVisayasRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];