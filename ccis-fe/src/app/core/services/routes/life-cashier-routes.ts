import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.LIFE_CASHIER.dashboard.key,
  path: ROUTES.LIFE_CASHIER.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.lifeCashier],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.LIFE_CASHIER.requestDashboard.key,
  path: ROUTES.LIFE_CASHIER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.lifeCashier],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.LIFE_CASHIER.requestForm.key,
  path: ROUTES.LIFE_CASHIER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.lifeCashier],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.LIFE_CASHIER.viewRequestForm.key,
  path: ROUTES.LIFE_CASHIER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.lifeCashier],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.LIFE_CASHIER.notification.key,
  path: ROUTES.LIFE_CASHIER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.lifeCashier],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.LIFE_CASHIER.profile.key,
  path: ROUTES.LIFE_CASHIER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.lifeCashier],
};

export const lifeCashierRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];