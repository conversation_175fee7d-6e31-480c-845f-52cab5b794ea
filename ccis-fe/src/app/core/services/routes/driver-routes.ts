import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.DRIVER.dashboard.key,
  path: ROUTES.DRIVER.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.driver],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.DRIVER.requestDashboard.key,
  path: ROUTES.DRIVER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.driver],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.DRIVER.requestForm.key,
  path: ROUTES.DRIVER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.driver],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.DRIVER.viewRequestForm.key,
  path: ROUTES.DRIVER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.driver],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.DRIVER.notification.key,
  path: ROUTES.DRIVER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.driver],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.DRIVER.profile.key,
  path: ROUTES.DRIVER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.driver],
};

export const driverRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];