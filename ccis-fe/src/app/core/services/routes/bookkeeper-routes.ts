import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.BOOKKEEPER.dashboard.key,
  path: ROUTES.BOOKKEEPER.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.bookkeeper],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.BOOKKEEPER.requestDashboard.key,
  path: ROUTES.BOOKKEEPER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.bookkeeper],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.BOOKKEEPER.requestForm.key,
  path: ROUTES.BOOKKEEPER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.bookkeeper],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.BOOKKEEPER.viewRequestForm.key,
  path: ROUTES.BOOKKEEPER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.bookkeeper],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.BOOKKEEPER.notification.key,
  path: ROUTES.BOOKKEEPER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.bookkeeper],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.BOOKKEEPER.profile.key,
  path: ROUTES.BOOKKEEPER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.bookkeeper],
};

export const bookkeeperRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];