import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.BILLING_COLLECTIONS.dashboard.key,
  path: ROUTES.BILLING_COLLECTIONS.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.billingCollections],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.BILLING_COLLECTIONS.requestDashboard.key,
  path: ROUTES.BILLING_COLLECTIONS.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.billingCollections],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.BILLING_COLLECTIONS.requestForm.key,
  path: ROUTES.BILLING_COLLECTIONS.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.billingCollections],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.BILLING_COLLECTIONS.viewRequestForm.key,
  path: ROUTES.BILLING_COLLECTIONS.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.billingCollections],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.BILLING_COLLECTIONS.notification.key,
  path: ROUTES.BILLING_COLLECTIONS.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.billingCollections],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.BILLING_COLLECTIONS.profile.key,
  path: ROUTES.BILLING_COLLECTIONS.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.billingCollections],
};

export const billingCollectionsRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];
