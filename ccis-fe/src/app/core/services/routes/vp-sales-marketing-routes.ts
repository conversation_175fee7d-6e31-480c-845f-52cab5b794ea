import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.VP_SALES_MARKETING.dashboard.key,
  path: ROUTES.VP_SALES_MARKETING.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.vpSalesMarketing],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.VP_SALES_MARKETING.requestDashboard.key,
  path: ROUTES.VP_SALES_MARKETING.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.vpSalesMarketing],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.VP_SALES_MARKETING.requestForm.key,
  path: ROUTES.VP_SALES_MARKETING.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.vpSalesMarketing],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.VP_SALES_MARKETING.viewRequestForm.key,
  path: ROUTES.VP_SALES_MARKETING.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.vpSalesMarketing],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.VP_SALES_MARKETING.notification.key,
  path: ROUTES.VP_SALES_MARKETING.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.vpSalesMarketing],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.VP_SALES_MARKETING.profile.key,
  path: ROUTES.VP_SALES_MARKETING.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.vpSalesMarketing],
};

export const vpSalesMarketingRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];