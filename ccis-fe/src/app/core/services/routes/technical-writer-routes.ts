import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.TECHNICAL_WRITER.dashboard.key,
  path: ROUTES.TECHNICAL_WRITER.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.technicalWriter],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.TECHNICAL_WRITER.requestDashboard.key,
  path: ROUTES.TECHNICAL_WRITER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.technicalWriter],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.TECHNICAL_WRITER.requestForm.key,
  path: ROUTES.TECHNICAL_WRITER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.technicalWriter],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.TECHNICAL_WRITER.viewRequestForm.key,
  path: ROUTES.TECHNICAL_WRITER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.technicalWriter],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.TECHNICAL_WRITER.notification.key,
  path: ROUTES.TECHNICAL_WRITER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.technicalWriter],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.TECHNICAL_WRITER.profile.key,
  path: ROUTES.TECHNICAL_WRITER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.technicalWriter],
};

export const technicalWriterRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];