import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.AVP_INVESTMENT_TREASURY.dashboard.key,
  path: ROUTES.AVP_INVESTMENT_TREASURY.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.avpInvestmentTreasury],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.AVP_INVESTMENT_TREASURY.requestDashboard.key,
  path: ROUTES.AVP_INVESTMENT_TREASURY.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.avpInvestmentTreasury],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.AVP_INVESTMENT_TREASURY.requestForm.key,
  path: ROUTES.AVP_INVESTMENT_TREASURY.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.avpInvestmentTreasury],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.AVP_INVESTMENT_TREASURY.viewRequestForm.key,
  path: ROUTES.AVP_INVESTMENT_TREASURY.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.avpInvestmentTreasury],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.AVP_INVESTMENT_TREASURY.notification.key,
  path: ROUTES.AVP_INVESTMENT_TREASURY.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.avpInvestmentTreasury],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.AVP_INVESTMENT_TREASURY.profile.key,
  path: ROUTES.AVP_INVESTMENT_TREASURY.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.avpInvestmentTreasury],
};

export const avpInvestmentTreasuryRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];