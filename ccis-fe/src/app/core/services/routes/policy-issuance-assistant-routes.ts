import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.POLICY_ISSUANCE_ASSISTANT.dashboard.key,
  path: ROUTES.POLICY_ISSUANCE_ASSISTANT.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.policyIssuanceAssistant],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.POLICY_ISSUANCE_ASSISTANT.requestDashboard.key,
  path: ROUTES.POLICY_ISSUANCE_ASSISTANT.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.policyIssuanceAssistant],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.POLICY_ISSUANCE_ASSISTANT.requestForm.key,
  path: ROUTES.POLICY_ISSUANCE_ASSISTANT.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.policyIssuanceAssistant],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.POLICY_ISSUANCE_ASSISTANT.viewRequestForm.key,
  path: ROUTES.POLICY_ISSUANCE_ASSISTANT.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.policyIssuanceAssistant],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.POLICY_ISSUANCE_ASSISTANT.notification.key,
  path: ROUTES.POLICY_ISSUANCE_ASSISTANT.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.policyIssuanceAssistant],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.POLICY_ISSUANCE_ASSISTANT.profile.key,
  path: ROUTES.POLICY_ISSUANCE_ASSISTANT.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.policyIssuanceAssistant],
};

export const policyIssuanceAssistantRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];