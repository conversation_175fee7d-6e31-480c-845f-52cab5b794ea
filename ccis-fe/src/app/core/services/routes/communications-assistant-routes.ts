import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.COMMUNICATIONS_ASSISTANT.dashboard.key,
  path: ROUTES.COMMUNICATIONS_ASSISTANT.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.communicationsAssistant],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.COMMUNICATIONS_ASSISTANT.requestDashboard.key,
  path: ROUTES.COMMUNICATIONS_ASSISTANT.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.communicationsAssistant],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.COMMUNICATIONS_ASSISTANT.requestForm.key,
  path: ROUTES.COMMUNICATIONS_ASSISTANT.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.communicationsAssistant],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.COMMUNICATIONS_ASSISTANT.viewRequestForm.key,
  path: ROUTES.COMMUNICATIONS_ASSISTANT.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.communicationsAssistant],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.COMMUNICATIONS_ASSISTANT.notification.key,
  path: ROUTES.COMMUNICATIONS_ASSISTANT.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.communicationsAssistant],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.COMMUNICATIONS_ASSISTANT.profile.key,
  path: ROUTES.COMMUNICATIONS_ASSISTANT.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.communicationsAssistant],
};

export const communicationsAssistantRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];