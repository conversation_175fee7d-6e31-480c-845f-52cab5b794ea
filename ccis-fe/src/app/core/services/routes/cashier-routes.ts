import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import IncomingOutgoingCashierDashboard from "@modules/dashboard/IncomingOutgoingCashierDashboard.tsx";
// import { PiCertificate } from "react-icons/pi";
// import SetCurrentPICMortalityRate from "@modules/actuary/utilities/mortalityRate/components/forms/setCurrentPICRates";
// import Shares from "@modules/admin/shares";
import CashierNewForms from "@modules/branch-cashier";
import ForPrReceiving from "@modules/branch-cashier/ForPrReceiving";
import ViewTransmittalForm from "@modules/branch-cashier/forms/TransmittalForm";
import ViewFormReceiving from "@modules/branch-cashier/forms/ForReceivingView";
import Inventory from "@modules/branch-cashier/tabs/Inventory";
import RequestPads from "@modules/branch-cashier/request-pads";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import { FaFile, FaFileAlt } from "react-icons/fa";
import { FaBoxArchive } from "react-icons/fa6";
import { TbMessageForward } from "react-icons/tb";
import { UserArea } from "@enums/users-management";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.CASHIER.actuaryDashboard.key,
  path: ROUTES.CASHIER.actuaryDashboard.key,
  component: IncomingOutgoingCashierDashboard,
  guard: AuthGuard,
  roles: [UserRoles.cashier],
  icon: MdDashboard,
  isSidebar: true,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.CASHIER.notification.key,
  path: ROUTES.CASHIER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.user],
  icon: MdDashboard,
};

export const HQNewForm: RouteItem = {
  name: "SI/OR/PR Forms",
  id: ROUTES.CASHIER.hqCashierNewForm.key,
  path: ROUTES.CASHIER.hqCashierNewForm.key,
  component: CashierNewForms,
  guard: AuthGuard,
  roles: [UserRoles.cashier],
  icon: FaFile,
  isSidebar: true,
  conditions: (user) => user?.userArea.userAreaCode === UserArea.HEADQUARTER,
};

export const PrReceiving: RouteItem = {
  name: "For Receiving PR's",
  id: ROUTES.CASHIER.prReceiving.key,
  path: ROUTES.CASHIER.prReceiving.key,
  component: ForPrReceiving,
  guard: AuthGuard,
  roles: [UserRoles.cashier],
  icon: FaFileAlt,
  isSidebar: true,
  conditions: (user) => user?.userArea.userAreaCode !== UserArea.HEADQUARTER,
};

export const newForm: RouteItem = {
  name: "SI/OR Forms",
  id: ROUTES.CASHIER.cashierNewForm.key,
  path: ROUTES.CASHIER.cashierNewForm.key,
  component: CashierNewForms,
  guard: AuthGuard,
  roles: [UserRoles.cashier],
  icon: FaFile,
  isSidebar: true,
  conditions: (user) => user?.userArea.userAreaCode !== UserArea.HEADQUARTER,
};

export const viewForReceivingForm: RouteItem = {
  name: "View For Receiving Form",
  id: ROUTES.CASHIER.viewForReceivingForm.key,
  path: ROUTES.CASHIER.viewForReceivingForm.key,
  component: ViewFormReceiving,
  guard: AuthGuard,
  roles: [UserRoles.cashier],
  isSidebar: false,
};

export const transmittalViewForm: RouteItem = {
  name: "View Transmittal Form",
  id: ROUTES.CASHIER.viewReleaseTransmittalForm.key,
  path: ROUTES.CASHIER.viewReleaseTransmittalForm.key,
  component: ViewTransmittalForm,
  guard: AuthGuard,
  roles: [UserRoles.cashier],
  isSidebar: false,
};

export const inventory: RouteItem = {
  name: "Inventory",
  id: ROUTES.CASHIER.inventory.key,
  path: ROUTES.CASHIER.inventory.key,
  component: Inventory,
  guard: AuthGuard,
  roles: [UserRoles.cashier],
  icon: FaBoxArchive,
  isSidebar: true,
};

export const requestPads: RouteItem = {
  name: "Request Pads",
  id: ROUTES.CASHIER.requestPads.key,
  path: ROUTES.CASHIER.requestPads.key,
  component: RequestPads,
  guard: AuthGuard,
  roles: [UserRoles.cashier],
  icon: TbMessageForward,
  isSidebar: true,
  conditions: (user) => user?.userArea.userAreaCode !== UserArea.HEADQUARTER,
};

// export const shares: RouteItem = {
//   name: "Shares",
//   id: ROUTES.CASHIER.shares.key,
//   path: ROUTES.CASHIER.shares.key,
//   component: Shares,
//   guard: AuthGuard,
//   roles: [UserRoles.cashier],
//   icon: PiCertificate,
//   isSidebar: true,
// };

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.CASHIER.profile.key,
  path: ROUTES.CASHIER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.cashier],
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.CASHIER.requestForm.key,
  path: ROUTES.CASHIER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.cashier],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.CASHIER.viewRequestForm.key,
  path: ROUTES.CASHIER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.cashier],
  isSidebar: false,
};
export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.CASHIER.requestDashboard.key,
  path: ROUTES.CASHIER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.cashier],
  icon: MdDashboard,
  isSidebar: true,
};

export const cashierRoutes = [
  overview,
  notification,
  newForm,
  HQNewForm,
  PrReceiving,
  viewForReceivingForm,
  transmittalViewForm,
  requestPads,
  inventory,
  profile,
  requestForm,
  viewRequest,
  requestDashboard,
];
