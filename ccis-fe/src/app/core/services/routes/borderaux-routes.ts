import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.BORDEREAUX.dashboard.key,
  path: ROUTES.BORDEREAUX.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.bordereaux],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.BORDEREAUX.requestDashboard.key,
  path: ROUTES.BORDEREAUX.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.bordereaux],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.BORDEREAUX.requestForm.key,
  path: ROUTES.BORDEREAUX.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.bordereaux],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.BORDEREAUX.viewRequestForm.key,
  path: ROUTES.BORDEREAUX.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.bordereaux],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.BORDEREAUX.notification.key,
  path: ROUTES.BORDEREAUX.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.bordereaux],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.BORDEREAUX.profile.key,
  path: ROUTES.BORDEREAUX.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.bordereaux],
};

export const bordereauxRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];