import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import IncomingOutgoingCashierDashboard from "@modules/dashboard/IncomingOutgoingCashierDashboard.tsx";
import NewFormsUtilities from "@modules/admin/form-inventory-and-tracking/new-forms";
import IncomingForm from "@modules/admin/form-inventory-and-tracking/new-forms/incoming";
import ViewIncomingForm from "@modules/admin/form-inventory-and-tracking/new-forms/incoming/components/ViewIncomingForm";
import OutgoingForm from "@modules/admin/form-inventory-and-tracking/new-forms/outgoing";
import { HiChevronRight } from "react-icons/hi2";
import { RiFile2Line } from "react-icons/ri";
import ViewTransmittalForm from "@modules/admin/form-inventory-and-tracking/new-forms/outgoing/components/ViewTransmittalForm";
import ViewFormReceiving from "@modules/admin/form-inventory-and-tracking/new-forms/outgoing/components/ViewReceiveForm";
import ViewReleasedForms from "@modules/admin/form-inventory-and-tracking/new-forms/outgoing/components/ViewReleasedForms";
import ICOGInventory from "@modules/admin/form-inventory-and-tracking/new-forms/outgoing/inventory";
import { FaBoxArchive } from "react-icons/fa6";
import InventoryVerifiedList from "@modules/admin/form-inventory-and-tracking/new-forms/outgoing/inventory/verifiedList";
import InventoryNewForms from "@modules/admin/form-inventory-and-tracking/new-forms/outgoing/inventory/newForms";
import InventoryUsedForms from "@modules/admin/form-inventory-and-tracking/new-forms/outgoing/inventory/usedForms";
import NotificationPage from "@modules/shared/notification";
import UsedForms from "@modules/admin/form-inventory-and-tracking/used-forms";
import { FaFileAlt } from "react-icons/fa";
import ViewIOCTransmittalForm from "@modules/admin/form-inventory-and-tracking/used-forms/components/forms/ViewIOCTransmittalForm";
import ViewReturnedFormReceiving from "@components/template/Forms/UsedForms/ViewReturnedForReceivingForm";
import Profile from "@modules/shared/profile";
import ViewReturnedForm from "@components/template/Forms/UsedForms/ViewReturnedForm";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.incomingOutgoingCashierDashboard.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.incomingOutgoingCashierDashboard.key,
  component: IncomingOutgoingCashierDashboard,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  icon: MdDashboard,
  isSidebar: true,
};
export const newFormsIncoming: RouteItem = {
  name: "Incoming",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.newFormsIncoming.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.newFormsIncoming.key,
  component: IncomingForm,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  icon: HiChevronRight,
  // isSidebar: true,
};

export const newFormsOutgoing: RouteItem = {
  name: "Outgoing",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.newFormsOutgoing.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.newFormsOutgoing.key,
  component: OutgoingForm,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  icon: HiChevronRight,
  // isSidebar: true,
};

export const newForms: RouteItem = {
  name: "New Forms",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.newForms.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.newForms.key,
  component: NewFormsUtilities,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  icon: RiFile2Line,
  isSidebar: true,
  children: [newFormsIncoming, newFormsOutgoing],
};
export const viewNewForm: RouteItem = {
  name: "View New Form",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.viewNewForm.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.viewNewForm.key,
  component: ViewIncomingForm,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  icon: HiChevronRight,
};
export const viewOutgoingForm: RouteItem = {
  name: "View Outgoing Form",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.viewOutgoingForm.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.viewOutgoingForm.key,
  component: ViewTransmittalForm,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  isSidebar: false,
};
export const forReceivingForm: RouteItem = {
  name: "For Receiving Form",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.forReceivingForm.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.forReceivingForm.key,
  component: ViewFormReceiving,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  isSidebar: false,
};

export const releasedForms: RouteItem = {
  name: "For Released Form",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.releasedForms.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.releasedForms.key,
  component: ViewReleasedForms,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  isSidebar: false,
};

export const viewIOCTransmittalTrail: RouteItem = {
  name: "View New Transmittal Form",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.viewReturnedForm.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.viewReturnedForm.key,
  component: ViewReturnedForm,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  isSidebar: false,
};

export const viewIOCTransmittalForm: RouteItem = {
  name: "View New Transmittal Form",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.viewIOCTransmittal.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.viewIOCTransmittal.key,
  component: ViewIOCTransmittalForm,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  isSidebar: false,
};

export const forReturnedReceivingForm: RouteItem = {
  name: "View Recieve Returned Form",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.forReturnedReceivingForm.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.forReturnedReceivingForm.key,
  component: ViewReturnedFormReceiving,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  isSidebar: false,
};

export const usedForms: RouteItem = {
  name: "Used Forms",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.usedForms.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.usedForms.key,
  component: UsedForms,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  icon: FaFileAlt,
  isSidebar: true,
};

export const verifiedListInventory: RouteItem = {
  name: "Verified List",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.verifiedFormsInventory.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.verifiedFormsInventory.key,
  component: InventoryVerifiedList,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  isSidebar: false,
  icon: HiChevronRight,
};

export const newFormsInventory: RouteItem = {
  name: "New Forms",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.newFormsInventory.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.newFormsInventory.key,
  component: InventoryNewForms,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  icon: HiChevronRight,
};

export const usedFormInventory: RouteItem = {
  name: "Used Forms ",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.usedFormInventory.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.usedFormInventory.key,
  component: InventoryUsedForms,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  icon: HiChevronRight,
};

export const inventory: RouteItem = {
  name: "Inventory",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.inventory.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.inventory.key,
  component: ICOGInventory,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  icon: FaBoxArchive,
  isSidebar: true,
  children: [verifiedListInventory, newFormsInventory, usedFormInventory],
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.notification.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.profile.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.requestForm.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.viewRequestForm.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  isSidebar: false,
};
export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.INCOMINGOUTGOINGCASHIER.requestDashboard.key,
  path: ROUTES.INCOMINGOUTGOINGCASHIER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  icon: MdDashboard,
  isSidebar: true,
};

export const incomingOutgoingCashierRoutes = [
  overview,
  newForms,
  newFormsIncoming,
  newFormsOutgoing,
  viewNewForm,
  viewOutgoingForm,
  forReceivingForm,
  releasedForms,
  usedForms,
  viewIOCTransmittalTrail,
  forReturnedReceivingForm,
  inventory,
  verifiedListInventory,
  newFormsInventory,
  usedFormInventory,
  notification,
  viewIOCTransmittalForm,
  profile,
  requestForm,
  viewRequest,
  requestDashboard,
];
