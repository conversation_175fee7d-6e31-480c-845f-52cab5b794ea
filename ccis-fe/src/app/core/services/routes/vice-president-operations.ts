import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import Dashboard from "@modules/dashboard";
import { HiChevronRight } from "react-icons/hi2";
import CommissionAndRequirements from "@modules/marketing/validation/commission-and-requirements";
import MyApprovals from "@modules/admin/approval-aer";
import { FaFileCircleCheck } from "react-icons/fa6";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import Approval from "@modules/marketing/validation/commission-and-requirements/Approval";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.VICEPRESIDENTFOROPERATION.dashboard.key,
  path: ROUTES.VICEPRESIDENTFOROPERATION.dashboard.key,
  component: Dashboard,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentOperations],
  icon: MdDashboard,
  isSidebar: true,
};
export const commissionAndRequirements: RouteItem = {
  name: "Commission and Requirements",
  id: ROUTES.VICEPRESIDENTFOROPERATION.commissionAndRequirements.key,
  path: ROUTES.VICEPRESIDENTFOROPERATION.commissionAndRequirements.key,
  component: CommissionAndRequirements,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentOperations],
  icon: HiChevronRight,
};

export const myApprovals: RouteItem = {
  name: "My Approvals",
  id: ROUTES.VICEPRESIDENTFOROPERATION.myApprovals.key,
  path: ROUTES.VICEPRESIDENTFOROPERATION.myApprovals.key,
  component: MyApprovals,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentOperations],
  icon: FaFileCircleCheck,
  isSidebar: true,
  children: [commissionAndRequirements],
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.VICEPRESIDENTFOROPERATION.notification.key,
  path: ROUTES.VICEPRESIDENTFOROPERATION.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentOperations],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.VICEPRESIDENTFOROPERATION.profile.key,
  path: ROUTES.VICEPRESIDENTFOROPERATION.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentOperations],
};

export const approval: RouteItem = {
  name: "Approval",
  id: ROUTES.VICEPRESIDENTFOROPERATION.viewProductProposalSignatory.key,
  path: ROUTES.VICEPRESIDENTFOROPERATION.viewProductProposalSignatory.key,
  component: Approval,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentOperations],
  isSidebar: false,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.VICEPRESIDENTFOROPERATION.requestDashboard.key,
  path: ROUTES.VICEPRESIDENTFOROPERATION.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentOperations],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.VICEPRESIDENTFOROPERATION.requestForm.key,
  path: ROUTES.VICEPRESIDENTFOROPERATION.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentOperations],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.VICEPRESIDENTFOROPERATION.viewRequestForm.key,
  path: ROUTES.VICEPRESIDENTFOROPERATION.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentOperations],
  isSidebar: false,
};

export const vicePresidentOperationRoutes = [overview, myApprovals, commissionAndRequirements, notification, approval, profile, requestDashboard, requestForm, viewRequest];
