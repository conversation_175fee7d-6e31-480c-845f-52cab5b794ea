import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.CASHIER_NL.dashboard.key,
  path: ROUTES.CASHIER_NL.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.cashierNl],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.CASHIER_NL.requestDashboard.key,
  path: ROUTES.CASHIER_NL.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.cashierNl],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.CASHIER_NL.requestForm.key,
  path: ROUTES.CASHIER_NL.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.cashierNl],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.CASHIER_NL.viewRequestForm.key,
  path: ROUTES.CASHIER_NL.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.cashierNl],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.CASHIER_NL.notification.key,
  path: ROUTES.CASHIER_NL.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.cashierNl],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.CASHIER_NL.profile.key,
  path: ROUTES.CASHIER_NL.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.cashierNl],
};

export const cashierNlRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];