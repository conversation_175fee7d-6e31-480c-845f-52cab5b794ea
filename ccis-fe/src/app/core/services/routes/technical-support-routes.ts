import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.TECHNICAL_SUPPORT.dashboard.key,
  path: ROUTES.TECHNICAL_SUPPORT.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.technicalSupport],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.TECHNICAL_SUPPORT.requestDashboard.key,
  path: ROUTES.TECHNICAL_SUPPORT.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.technicalSupport],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.TECHNICAL_SUPPORT.requestForm.key,
  path: ROUTES.TECHNICAL_SUPPORT.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.technicalSupport],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.TECHNICAL_SUPPORT.viewRequestForm.key,
  path: ROUTES.TECHNICAL_SUPPORT.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.technicalSupport],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.TECHNICAL_SUPPORT.notification.key,
  path: ROUTES.TECHNICAL_SUPPORT.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.technicalSupport],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.TECHNICAL_SUPPORT.profile.key,
  path: ROUTES.TECHNICAL_SUPPORT.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.technicalSupport],
};

export const technicalSupportRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];