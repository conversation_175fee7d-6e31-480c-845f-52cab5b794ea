import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import Dashboard from "@modules/dashboard";
import CommissionAndRequirements from "@modules/marketing/validation/commission-and-requirements";
import MyApprovals from "@modules/admin/approval-aer";
import { FaFileCircleCheck } from "react-icons/fa6";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import Approval from "@modules/marketing/validation/commission-and-requirements/Approval";

import RequestTypes from "@modules/admin/departmental-ticketing-utilities/request-types";
import ApplicationUtility from "@modules/admin/departmental-ticketing-utilities/applications";
import DevicesSystems from "@modules/admin/departmental-ticketing-utilities/devices-systems";
import OperatingSystems from "@modules/admin/departmental-ticketing-utilities/operating-systems";
import TicketUtilities from "@modules/admin/departmental-ticketing-utilities";
import { HiChevronRight } from "react-icons/hi2";
import { FaBookmark } from "react-icons/fa";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.AREASALESMANAGER.dashboard.key,
  path: ROUTES.AREASALESMANAGER.dashboard.key,
  component: Dashboard,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
  icon: MdDashboard,
  isSidebar: true,
};
export const commissionAndRequirements: RouteItem = {
  name: "Commission and Requirements",
  id: ROUTES.AREASALESMANAGER.commissionAndRequirements.key,
  path: ROUTES.AREASALESMANAGER.commissionAndRequirements.key,
  component: CommissionAndRequirements,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
  icon: HiChevronRight,
};

export const myApprovals: RouteItem = {
  name: "My Approvals",
  id: ROUTES.AREASALESMANAGER.myApprovals.key,
  path: ROUTES.AREASALESMANAGER.myApprovals.key,
  component: MyApprovals,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
  icon: FaFileCircleCheck,
  isSidebar: true,
  children: [commissionAndRequirements],
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.AREASALESMANAGER.notification.key,
  path: ROUTES.AREASALESMANAGER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.AREASALESMANAGER.profile.key,
  path: ROUTES.AREASALESMANAGER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
};

export const approval: RouteItem = {
  name: "Approval",
  id: ROUTES.AREASALESMANAGER.viewProductProposalSignatory.key,
  path: ROUTES.AREASALESMANAGER.viewProductProposalSignatory.key,
  component: Approval,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
  isSidebar: false,
};

export const requestTypeUtilities: RouteItem = {
  name: "Request Type",
  id: ROUTES.AREASALESMANAGER.requestTypesUtility.key,
  path: ROUTES.AREASALESMANAGER.requestTypesUtility.key,
  component: RequestTypes,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
  icon: HiChevronRight,
  isSidebar: false,
};

export const applicationUtilities: RouteItem = {
  name: "Applications",
  id: ROUTES.AREASALESMANAGER.applicationsUtility.key,
  path: ROUTES.AREASALESMANAGER.applicationsUtility.key,
  component: ApplicationUtility,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
  icon: HiChevronRight,
  isSidebar: false,
};

export const devicesUtilities: RouteItem = {
  name: "Devices/Systems",
  id: ROUTES.AREASALESMANAGER.devicesSystemUtility.key,
  path: ROUTES.AREASALESMANAGER.devicesSystemUtility.key,
  component: DevicesSystems,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
  icon: HiChevronRight,
  isSidebar: false,
};

export const operatingSystemsUtilities: RouteItem = {
  name: "Operating Systems",
  id: ROUTES.AREASALESMANAGER.operatingSystemsUtility.key,
  path: ROUTES.AREASALESMANAGER.operatingSystemsUtility.key,
  component: OperatingSystems,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
  icon: HiChevronRight,
  isSidebar: false,
};

export const ticketUtilities: RouteItem = {
  name: "Ticket Utilities",
  id: ROUTES.AREASALESMANAGER.ticketUtilities.key,
  path: ROUTES.AREASALESMANAGER.ticketUtilities.key,
  component: TicketUtilities,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
  icon: FaBookmark,
  isSidebar: true,
  children: [requestTypeUtilities, applicationUtilities, devicesUtilities, operatingSystemsUtilities],
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.AREASALESMANAGER.requestForm.key,
  path: ROUTES.AREASALESMANAGER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.AREASALESMANAGER.viewRequestForm.key,
  path: ROUTES.AREASALESMANAGER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
  isSidebar: false,
};
export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.AREASALESMANAGER.requestDashboard.key,
  path: ROUTES.AREASALESMANAGER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
  icon: MdDashboard,
  isSidebar: true,
};

export const areaSalesManagerRoutes = [
  overview,
  myApprovals,
  commissionAndRequirements,
  notification,
  approval,
  profile,
  ticketUtilities,
  requestTypeUtilities,
  applicationUtilities,
  devicesUtilities,
  operatingSystemsUtilities,
  requestDashboard,
  requestForm,
  viewRequest,
];
