import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.SYSTEM_PROGRAMMER.dashboard.key,
  path: ROUTES.SYSTEM_PROGRAMMER.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.systemProgrammer],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.SYSTEM_PROGRAMMER.requestDashboard.key,
  path: ROUTES.SYSTEM_PROGRAMMER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.systemProgrammer],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.SYSTEM_PROGRAMMER.requestForm.key,
  path: ROUTES.SYSTEM_PROGRAMMER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.systemProgrammer],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.SYSTEM_PROGRAMMER.viewRequestForm.key,
  path: ROUTES.SYSTEM_PROGRAMMER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.systemProgrammer],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.SYSTEM_PROGRAMMER.notification.key,
  path: ROUTES.SYSTEM_PROGRAMMER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.systemProgrammer],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.SYSTEM_PROGRAMMER.profile.key,
  path: ROUTES.SYSTEM_PROGRAMMER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.systemProgrammer],
};

export const systemProgrammerRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];