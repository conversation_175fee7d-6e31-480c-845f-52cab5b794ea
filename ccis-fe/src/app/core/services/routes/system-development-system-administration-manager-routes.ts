import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

import RequestTypes from "@modules/admin/departmental-ticketing-utilities/request-types";
import ApplicationUtility from "@modules/admin/departmental-ticketing-utilities/applications";
import DevicesSystems from "@modules/admin/departmental-ticketing-utilities/devices-systems";
import OperatingSystems from "@modules/admin/departmental-ticketing-utilities/operating-systems";
import TicketUtilities from "@modules/admin/departmental-ticketing-utilities";
import { HiChevronRight } from "react-icons/hi2";
import { FaBookmark } from "react-icons/fa";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.dashboard.key,
  path: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.systemDevelopmentSystemAdministrationManager],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.requestDashboard.key,
  path: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.systemDevelopmentSystemAdministrationManager],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.requestForm.key,
  path: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.systemDevelopmentSystemAdministrationManager],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.viewRequestForm.key,
  path: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.systemDevelopmentSystemAdministrationManager],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.notification.key,
  path: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.systemDevelopmentSystemAdministrationManager],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.profile.key,
  path: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.systemDevelopmentSystemAdministrationManager],
};

export const requestTypeUtilities: RouteItem = {
  name: "Request Type",
  id: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.requestTypesUtility.key,
  path: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.requestTypesUtility.key,
  component: RequestTypes,
  guard: AuthGuard,
  roles: [UserRoles.systemDevelopmentSystemAdministrationManager],
  icon: HiChevronRight,
  isSidebar: false,
};

export const applicationUtilities: RouteItem = {
  name: "Applications",
  id: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.applicationsUtility.key,
  path: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.applicationsUtility.key,
  component: ApplicationUtility,
  guard: AuthGuard,
  roles: [UserRoles.systemDevelopmentSystemAdministrationManager],
  icon: HiChevronRight,
  isSidebar: false,
};

export const devicesUtilities: RouteItem = {
  name: "Devices/Systems",
  id: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.devicesSystemUtility.key,
  path: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.devicesSystemUtility.key,
  component: DevicesSystems,
  guard: AuthGuard,
  roles: [UserRoles.systemDevelopmentSystemAdministrationManager],
  icon: HiChevronRight,
  isSidebar: false,
};

export const operatingSystemsUtilities: RouteItem = {
  name: "Operating Systems",
  id: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.operatingSystemsUtility.key,
  path: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.operatingSystemsUtility.key,
  component: OperatingSystems,
  guard: AuthGuard,
  roles: [UserRoles.systemDevelopmentSystemAdministrationManager],
  icon: HiChevronRight,
  isSidebar: false,
};

export const ticketUtilities: RouteItem = {
  name: "Ticket Utilities",
  id: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.ticketUtilities.key,
  path: ROUTES.SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER.ticketUtilities.key,
  component: TicketUtilities,
  guard: AuthGuard,
  roles: [UserRoles.systemDevelopmentSystemAdministrationManager],
  icon: FaBookmark,
  isSidebar: true,
  children: [requestTypeUtilities, applicationUtilities, devicesUtilities, operatingSystemsUtilities],
};

export const systemDevelopmentSystemAdministrationManagerRoutes = [
  overview,
  requestDashboard,
  requestForm,
  viewRequest,
  notification,
  profile,
  ticketUtilities,
  requestTypeUtilities,
  applicationUtilities,
  devicesUtilities,
  operatingSystemsUtilities,
];
