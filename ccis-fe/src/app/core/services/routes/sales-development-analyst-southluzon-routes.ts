import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.SALES_DEVELOPMENT_ANALYST_SOUTH_LUZON.dashboard.key,
  path: ROUTES.SALES_DEVELOPMENT_ANALYST_SOUTH_LUZON.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.salesDevelopmentAnalystSouthLuzon],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.SALES_DEVELOPMENT_ANALYST_SOUTH_LUZON.requestDashboard.key,
  path: ROUTES.SALES_DEVELOPMENT_ANALYST_SOUTH_LUZON.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.salesDevelopmentAnalystSouthLuzon],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.SALES_DEVELOPMENT_ANALYST_SOUTH_LUZON.requestForm.key,
  path: ROUTES.SALES_DEVELOPMENT_ANALYST_SOUTH_LUZON.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.salesDevelopmentAnalystSouthLuzon],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.SALES_DEVELOPMENT_ANALYST_SOUTH_LUZON.viewRequestForm.key,
  path: ROUTES.SALES_DEVELOPMENT_ANALYST_SOUTH_LUZON.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.salesDevelopmentAnalystSouthLuzon],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.SALES_DEVELOPMENT_ANALYST_SOUTH_LUZON.notification.key,
  path: ROUTES.SALES_DEVELOPMENT_ANALYST_SOUTH_LUZON.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.salesDevelopmentAnalystSouthLuzon],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.SALES_DEVELOPMENT_ANALYST_SOUTH_LUZON.profile.key,
  path: ROUTES.SALES_DEVELOPMENT_ANALYST_SOUTH_LUZON.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.salesDevelopmentAnalystSouthLuzon],
};

export const salesDevelopmentAnalystSouthLuzonRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];