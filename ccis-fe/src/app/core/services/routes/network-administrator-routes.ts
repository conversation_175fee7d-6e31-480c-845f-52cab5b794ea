import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.NETWORK_ADMINISTRATOR.dashboard.key,
  path: ROUTES.NETWORK_ADMINISTRATOR.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.networkAdministrator],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.NETWORK_ADMINISTRATOR.requestDashboard.key,
  path: ROUTES.NETWORK_ADMINISTRATOR.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.networkAdministrator],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.NETWORK_ADMINISTRATOR.requestForm.key,
  path: ROUTES.NETWORK_ADMINISTRATOR.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.networkAdministrator],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.NETWORK_ADMINISTRATOR.viewRequestForm.key,
  path: ROUTES.NETWORK_ADMINISTRATOR.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.networkAdministrator],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.NETWORK_ADMINISTRATOR.notification.key,
  path: ROUTES.NETWORK_ADMINISTRATOR.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.networkAdministrator],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.NETWORK_ADMINISTRATOR.profile.key,
  path: ROUTES.NETWORK_ADMINISTRATOR.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.networkAdministrator],
};

export const networkAdministratorRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];