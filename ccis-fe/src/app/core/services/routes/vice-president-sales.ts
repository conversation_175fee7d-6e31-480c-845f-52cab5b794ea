import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import Dashboard from "@modules/dashboard";
import { HiChevronRight } from "react-icons/hi2";
import CommissionAndRequirements from "@modules/marketing/validation/commission-and-requirements";
import MyApprovals from "@modules/admin/approval-aer";
import { FaFileCircleCheck } from "react-icons/fa6";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import Approval from "@modules/marketing/validation/commission-and-requirements/Approval";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.VICEPRESIDENTFORSALES.dashboard.key,
  path: ROUTES.VICEPRESIDENTFORSALES.dashboard.key,
  component: Dashboard,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentSales],
  icon: MdDashboard,
  isSidebar: true,
};
export const commissionAndRequirements: RouteItem = {
  name: "Commission and Requirements",
  id: ROUTES.VICEPRESIDENTFORSALES.commissionAndRequirements.key,
  path: ROUTES.VICEPRESIDENTFORSALES.commissionAndRequirements.key,
  component: CommissionAndRequirements,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentSales],
  icon: HiChevronRight,
};

export const myApprovals: RouteItem = {
  name: "My Approvals",
  id: ROUTES.VICEPRESIDENTFORSALES.myApprovals.key,
  path: ROUTES.VICEPRESIDENTFORSALES.myApprovals.key,
  component: MyApprovals,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentSales],
  icon: FaFileCircleCheck,
  isSidebar: true,
  children: [commissionAndRequirements],
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.VICEPRESIDENTFORSALES.notification.key,
  path: ROUTES.VICEPRESIDENTFORSALES.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentSales],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const approval: RouteItem = {
  name: "Approval",
  id: ROUTES.VICEPRESIDENTFORSALES.viewProductProposalSignatory.key,
  path: ROUTES.VICEPRESIDENTFORSALES.viewProductProposalSignatory.key,
  component: Approval,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentSales],
  isSidebar: false,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.VICEPRESIDENTFORSALES.profile.key,
  path: ROUTES.VICEPRESIDENTFORSALES.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentSales],
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.VICEPRESIDENTFORSALES.requestForm.key,
  path: ROUTES.VICEPRESIDENTFORSALES.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentSales],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.VICEPRESIDENTFORSALES.viewRequestForm.key,
  path: ROUTES.VICEPRESIDENTFORSALES.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentSales],
  isSidebar: false,
};
export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.VICEPRESIDENTFORSALES.requestDashboard.key,
  path: ROUTES.VICEPRESIDENTFORSALES.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentSales],
  icon: MdDashboard,
  isSidebar: true,
};

export const vicePresidentSalesRoutes = [overview, myApprovals, commissionAndRequirements, notification, approval, profile, requestForm, viewRequest, requestDashboard];
