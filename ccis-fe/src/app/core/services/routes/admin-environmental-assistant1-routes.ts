import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.ADMIN_ENVIRONMENTAL_ASSISTANT_1.dashboard.key,
  path: ROUTES.ADMIN_ENVIRONMENTAL_ASSISTANT_1.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.adminEnvironmentalAssistant1],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.ADMIN_ENVIRONMENTAL_ASSISTANT_1.requestDashboard.key,
  path: ROUTES.ADMIN_ENVIRONMENTAL_ASSISTANT_1.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.adminEnvironmentalAssistant1],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.ADMIN_ENVIRONMENTAL_ASSISTANT_1.requestForm.key,
  path: ROUTES.ADMIN_ENVIRONMENTAL_ASSISTANT_1.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.adminEnvironmentalAssistant1],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.ADMIN_ENVIRONMENTAL_ASSISTANT_1.viewRequestForm.key,
  path: ROUTES.ADMIN_ENVIRONMENTAL_ASSISTANT_1.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.adminEnvironmentalAssistant1],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.ADMIN_ENVIRONMENTAL_ASSISTANT_1.notification.key,
  path: ROUTES.ADMIN_ENVIRONMENTAL_ASSISTANT_1.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.adminEnvironmentalAssistant1],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.ADMIN_ENVIRONMENTAL_ASSISTANT_1.profile.key,
  path: ROUTES.ADMIN_ENVIRONMENTAL_ASSISTANT_1.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.adminEnvironmentalAssistant1],
};

export const adminEnvironmentalAssistant1Routes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];