import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.ADMINISTRATIVE_ASSISTANT_1_OOP.dashboard.key,
  path: ROUTES.ADMINISTRATIVE_ASSISTANT_1_OOP.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.administrativeAssistant1Oop],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.ADMINISTRATIVE_ASSISTANT_1_OOP.requestDashboard.key,
  path: ROUTES.ADMINISTRATIVE_ASSISTANT_1_OOP.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.administrativeAssistant1Oop],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.ADMINISTRATIVE_ASSISTANT_1_OOP.requestForm.key,
  path: ROUTES.ADMINISTRATIVE_ASSISTANT_1_OOP.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.administrativeAssistant1Oop],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.ADMINISTRATIVE_ASSISTANT_1_OOP.viewRequestForm.key,
  path: ROUTES.ADMINISTRATIVE_ASSISTANT_1_OOP.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.administrativeAssistant1Oop],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.ADMINISTRATIVE_ASSISTANT_1_OOP.notification.key,
  path: ROUTES.ADMINISTRATIVE_ASSISTANT_1_OOP.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.administrativeAssistant1Oop],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.ADMINISTRATIVE_ASSISTANT_1_OOP.profile.key,
  path: ROUTES.ADMINISTRATIVE_ASSISTANT_1_OOP.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.administrativeAssistant1Oop],
};

export const administrativeAssistant1OopRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];