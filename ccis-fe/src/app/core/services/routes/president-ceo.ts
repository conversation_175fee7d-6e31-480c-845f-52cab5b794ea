import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import Dashboard from "@modules/dashboard";
import { HiChevronRight } from "react-icons/hi2";
import CommissionAndRequirements from "@modules/marketing/validation/commission-and-requirements";
import MyApprovals from "@modules/admin/approval-aer";
import { FaFileCircleCheck } from "react-icons/fa6";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import Approval from "@modules/marketing/validation/commission-and-requirements/Approval";
export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.PRESIDENTANDCEO.dashboard.key,
  path: ROUTES.PRESIDENTANDCEO.dashboard.key,
  component: Dashboard,
  guard: AuthGuard,
  roles: [UserRoles.presidentCeo],
  icon: MdDashboard,
  isSidebar: true,
};
export const commissionAndRequirements: RouteItem = {
  name: "Commission and Requirements",
  id: ROUTES.PRESIDENTANDCEO.commissionAndRequirements.key,
  path: ROUTES.PRESIDENTANDCEO.commissionAndRequirements.key,
  component: CommissionAndRequirements,
  guard: AuthGuard,
  roles: [UserRoles.presidentCeo],
  icon: HiChevronRight,
};

export const myApprovals: RouteItem = {
  name: "My Approvals",
  id: ROUTES.PRESIDENTANDCEO.myApprovals.key,
  path: ROUTES.PRESIDENTANDCEO.myApprovals.key,
  component: MyApprovals,
  guard: AuthGuard,
  roles: [UserRoles.presidentCeo],
  icon: FaFileCircleCheck,
  isSidebar: true,
  children: [commissionAndRequirements],
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.PRESIDENTANDCEO.requestDashboard.key,
  path: ROUTES.PRESIDENTANDCEO.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.presidentCeo],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.PRESIDENTANDCEO.requestForm.key,
  path: ROUTES.PRESIDENTANDCEO.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.presidentCeo],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.PRESIDENTANDCEO.viewRequestForm.key,
  path: ROUTES.PRESIDENTANDCEO.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.presidentCeo],
  isSidebar: false,
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.PRESIDENTANDCEO.notification.key,
  path: ROUTES.PRESIDENTANDCEO.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.presidentCeo],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.PRESIDENTANDCEO.profile.key,
  path: ROUTES.PRESIDENTANDCEO.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.presidentCeo],
};
export const approval: RouteItem = {
  name: "Approval",
  id: ROUTES.PRESIDENTANDCEO.viewProductProposalSignatory.key,
  path: ROUTES.PRESIDENTANDCEO.viewProductProposalSignatory.key,
  component: Approval,
  guard: AuthGuard,
  roles: [UserRoles.presidentCeo],
  isSidebar: false,
};

export const presidentCeoRoutes = [overview, myApprovals, commissionAndRequirements, requestDashboard, requestForm, viewRequest, notification, approval, profile];
