import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.INFRASTRUCTUREOFFICER.dashboard.key,
  path: ROUTES.INFRASTRUCTUREOFFICER.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.infraOfficer],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.INFRASTRUCTUREOFFICER.requestDashboard.key,
  path: ROUTES.INFRASTRUCTUREOFFICER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.infraOfficer],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.INFRASTRUCTUREOFFICER.requestForm.key,
  path: ROUTES.INFRASTRUCTUREOFFICER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.infraOfficer],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.INFRASTRUCTUREOFFICER.viewRequestForm.key,
  path: ROUTES.INFRASTRUCTUREOFFICER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.infraOfficer],
  isSidebar: false,
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.INFRASTRUCTUREOFFICER.notification.key,
  path: ROUTES.INFRASTRUCTUREOFFICER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.infraOfficer],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.INFRASTRUCTUREOFFICER.profile.key,
  path: ROUTES.INFRASTRUCTUREOFFICER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.infraOfficer],
};

export const infrastructureOfficerRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];
