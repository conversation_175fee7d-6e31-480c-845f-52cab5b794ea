import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.PARALEGAL.dashboard.key,
  path: ROUTES.PARALEGAL.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.paralegal],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.PARALEGAL.requestDashboard.key,
  path: ROUTES.PARALEGAL.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.paralegal],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.PARALEGAL.requestForm.key,
  path: ROUTES.PARALEGAL.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.paralegal],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.PARALEGAL.viewRequestForm.key,
  path: ROUTES.PARALEGAL.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.paralegal],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.PARALEGAL.notification.key,
  path: ROUTES.PARALEGAL.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.paralegal],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.PARALEGAL.profile.key,
  path: ROUTES.PARALEGAL.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.paralegal],
};

export const paralegalRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];