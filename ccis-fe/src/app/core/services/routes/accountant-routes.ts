import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.ACCOUNTANT.dashboard.key,
  path: ROUTES.ACCOUNTANT.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.accountant],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.ACCOUNTANT.requestDashboard.key,
  path: ROUTES.ACCOUNTANT.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.accountant],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.ACCOUNTANT.requestForm.key,
  path: ROUTES.ACCOUNTANT.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.accountant],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.ACCOUNTANT.viewRequestForm.key,
  path: ROUTES.ACCOUNTANT.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.accountant],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.ACCOUNTANT.notification.key,
  path: ROUTES.ACCOUNTANT.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.accountant],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.ACCOUNTANT.profile.key,
  path: ROUTES.ACCOUNTANT.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.accountant],
};

export const accountantRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];