import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.CLAIMS_ADMIN_ASST.dashboard.key,
  path: ROUTES.CLAIMS_ADMIN_ASST.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.claimsAdminAsst],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.CLAIMS_ADMIN_ASST.requestDashboard.key,
  path: ROUTES.CLAIMS_ADMIN_ASST.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.claimsAdminAsst],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.CLAIMS_ADMIN_ASST.requestForm.key,
  path: ROUTES.CLAIMS_ADMIN_ASST.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.claimsAdminAsst],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.CLAIMS_ADMIN_ASST.viewRequestForm.key,
  path: ROUTES.CLAIMS_ADMIN_ASST.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.claimsAdminAsst],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.CLAIMS_ADMIN_ASST.notification.key,
  path: ROUTES.CLAIMS_ADMIN_ASST.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.claimsAdminAsst],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.CLAIMS_ADMIN_ASST.profile.key,
  path: ROUTES.CLAIMS_ADMIN_ASST.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.claimsAdminAsst],
};

export const claimsAdminAsstRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];