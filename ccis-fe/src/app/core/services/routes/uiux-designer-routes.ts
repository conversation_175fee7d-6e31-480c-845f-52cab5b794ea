import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.UI_UX_DESIGNER.dashboard.key,
  path: ROUTES.UI_UX_DESIGNER.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.uiUxDesigner],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.UI_UX_DESIGNER.requestDashboard.key,
  path: ROUTES.UI_UX_DESIGNER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.uiUxDesigner],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.UI_UX_DESIGNER.requestForm.key,
  path: ROUTES.UI_UX_DESIGNER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.uiUxDesigner],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.UI_UX_DESIGNER.viewRequestForm.key,
  path: ROUTES.UI_UX_DESIGNER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.uiUxDesigner],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.UI_UX_DESIGNER.notification.key,
  path: ROUTES.UI_UX_DESIGNER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.uiUxDesigner],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.UI_UX_DESIGNER.profile.key,
  path: ROUTES.UI_UX_DESIGNER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.uiUxDesigner],
};

export const uiUxDesignerRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];