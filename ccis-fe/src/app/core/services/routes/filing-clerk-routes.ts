import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.FILING_CLERK.dashboard.key,
  path: ROUTES.FILING_CLERK.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.filingClerk],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.FILING_CLERK.requestDashboard.key,
  path: ROUTES.FILING_CLERK.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.filingClerk],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.FILING_CLERK.requestForm.key,
  path: ROUTES.FILING_CLERK.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.filingClerk],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.FILING_CLERK.viewRequestForm.key,
  path: ROUTES.FILING_CLERK.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.filingClerk],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.FILING_CLERK.notification.key,
  path: ROUTES.FILING_CLERK.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.filingClerk],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.FILING_CLERK.profile.key,
  path: ROUTES.FILING_CLERK.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.filingClerk],
};

export const filingClerkRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];