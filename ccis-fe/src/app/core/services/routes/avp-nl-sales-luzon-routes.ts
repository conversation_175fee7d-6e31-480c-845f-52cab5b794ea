import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.AVP_NL_SALES_LUZON.dashboard.key,
  path: ROUTES.AVP_NL_SALES_LUZON.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.avpNlSalesLuzon],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.AVP_NL_SALES_LUZON.requestDashboard.key,
  path: ROUTES.AVP_NL_SALES_LUZON.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.avpNlSalesLuzon],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.AVP_NL_SALES_LUZON.requestForm.key,
  path: ROUTES.AVP_NL_SALES_LUZON.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.avpNlSalesLuzon],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.AVP_NL_SALES_LUZON.viewRequestForm.key,
  path: ROUTES.AVP_NL_SALES_LUZON.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.avpNlSalesLuzon],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.AVP_NL_SALES_LUZON.notification.key,
  path: ROUTES.AVP_NL_SALES_LUZON.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.avpNlSalesLuzon],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.AVP_NL_SALES_LUZON.profile.key,
  path: ROUTES.AVP_NL_SALES_LUZON.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.avpNlSalesLuzon],
};

export const avpNlSalesLuzonRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];