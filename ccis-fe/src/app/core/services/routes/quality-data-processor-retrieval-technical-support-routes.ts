import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.QUALITY_DATA_PROCESSOR_RETRIEVAL_TECHNICAL_SUPPORT.dashboard.key,
  path: ROUTES.QUALITY_DATA_PROCESSOR_RETRIEVAL_TECHNICAL_SUPPORT.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.qualityDataProcessorRetrievalTechnicalSupport],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.QUALITY_DATA_PROCESSOR_RETRIEVAL_TECHNICAL_SUPPORT.requestDashboard.key,
  path: ROUTES.QUALITY_DATA_PROCESSOR_RETRIEVAL_TECHNICAL_SUPPORT.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.qualityDataProcessorRetrievalTechnicalSupport],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.QUALITY_DATA_PROCESSOR_RETRIEVAL_TECHNICAL_SUPPORT.requestForm.key,
  path: ROUTES.QUALITY_DATA_PROCESSOR_RETRIEVAL_TECHNICAL_SUPPORT.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.qualityDataProcessorRetrievalTechnicalSupport],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.QUALITY_DATA_PROCESSOR_RETRIEVAL_TECHNICAL_SUPPORT.viewRequestForm.key,
  path: ROUTES.QUALITY_DATA_PROCESSOR_RETRIEVAL_TECHNICAL_SUPPORT.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.qualityDataProcessorRetrievalTechnicalSupport],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.QUALITY_DATA_PROCESSOR_RETRIEVAL_TECHNICAL_SUPPORT.notification.key,
  path: ROUTES.QUALITY_DATA_PROCESSOR_RETRIEVAL_TECHNICAL_SUPPORT.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.qualityDataProcessorRetrievalTechnicalSupport],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.QUALITY_DATA_PROCESSOR_RETRIEVAL_TECHNICAL_SUPPORT.profile.key,
  path: ROUTES.QUALITY_DATA_PROCESSOR_RETRIEVAL_TECHNICAL_SUPPORT.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.qualityDataProcessorRetrievalTechnicalSupport],
};

export const qualityDataProcessorRetrievalTechnicalSupportRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];