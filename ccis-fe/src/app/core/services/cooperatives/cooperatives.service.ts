import httpClient from "@clients/httpClient";
import { IDefaultParams } from "@interface/common.interface";
import { TCooperativesPayload, TCooperativesWithIDAndIndexPayload } from "@state/types/cooperatives";

const apiResource = "cooperatives";
export const getCooperativessService = async (payload: IDefaultParams) => {
  let query = `${apiResource}?relations=cooperativeType|cooperativeAffiliations.affiliation|cooperativeOfficers.position|cooperativeCategory`;

  if (payload.filter) {
    query += `|cooperativeCategory&coopName[like]=${payload.filter}`;
  }
  if (payload.condition) {
    query += `&${payload.condition}`;
  }
  if (payload.pageSize) {
    query += `&pageSize=${payload.pageSize}`;
  }
  if (payload.page) {
    query += `&page=${payload.page}`;
  }
  if (payload.statusFilter) {
    query += `&status[eq]=${payload.statusFilter}`;
  }

  // query += `&pageSize=${payload.pageSize ?? 10}&page=${payload.page ?? 1}`;

  return httpClient.get(query);
};
// let queryParams =
//     "relations=product|quotation.quotationPremium|quotation.quotationCondition|quotation.gyrtQuotations|quotation.quotationClaimsExperienceAge|quotation.quotationClaimsExperienceYear|quotation.quotationCommissionDistribution|quotation.cooperative|quotation.gyrtAge|quotation.gyrtBenefits|approval.signatories.user.position|quotation.clspQuotation|quotation.clspPortfolioAges|quotation.clspPortfolioYears|quotation.clspBenefits|quotation.fipCoInsuredDependentBenefit|quotation.fipCoInsuredDependent|quotation.fipPrincipalMember|quotation.fipPrincipalMemberBenefit|quotation.fipAges|quotation.projection|quotation.rating|quotation.quotationCommissionDistribution|product.productRevisions.productGuidelines|quotation.clppBenefits|quotation.clppLoanPortfolioYears|quotation.clppLoanPortfolioAges&sort=updatedAt,desc";
//   // page;
//   if (params.page) {
//     queryParams += `&page=${params.page}`;
//   }
//   //pagesize
//   if (params.pageSize) {
//     queryParams += `&pageSize=${params.pageSize}`;
//   }
//   if (params.filter) {
//     queryParams += `&quotation.cooperative.coopName[like]=${params.filter}`;
//   }
//   // Filter by status
//   if (params.statusFilter) {
//     queryParams += `&status[eq]=${params.statusFilter}`;
//   }
//   // Filter by date
//   if (params?.dateFrom && params?.dateTo) {
//     queryParams += `&createdAt[between]=${params.dateFrom},${params.dateTo}`;
//   }
//   // Filter by product type
//   if (params?.productTypeFilter) {
//     queryParams += `&product.productTypeId[eq]=${params.productTypeFilter}`;
//   }
//   //add new condtion
//   if (params?.condition) {
//     queryParams += `${params.condition}`;
//   }
//   //Search by Cooperative name
//   if (params?.cooperative) {
//     queryParams += `&quotation.cooperative.coopName[like]=${params.cooperative}`;
//   }

//   if (params?.user) {
//     queryParams += `&createdBy[eq]=${params.user}`;
//   }
//   return httpClient.get(`actuary-evaluation-report?${queryParams}`);

export const getCooperativesByIDService = async (id: number) => {
  return httpClient.get(`${apiResource}/${id}`);
};

export const postCooperativesService = async (payload: TCooperativesPayload) => {
  return httpClient.post(`${apiResource}`, payload);
};

export const putCooperativesService = async (payload: TCooperativesPayload) => {
  return httpClient.put(`${apiResource}/${payload.id}`, payload);
};

export const destroyCooperativesService = async (payload: TCooperativesWithIDAndIndexPayload) => {
  return httpClient.delete(`${apiResource}/${payload.id}`);
};
