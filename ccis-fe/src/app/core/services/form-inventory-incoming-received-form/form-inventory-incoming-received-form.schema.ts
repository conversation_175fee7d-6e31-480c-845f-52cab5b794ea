import * as Yup from "yup";

export const CreateIncomingReceivedSchema = Yup.object().shape({
  divisionId: Yup.number().required("Division is required").min(1, "Please select a valid division"),
  formTypeId: Yup.number().required("Form type is required").min(1, "Please select a valid form type"),
  areaId: Yup.number().required("Area is required").min(1, "Please select a valid area"),
  receivedDate: Yup.string().required("Date is required"),
  seriesTo: Yup.number()
    .required("Series To is required")
    .min(1, "Series To must be a positive number")
    .test("is-greater-than-seriesFrom", "Series To must be greater than Series From", function (value) {
      const { seriesFrom } = this.parent;
      if (!seriesFrom || !value) return true;
      return value > seriesFrom;
    }),
  seriesFrom: Yup.number()
    .required("Series From is required")
    .min(1, "Series From must be a positive number")
    .test("no-zero-ending", "Series From should not end with 0", (value) => value !== undefined && value % 10 !== 0),
  atpNumber: Yup.string()
    .required("ATP Number is required")
    .matches(/^[a-zA-Z0-9]*$/, "ATP Number must be alphanumeric"),
  noPads: Yup.number().required("Number of Pads is required"),
  attachments: Yup.array()
    .of(
      Yup.object().shape({
        file: Yup.mixed().required("File is required"),
        label: Yup.string().required("Label is required"),
        description: Yup.string().required("Description is required"),
      })
    )
    .min(1, "Attachment is required")
    .required("Attachments are required"),
});

export const VerifyFormsSchema = Yup.object().shape({
  status: Yup.string().required("Status is required"),
  remarks: Yup.string().optional(),
});
