import httpClient from "@clients/httpClient"; // Import the HTTP client module for API requests
import { IDefaultParams } from "@interface/common.interface";
import { TUtilitiesPositionsPayload, TUtilitiesPositionsIDAndIndexPayload } from "@state/types/utilities-positions"; // Import type definitions for user payloads

const apiResource = "positions"; // Define the API resource endpoint for Positions

// Function to fetch Positions from the API
export const getPositionsService = async (params: IDefaultParams) => {
  let queryParams = "";
  if (params.page) {
    queryParams += `&page=${params.page}`;
  }
  if (params.pageSize) {
    queryParams += `&pageSize=${params.pageSize}`;
  }
  if (params.condition) {
    queryParams += `&${params.condition}`;
  }

  // Perform a GET request to fetch Positions with filters and roles information
  return httpClient.get(`${apiResource}?PositionName[like]=${params.filter}${queryParams}`);
};

// Function to fetch user data from the API
export const getPositionService = async (id: string | number) => {
  // Need roles when gettings single record
  return httpClient.get(`${apiResource}/${id}`); // Perform a GET request to fetch Positions with roles information
};

// Function to create a new user via POST request
export const postPositionsService = async (payload: TUtilitiesPositionsPayload) => {
  return httpClient.post(`${apiResource}`, payload); // Perform a POST request to create a new user with the provided data
};

// Function to update an existing user via PUT request
export const putPositionsService = async (payload: TUtilitiesPositionsPayload) => {
  return httpClient.put(`${apiResource}/${payload.id}`, payload); // Perform a PUT request to update an existing user with the provided data
};

// Function to delete a user by ID via DELETE request
export const destroyPositionsService = async (payload: TUtilitiesPositionsIDAndIndexPayload) => {
  return httpClient.delete(`${apiResource}/${payload.id}`); // Perform a DELETE request to delete a user by ID
};
