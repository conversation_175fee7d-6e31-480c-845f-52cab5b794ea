import { IDefaultParams } from "@interface/common.interface";
import { IIssuanceInterface, IssuanceResponse } from "@interface/master-policy.interface";
import { TIssuancePayload } from "@state/types/master-policy";
import { ProposableTypes } from "@enums/enums";

//TODO: Remove mock data once we got Back-End online
const mockData: IIssuanceInterface[] = [
  {
    id: 1,
    policyNumber: "----",
    proposalNumber: "0102",
    mainCode: "0101",
    CoopCode: "0102",
    CoopName: "J&E Cooperative",
    CoopBranch: "Cagayan De Oro Branch",
    productName: "Life Insurance",
    date: "1/25/2019",
    status: "Pending",
    proposalType: ProposableTypes.PRODUCT_REVISION,
  },
  {
    id: 2,
    policyNumber: "----",
    proposalNumber: "0103",
    mainCode: "0102",
    CoopCode: "0103",
    CoopName: "Santa Cruz Cooperative",
    CoopBranch: "Manila Branch",
    productName: "Accident Insurance",
    date: "10/31/2022",
    status: "Pending",
    proposalType: ProposableTypes.AER,
  }
];

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const getIssuanceService = async (params: IDefaultParams): Promise<{ data: IssuanceResponse }> => {
  await delay(500); // Simulate network delay
  
  let filteredProducts = [...mockData];
  
  // Apply filters
  if (params.filter) {
    filteredProducts = filteredProducts.filter(p => 
      p.productName.toLowerCase().includes(params.filter!.toLowerCase())
    );
  }

  // Pagination
  const current_page = params.page || 1;
  const pageSize = params.pageSize || 10;
  const startIndex = (current_page - 1) * pageSize;
  const paginatedProducts = filteredProducts.slice(startIndex, startIndex + pageSize);
  
  return {
    data: {
      data: paginatedProducts,
      meta: {
        total: filteredProducts.length,
        current_page,
        per_page: pageSize,
      }
    }
  };
};

export const getIssuanceServiceById = async (id: number): Promise<{ data: IIssuanceInterface }> => {
  await delay(300);
  const product = mockData.find(p => p.id === id);
  if (!product) throw new Error("Product not found");
  return { data: product };
};

export const postIssuanceService = async (payload: TIssuancePayload): Promise<{ data: IIssuanceInterface }> => {
  await delay(500);
  const newProduct: IIssuanceInterface = {
    id: Math.max(...mockData.map(p => p.id)) + 1,
    ...payload,
  };
  mockData.push(newProduct);
  return { data: newProduct };
};

export const putIssuanceService = async (id: number, payload: TIssuancePayload): Promise<{ data: IIssuanceInterface }> => {
  await delay(500);
  const index = mockData.findIndex(p => p.id === id);
  if (index === -1) throw new Error("Product not found");
  
  mockData[index] = {
    ...mockData[index],
    ...payload,
  };
  return { data: mockData[index] };
};

export const deleteIssuanceService = async (id: number): Promise<{ data: { message: string } }> => {
  await delay(300);
  const index = mockData.findIndex(p => p.id === id);
  if (index === -1) throw new Error("Product not found");
  
  mockData.splice(index, 1);
  return { data: { message: "Product deleted successfully" } };
};