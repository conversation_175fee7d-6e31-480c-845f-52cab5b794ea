import * as Yup from "yup";

export const CooperativeAffiliationSchema = Yup.object().shape({
  status: Yup.string().required("Affiliation status is required"),
  effectivityDate: Yup.string().required("Affiliation effectivity date is required"),
  affiliationId: Yup.string().required("Affiliation ID is required"),
  affilationName: Yup.string(),
});

export const CoopOfficerSchema = Yup.object().shape({
  title: Yup.string(),
  firstName: Yup.string().required("Officer first name is required"),
  middleName: Yup.string(),
  lastName: Yup.string().required("Officer last name is required"),
  generation: Yup.string(),
  gender: Yup.string().required("Officer gender is required"),
  emailAddress: Yup.string().email("Invalid email address"),
  contactNumber: Yup.string().required("Officer contact number is required"),
  effectivityDate: Yup.string().required("Officer effectivity date is required"),
  status: Yup.string().required("Officer status is required"),
  positionId: Yup.string().required("Officer position ID is required"),
  signatory: Yup.boolean().required("Specify if officer is a signatory"),
});

export const CreateCooperativeSchema = Yup.object().shape({
  coopName: Yup.string().required("Coop name is required"),
  coopAcronym: Yup.string().required("Coop acronym is required"),
  streetAddress: Yup.string().required("Street address is required"),
  barangay: Yup.string().required("Barangay is required"),
  city: Yup.string().required("City is required"),
  province: Yup.string().required("Province is required"),
  zipCode: Yup.string().required("Zip code is required"),
  emailAddress: Yup.string().email("Invalid email address"),
  websiteAddress: Yup.string().nullable().url("Invalid URL"),
  telephoneNumber: Yup.string(),
  //User may change their minds, future use
  cdaRegistrationNumber: Yup.string().required("CDA registration number is required"),
  cdaRegistrationDate: Yup.string().required("CDA registration date is required"),
  cdaCocNumber: Yup.string().required("CDA COC number is required"),
  cdaCocDate: Yup.string().required("CDA COC date is required"),
  taxIdNumber: Yup.string().required("Tax ID number is required"),
  taxIdDate: Yup.string().required("Tax ID date is required"),
  taxCteNumber: Yup.string().required("Tax CTE number is required"),
  taxCteExpiryDate: Yup.string().required("Tax CTE expiry date is required"),
  // cdaRegistrationNumber: Yup.string().nullable(),
  // cdaRegistrationDate: Yup.string().nullable(),
  // cdaCocNumber: Yup.string().nullable(),
  // cdaCocDate: Yup.string().nullable(),
  // taxIdNumber: Yup.string().nullable(),
  // taxIdDate: Yup.string().nullable(),
  // taxCteNumber: Yup.string().nullable(),
  // taxCteExpiryDate: Yup.string().nullable(),
  coopBranchesCount: Yup.number().typeError("Please use numerical values").min(0, "Please use a number greater than or equal to zero").required("Coop branches count is required"),
  coopMembersCount: Yup.number().typeError("Please use numerical values").min(0, "Please use a number greater than or equal to zero").required("Coop members count is required"),
  coopTotalAssets: Yup.number().typeError("Please use numerical values").min(0, "Please use a number greater than or equal to zero").required("Coop total assets is required"),
  coopMembersFemaleCount: Yup.number().typeError("Please use numerical values").min(0, "Please use a number greater than or equal to zero").required("Coop female members count is required"),
  coopMembersMaleCount: Yup.number().typeError("Please use numerical values").min(0, "Please use a number greater than or equal to zero").required("Coop male members count is required"),
  // status: Yup.string().required("Status is required"),
  cooperativeTypeId: Yup.string().required("Cooperative type ID is required"),
  cooperativeCategoryId: Yup.string().required("Cooperative category ID is required"),
  cooperativeAffiliations: Yup.array().of(CooperativeAffiliationSchema),
  cooperativeOfficers: Yup.array().of(CoopOfficerSchema),
  marketArea: Yup.number().typeError("Please use numerical values").min(0, "Please use a number greater than or equal to zero").required("Market area is required"),
  regionId: Yup.number().typeError("Please use numerical values").min(0, "Please use a number greater than or equal to zero").required("Region is required"),
});

export const agreementSchema = Yup.object().shape({
  agreementSignedDate: Yup.date().required("Agreement signed date is required"),
  agreementSignedRemarks: Yup.string().optional(),
  attachments: Yup.array()
    .of(
      Yup.object().shape({
        file: Yup.mixed().required("File is required"),
        label: Yup.string().required("Label is required"),
        description: Yup.string().required("Description is required"),
      })
    )
    .min(1, "At least one attachment is required"),
});

export const ApprovalSchema = Yup.object().shape({
  approveRejectDate: Yup.date().required("Date is required"),
  attachments: Yup.array()
    .of(
      Yup.object().shape({
        file: Yup.mixed().required("File is required"),
        label: Yup.string().required("Label is required"),
        description: Yup.string().required("Description is required"),
      })
    )
    .min(1, "At least one attachment is required"),
});

export const ApprovalRejectSchema = Yup.object().shape({
  approveRejectDate: Yup.date().required("Approve/Reject date is required"),
  approveRejectRemarks: Yup.string().required("Remarks is required"),
});

export const ProductProposalCommisionApprovalSchema = Yup.object().shape({
  signatoryTemplateId: Yup.number()
    .typeError("Signatory Template must be a number") // Ensures type error message
    .required("Signatory Template is required") // Ensures the field is required
    .positive("Signatory Template must be a positive number") // Ensures it is positive
    .integer("Signatory Template must be an integer"), // Ensures it is an integer
  approvableType: Yup.string().required("Approval Type required"),
  approvableId: Yup.number().required("Commission Id is required"),
  status: Yup.string().required("Status is required"),
});
