import { ModeOfpayment } from "@enums/payment";
import { IPaymentMethod } from "@interface/form-inventory-utilities";
import * as Yup from "yup";

export const CreateTransmittalAdminOutgoingSchema = Yup.object().shape({
  releasedArea: Yup.number().typeError("Release Area Must be a number").required("Required Released Area"),
  releasedTo: Yup.number().typeError("Release to must be a number").required("Required Released to"),
  releasedMethodId: Yup.number()
    .typeError("Value must be a number") // Handles non-number inputs
    .required("Required Released Method")
    .notOneOf([0], "Please Select the Requirement Template"),
  deliveredBy: Yup.string().when("releasedMethodId", {
    is: 1, // Assuming 1 = mailCourier ID
    then: (schema) => schema.required("Courier service name is required"),
    otherwise: (schema) => schema.notRequired(),
  }),
  trackingNo: Yup.string().when("releasedMethodId", {
    is: 1,
    then: (schema) => schema.required("Tracking number is required"),
    otherwise: (schema) => schema.notRequired(),
  }),
  status: Yup.string().required("Please fill up the Status"),
});

export const ReturnPadsValidationSchema = Yup.object().shape({
  releasedTo: Yup.number().notOneOf([0], "Please select a valid user").required("Released To is required"),
  releasedMethodId: Yup.number().notOneOf([0], "Please select a valid method").required("Released Method is required"),
  deliveredBy: Yup.string().required("This Field is required"),

  // Conditional required trackingNo
  trackingNo: Yup.string().when("releasedMethodId", ([val], schema) => (val === 5 ? schema.required("Tracking Number is required") : schema.notRequired())),
});

export const ReturnTransmittalValidationSchema = Yup.object().shape({
  releasedTo: Yup.number().notOneOf([0], "Please select a valid user").required("Released To is required"),
  releasedMethodId: Yup.number().notOneOf([0], "Please select a valid method").required("Released Method is required"),
  deliveredBy: Yup.string().required("This Field is required"),

  // Conditional required trackingNo
  trackingNo: Yup.string().when("releasedMethodId", ([val], schema) => (val === 5 ? schema.required("Tracking Number is required") : schema.notRequired())),
});

export const ReturnWithoutReleasedViaValidationSchema = Yup.object().shape({
  releasedTo: Yup.number().notOneOf([0], "Please select a valid user").required("Released To is required"),
});

export const CancelPadValidation = Yup.object().shape({
  remarks: Yup.string().required("Remarks are required"),
  attachments: Yup.array()
    .of(
      Yup.object().shape({
        file: Yup.mixed().required("File is required"),
      })
    )
    .min(1, "At least one attachment is required"),
});

export const IssueSeriesValidation = (paymentMethods: IPaymentMethod[]) => {
  const isRequired = (id: number, methods: string[]) => {
    const selectedMethod = paymentMethods.find((method) => method.id === id);
    // Check if it has selected payment method
    if (!selectedMethod) {
      return false;
    }
    return methods.includes(selectedMethod?.paymentMethodCode);
  };

  return Yup.object().shape({
    issuedBy: Yup.number(), // required
    remitTo: Yup.number().moreThan(0, "You must have select a user").required("Remit To is required"), // required
    productId: Yup.number().moreThan(0, "You must have select a product").required("Product is required"), // required
    cooperativeId: Yup.number().moreThan(0, "You must have select a cooperative").required("Cooperative is required"), // required
    releasedAt: Yup.string().required("Released date is required"), // required
    status: Yup.string(),
    remarks: Yup.string(),
    attachments: Yup.array()
      .of(
        Yup.object().shape({
          file: Yup.mixed().required("File is required"),
        })
      )
      .min(1, "At least one attachment is required"),

    paymentDetail: Yup.object().shape({
      paymentMethodId: Yup.number().moreThan(0, "You must have select a mode of payment").required("Payment Method is required"),

      amount: Yup.number().when("paymentMethodId", {
        is: (id: number) => isRequired(id, [ModeOfpayment.CASH, ModeOfpayment.CHEQUE, ModeOfpayment.DEPOSIT_SLIP, ModeOfpayment.COOP_SAVINGS]),
        then: (schema) => schema.moreThan(0, "Amount must not be 0").required(),
        otherwise: (schema) => schema.notRequired(),
      }),

      dateDeposit: Yup.string().when("paymentMethodId", {
        is: (id: number) => isRequired(id, [ModeOfpayment.CASH, ModeOfpayment.CHEQUE, ModeOfpayment.DEPOSIT_SLIP, ModeOfpayment.COOP_SAVINGS]),
        then: (schema) => schema.required("Date is required"),
        otherwise: (schema) => schema.notRequired(),
      }),

      accountNumber: Yup.string().when("paymentMethodId", {
        is: (id: number) => isRequired(id, [ModeOfpayment.CASH, ModeOfpayment.CHEQUE, ModeOfpayment.DEPOSIT_SLIP, ModeOfpayment.COOP_SAVINGS]),
        then: (schema) => schema.required("Account Number is required"),
        otherwise: (schema) => schema.notRequired(),
      }),

      bankAccountId: Yup.number().when("paymentMethodId", {
        is: (id: number) => isRequired(id, [ModeOfpayment.CASH, ModeOfpayment.CHEQUE, ModeOfpayment.DEPOSIT_SLIP]),
        then: (schema) => schema.moreThan(0, "You must select a bank").required(),
        otherwise: (schema) => schema.notRequired(),
      }),

      chequeNumber: Yup.string().when("paymentMethodId", {
        is: (id: number) => isRequired(id, [ModeOfpayment.CHEQUE]),
        then: (schema) => schema.required("Cheque Number is required"),
        otherwise: (schema) => schema.notRequired(),
      }),

      coopName: Yup.string().when("paymentMethodId", {
        is: (id: number) => isRequired(id, [ModeOfpayment.COOP_SAVINGS]),
        then: (schema) => schema.required("Coop Name is required"),
        otherwise: (schema) => schema.notRequired(),
      }),
    }),
  });
};
