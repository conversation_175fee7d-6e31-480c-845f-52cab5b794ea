import React, { ChangeEvent, useEffect, useState, useMemo } from "react";
import { useDebouncedCallback } from "use-debounce";
import Table from "@components/common/Table";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { TableColumn } from "react-data-table-component";
import { IActions, IDefaultParams } from "@interface/common.interface";
import { FormStatus, RoleType, InventoryStatus } from "@enums/form-status";
import { IFormTransmittal } from "@interface/form-inventory.interface";
import { InventoryMetric } from "@components/template/Card";
import FilterPanel from "@components/template/FilterPanel";
import MetricsGrid from "@components/template/MetricsGrid";
import { useRoleBasedInventoryData } from "@hooks/useRoleBasedInventoryData";
import { FormStatusLogic } from "@hooks/useStatusLogic";
import { useRoleBasedActions } from "@hooks/useRoleBasedActions";
import { getRoleBasedColumns } from "../TableColumns";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";

interface GenericInventoryProps {
  userRole: RoleType;
  title?: string;
  description?: string;
  additionalFilters?: any;
  customColumns?: TableColumn<any>[];
  customMetrics?: InventoryMetric[];
  isReturned?: boolean;
  isVerifyList?: boolean;
}

const GenericInventoryTab: React.FC<GenericInventoryProps> = ({
  userRole,
  title = "INVENTORY (ON-HAND)",
  description = "This page lists all forms across all received series. Use filters to narrow down results by division, or type.",
  additionalFilters = {},
  customColumns,
  // customMetrics,
  isVerifyList = false,
  isReturned = false,
}) => {
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();

  const [filters, setFilters] = useState<IDefaultParams>({
    filter: "",
    divisionFilter: undefined,
    type: undefined,
    dateFrom: "",
    dateTo: "",
    page: 1,
    pageSize: 10,
    ...additionalFilters,
  });

  const [resetCounter, setResetCounter] = useState(0);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [initialFormsData, setInitialFormsData] = useState<IFormTransmittal[]>([]);

  // Get divisions and form types from backend
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const formTypes = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);

  const { allFormsData, isDataLoading, fetchForms, userId } = useRoleBasedInventoryData({
    filters,
    userRole,
    additionalFilters,
    isReturned,
    isVerifyList,
  });

  const { getActionEvents: getRoleBasedActions } = useRoleBasedActions();

  useEffect(() => {
    getDivisions({ filter: "" });
    getFormTypes({ filter: "" });
    getAreas({ filter: "" });
  }, []);

  useEffect(() => {
    if (allFormsData && allFormsData.length > 0 && isInitialLoad) {
      setInitialFormsData(allFormsData);
    }
  }, [allFormsData, isInitialLoad]);

  const updateFilter = (key: keyof IDefaultParams, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value, page: 1 }));
  };

  const divisionOptions = useMemo(
    () => [
      // { value: 0, text: "Select Division" },
      ...divisions.map((value) => ({
        value: value.id,
        text: value.divisionName,
      })),
    ],
    [divisions]
  );

  const typeOptions = useMemo(
    () => [
      // { value: 0, text: "Select Type" },
      ...formTypes.map((value) => ({
        value: value.id,
        text: value.formTypeName,
      })),
    ],
    [formTypes]
  );

  const handleSearch = useDebouncedCallback((event: ChangeEvent<HTMLInputElement>) => {
    updateFilter("filter", event.target.value);
  }, 500);

  const handleDivisionChange = (event: ChangeEvent<HTMLSelectElement>) => {
    const value = parseInt(event.target.value);
    updateFilter("divisionFilter", value === 0 ? undefined : value);
  };

  const handleTypeChange = (event: ChangeEvent<HTMLSelectElement>) => {
    const value = parseInt(event.target.value);
    updateFilter("type", value === 0 ? undefined : value);
  };

  const handleDateFromChange = (event: ChangeEvent<HTMLInputElement>) => {
    updateFilter("dateFrom", event.target.value);
  };

  const handleDateToChange = (event: ChangeEvent<HTMLInputElement>) => {
    updateFilter("dateTo", event.target.value);
  };

  const handleClearAll = () => {
    setFilters({
      filter: "",
      divisionFilter: undefined,
      type: undefined,
      dateFrom: "",
      dateTo: "",
      page: 1,
      pageSize: 10,
      ...additionalFilters,
    });
    setResetCounter((prev) => prev + 1);
  };

  const handleRowsChange = (newPerPage: number, page: number) => {
    updateFilter("pageSize", newPerPage);
    updateFilter("page", page);
  };

  const handlePaginate = (page: number) => {
    updateFilter("page", page);
  };

  const getDisplayStatus = (row: any) => {
    if (isReturned) {
      return FormStatusLogic.getReturnedInventoryStatus(row);
    }

    switch (userRole) {
      case RoleType.IOC:
        if (isVerifyList) { return FormStatusLogic.getIocStatus(row); }
        return FormStatusLogic.getTransmittalInventoryStatus(row, userId);
      case RoleType.CHIEFCASHIER:
        return FormStatusLogic.getHeadCashierStatus(row);
      case RoleType.ADMINOUTGOING:
        return FormStatusLogic.getTransmittalInventoryStatus(row, userId);
      case RoleType.CLIFSA:
        return FormStatusLogic.getTransmittalInventoryStatus(row, userId);
      case RoleType.GAM:
        return FormStatusLogic.getGamDisplayStatus(row, userId);
      default:
        return row.status || "Unknown";
    }
  };

  // Use initial data for metrics, filtered data for table
  const metricsData = isInitialLoad ? allFormsData : initialFormsData;
  const tableData = allFormsData;

  // Memoize the metrics calculation to prevent unnecessary recalculations
  const inventoryMetrics: InventoryMetric[] = useMemo(() => {
    if (isInitialLoad && isDataLoading) {
      // Loading state metrics for returned forms
      if (isReturned) {
        return [
          { title: InventoryStatus.TOTAL_PADS, value: 0, variant: "primary" as const },
          { title: InventoryStatus.FOR_RECEIVING, value: 0, showIcon: true },
          { title: InventoryStatus.ON_HAND, value: 0, showIcon: true },
          { title: InventoryStatus.RETURNED, value: 0, showIcon: true },
        ];
      }

      // Original loading state metrics for different roles
      if (userRole === RoleType.IOC) {
        return [
          { title: InventoryStatus.TOTAL_PADS, value: 0, variant: "primary" as const },
          { title: InventoryStatus.INCOMING_RECEIVED, value: 0, showIcon: true },
          { title: InventoryStatus.VERIFIED, value: 0, showIcon: true },
          { title: InventoryStatus.DENIED, value: 0, showIcon: true },
        ];
      } else if (userRole === RoleType.CHIEFCASHIER) {
        return [
          { title: InventoryStatus.TOTAL_PADS, value: 0, variant: "primary" as const },
          { title: InventoryStatus.VERIFY_LIST, value: 0, showIcon: true },
          { title: InventoryStatus.VERIFIED, value: 0, showIcon: true },
          { title: InventoryStatus.REJECTED, value: 0, showIcon: true },
          { title: InventoryStatus.RELEASED, value: 0, showIcon: true },
        ];
      } else if (userRole === RoleType.GAM) {
        return [
          { title: InventoryStatus.TOTAL_PADS, value: 0, variant: "primary" as const },
          { title: InventoryStatus.FOR_RECEIVING, value: 0, showIcon: true },
          { title: InventoryStatus.ON_HAND, value: 0, showIcon: true },
          { title: InventoryStatus.RETURNED, value: 0, showIcon: true },
        ];
      } else {
        return [
          { title: InventoryStatus.TOTAL_PADS, value: 0, variant: "primary" as const },
          { title: InventoryStatus.FOR_RECEIVING, value: 0, showIcon: true },
          { title: InventoryStatus.ON_HAND, value: 0, showIcon: true },
          { title: InventoryStatus.RELEASED, value: 0, showIcon: true },
        ];
      }
    }

    // Metrics calculation for returned forms
    if (isReturned) {
      let forReceiving = 0;
      let onHand = 0;
      let returned = 0;
      let totalPads = 0;

      metricsData.forEach((form: IFormTransmittal) => {
        const displayStatus = getDisplayStatus(form);
        const returnedPadCount = form.returnedPads?.length || 0;

        totalPads += returnedPadCount;

        if (displayStatus === FormStatus.FOR_RECEIVING) {
          forReceiving += returnedPadCount;
        } else if (displayStatus === FormStatus.ON_HAND) {
          onHand += returnedPadCount;
        } else if (displayStatus === FormStatus.RETURNED) {
          returned += returnedPadCount;
        }
      });

      return [
        { title: InventoryStatus.TOTAL_PADS, value: totalPads, variant: "primary" as const },
        { title: InventoryStatus.FOR_RECEIVING, value: forReceiving, showIcon: true },
        { title: InventoryStatus.ON_HAND, value: onHand, showIcon: true },
        { title: InventoryStatus.RETURNED, value: returned, showIcon: true },
      ];
    }

    // Original metrics calculation for non-returned forms
    // IC/OG
    if (userRole === RoleType.IOC && isVerifyList) {
      let incomingReceived = 0;
      let verified = 0;
      let denied = 0;
      let totalPads = 0;

      metricsData.forEach((form: IFormTransmittal) => {
        const displayStatus = getDisplayStatus(form);
        const formPadCount = form.padAssignments?.length || 0;
        totalPads += formPadCount;

        if (displayStatus === FormStatus.INCOMING_RECEIVED) {
          incomingReceived += formPadCount;
        } else if (displayStatus === FormStatus.VERIFIED) {
          verified += formPadCount;
        } else if (displayStatus === FormStatus.DENIED) {
          denied += formPadCount;
        }
      });

      return [
        { title: InventoryStatus.TOTAL_PADS, value: totalPads, variant: "primary" as const },
        { title: InventoryStatus.INCOMING_RECEIVED, value: incomingReceived, showIcon: true },
        { title: InventoryStatus.VERIFIED, value: verified, showIcon: true },
        { title: InventoryStatus.DENIED, value: denied, showIcon: true },
      ];
    }

    // Chief Cashier
    if (userRole === RoleType.CHIEFCASHIER) {
      let verify_list = 0;
      let verified = 0;
      let rejected = 0;
      let released = 0;
      let totalPads = 0;

      metricsData.forEach((form: IFormTransmittal) => {
        const displayStatus = getDisplayStatus(form);
        const formPadCount = form.padAssignments?.length || 0;
        totalPads += formPadCount;

        if (displayStatus === FormStatus.VERIFY_LIST) {
          verify_list += formPadCount;
        } else if (displayStatus === FormStatus.VERIFIED) {
          verified += formPadCount;
        } else if (displayStatus === FormStatus.REJECTED) {
          rejected += formPadCount;
        }

        form.padAssignments?.forEach((padAssignment) => {
          const padStatus = padAssignment.status;
          if (padStatus !== FormStatus.PENDING) {
            released++;
            verified--;
          }
        });
      });

      return [
        { title: InventoryStatus.TOTAL_PADS, value: totalPads, variant: "primary" as const },
        { title: InventoryStatus.VERIFY_LIST, value: verify_list, showIcon: true },
        { title: InventoryStatus.VERIFIED, value: verified, showIcon: true },
        { title: InventoryStatus.DENIED, value: rejected, showIcon: true },
        { title: InventoryStatus.RELEASED, value: released, showIcon: true },
      ];
    }

    // GAM
    if (userRole === RoleType.GAM) {
      let forReceiving = 0;
      let onHand = 0;
      let returned = 0;
      let totalPads = 0;

      metricsData.forEach((form: IFormTransmittal) => {
        const displayStatus = getDisplayStatus(form);
        const formPadCount = form.padAssignments?.length || 0;
        const padCount = form.returnedPads?.length || 0;
        totalPads += formPadCount;
        totalPads += padCount;

        // Count based on form status, not individual pad assignments
        if (displayStatus === FormStatus.FOR_RECEIVING || displayStatus === FormStatus.NOT_YET_RECEIVED) {
          forReceiving += formPadCount;
        } else if (displayStatus === FormStatus.ON_HAND) {
          onHand += formPadCount;
        } else if (displayStatus === FormStatus.RETURNED) {
          returned += padCount;
        }
      });

      return [
        { title: InventoryStatus.TOTAL_PADS, value: totalPads, variant: "primary" as const },
        { title: InventoryStatus.FOR_RECEIVING, value: forReceiving, showIcon: true },
        { title: InventoryStatus.ON_HAND, value: onHand, showIcon: true },
        { title: InventoryStatus.RETURNED, value: returned, showIcon: true },
      ];
    }

    // Default metrics for other roles
    let forReceiving = 0;
    let onHand = 0;
    let released = 0;
    let totalPads = 0;

    metricsData.forEach((form: IFormTransmittal) => {
      const displayStatus = getDisplayStatus(form);
      const formPadCount = form.padAssignments?.length || 0;
      totalPads += formPadCount;

      // Count based on form status, not individual pad assignments
      if (displayStatus === FormStatus.FOR_RECEIVING || displayStatus === FormStatus.NOT_YET_RECEIVED) {
        forReceiving += formPadCount;
      } else if (displayStatus === FormStatus.ON_HAND) {
        onHand += formPadCount;
      } else if (displayStatus === FormStatus.RELEASED) {
        released += formPadCount;
      }
    });

    return [
      { title: InventoryStatus.TOTAL_PADS, value: totalPads, variant: "primary" as const },
      { title: InventoryStatus.FOR_RECEIVING, value: forReceiving, showIcon: true },
      { title: InventoryStatus.ON_HAND, value: onHand, showIcon: true },
      { title: InventoryStatus.RELEASED, value: released, showIcon: true },
    ];
  }, [metricsData, isInitialLoad, isDataLoading, userRole, userId]);

  const getActionEvents = (Transmittal: IFormTransmittal): IActions<any>[] => {
    return getRoleBasedActions(Transmittal, userRole, isReturned, isVerifyList, getDisplayStatus);
  };

  const roleBasedColumns = useMemo(() => getRoleBasedColumns(userRole, getActionEvents, divisions, formTypes, area, isReturned, isVerifyList), [userRole, getActionEvents, divisions, formTypes, area, isReturned, isVerifyList]);

  const finalColumns = customColumns || roleBasedColumns;

  const sortedTransmittal = useMemo(() => {
    if (!tableData || tableData.length === 0) return [];

    return tableData.slice().sort((a: IFormTransmittal, b: IFormTransmittal) => {
      const aStatus = getDisplayStatus(a);
      const bStatus = getDisplayStatus(b);

      if (aStatus === FormStatus.ON_HAND && bStatus !== FormStatus.ON_HAND) return -1;
      if (bStatus === FormStatus.ON_HAND && aStatus !== FormStatus.ON_HAND) return 1;

      if (aStatus === FormStatus.FOR_RECEIVING && bStatus !== FormStatus.FOR_RECEIVING) return -1;
      if (bStatus === FormStatus.FOR_RECEIVING && aStatus !== FormStatus.FOR_RECEIVING) return 1;

      return Number(b.id) - Number(a.id);
    });
  }, [tableData, userId, userRole]);

  
  useEffect(() => {
    if (!isDataLoading && isInitialLoad) {
      setIsInitialLoad(false);
    }
  }, [isDataLoading, isInitialLoad]);

  useEffect(() => {
    fetchForms();
  }, [filters, fetchForms]);

  // // Replace the filter change useEffect with this:
  // useEffect(() => {
  //   const hasChanged =
  //     filters.filter !== "" || filters.divisionFilter !== undefined || filters.type !== undefined || filters.dateFrom !== "" || filters.dateTo !== "" || filters.page !== 1 || filters.pageSize !== 10;

  //   if (hasChanged) {
  //     fetchForms();
  //   }
  // }, [filters, fetchForms]);

  return (
    <div className="p-4">
      <div className="text-xl font-semibold uppercase mt-4 tracking-wider text-[#042781]">{title}</div>
      <p className="pt-6 pb-4 font-poppins text-base text-[#01081C]">{description}</p>

      <MetricsGrid metrics={inventoryMetrics} isLoading={isInitialLoad && isDataLoading} />

      <div className="mt-8">
        <div className="flex flex-row justify-between">
          <FilterPanel
            filters={filters}
            resetCounter={resetCounter}
            onSearch={handleSearch}
            onDateFromChange={handleDateFromChange}
            onDateToChange={handleDateToChange}
            onDivisionChange={handleDivisionChange}
            onTypeChange={handleTypeChange}
            onClearAll={handleClearAll}
            divisionOptions={divisionOptions}
            typeOptions={typeOptions}
          />
        </div>
        <Table
          className="!min-h-[100%] h-[500px] border-[1px] border-zinc-300 mt-8 z-0"
          columns={finalColumns}
          data={sortedTransmittal || []}
          paginationServer={true}
          paginationTotalRows={tableData?.totalCount || 0}
          loading={isDataLoading}
          onChangeRowsPerPage={handleRowsChange}
          onPaginate={handlePaginate}
          searchable={false}
          multiSelect={false}
        />
      </div>
    </div>
  );
};

export default GenericInventoryTab;
