// components/MetricsGrid.tsx
import React from "react";
import { InventoryMetricCard, type InventoryMetric } from "@components/template/Card";

interface MetricsGridProps {
  metrics: InventoryMetric[];
  isLoading: boolean;
}

const MetricsGrid: React.FC<MetricsGridProps> = ({ metrics, isLoading }) => {
  // Determine grid classes based on number of metrics
  const getGridClasses = () => {
    const count = metrics.length;

    switch (count) {
      case 1:
        return "grid-cols-1";
      case 2:
        return "grid-cols-1 md:grid-cols-2";
      case 3:
        return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3";
      case 4:
        return "grid-cols-1 md:grid-cols-2 lg:grid-cols-4";
      case 5:
        return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5";
      case 6:
        return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6";
      default:
        return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4";
    }
  };

  const loading_text = ". . .";

  return (
    <div className={`grid ${getGridClasses()} gap-6`}>
      {metrics.map((metric, index) => (
        <div key={index} className="relative">
          {isLoading ? (
            <div className="bg-base-100 rounded-lg shadow-sm p-8 border border-base-300">
              <div className="animate-pulse">
                <div className="flex items-center justify-between mb-4">
                  <div className="h-4 bg-gray-200 rounded w-24 "></div>
                  <div className="h-6 w-6 bg-gray-200 rounded"></div>
                </div>
                <div className="h-8 bg-gray-200 rounded w-16 mb-2 text-gray-200">{loading_text}</div>
                <div className="h-3 bg-gray-100 rounded w-20"></div>
              </div>
            </div>
          ) : (
            <div className="group cursor-pointer">
              <div className="transform transition-all duration-300 group-hover:scale-105 group-hover:shadow-lg overflow-hidden rounded-lg bg-white">
                <InventoryMetricCard metric={metric}/>
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default MetricsGrid;
