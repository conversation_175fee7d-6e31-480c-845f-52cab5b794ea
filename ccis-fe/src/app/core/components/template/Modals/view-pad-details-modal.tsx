import Modal from "@components/common/Modal";
import Typography from "@components/common/Typography";
import TextField from "@components/form/TextField";
import { formatWordDateDDMMYYY } from "@helpers/date";
import { IFormTransmittal, IPadAssignments } from "@interface/form-inventory.interface";
import { useEffect, useRef, useState } from "react";
import { IoChevronBackOutline, IoChevronForwardOutline } from "react-icons/io5";
import AttachmentItem from "./AttachmentItem";
import { IAttachment } from "@interface/products.interface";
import { FormStatus } from "@enums/form-status";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { toast } from "react-toastify";

type ViewPadDetailsModalProps = {
  isViewOpen: boolean;
  isReturned?: boolean;
  handleToggleViewModal: () => void;
  padAssignment?: IPadAssignments;
  returnedPads?: IPadAssignments;
  formData?: IFormTransmittal | null;
};

const ViewPadDetailsModal = ({ isViewOpen, isReturned, handleToggleViewModal, padAssignment, returnedPads }: ViewPadDetailsModalProps) => {
  const [seriesNumber, setSeriesNumber] = useState(1);
  const [isEditing, setIsEditing] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const { userPadSeriesDetails } = useSelector((state: RootState) => state.formInventoryTransmittal);
  const loading = useSelector((state: RootState) => state?.formInventoryTransmittal?.userPadSeriesDetails?.loading);
  const { getUserPadSeriesDetails } = useTransmittalFormActions();
  const activePadData = isReturned ? returnedPads : padAssignment;

  // Use the fetched data from Redux store instead of activePadData
  const currentSeries = userPadSeriesDetails?.data || null;

  // Check if the current series is cancelled
  const isCancelled = currentSeries?.status === FormStatus.CANCELLED;

  // Function to fetch data for current series
  const fetchCurrentSeriesData = async (seriesNum: number) => {
    if (!activePadData?.padSeriesDetails || !Array.isArray(activePadData.padSeriesDetails)) {
      toast.error("padSeriesDetails not available or not an array");
      return;
    }
    // Get the actual series object from the padSeriesDetails array
    const currentSeriesObj = activePadData.padSeriesDetails[seriesNum - 1];

    getUserPadSeriesDetails({ id: currentSeriesObj.id });
  };

  const handleClick = () => {
    setIsEditing(true);
    setTimeout(() => {
      inputRef.current?.focus();
    }, 0);
  };

  const handleBlur = (e: React.FormEvent<HTMLDivElement>) => {
    const target = e.currentTarget as HTMLDivElement;
    const newSeriesNumber = parseInt(target.innerText);

    if (isNaN(newSeriesNumber) || newSeriesNumber < 1 || newSeriesNumber > 50 || (activePadData?.padSeriesDetails && activePadData?.padSeriesDetails.length < newSeriesNumber)) {
      // Revert to previous valid number
      target.innerText = seriesNumber.toString();
    } else {
      setSeriesNumber(newSeriesNumber);
    }

    setIsEditing(false);
  };

  const handleChange = (e: React.FormEvent<HTMLDivElement>) => {
    const target = e.currentTarget as HTMLDivElement;

    if (/^\d+$/.test(target.innerText)) {
      const series = parseInt(target.innerText, 10);

      // Check range
      if (series >= 1 && series <= 50) {
        // Don't update state here, wait for onBlur
        return;
      }
    }
  };

  const handleBack = () => {
    if (seriesNumber > 1) {
      setSeriesNumber((prev) => prev - 1);
    }
  };

  const handleForward = () => {
    const maxSeries = activePadData?.padSeriesDetails?.length || 50;
    if (seriesNumber < Math.min(maxSeries, 50)) {
      setSeriesNumber((prev) => prev + 1);
    }
  };

  // Add these state variables inside your ViewPadDetailsModal component, near the other useState declarations
  const [previewAttachment, setPreviewAttachment] = useState<IAttachment | null>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  // Add this handler function inside your ViewPadDetailsModal component
  const handleAttachmentPreview = (attachment: IAttachment) => {
    setPreviewAttachment(attachment);
    setIsPreviewOpen(true);
  };

  const handleClosePreview = () => {
    setIsPreviewOpen(false);
    setPreviewAttachment(null);
  };

  // Effect to fetch data when seriesNumber changes
  useEffect(() => {
    if (isViewOpen && activePadData) {
      fetchCurrentSeriesData(seriesNumber);
    }
  }, [seriesNumber, isViewOpen, activePadData]);

  // Initial fetch when modal opens
  useEffect(() => {
    if (isViewOpen && activePadData) {
      fetchCurrentSeriesData(seriesNumber);
    }
  }, [isViewOpen]);

  return (
    <Modal title="View Pad Details" modalContainerClassName="max-w-6xl" titleClass="text-primary text-lg uppercase" isOpen={isViewOpen} onClose={handleToggleViewModal}>
      <div className="w-full">
        {loading ? (
          <div className="flex flex-col justify-center items-center py-20 min-h-[400px]">
            <div className="loading loading-spinner loading-lg"></div>
            <span className="ml-2 mt-4 text-gray-600">Loading series data...</span>
          </div>
        ) : (
          <>
            <div className="w-full flex flex-col justify-end">
              {/* PR Number */}
              <div className="flex items-center justify-end">
                <span className="mr-1">PR NO: </span>
                <Typography size="3xl" className="text-red-500 font-poppins-semibold">
                  {currentSeries?.seriesNo || ""}
                </Typography>
              </div>

              {/* IF CANCELLED */}
              {isCancelled ? (
                <div className="mt-10 px-10">
                  <div>
                    <div className="flex gap-1">
                      <Typography size="2xl" className="font-poppins-medium">
                        Cancelled PR
                      </Typography>
                    </div>

                    <div className="mt-10">
                      <div className="grid grid-cols-3 gap-4 w-1/3">
                        <div className="col-span-1">
                          <div className="flex items-center justify-between">
                            <div className="">
                              <Typography size="lg" className="font-poppins-medium">
                                PR Date
                              </Typography>
                            </div>
                            <div className="">:</div>
                          </div>
                        </div>
                        <div className="col-span-2">{currentSeries?.createdAt ? formatWordDateDDMMYYY(currentSeries.createdAt, true) : ""}</div>
                      </div>
                      <div className="grid grid-cols-3 gap-4 w-1/3 mt-4">
                        <div className="col-span-1">
                          <div className="flex items-center justify-between">
                            <div className="">Cancel Date</div>
                            <div className="">:</div>
                          </div>
                        </div>
                        <div className="col-span-2">{currentSeries?.cancelledAt ? formatWordDateDDMMYYY(currentSeries.cancelledAt, true) : ""}</div>
                      </div>
                      <div className="grid grid-cols-3 gap-4 w-1/3 mt-4">
                        <div className="col-span-1">
                          <div className="flex items-center justify-between">
                            <div className="">Remarks</div>
                            <div className="">:</div>
                          </div>
                        </div>
                        <div className="col-span-2">{currentSeries?.remarks}</div>
                      </div>
                      <div className="mt-8 gap-4 flex justify-center">
                        <div className="w-full flex flex-col gap-10">
                          <div className="flex w-full flex-col">
                            <div className="divider divider-start font-semibold">ATTACHMENTS</div>
                          </div>
                          <div className="grid grid-cols-4 gap-4">
                            {currentSeries?.attachments &&
                              (currentSeries.attachments as IAttachment[]).map((attachment: IAttachment, index: number) => {
                                return (
                                  <AttachmentItem
                                    key={index}
                                    filename={attachment?.filename || ""}
                                    filepath={attachment?.filepath || ""}
                                    mimeType={attachment?.mimeType || ""}
                                    size={attachment?.size || 0}
                                    onClick={() => handleAttachmentPreview(attachment)}
                                  />
                                );
                              })}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="px-10">
                  {/* Return Details */}
                  <div className="mt-8 gap-4 flex justify-center">
                    <div className="w-full flex flex-col gap-10">
                      <div className="flex w-full flex-col">
                        <div className="divider divider-start font-semibold">RETURN DETAILS</div>
                      </div>
                      <div className="grid grid-cols-4 gap-4">
                        <div className="flex flex-col gap-2">
                          <span className="text-sm text-grey-500">Issue By</span>
                          <Typography size="sm" className="font-poppins-semibold">
                            {currentSeries?.issuedBy?.firstname} {currentSeries?.issuedBy?.lastname}
                          </Typography>
                        </div>
                        <div className="flex flex-col gap-2">
                          <span className="text-sm text-grey-500">Remit To</span>
                          <Typography size="sm" className="font-poppins-semibold">
                            {currentSeries?.remitTo?.firstname} {currentSeries?.remitTo?.lastname}
                          </Typography>
                        </div>
                        <div className="flex flex-col gap-2">
                          <span className="text-sm text-grey-500">Released At</span>
                          <Typography size="sm" className="font-poppins-semibold">
                            {currentSeries?.releasedAt ? formatWordDateDDMMYYY(currentSeries.releasedAt, true) : ""}
                          </Typography>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* PR Details */}
                  <div className="mt-8 gap-4 flex justify-center">
                    <div className="w-full flex flex-col gap-10">
                      <div className="flex w-full flex-col">
                        <div className="divider divider-start font-semibold">PR DETAILS</div>
                      </div>
                      <div className="grid grid-cols-4 gap-4">
                        <div className="flex flex-col gap-2">
                          <span className="text-sm text-grey-500">Division</span>
                          <Typography size="sm" className="font-poppins-semibold">
                            {currentSeries?.padAssignment?.form?.division?.divisionName}
                          </Typography>
                        </div>
                        <div className="flex flex-col gap-2">
                          <span className="text-sm text-grey-500">Type</span>
                          <Typography size="sm" className="font-poppins-semibold">
                            {currentSeries?.padAssignment?.form?.formType?.formTypeName}
                          </Typography>
                        </div>
                        <div className="flex flex-col gap-2">
                          <span className="text-sm text-grey-500">Area Released</span>
                          <Typography size="sm" className="font-poppins-semibold">
                            {currentSeries?.padAssignment?.form?.area?.areaName}
                          </Typography>
                        </div>
                        <div className="flex flex-col gap-2">
                          <span className="text-sm text-grey-500">Transmittal No.</span>
                          <Typography size="sm" className="font-poppins-semibold">
                            {currentSeries?.padAssignment?.formTransmittal?.transmittalNumber}
                          </Typography>
                        </div>
                        <div className="flex flex-col gap-2">
                          <span className="text-sm text-grey-500">ATP No.</span>
                          <Typography size="sm" className="font-poppins-semibold">
                            {currentSeries?.padAssignment?.form?.atpNumber}
                          </Typography>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* COOP Details */}
                  <div className="mt-8 gap-4 flex justify-center">
                    <div className="w-full flex flex-col gap-10">
                      <div className="flex w-full flex-col">
                        <div className="divider divider-start font-semibold">COOP DETAILS</div>
                      </div>
                      <div className="grid grid-cols-4 gap-4">
                        <div className="flex flex-col gap-2">
                          <span className="text-sm text-grey-500">Coop No.</span>
                          <Typography size="sm" className="font-poppins-semibold">
                            {currentSeries?.cooperative?.coopCode || ""}
                          </Typography>
                        </div>
                        <div className="flex flex-col gap-2">
                          <span className="text-sm text-grey-500">Coop Name</span>
                          <Typography size="sm" className="font-poppins-semibold">
                            {currentSeries?.cooperative?.coopName || ""}
                          </Typography>
                        </div>
                        <div className="flex flex-col gap-2">
                          <span className="text-sm text-grey-500">Coop Branch</span>
                          <Typography size="sm" className="font-poppins-semibold">
                            {currentSeries?.cooperative?.branchName || "Main Branch"}
                          </Typography>
                        </div>
                        <div className="flex flex-col gap-2">
                          <span className="text-sm text-grey-500">Product Name</span>
                          <Typography size="sm" className="font-poppins-semibold">
                            {currentSeries?.product?.productCode || ""}
                          </Typography>
                        </div>
                        <div className="flex flex-col gap-2">
                          <span className="text-sm text-grey-500">PR Date</span>
                          <Typography size="sm" className="font-poppins-semibold">
                            {currentSeries?.createdAt ? formatWordDateDDMMYYY(currentSeries.createdAt, true) : ""}
                          </Typography>
                        </div>
                        <div className="flex flex-col gap-2">
                          <span className="text-sm text-grey-500">Released Date</span>
                          <Typography size="sm" className="font-poppins-semibold">
                            {currentSeries?.releasedAt ? formatWordDateDDMMYYY(currentSeries.releasedAt, true) : ""}
                          </Typography>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Payment Details */}
                  {currentSeries?.paymentDetail && (
                    <div className="mt-8 gap-4 flex justify-center">
                      <div className="w-full flex flex-col gap-10">
                        <div className="flex w-full flex-col">
                          <div className="divider divider-start font-semibold">PAYMENT DETAILS</div>
                        </div>
                        <div className="grid grid-cols-4 gap-4">
                          <div className="flex flex-col gap-2">
                            <span className="text-sm text-grey-500">Payment Method</span>
                            <Typography size="sm" className="font-poppins-semibold">
                              {currentSeries.paymentDetail.paymentMethod?.paymentMethodName}
                            </Typography>
                          </div>
                          <div className="flex flex-col gap-2">
                            <span className="text-sm text-grey-500">Amount</span>
                            <Typography size="sm" className="font-poppins-semibold">
                              ₱{parseFloat(currentSeries.paymentDetail.amount).toLocaleString()}
                            </Typography>
                          </div>
                          <div className="flex flex-col gap-2">
                            <span className="text-sm text-grey-500">Cheque Number</span>
                            <Typography size="sm" className="font-poppins-semibold">
                              {currentSeries.paymentDetail.chequeNumber}
                            </Typography>
                          </div>
                          <div className="flex flex-col gap-2">
                            <span className="text-sm text-grey-500">Date Deposit</span>
                            <Typography size="sm" className="font-poppins-semibold">
                              {currentSeries.paymentDetail.dateDeposit ? formatWordDateDDMMYYY(currentSeries.paymentDetail.dateDeposit, true) : ""}
                            </Typography>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Attachment Details */}
                  <div className="mt-8 gap-4 flex justify-center">
                    <div className="w-full flex flex-col gap-10">
                      <div className="flex w-full flex-col">
                        <div className="divider divider-start font-semibold">ATTACHMENTS</div>
                      </div>
                      <div className="grid grid-cols-4 gap-4">
                        {currentSeries?.attachments && currentSeries.attachments.length > 0 ? (
                          (currentSeries.attachments as IAttachment[]).map((attachment: IAttachment, index: number) => {
                            return (
                              <AttachmentItem
                                key={index}
                                filename={attachment?.filename || ""}
                                filepath={attachment?.filepath || ""}
                                mimeType={attachment?.mimeType || ""}
                                size={attachment?.size || 0}
                                onClick={() => handleAttachmentPreview(attachment)}
                              />
                            );
                          })
                        ) : (
                          <div className="col-span-4 text-center text-gray-500 py-4">No attachments available</div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Official Receipt Details */}
                  <div className="mt-8 gap-4 flex justify-center">
                    <div className="w-full flex flex-col gap-10">
                      <div className="flex w-full flex-col">
                        <div className="font-semibold">OFFICIAL RECEIPT(s) DETAILS</div>
                      </div>
                      <div className="grid grid-cols-4 gap-4">
                        <div className="flex flex-col gap-2 border border-[#01081C26] rounded py-5 px-4 col-span-4 shadow-lg">
                          <div className="flex flex-col gap-2 w-1/3">
                            <span className="text-sm text-grey-500">Official Receipt Number</span>
                            <TextField size="sm" disabled placeholder="No OR Number" value={currentSeries?.orNumber || ""} />
                            {currentSeries?.orNumber && (
                              <div>
                                <span className="text-[#60a5fa] text-sm underline cursor-pointer">View Details</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Pagination - only show when not loading */}
              <div className="w-full mt-8 flex justify-end">
                <div className="flex">
                  <div
                    onClick={handleBack}
                    className={`border border-[#a3a3a3] rounded-l-lg py-2 px-2 flex items-center justify-center cursor-pointer ${
                      seriesNumber === 1 ? "opacity-50 cursor-not-allowed" : "hover:bg-gray-100"
                    }`}
                  >
                    <IoChevronBackOutline />
                  </div>
                  <div
                    className="tooltip border-t border-b border-[#a3a3a3] py-2 px-4 flex items-center justify-center cursor-pointer hover:bg-gray-100"
                    data-tip="Go to specific PR(1–50)"
                    contentEditable={isEditing}
                    suppressContentEditableWarning={true}
                    onClick={handleClick}
                    onBlur={handleBlur}
                    onInput={handleChange}
                  >
                    {seriesNumber}
                  </div>
                  <div
                    onClick={handleForward}
                    className={`border rounded-r-lg py-2 px-2 border-[#a3a3a3] flex items-center justify-center cursor-pointer ${
                      seriesNumber >= Math.min(activePadData?.padSeriesDetails?.length || 50, 50) ? "opacity-50 cursor-not-allowed" : "hover:bg-gray-100"
                    }`}
                  >
                    <IoChevronForwardOutline />
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
      {isPreviewOpen && previewAttachment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-auto m-4 w-full">
            <div className="flex justify-between items-center p-4 border-b">
              <h3 className="text-lg font-semibold">Preview: {previewAttachment.filename}</h3>
              <button onClick={handleClosePreview} className="text-gray-500 hover:text-gray-700 text-2xl">
                ×
              </button>
            </div>
            <div className="p-4">
              {previewAttachment.mimeType === "application/pdf" ? (
                <iframe src={`${import.meta.env.VITE_AWS_S3_ENDPOINT}/${previewAttachment.filepath}`} className="w-full h-[70vh] border-0" title={previewAttachment.filename} />
              ) : ["image/jpeg", "image/png", "image/jpg"].includes(previewAttachment.mimeType || "") ? (
                <img src={`${import.meta.env.VITE_AWS_S3_ENDPOINT}/${previewAttachment.filepath}`} alt={previewAttachment.filename} className="max-w-full max-h-[70vh] object-contain mx-auto" />
              ) : (
                <div className="text-center py-8">
                  <p>Preview not available for this file type</p>
                  <a
                    href={`${import.meta.env.VITE_AWS_S3_ENDPOINT}/${previewAttachment.filepath}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="mt-4 inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                  >
                    Open in New Tab
                  </a>
                </div>
              )}
            </div>
            <div className="flex justify-between items-center p-4 border-t bg-gray-50">
              <div className="text-sm text-gray-600">Size: {((typeof previewAttachment.size === "string" ? parseInt(previewAttachment.size) : previewAttachment.size || 0) / 1024).toFixed(1)} KB</div>
              <div className="flex gap-2">
                <a
                  href={`${import.meta.env.VITE_AWS_S3_ENDPOINT}/${previewAttachment.filepath}`}
                  download={previewAttachment.filename}
                  className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors"
                >
                  Download
                </a>
                <a
                  href={`${import.meta.env.VITE_AWS_S3_ENDPOINT}/${previewAttachment.filepath}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-primary text-white px-3 py-1 rounded text-sm hover:bg-info transition-colors"
                >
                  Open in New Tab
                </a>
              </div>
            </div>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default ViewPadDetailsModal;
