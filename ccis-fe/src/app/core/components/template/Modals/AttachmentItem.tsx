import { FaRegFilePdf } from "react-icons/fa";

type AttachmentItemProps = {
  filepath?: string;
  mimeType?: string;
  filename?: string;
  size?: number | string;
  isForViewing?: boolean;
  onClick?: () => void; // Add this optional prop
};

const AttachmentItem = ({ 
  filepath = "", 
  mimeType = "", 
  filename = "", 
  size = 0, 
  isForViewing = false,
  onClick // Add this to destructuring
}: AttachmentItemProps) => {
  const fullPath = `${import.meta.env.VITE_AWS_S3_ENDPOINT}/${filepath}`;
  const fileSize = typeof size === "string" ? parseInt(size) : size;

  const handleClick = () => {
    if (!isForViewing) {
      // If a custom onClick is provided, use it; otherwise use default behavior
      if (onClick) {
        onClick();
      } else {
        window.open(`${fullPath}`, "_blank");
      }
    }
  };

  return (
    <div>
      {/* PDF */}
      {mimeType === "application/pdf" && (
        <div 
          className={`bg-[#f5f5f5] rounded px-4 py-6 w-60 h-14 flex items-center gap-3 shadow-md ${
            !isForViewing && "cursor-pointer hover:bg-gray-200 transition-colors"
          }`} 
          onClick={handleClick}
        >
          <FaRegFilePdf size={30} />
          <div className="flex flex-col">
            <span className="text-sm font-semibold">{filename}</span>
            <span className="text-xs">{(fileSize / 1024).toFixed(2)} KB</span>
          </div>
        </div>
      )}

      {/* Images */}
      {["image/jpeg", "image/png", "image/jpg"].includes(mimeType) && (
        <div className={!isForViewing ? "cursor-pointer" : ""} onClick={handleClick}>
          <img 
            className="h-30 w-56 object-cover rounded shadow-md hover:shadow-lg transition-shadow" 
            src={fullPath} 
            alt={filename}
          />
        </div>
      )}
    </div>
  );
};

export default AttachmentItem;