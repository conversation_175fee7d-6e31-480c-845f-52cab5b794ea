import Button from "@components/common/Button";
import Typography from "@components/common/Typography";
import Loader from "@components/Loader";
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { IFormTransmittal, IPadAssignments } from "@interface/form-inventory.interface";
import { toast } from "react-toastify";
import { VerifyFormsSchema } from "@services/form-inventory-incoming-received-form/form-inventory-incoming-received-form.schema";
import { confirmSaveOrEdit } from "@helpers/prompt";
import { useFormik } from "formik";
import { FormStatus } from "@enums/form-status";
import { useUserManagementActions } from "@state/reducer/users-management";
import { findItem } from "@helpers/array";
import Table from "@components/common/Table";
import { getColumns } from "@components/template/TableColumns/completed-column";
import { IActions, IDefaultParams } from "@interface/common.interface";
import { FaEye } from "react-icons/fa";
import ViewPadDetailsModal from "@components/template/Modals/view-pad-details-modal";
import { navigateBack } from "@helpers/navigatorHelper";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";

const ViewReturnedFormReceiving: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [data, setData] = useState<IFormTransmittal | null>(null);
  const { getUsers } = useUserManagementActions();
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const formTypes = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const userId = useSelector((state: RootState) => state?.auth?.user.data?.id);
  // Get data from Redux state
  const returnedTransmittalData = useSelector((state: RootState) => state.formInventoryTransmittal.getReturnedTransmittalTrail);
  const returnLoading = useSelector((state: RootState) => state.formInventoryTransmittal.getReturnedTransmittalTrail.loading);
  const filterDivision = "";
  const filterFormType = "";
  const filterArea = "";
  const filterUser = "";

  const putTrailSuccess = useSelector((state: RootState) => state.formInventoryTransmittal.putFormTransmittalTrail?.success);
  

  const { getReturnedTransmittalTrail, putFormTransmittalTrail } = useTransmittalFormActions();
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const padDetails = data;
  const [viewPadAssignmentId, setViewPadAssignmentId] = useState<number | undefined>();

  const [isViewOpen, setIsViewOpen] = useState<boolean>(false);

  const getActionEvents = (row: IPadAssignments): IActions<any>[] => {
    const actions: IActions<any>[] = [
      {
        name: "View",
        event: () => {
          setViewPadAssignmentId(row.id);
          handleToggleViewModal();
        },
        icon: FaEye,
        color: "primary",
      },
    ];
    return actions;
  };

  const columns = getColumns({
    getActionEvents,
    transmittalNumber: data?.transmittalNumber,
    divisionName: String(findItem(divisions, "id", Number(data?.returnedPads?.[0]?.form?.divisionId), "divisionName") || "N/A"),
  });
  const handleToggleViewModal = () => {
    setIsViewOpen((prev) => !prev);
  };

  const formik = useFormik({
    initialValues: {
      status: "",
    },
    validationSchema: VerifyFormsSchema,
    onSubmit: async (values, { resetForm }) => {
      const isConfirmed = await confirmSaveOrEdit("Are you sure you want to receive this transmittal?");
      if (isConfirmed) {
        try {
          if (data?.latestFormTransmittalTrail?.id) {
            await putFormTransmittalTrail({
              id: data.latestFormTransmittalTrail?.id,
              status: values.status,
            });
            resetForm();
          } else {
            toast.error("Failed to process form: Form Transmittal Trail ID is undefined");
          }
        } catch (error) {
          toast.error("Failed to approve form");
        }
      }
    },
  });

  // Handle put operation success
  useEffect(() => {
    if (putTrailSuccess) {
      toast.success("Received form successfully");
      navigateBack();
    }
  }, [putTrailSuccess]);

  const fetchForm = async () => {
    try {
      if (id) {
        await getReturnedTransmittalTrail({ id: Number(id) } as IDefaultParams);
      }
    } catch (error) {
      toast.error("Failed to load proposal data. Please try again later.");
    }
  };

  // Add useEffect to handle the Redux state update
  useEffect(() => {
    if (returnedTransmittalData.data && !returnedTransmittalData.loading) {
      setData(returnedTransmittalData.data);
    }
  }, [returnedTransmittalData.data, returnedTransmittalData.loading]);

  // Call fetchForm when component mounts or id changes
  useEffect(() => {
    fetchForm();
  }, [id]);

  useEffect(() => {
    getDivisions({ filter: filterDivision });
    getFormTypes({ filter: filterFormType });
    getAreas({ filter: filterArea });
  }, []);

  useEffect(() => {
    getUsers({ filter: filterUser });
  }, []);

  // REALEASED METHOD
  const trail = data?.formTransmittalTrails?.find((item: any) => item.releasedTo?.id === userId);
  const method = trail?.releasedMethod?.releasedMethodName;

  return returnLoading ? (
    <div className="flex justify-center items-center h-screen">
      <Loader />
    </div>
  ) : (
    <div>
      <Button classNames="btn bg-slate-600 btn-sm" onClick={() => navigateBack()}>
        Back
      </Button>
      <div className="mx-6">
        <Typography className="mt-6 text-primary font-poppins-semibold">FOR RECEIVING DETAILS</Typography>

        <div className="mt-8 gap-4 flex justify-center">
          <div className="w-full flex flex-col gap-10">
            <div className="flex w-full flex-col">
              <div className="divider divider-start font-semibold">RETURN DETAILS</div>
            </div>
            <div className="grid grid-cols-4 gap-4 pb-4">
              <div className="flex flex-col gap-2">
                <span className="text-sm text-grey-500">Return Transmittal No.</span>
                <p className="text-sm">{data?.transmittalNumber}</p>
              </div>
              <div className="flex flex-col gap-2">
                <span className="text-sm text-grey-500">Area Returned</span>
                <p className="text-sm">{String(findItem(area, "id", Number(data?.releasedAreaId), "areaName") || "N/A")}</p>
              </div>
              <div className="flex flex-col gap-2">
                <span className="text-sm text-grey-500">Returned To</span>
                <p className="text-sm">{data?.releasedTo ? `${data?.releasedTo?.firstname} ${data?.releasedTo?.middlename || ""} ${data?.releasedTo?.lastname}` : "N/A"}</p>
              </div>
              <div className="flex flex-col gap-2">
                <span className="text-sm text-grey-500">Date Returned</span>
                <p className="text-sm">{data?.returnedPads?.[0]?.returnedAt ? new Date(data?.returnedPads?.[0]?.returnedAt).toLocaleDateString("en-US") : "N/A"}</p>
              </div>
            </div>
            <div className="grid grid-cols-4 gap-4">
              {method && (
                <div className="flex flex-col gap-2">
                  <span className="text-sm text-grey-500">Returned Via</span>
                  <p className="text-sm">{method}</p>
                </div>
              )}

              {method && String(method) === "Mail Courier" && (
                <div className="flex flex-col gap-2">
                  <span className="text-sm text-grey-500">Courier Service</span>
                  <p className="text-sm">{trail?.deliveredBy || "N/A"}</p>
                </div>
              )}

              {method && String(method) !== "Mail Courier" && (
                <div className="flex flex-col gap-2">
                  <span className="text-sm text-grey-500">Handed By</span>
                  <p className="text-sm">{trail?.deliveredBy || "N/A"}</p>
                </div>
              )}

              {method && (
                <div className="flex flex-col gap-2">
                  <span className="text-sm text-grey-500">Tracking No.</span>
                  <p className="text-sm">{trail?.trackingNo || "N/A"}</p>
                </div>
              )}
            </div>
            <div className="flex w-full flex-col">
              <div className="divider divider-start font-semibold">SERIES OVERVIEW</div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-3 grid-flow-col gap-4">
                <div className="p-2">
                  <span className="text-sm text-grey-500">Division</span>
                  <div className="border-b-2 border-slate-300 max-w-40 text-sm">
                    <p>{String(findItem(divisions, "id", Number(data?.returnedPads?.[0]?.form?.divisionId), "divisionName") || "N/A")}</p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Type</p>
                  <div className="border-b-2 border-slate-300 w-33 text-sm">
                    <p>{String(findItem(formTypes, "id", Number(data?.returnedPads?.[0]?.form?.formTypeId), "formTypeName") || "N/A")}</p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">AREA</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    <p>{String(findItem(area, "id", Number(data?.returnedPads?.[0]?.form?.areaId), "areaName") || "N/A")}</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="border rounded-md border-slate-300">
              <div className="mb-4 flex w-full">
                <div className="overflow-auto max-h-64 w-full">
                  <Table
                    className="!min-h-[100%] h-[300px] border-[1px] border-zinc-300"
                    columns={columns}
                    data={padDetails?.returnedPads || []}
                    searchable={false}
                    multiSelect={false}
                    selectable={false}
                    paginationTotalRows={data?.returnedPads?.length || 0}
                    paginationServer={true}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* View Modal */}
        <ViewPadDetailsModal
          isViewOpen={isViewOpen}
          isReturned={true}
          handleToggleViewModal={handleToggleViewModal}
          returnedPads={padDetails?.returnedPads?.find((pad) => pad.id === viewPadAssignmentId)}
          formData={data}
        />
        <div className="flex justify-center gap-2 mt-4">
          <Button
            type="submit"
            classNames="bg-sky-500 w-80 font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2"
            onClick={() => {
              formik.setFieldValue("status", FormStatus.RECEIVED);
              formik.handleSubmit();
            }}
          >
            Receive
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ViewReturnedFormReceiving;
