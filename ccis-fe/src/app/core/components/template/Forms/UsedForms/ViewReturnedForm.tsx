import Button from "@components/common/Button";
import Typography from "@components/common/Typography";
import Loader from "@components/Loader";
import React, { useEffect, useState } from "react";
import { FaCircleDot, FaClockRotateLeft, FaPrint } from "react-icons/fa6";
import { LiaDotCircleSolid } from "react-icons/lia";
import { useParams } from "react-router-dom";

import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { useAreaAdminActions } from "@state/reducer/form-inventory-utilities-area-admins";
import { IFormTransmittal } from "@interface/form-inventory.interface";
import { toast } from "react-toastify";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { useUserManagementActions } from "@state/reducer/users-management";

import httpClient from "@clients/httpClient";
import { RoleType } from "@enums/form-status";
import { findItem } from "@helpers/array";
import { FaEye } from "react-icons/fa";
import ViewPadDetailsModal from "@components/template/Modals/view-pad-details-modal";
import { navigateBack } from "@helpers/navigatorHelper";
import { IDefaultParams } from "@interface/common.interface";

const ViewReturnedForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [data, setData] = useState<IFormTransmittal | null>(null);
  // const [activityLogs, setActivityLogs] = useState<IFormActivityLogs | null>(null);
  const userId = useSelector((state: RootState) => state?.auth?.user.data?.id);
  const [currentPage] = useState(1);
  const [viewPadAssignmentId, setViewPadAssignmentId] = useState<number | undefined>();
  const [isViewOpen, setIsViewOpen] = useState<boolean>(false);
  // Get data from Redux state
  const returnedTransmittalData = useSelector((state: RootState) => state.formInventoryTransmittal.getReturnedTransmittalTrail);
  const returnLoading = useSelector((state: RootState) => state.formInventoryTransmittal.getReturnedTransmittalTrail.loading);

  const activityLogsResponse = useSelector((state: RootState) => state.formInventoryTransmittal.getFormActivityLogs);
  const activityLogs = activityLogsResponse?.data || [];
  const activityLogsLoading = activityLogsResponse?.loading || false;

  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const formTypes = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const areaAdmins = useSelector((state: RootState) => state.formInventoryUtilitiesAreaAdmins.areaAdmins);
  const filterDivision = "";
  const filterFormType = "";
  const filterArea = "";

  const { getReturnedTransmittalTrail, getFormActivityLogs } = useTransmittalFormActions();
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const { getAreaAdmins } = useAreaAdminActions();
  const { getUsers } = useUserManagementActions();

  const fetchForm = async () => {
    try {
      if (id) {
        await getReturnedTransmittalTrail({ id: Number(id) } as IDefaultParams);
      }
    } catch (error) {
      toast.error("Failed to load proposal data. Please try again later.");
    }
  };

  // Add useEffect to handle the Redux state update
  useEffect(() => {
    if (returnedTransmittalData.data && !returnedTransmittalData.loading) {
      setData(returnedTransmittalData.data);

      // Get the current pad assignment ID
      const currentPadAssignment = returnedTransmittalData.data.returnedPads?.[currentPage - 1];
      if (currentPadAssignment) {
        const padAssignmentId = currentPadAssignment.id;

        getFormActivityLogs({ id: Number(padAssignmentId) } as IDefaultParams);
      }
    }
  }, [returnedTransmittalData.data, returnedTransmittalData.loading, currentPage]);

  // Call fetchForm when component mounts or id changes
  useEffect(() => {
    fetchForm();
  }, [id]);

  const handlePrintPDF = async () => {
    try {
      if (!data?.id) {
        toast.error("Missing Form Transmittal ID.");
        return;
      }
      const response: any = await httpClient.post("/form-transmittal/export-letter/outgoing-cashier", { formTransmittalId: data.id }, { responseType: "blob" });

      // Use the same blob creation pattern as the working component
      const blob = new Blob([response], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);
      window.open(url, "_blank");

      // Clean up the URL after a delay
      setTimeout(() => window.URL.revokeObjectURL(url), 1000);
    } catch (error) {
      toast.error(`PDF export failed: ${String(error)}`);
    }
  };

  const handlePrintReportPDF = async () => {
    try {
      if (!data?.id) {
        toast.error("Missing Form Transmittal ID.");
        return;
      }
      const formTransmittalIds = Array.isArray(data?.id) ? data.id : [data?.id];

      const response: any = await httpClient.post("/form-transmittal/export-report/outgoing-cashier", { formTransmittalIds }, { responseType: "blob" });

      // Use the same blob creation pattern as the working component
      const blob = new Blob([response], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);
      window.open(url, "_blank");

      // Clean up the URL after a delay
      setTimeout(() => window.URL.revokeObjectURL(url), 1000);
    } catch (error) {
      toast.error(`PDF export failed: ${String(error)}`);
    }
  };

  const hasClifsaAssignment = data?.returnedPads?.some((assignment) => {
    const areaName = area.find((a) => a.id === assignment.form?.areaId)?.areaName;
    return areaName === RoleType.CLIFSA;
  });

  useEffect(() => {
    fetchForm();
  }, [id]);

  useEffect(() => {
    getDivisions({ filter: filterDivision });
    getFormTypes({ filter: filterFormType });
    getAreas({ filter: filterArea });
    getAreaAdmins({ filter: "" });
    getUsers({ filter: "" });
  }, []);

  // useEffect(() => {
  //   if (data) {
  //     const fetchActivityLogs = async () => {
  //       const currentPadAssignment = data.returnedPads?.[currentPage - 1];
  //       if (currentPadAssignment) {
  //         const padAssignmentId = currentPadAssignment.id;
  //         const activityLogsResponse = await getFormActivityLogsService(padAssignmentId);
  //         if (activityLogsResponse?.data) {
  //           setActivityLogs(activityLogsResponse.data);
  //         }
  //       }
  //     };
  //     fetchActivityLogs();
  //   }
  // }, [currentPage, data]);

  useEffect(() => {
    if (!data?.releasedAreaId) return;

    const matchedAdmin = areaAdmins.find((admin) => admin.userAreaId === data.releasedAreaId);

    // Only update if releasedTo is not already correct
    if (matchedAdmin && data.releasedToId !== matchedAdmin.userId) {
      setData((prevData) => ({
        ...prevData,
        releasedToId: matchedAdmin.userId,
      }));
    }
  }, [data?.releasedAreaId, areaAdmins]);

  // REALEASED METHOD
  const trail = data?.formTransmittalTrails?.find((item: any) => item.releasedTo?.id === userId);
  const method = trail?.releasedMethod?.releasedMethodName;

  return returnLoading || activityLogsLoading? (
    <div className="flex justify-center items-center h-screen">
      <Loader />
    </div>
  ) : (
    <div>
      <Button
        classNames="btn bg-slate-600 btn-sm"
        onClick={() => {
          navigateBack();
        }}
      >
        Back
      </Button>
      <div className="mx-6">
        <Typography className="mt-6 text-primary font-poppins-semibold uppercase">Transmittal Returned Details</Typography>
        <div className="flex justify-end gap-2">
          <Button classNames="btn btn-sm bg-slate-600 text-slate-900" onClick={handlePrintPDF}>
            <FaPrint /> Print Transmittal Letter
          </Button>

          {hasClifsaAssignment && (
            <Button classNames="btn btn-sm bg-slate-600 text-slate-900" onClick={handlePrintReportPDF}>
              <FaPrint /> Print Transmittal Report
            </Button>
          )}
        </div>
        <div className="mt-8 gap-4 flex justify-center">
          <div className="w-5/6 ">
            <div className="flex w-full flex-col">
              <div className="divider divider-start">Series Overview</div>
            </div>
            <div className="border rounded border-slate-300 p-6">
              <div className="grid grid-cols-3 gap-4">
                <div className="p-2">
                  <p className="text-sm">Division</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    <p>{String(findItem(divisions, "id", Number(data?.returnedPads?.[0]?.form?.divisionId), "divisionName") || "N/A")}</p>
                  </div>
                </div>

                <div className="p-2">
                  <p className="text-sm">Type</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    <p>{String(findItem(formTypes, "id", Number(data?.returnedPads?.[0]?.form?.formTypeId), "formTypeCode") || "N/A")}</p>
                  </div>
                </div>

                <div className="p-2">
                  <p className="text-sm">Area</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    <p>{String(findItem(area, "id", Number(data?.returnedPads?.[0]?.form?.areaId), "areaName") || "N/A")}</p>
                  </div>
                </div>

                {method && (
                  <div className="p-2">
                    <p className="text-sm">Returned Via</p>
                    <div className="border-b-2 border-slate-300 max-w-40 text-sm">
                      <p>{trail?.releasedMethod?.releasedMethodName}</p>
                    </div>
                  </div>
                )}

                {/* Conditional rendering */}
                {method && (
                  <>
                    {String(method) === "Mail Courier" ? (
                      <div className="p-2">
                        <p className="text-sm">Courier Service</p>
                        <div className="border-b-2 border-slate-300 w-32 text-sm">
                          <p>{trail?.deliveredBy || "N/A"}</p>
                        </div>
                      </div>
                    ) : (
                      <div className="p-2">
                        <p className="text-sm">Handed By</p>
                        <div className="border-b-2 border-slate-300 w-32 text-sm">
                          <p>{trail?.deliveredBy || "N/A"}</p>
                        </div>
                      </div>
                    )}
                  </>
                )}

                {trail?.trackingNo && (
                  <div className="p-2">
                    <p className="text-sm">Tracking No.</p>
                    <div className="border-b-2 border-slate-300 w-32 text-sm">
                      <p>{trail?.trackingNo}</p>
                    </div>
                  </div>
                )}

                <div className="p-2">
                  <p className="text-sm">Return Transmittal No.</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    <p>{data?.transmittalNumber}</p>
                  </div>
                </div>

                <div className="p-2">
                  <p className="text-sm">Area Released</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    <p>{String(findItem(area, "id", Number(data?.releasedAreaId), "areaName") || "N/A")}</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="border rounded-md border-slate-300 p-2 mt-4 mb-4 flex w-full">
              <table className="w-full">
                <thead className="bg-gradient-to-r from-zinc-50 to-indigo-50 p-4">
                  <tr>
                    <th className="px-2 text-sm border-zinc-100">Pad Number</th>
                    <th className="p-4 text-sm">Series From</th>
                    <th className="p-4 text-sm">Series To</th>
                    <th className="p-4 text-sm">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {data?.returnedPads?.map((assignment) => (
                    <tr key={assignment.id}>
                      <td className="p-4 text-sm border border-slate-100 text-center">{assignment.padNumber}</td>
                      <td className="p-4 text-sm border border-slate-100 text-center">{assignment.seriesFrom}</td>
                      <td className="p-4 text-sm border border-slate-100 text-center">{assignment.seriesTo}</td>
                      <td className="p-4 text-sm border border-slate-100 text-center">
                        {
                          <button
                            className="btn btn-sm"
                            onClick={() => {
                              setViewPadAssignmentId(assignment.id);
                              setIsViewOpen(true);
                            }}
                          >
                            <FaEye className="mr-1" /> View
                          </button>
                        }
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="flex w-full flex-col p-4 border border-slate-300 rounded">
              <div className="divider divider-start uppercase">Assignee Details</div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <p className="text-sm">Returned By</p>
                  <div className="border-b-2 border-slate-300 w-auto text-sm">
                    {data?.latestFormTransmittalTrail?.createdBy
                      ? `${data?.latestFormTransmittalTrail?.createdBy?.firstname} 
                    ${data?.latestFormTransmittalTrail?.createdBy?.middlename || ""} 
                    ${data?.latestFormTransmittalTrail?.createdBy?.lastname}`
                      : "N/A"}
                  </div>
                </div>
                <div>
                  <p className="text-sm">Returned To</p>
                  <div className="border-b-2 border-slate-300 w-full text-sm">
                    {data?.latestFormTransmittalTrail?.releasedTo
                      ? `${data?.latestFormTransmittalTrail?.releasedTo?.firstname} ${
                          data?.latestFormTransmittalTrail?.releasedTo?.middlename || ""
                        } ${data?.latestFormTransmittalTrail?.releasedTo?.lastname}`
                      : "N/A"}
                  </div>
                </div>
                <div>
                  <p className="text-sm">Date Returned</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    {data?.latestFormTransmittalTrail?.createdAt
                      ? new Date(data.latestFormTransmittalTrail?.createdAt).toLocaleDateString("en-US", {
                          day: "2-digit",
                          month: "2-digit",
                          year: "numeric",
                        })
                      : "N/A"}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="col-span-1 rounded border border-slate-300 max-w-3/5 p-4 max-h-screen overflow-y-auto">
            <div className="border-b-2 border-slate-200 p-4 justify-center flex gap-2 mb-2">
              <FaClockRotateLeft className="text-sm mt-1" /> <p>Status</p>
            </div>
            {Array.isArray(activityLogs) &&
              activityLogs
                .slice()
                .reverse()
                .map((log, index) => (
                  <React.Fragment key={log.id}>
                    <div className="flex gap-4">
                      <div className="flex gap-4">
                        <div>
                          <div className="text-xs text-end">
                            {new Date(log.created_at).toLocaleDateString("en-US", {
                              day: "2-digit",
                              month: "2-digit",
                              year: "numeric",
                            })}
                          </div>
                          <div className="text-xs text-slate-400 text-end">
                            {new Date(log.created_at).toLocaleTimeString("en-US", {
                              hour: "2-digit",
                              minute: "2-digit",
                            })}
                          </div>
                        </div>
                        <div>
                          <div>
                            {index === 0 ? <FaCircleDot className="text-primary size-6 mt-1" /> : <LiaDotCircleSolid className="text-zinc-400 size-6 mt-1" />}
                            {index !== activityLogs.length - 1 && (
                              <div className="flex mt-1 flex-col items-center">
                                <div className="h-8 border-l-2 border-slate-300"></div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      <div>
                        <div>{log.description}</div>
                        <div className="text-xs">
                          {log.causer.firstname} {log.causer.lastname} | {log.causer.position.positionName}
                        </div>
                      </div>
                    </div>
                  </React.Fragment>
                ))}
          </div>

          {/* Only render the modal when it's actually open */}
          {isViewOpen && (
            <ViewPadDetailsModal
              isViewOpen={isViewOpen}
              isReturned={true}
              handleToggleViewModal={() => setIsViewOpen(false)}
              returnedPads={data?.returnedPads?.find((pad) => pad.id === viewPadAssignmentId)}
              formData={data}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default ViewReturnedForm;
