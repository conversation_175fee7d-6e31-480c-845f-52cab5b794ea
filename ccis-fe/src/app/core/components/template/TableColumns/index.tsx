// columns/roleBasedColumns.ts
import { TableColumn } from "react-data-table-component";
import { Column } from "@enums/table-columns";
import ActionDropdown from "@components/common/ActionDropdown";
import Typography from "@components/common/Typography";
import { RoleType } from "@enums/form-status";
import { findItem } from "@helpers/array";

// Common column settings
export const commonSetting = {
  sortable: true,
  reorder: true,
};

export const getDivisionName = (row: any, divisions: any[]): string => {
  if (row.division?.divisionName) {
    return row.division.divisionName;
  }
  if (row.divisionId && divisions.length > 0) {
    const division = divisions.find(d => d.id === row.divisionId);
    return division?.divisionName || "N/A";
  }
  if (typeof row.division === 'string') {
    return row.division;
  }
  return "N/A";
};

export const getFormTypeName = (row: any, formTypes: any[]): string => {
  if (row.formType?.formTypeName) {
    return row.formType.formTypeName || "N/A";
  }
  if (row.formTypeId && formTypes.length > 0) {
    const formType = formTypes.find(ft => ft.id === row.formTypeId);
    return formType?.formTypeName || "N/A";
  }
  if (typeof row.formType === 'string') {
    return row.formType || "N/A";
  }
  return "N/A";
};

// Helper function to get the appropriate data source based on isReturned
const getDataSource = (row: any, isReturned?: boolean) => {
  return isReturned ? row.returnedPads || [] : row.padAssignments || [];
};

// Base columns that are shared across most roles
const getBaseColumns = (getActionEvents: (row: any) => any[]): TableColumn<any>[] => [
  {
    name: Column.NO,
    cell: (row) => row.id,
    width: "80px",
    ...commonSetting,
  },
  {
    name: <Typography className="flex flex-1 justify-center !text-black !text-xs">{Column.ACTION}</Typography>,
    cell: (row, rowIndex) => <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />,
  },
];

// Role-specific column extensions
const getIocColumns = (divisions: any[], isReturned?: boolean): TableColumn<any>[] => [
  {
    name: Column.DIVISION,
    cell: (row) => getDivisionName(row, divisions),
    ...commonSetting,
  },
  {
    name: Column.TYPE,
    cell: (row) => row.formType.formTypeName || "N/A",
    ...commonSetting,
  },
  {
    name: Column.AREA,
    cell: (row) => row.area?.areaName || "N/A",
    ...commonSetting,
  },
  {
    name: Column.SERIES_FROM,
    cell: (row) => row.seriesFrom || "N/A",
    ...commonSetting,
  },
  {
    name: Column.SERIES_TO,
    cell: (row) => row.seriesTo || "N/A",
    ...commonSetting,
  },
  {
    name: Column.NO_OF_PADS,
    cell: (row) => getDataSource(row, isReturned).length || "N/A",
    width: "120px",
    ...commonSetting,
  },
];

const getHeadCashierColumns = (divisions: any[], formTypes: any[], isReturned?: boolean): TableColumn<any>[] => [
  {
    name: Column.DIVISION,
    cell: (row) => getDivisionName(row, divisions),
    ...commonSetting,
  },
  {
    name: Column.TYPE,
    cell: (row) => getFormTypeName(row, formTypes),
    ...commonSetting,
  },
  {
    name: Column.AREA,
    cell: (row) => row.area?.areaName || "N/A",
    ...commonSetting,
  },
  {
    name: Column.SERIES_FROM,
    cell: (row) => row.seriesFrom || "N/A",
    ...commonSetting,
  },
  {
    name: Column.SERIES_TO,
    cell: (row) => row.seriesTo || "N/A",
    ...commonSetting,
  },
  {
    name: Column.NO_OF_PADS,
    cell: (row) => getDataSource(row, isReturned).length || "N/A",
    width: "120px",
    ...commonSetting,
  },
];

const getAdminOutgoingColumns = (divisions: any[], formTypes: any[], area: any[], isReturned?: boolean): TableColumn<any>[] => [
  {
    name: Column.TRANSMITTAL_NO,
    cell: (row) => row.transmittalNumber,
    ...commonSetting,
  },
  {
    name: Column.DIVISION,
    cell: (row) => {
      const dataSource = getDataSource(row, isReturned);
      const divisionsList = Array.from(
        new Set(
          dataSource
            .map((assignment: { form?: { divisionId?: number } }) => {
              const division = divisions.find((division) => division.id === assignment.form?.divisionId);
              return division ? division.divisionName : null;
            })
            .filter(Boolean)
        )
      );
      return divisionsList.length > 0 ? divisionsList.join(", ") : "N/A";
    },
    ...commonSetting,
  },
  {
    name: Column.TYPE,
    cell: (row) => {
      const dataSource = getDataSource(row, isReturned);
      const uniqueFormTypes = Array.from(
        new Set(
          dataSource
            .map((assignment: { form?: { formTypeId?: number } }) => {
              const formType = formTypes.find((type) => type.id === assignment.form?.formTypeId);
              return formType ? formType.formTypeCode : null;
            })
            .filter(Boolean)
        )
      );
      return uniqueFormTypes.length > 0 ? uniqueFormTypes.join(", ") : "N/A";
    },
    ...commonSetting,
  },
  {
    name: Column.AREA,
    cell: (row) => <div>{String(findItem(area, "id", Number(row?.releasedAreaId), "areaName") || "N/A")}</div>,
    ...commonSetting,
  },
  {
    name: Column.NO_OF_PADS,
    cell: (row) => getDataSource(row, isReturned).length || "N/A",
    width: "120px",
    ...commonSetting,
  },
];

const getClifsaColumns = (divisions: any[], formTypes: any[], area: any[], isReturned?: boolean): TableColumn<any>[] => [
  {
    name: Column.TRANSMITTAL_NO,
    cell: (row) => row.transmittalNumber,
    ...commonSetting,
  },
  {
    name: Column.DIVISION,
    cell: (row) => {
      const dataSource = getDataSource(row, isReturned);
      const divisionsList = Array.from(
        new Set(
          dataSource
            .map((assignment: { form?: { divisionId?: number } }) => {
              const division = divisions.find((division) => division.id === assignment.form?.divisionId);
              return division ? division.divisionName : null;
            })
            .filter(Boolean)
        )
      );
      return divisionsList.length > 0 ? divisionsList.join(", ") : "N/A";
    },
    ...commonSetting,
  },
  {
    name: Column.TYPE,
    cell: (row) => {
      const dataSource = getDataSource(row, isReturned);
      const uniqueFormTypes = Array.from(
        new Set(
          dataSource
            .map((assignment: { form?: { formTypeId?: number } }) => {
              const formType = formTypes.find((type) => type.id === assignment.form?.formTypeId);
              return formType ? formType.formTypeCode : null;
            })
            .filter(Boolean)
        )
      );
      return uniqueFormTypes.length > 0 ? uniqueFormTypes.join(", ") : "N/A";
    },
    ...commonSetting,
  },
  {
    name: Column.AREA,
    cell: (row) => <div>{String(findItem(area, "id", Number(row?.releasedAreaId), "areaName") || "N/A")}</div>,
    ...commonSetting,
  },
  {
    name: Column.NO_OF_PADS,
    cell: (row) => getDataSource(row, isReturned).length || "N/A",
    width: "120px",
    ...commonSetting,
  },
];

const getGamColumns = (divisions: any[], formTypes: any[], area: any[], isReturned?: boolean): TableColumn<any>[] => [
  {
    name: Column.TRANSMITTAL_NO,
    cell: (row) => row.transmittalNumber,
    ...commonSetting,
  },
  {
    name: Column.DIVISION,
    cell: (row) => {
      // For GAM, try both data sources
      let dataSource = getDataSource(row, isReturned);
      if (dataSource.length === 0) {
        dataSource = getDataSource(row, !isReturned); // Try the other data source
      }
      
      const divisionsList = Array.from(
        new Set(
          dataSource
            .map((assignment: { form?: { divisionId?: number } }) => {
              const division = divisions.find((division) => division.id === assignment.form?.divisionId);
              return division ? division.divisionName : null;
            })
            .filter(Boolean)
        )
      );
      return divisionsList.length > 0 ? divisionsList.join(", ") : "-----";
    },
    ...commonSetting,
  },
  {
    name: Column.TYPE,
    cell: (row) => {
      // For GAM, try both data sources
      let dataSource = getDataSource(row, isReturned);
      if (dataSource.length === 0) {
        dataSource = getDataSource(row, !isReturned); // Try the other data source
      }
      
      const uniqueFormTypes = Array.from(
        new Set(
          dataSource
            .map((assignment: { form?: { formTypeId?: number } }) => {
              const formType = formTypes.find((type) => type.id === assignment.form?.formTypeId);
              return formType ? formType.formTypeCode : null;
            })
            .filter(Boolean)
        )
      );
      return uniqueFormTypes.length > 0 ? uniqueFormTypes.join(", ") : "-----";
    },
    ...commonSetting,
  },
  {
    name: Column.AREA,
    cell: (row) => {
      const areaName = String(findItem(area, "id", Number(row?.releasedAreaId), "areaName") || "N/A");
      return areaName !== "N/A" ? areaName : "-----";
    },
    ...commonSetting,
  },
  {
    name: Column.NO_OF_PADS,
    cell: (row) => {
      // For GAM, try both data sources
      let dataSource = getDataSource(row, isReturned);
      if (dataSource.length === 0) {
        dataSource = getDataSource(row, !isReturned); // Try the other data source
      }
      
      return dataSource.length > 0 ? dataSource.length : "-----";
    },
    width: "120px",
    ...commonSetting,
  },
];


const getReturnedColumns = (divisions: any[], formTypes: any[], area: any[], isReturned?: boolean): TableColumn<any>[] => [
  {
    name: Column.TRANSMITTAL_NO,
    cell: (row) => row.transmittalNumber,
    ...commonSetting,
  },
  {
    name: Column.DIVISION,
    cell: (row) => {
      const dataSource = getDataSource(row, isReturned);
      const divisionsList = Array.from(
        new Set(
          dataSource
            .map((assignment: { form?: { divisionId?: number } }) => {
              const division = divisions.find((division) => division.id === assignment.form?.divisionId);
              return division ? division.divisionName : null;
            })
            .filter(Boolean)
        )
      );
      return divisionsList.length > 0 ? divisionsList.join(", ") : "N/A";
    },
    ...commonSetting,
  },
  {
    name: Column.TYPE,
    cell: (row) => {
      const dataSource = getDataSource(row, isReturned);
      const uniqueFormTypes = Array.from(
        new Set(
          dataSource
            .map((assignment: { form?: { formTypeId?: number } }) => {
              const formType = formTypes.find((type) => type.id === assignment.form?.formTypeId);
              return formType ? formType.formTypeCode : null;
            })
            .filter(Boolean)
        )
      );
      return uniqueFormTypes.length > 0 ? uniqueFormTypes.join(", ") : "N/A";
    },
    ...commonSetting,
  },
  {
    name: Column.AREA,
    cell: (row) => <div>{String(findItem(area, "id", Number(row?.releasedAreaId), "areaName") || "N/A")}</div>,
    ...commonSetting,
  },
  {
    name: Column.NO_OF_PADS,
    cell: (row) => getDataSource(row, isReturned).length || "N/A",
    width: "120px",
    ...commonSetting,
  },
];

// Default columns (fallback)
const getDefaultColumns = (divisions: any[], formTypes: any[], area: any[], isReturned?: boolean): TableColumn<any>[] => [
  {
    name: Column.TRANSMITTAL_NO,
    cell: (row) => row.transmittalNumber,
    ...commonSetting,
  },
  {
    name: Column.DIVISION,
    cell: (row) => {
      const dataSource = getDataSource(row, isReturned);
      const divisionsList = Array.from(
        new Set(
          dataSource
            .map((assignment: { form?: { divisionId?: number } }) => {
              const division = divisions.find((division) => division.id === assignment.form?.divisionId);
              return division ? division.divisionName : null;
            })
            .filter(Boolean)
        )
      );
      return divisionsList.length > 0 ? divisionsList.join(", ") : "N/A";
    },
    ...commonSetting,
  },
  {
    name: Column.TYPE,
    cell: (row) => {
      const dataSource = getDataSource(row, isReturned);
      const uniqueFormTypes = Array.from(
        new Set(
          dataSource
            .map((assignment: { form?: { formTypeId?: number } }) => {
              const formType = formTypes.find((type) => type.id === assignment.form?.formTypeId);
              return formType ? formType.formTypeCode : null;
            })
            .filter(Boolean)
        )
      );
      return uniqueFormTypes.length > 0 ? uniqueFormTypes.join(", ") : "N/A";
    },
    ...commonSetting,
  },
  {
    name: Column.AREA,
    cell: (row) => <div>{String(findItem(area, "id", Number(row?.releasedAreaId), "areaName") || "N/A")}</div>,
    ...commonSetting,
  },
  {
    name: Column.NO_OF_PADS,
    cell: (row) => getDataSource(row, isReturned).length || "N/A",
    width: "120px",
    ...commonSetting,
  },
];

// Main function to get role-based columns
export const getRoleBasedColumns = (
  userRole: RoleType,
  getActionEvents: (row: any) => any[],
  divisions: any[],
  formTypes: any[],
  area: any[],
  isReturned?: boolean,
  isVerifyList?: boolean

): TableColumn<any>[] => {
  const baseColumns = getBaseColumns(getActionEvents);
  let roleSpecificColumns: TableColumn<any>[] = [];

  switch (userRole) {
    case RoleType.IOC:
      if (isReturned) {
        roleSpecificColumns = getReturnedColumns(divisions, formTypes, area, isReturned);
      } else if (isVerifyList) {
        roleSpecificColumns = getIocColumns(divisions);
      } else {
        roleSpecificColumns = getDefaultColumns(divisions, formTypes, area, isReturned);
      }
      break;
    case RoleType.CHIEFCASHIER:
      if (isReturned) {
        roleSpecificColumns = getReturnedColumns(divisions, formTypes, area, isReturned);
      } else {
        roleSpecificColumns = getHeadCashierColumns(divisions, formTypes);
      }
      break;
    case RoleType.ADMININCOMING:
      roleSpecificColumns = getReturnedColumns(divisions, formTypes, area, isReturned);
      break;
    case RoleType.ADMINOUTGOING:
      roleSpecificColumns = getAdminOutgoingColumns(divisions, formTypes, area, isReturned);
      break;
    case RoleType.CLIFSA:
      roleSpecificColumns = getClifsaColumns(divisions, formTypes, area, isReturned);
      break;
    case RoleType.GAM:
      roleSpecificColumns = getGamColumns(divisions, formTypes, area, isReturned);
      break;
    default:
      roleSpecificColumns = getDefaultColumns(divisions, formTypes, area, isReturned);
      break;
  }

  // Combine base columns with role-specific columns
  const actionColumn = baseColumns.pop(); // Remove action column temporarily
  return [...baseColumns, ...roleSpecificColumns, actionColumn!];
};