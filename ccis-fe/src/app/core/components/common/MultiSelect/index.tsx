// Import dependencies
import { FC, MouseEvent, FocusEvent, useState, Fragment, useRef, useEffect } from "react";
import ErrorText from "@components/common/ErrorText";
import classNames from "classnames";
import { ISelectOptions } from "@interface/common.interface";

type TOptions = {
  value: string | number;
  text: string;
  disabled?: boolean;
};

// Define the props type for the MultiSelect component
type TMultiSelectProps = {
  options?: TOptions[] | ISelectOptions[];
  value?: (string | number)[];
  placeholder?: string;
  name?: string;
  id?: string;
  required?: boolean;
  readOnly?: boolean;
  className?: string;
  variant?: "primary" | "secondary" | "danger" | "default";
  size?: "xs" | "sm" | "md" | "lg";
  disabled?: boolean;
  error?: boolean;
  errorText?: string;
  errorIcon?: boolean;
  maxHeight?: string;
  onClick?: (e: MouseEvent<HTMLDivElement>) => void;
  onChange?: (selectedValues: (string | number)[]) => void;
  onBlur?: (e: FocusEvent<HTMLDivElement>) => void;
};

/**
 * MultiSelect component that looks like a select but allows multiple selections
 */
const MultiSelect: FC<TMultiSelectProps> = ({
  options = [],
  value = [],
  placeholder = "Choose from options",
  name,
  id,
  // required = false,
  readOnly = false,
  className = "",
  variant = "primary",
  size = "md",
  disabled = false,
  error = false,
  errorText,
  errorIcon = false,
  maxHeight = "200px",
  onChange = () => {},
  onClick = () => {},
  onBlur = () => {},
}) => {
  const [selectedValues, setSelectedValues] = useState<(string | number)[]>(value);
  const [isOpen, setIsOpen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Generate the CSS classes for the select-like container
  const selectClass = classNames(`select w-full select-bordered select-${variant} cursor-pointer`, `select-${size}`, { "select-error": error }, { "select-disabled": disabled }, className);

  // Handle option selection
  const handleOptionClick = (optionValue: string | number) => {
    if (disabled || readOnly) return;

    let newSelectedValues: (string | number)[];

    if (selectedValues.includes(optionValue)) {
      // Remove if already selected
      newSelectedValues = selectedValues.filter((val) => val !== optionValue);
    } else {
      // Add if not selected
      newSelectedValues = [...selectedValues, optionValue];
    }

    setSelectedValues(newSelectedValues);
    onChange(newSelectedValues);
  };

  // Handle container click to toggle dropdown
  const handleContainerClick = (e: MouseEvent<HTMLDivElement>) => {
    if (!disabled && !readOnly) {
      setIsOpen(!isOpen);
      onClick(e);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: Event) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Update selected values when value prop changes
  useEffect(() => {
    setSelectedValues(value);
  }, [value]);

  // Get display text for selected values
  const getDisplayText = () => {
    if (selectedValues.length === 0) {
      return placeholder;
    }

    const selectedTexts = selectedValues.map((val) => {
      const option = options.find((opt) => (opt.value ?? opt) === val);
      return option ? (option.text ?? option) : val;
    });

    if (selectedTexts.length <= 2) {
      return selectedTexts.join(", ");
    } else {
      return `${selectedTexts.slice(0, 2).join(", ")} +${selectedTexts.length - 2} more`;
    }
  };

  const displayText = getDisplayText();
  const hasSelection = selectedValues.length > 0;

  return (
    <Fragment>
      <div ref={containerRef} className="relative">
        {/* Hidden input for form submission */}
        <input type="hidden" name={name} value={JSON.stringify(selectedValues)} />

        {/* Select-like container */}
        <div
          id={id}
          className={`${selectClass} flex items-center justify-between ${hasSelection ? "text-base-content" : "text-base-content/50"}`}
          onClick={handleContainerClick}
          onBlur={onBlur}
          tabIndex={disabled ? -1 : 0}
        >
          <span className="truncate">{displayText}</span>
          <svg className={`w-4 h-4 transition-transform duration-200 ${isOpen ? "rotate-180" : ""}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>

        {/* Dropdown options */}
        {isOpen && !disabled && !readOnly && (
          <div className="absolute z-50 w-full mt-1 bg-base-100 border border-base-300 rounded-lg shadow-lg" style={{ maxHeight, overflowY: "auto" }}>
            {options.map((opt, index) => {
              const optionValue = opt.value ?? opt;
              const optionText = opt.text ?? opt;
              const isSelected = selectedValues.includes(optionValue);
              const isDisabled = opt?.disabled;

              return (
                <div
                  key={`opt-${index}`}
                  className={classNames("px-3 py-2 cursor-pointer hover:bg-base-200 flex items-center gap-2", {
                    "bg-primary/10 text-primary": isSelected,
                    "opacity-50 cursor-not-allowed": isDisabled,
                  })}
                  onClick={() => !isDisabled && handleOptionClick(optionValue)}
                >
                  <input
                    type="checkbox"
                    checked={isSelected}
                    onChange={() => {}} // Controlled by parent click
                    className={`checkbox checkbox-${size} checkbox-${variant}`}
                    disabled={isDisabled}
                    tabIndex={-1}
                  />
                  <span className="flex-1">{optionText}</span>
                </div>
              );
            })}
            {options.length === 0 && <div className="px-3 py-2 text-base-content/50">No options available</div>}
          </div>
        )}
      </div>

      {/* Displays error message if error prop is true */}
      {error && <ErrorText text={errorText} withIcon={errorIcon} />}
    </Fragment>
  );
};

// Exporting MultiSelect component as default
export default MultiSelect;
