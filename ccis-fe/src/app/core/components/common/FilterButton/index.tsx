//This is for all of the important imports
import { useState, ReactNode, useRef, useEffect } from "react";
import Button from "../Button";
import { FiFilter } from "react-icons/fi";

//For props that would be inserted into the button depending on the property
type Props<T> = {
  onApply?: (criteria: T) => void;
  children?: ReactNode | ((props: { criteria: T; setCriteria: (criteria: T) => void }) => ReactNode);
  isOpen?: boolean;
  onClose?: () => void;
  onClear?: () => void;
  initialCriteria?: T;
  isMobile?: boolean;
};

//This is the main function of the component
const FilterButton = <T extends Record<string, any>>({ 
    onApply, 
    children, 
    isOpen, 
    initialCriteria = {} as T,
    isMobile = false,
}: Props<T>) => {
 
  //For checking if the modal panel is open or closed
  const [isModalOpen, setIsModalOpen] = useState<boolean>(isOpen ?? false);
  //Setting the filter parameters
  const [criteria, setCriteria] = useState<T>(initialCriteria);
  //For reference to the Filter panel, primarily for the click outside to close modal function
  const modalRef = useRef<HTMLDivElement>(null);

  //* Refactor this code. It's only gonna disappear if we click anything outside of the button any NOTHING ELSE.
  // Handle click outside to close the modal
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        setIsModalOpen(false);
      }
    };

    if (isModalOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isModalOpen]);
  //End click outside functionality

  //For apply button
  const handleApply = () => {
    if (onApply) {
      onApply(criteria);
    }
    setIsModalOpen(false);
  };

  //For clear button
  const handleClear = () => {
    setIsModalOpen(false);
  };

  //For toggle button, primarily to open and close the modal
  const handleToggle = () => {
    setIsModalOpen((prev) => !prev);
  };

  return (
    <div className="flex relative">
      <Button 
        classNames="border border-slate-400 hover:border-slate-400 hover:bg-slate-100 bg-white btn rounded-l btn-sm" 
        onClick={handleToggle}
        isSubmitting={isModalOpen}
      >
        <FiFilter className="text-slate-400 hover:text-slate-600" size={15} />
      </Button>
      <div ref={modalRef}>
        <div
          //* The isModalOpen is for the modal to appear and disappear at will. 
          // 56 is the x translate
          className={`bg-zinc-100 border border-zinc-200 rounded-3xl h-max absolute top-10 p-4 pb-8 transition duration-500 ${isModalOpen ? "opacity-100 z-[50]" : "opacity-0 -z-[60] pointer-events-none"} ${isMobile ? 'left-0 -translate-x-[58%] w-80' : 'right-0 w-96'}`} 
        >
          <div>
            {typeof children === 'function' 
              ? children({ criteria, setCriteria }) 
              : children}
          </div>

          <div className="w-full text-xs justify-end flex mt-4 gap-4">
            <div className="text-info flex items-center justify-center cursor-pointer px-4 border p-2 rounded" onClick={handleClear}>
              Cancel
            </div>
            <div className="text-info flex items-center justify-center cursor-pointer px-4 border p-2 rounded" onClick={handleApply}>
              Apply Filters
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterButton;
