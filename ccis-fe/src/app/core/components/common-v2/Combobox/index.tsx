import TextField from "@components/form/TextField";
import { ChangeEvent, useEffect, useState } from "react";
import { IoMdClose, IoMdSearch } from "react-icons/io";
import { LuChevronsUpDown } from "react-icons/lu";

type ComboboxProps = {
  suggestionOptions: any[];
  optionLabel: (item: any) => string;
  optionValue: (item: any) => string;
  placeholder?: string;
  onInputChange?: (e: ChangeEvent<HTMLInputElement>) => void;
  onClear?: () => void;
  setData?: (item: any) => void;
};

export const Combobox = ({ suggestionOptions, optionLabel, optionValue, placeholder = "Select", onInputChange, onClear, setData }: ComboboxProps) => {
  const [value, setValue] = useState<string | number | null>(null);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);

  useEffect(() => {
    if (value !== null) {
      setSelectedItem(optionLabel(suggestionOptions.find((item) => optionValue(item).toString() === value)));
    }
  }, [value]);

  const handleSelect = (item: any) => {
    setValue(optionValue(item).toString());
    setData?.(item);

    // Blur the currently focused element to close the dropdown
    if (document.activeElement instanceof HTMLElement) {
      document.activeElement.blur();
    }
  };

  const clearData = () => {
    setSelectedItem(null);
    setValue(null);
    onClear?.();
  };

  return (
    <>
      <div className="dropdown dropdown-start w-full">
        <div tabIndex={0} role="button" className="btn btn-sm w-full">
          {selectedItem ? (
            <div className="flex justify-between w-full">
              {selectedItem}
              <IoMdClose size={"1rem"} onClick={clearData} />
            </div>
          ) : (
            <div className="flex justify-between w-full">
              {placeholder}
              <LuChevronsUpDown />
            </div>
          )}
        </div>
        <ul tabIndex={0} className="dropdown-content list-none bg-base-100 overflow-y-auto overflow-x-hidden rounded-box z-1 w-full max-h-[200px] p-2 shadow-md">
          <div className="mb-1 mt-2">
            <TextField className="border-0 focus-within:outline-none" size="sm" leftIcon={<IoMdSearch />} placeholder="Search..." onChange={onInputChange} />
            <div className="divider mt-0 mb-0 " />
          </div>

          {suggestionOptions.map((item, index) => {
            return (
              <li key={index} className="cursor-pointer rounded px-2 py-2 mr-3 hover:bg-[#d4d4d4] " onClick={() => handleSelect(item)}>
                {optionLabel(item)}
              </li>
            );
          })}
        </ul>
      </div>
    </>
  );
};
