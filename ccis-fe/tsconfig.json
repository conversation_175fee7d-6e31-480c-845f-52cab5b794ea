{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* Import Alias*/
    "baseUrl": "./",
    "paths": {
      "@assets/*": ["assets/*"],
      "@clients/*": ["src/app/core/clients/*"],
      "@components/*": ["src/app/core/components/*"],
      "@constants/*": ["src/app/core/constants/*"],
      "@context/*": ["src/app/core/context/*"],
      "@enums/*": ["src/app/core/enums/*"],
      "@helpers/*": ["src/app/core/helpers/*"],
      "@interface/*": ["src/app/core/interface/*"],
      "@layouts/*": ["src/app/core/layouts/*"],
      "@services/*": ["src/app/core/services/*"],
      "@state/*": ["src/app/core/state/*"],
      "@modules/*": ["src/app/modules/*"],
      "@hooks/*": ["src/app/core/hooks/*"]
    }
  },
  "include": ["src", "src/app/modules/chart-of-accounts/components/Modals/CreateNewChartOfAccount.tsx"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
