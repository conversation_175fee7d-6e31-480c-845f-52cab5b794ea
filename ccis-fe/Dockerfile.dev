
# # Development Dockerfile
# FROM node:18-alpine
# # Set working directory
# WORKDIR /app
# # Copy package files
# COPY package*.json ./
# # Install dependencies
# RUN npm install
# # Copy source code
# COPY . .
# # Expose port
# EXPOSE 3000
# # Start development server (use "dev" if that's your script name)
# CMD ["npm", "run", "dev"]

# Production Dockerfile
FROM node:18-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .

# Accept build arguments for all VITE_ variables
ARG VITE_API_ENDPOINT
ARG VITE_AWS_S3_ENDPOINT
ARG VITE_APP_NAME
ARG VITE_APP_FRONTEND
ARG VITE_APP_VERSION
ARG VITE_HOST
ARG VITE_REVERB_APP_ID
ARG VITE_REVERB_APP_KEY
ARG VITE_REVERB_APP_SECRET
ARG VITE_REVERB_HOST
ARG VITE_REVERB_WSPORT
ARG VITE_REVERB_WSSPORT
ARG VITE_REVERB_SCHEME
ARG VITE_REVERB_FORCETLS
ARG VITE_REVERB_DISABLE_STATS
ARG VITE_REVERB_ENABLED_TRANSPORTS

# Set environment variables for build
ENV VITE_API_ENDPOINT=$VITE_API_ENDPOINT
ENV VITE_AWS_S3_ENDPOINT=$VITE_AWS_S3_ENDPOINT
ENV VITE_APP_NAME=$VITE_APP_NAME
ENV VITE_APP_FRONTEND=$VITE_APP_FRONTEND
ENV VITE_APP_VERSION=$VITE_APP_VERSION
ENV VITE_HOST=$VITE_HOST
ENV VITE_REVERB_APP_ID=$VITE_REVERB_APP_ID
ENV VITE_REVERB_APP_KEY=$VITE_REVERB_APP_KEY
ENV VITE_REVERB_APP_SECRET=$VITE_REVERB_APP_SECRET
ENV VITE_REVERB_HOST=$VITE_REVERB_HOST
ENV VITE_REVERB_WSPORT=$VITE_REVERB_WSPORT
ENV VITE_REVERB_WSSPORT=$VITE_REVERB_WSSPORT
ENV VITE_REVERB_SCHEME=$VITE_REVERB_SCHEME
ENV VITE_REVERB_FORCETLS=$VITE_REVERB_FORCETLS
ENV VITE_REVERB_DISABLE_STATS=$VITE_REVERB_DISABLE_STATS
ENV VITE_REVERB_ENABLED_TRANSPORTS=$VITE_REVERB_ENABLED_TRANSPORTS

RUN npm run build

FROM nginx:alpine
WORKDIR /usr/share/nginx/html
COPY --from=build /app/dist ./
# Copy nginx config file
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
